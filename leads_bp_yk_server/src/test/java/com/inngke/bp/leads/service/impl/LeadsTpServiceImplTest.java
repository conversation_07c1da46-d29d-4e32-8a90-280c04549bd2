package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.service.AiCustomerServiceLeadsService;
import com.inngke.common.dto.request.BaseBidRequest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/3/3 11:38
 */
public class LeadsTpServiceImplTest extends BaseJunitTest {

    @Autowired
    private AiCustomerServiceLeadsService aiCustomerServiceLeadsService;

    @Test
    public void testAiCustomerPull() {
//        BaseBidRequest request = new BaseBidRequest();
//        request.setBid(1);
//
//        aiCustomerServiceLeadsService.pullLeads(request);
    }
}

//package com.inngke.bp.leads.db.leads.manager.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.inngke.bp.leads.db.leads.entity.*;
//import com.inngke.bp.leads.dto.LeadsTransferResultDto;
//import com.inngke.bp.leads.dto.request.GetPreFollowStaffReportRequest;
//import com.inngke.bp.leads.dto.request.LeadsEditByInforRequest;
//import com.inngke.bp.leads.dto.request.LeadsForwardRequest;
//import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
//import com.inngke.bp.leads.dto.response.PreFollowStaffReportDto;
//import com.inngke.bp.leads.enums.LeadsTransferEnum;
//import com.inngke.bp.leads.facatory.LeadsFollowFactory;
//import com.inngke.bp.organize.dto.response.StaffDto;
//import com.inngke.common.service.JsonService;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.util.*;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class LeadsManagerImplTest {
//
//    @Mock
//    private JsonService mockJsonService;
//    @Mock
//    private LeadsFollowFactory mockLeadsFollowFactory;
//
//    @Mock
//    private LeadsManagerImpl leadsManagerImplUnderTest;
//
//    @Test
//    public void testAddLeads() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//
//        when(mockJsonService.toJson(new Leads())).thenReturn("followInfo");
//
//        // Run the test
//        final Long result = leadsManagerImplUnderTest.addLeads(leads, 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//    }
//
//    @Test
//    public void testSaveDistributeLeads() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> leadsList = Arrays.asList(leads);
//        when(mockJsonService.toJson(new Leads())).thenReturn("followInfo");
//
//        // Run the test
//        leadsManagerImplUnderTest.saveDistributeLeads(leadsList, 0L);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testUpdateLeads1() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//
//        when(mockJsonService.toJson(new Leads())).thenReturn("followInfo");
//
//        // Run the test
//        leadsManagerImplUnderTest.updateLeads(leads, 0L);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testPushBack() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//
//        when(mockJsonService.toJson(new Leads())).thenReturn("followInfo");
//
//        // Run the test
//        leadsManagerImplUnderTest.pushBack(leads, 0L);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testBatchDelete() {
//        // Setup
//        // Run the test
//        leadsManagerImplUnderTest.batchDelete(0, Arrays.asList(0L), 0L);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testForward() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//
//        final LeadsForwardRequest request = new LeadsForwardRequest();
//        request.setBid(0);
//        request.setOperatorId(0L);
//        request.setStaffId(0L);
//        request.setOperatorStaffId(0L);
//
//        when(mockJsonService.toJson(new Leads())).thenReturn("followInfo");
//
//        // Run the test
//        leadsManagerImplUnderTest.forward(leads, request, 0L, "forwardStaffName", "acceptStaffName");
//
//        // Verify the results
//    }
//
//    @Test
//    public void testGetGroupCount() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final QueryWrapper<Leads> queryWrapper = new QueryWrapper<>(leads);
//
//        // Run the test
//        final Integer result = leadsManagerImplUnderTest.getGroupCount(queryWrapper);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0);
//    }
//
//    @Test
//    public void testRecoveryLeads1() {
//        // Setup
//        when(mockJsonService.toJson(new Leads())).thenReturn("followInfo");
//
//        // Run the test
//        final List<Long> result = leadsManagerImplUnderTest.recoveryLeads(Arrays.asList(0L), 0L, 0, 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(Arrays.asList(0L));
//    }
//
//    @Test
//    public void testUpdateLeads2() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//
//        final LeadsFollow leadsFollow = LeadsFollow.builder()
//                .bid(0)
//                .leadsId(0L)
//                .staffId(0L)
//                .userId(0L)
//                .followType(0)
//                .followContent("followInfo")
//                .leadsStatus(0)
//                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .build();
//        final LeadsLog leadsLog = new LeadsLog();
//        leadsLog.setId(0);
//        leadsLog.setBid(0);
//        leadsLog.setLeadsId(0L);
//        leadsLog.setOperatorId(0L);
//        leadsLog.setDistributeStaffId(0L);
//        leadsLog.setStatusChange(0);
//        leadsLog.setLogContent("followInfo");
//        leadsLog.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//
//        // Run the test
//        final Boolean result = leadsManagerImplUnderTest.updateLeads(leads, leadsFollow, leadsLog);
//
//        // Verify the results
//        assertThat(result).isFalse();
//    }
//
//    @Test
//    public void testGetMobileAndDistributeTime() {
//        // Setup
//        final Map<String, LocalDateTime> expectedResult = new HashMap<>();
//
//        // Run the test
//        final Map<String, LocalDateTime> result = leadsManagerImplUnderTest.getMobileAndDistributeTime();
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    public void testEditByInfo() {
//        // Setup
//        final LeadsEditByInforRequest request = new LeadsEditByInforRequest();
//        request.setBid(0);
//        request.setOperatorId(0L);
//        request.setId(0L);
//        request.setName("name");
//        request.setMobile("mobile");
//        request.setWeChat("weChat");
//        request.setProvinceId(0);
//        request.setProvinceName("provinceName");
//        request.setCityId(0);
//        request.setCityName("cityName");
//        request.setAreaId(0);
//        request.setGender(0);
//        request.setAge(0);
//        request.setQq("qq");
//        request.setEmail("email");
//
//        // Run the test
//        final Boolean result = leadsManagerImplUnderTest.editByInfo(request);
//
//        // Verify the results
//        assertThat(result).isFalse();
//    }
//
//    @Test
//    public void testGetById() {
//        // Setup
//        final Leads expectedResult = new Leads();
//        expectedResult.setId(0L);
//        expectedResult.setBid(0);
//        expectedResult.setCustomerId(0L);
//        expectedResult.setCustomerUid(0L);
//        expectedResult.setChannelId(0L);
//        expectedResult.setName("name");
//        expectedResult.setMobile("mobile");
//        expectedResult.setWeChat("weChat");
//        expectedResult.setStatus(0);
//        expectedResult.setContactIn24(0);
//        expectedResult.setProvinceId(0);
//        expectedResult.setProvinceName("provinceName");
//        expectedResult.setCityId(0);
//        expectedResult.setCityName("cityName");
//        expectedResult.setAreaId(0);
//        expectedResult.setAreaName("areaName");
//        expectedResult.setAddress("address");
//        expectedResult.setChannel(0);
//        expectedResult.setChannelType(0);
//        expectedResult.setChannelSource(0);
//        expectedResult.setOrderAccount("orderAccount");
//        expectedResult.setOrderSn("orderSn");
//        expectedResult.setGoodsName("goodsName");
//        expectedResult.setGoodsLink("goodsLink");
//        expectedResult.setGoodsNum(0);
//        expectedResult.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setPayAmount(new BigDecimal("0.00"));
//        expectedResult.setOrderMessage("orderMessage");
//        expectedResult.setRemark("remark");
//        expectedResult.setTpLeadsId("externalId");
//        expectedResult.setPromotionName("promotionName");
//        expectedResult.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setExpectIn(0);
//        expectedResult.setStyle(0);
//        expectedResult.setBatchId(0L);
//        expectedResult.setDistributeStaffId(0L);
//        expectedResult.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setErrorMsg("followInfo");
//        expectedResult.setLastFollowId(0L);
//        expectedResult.setExtData("extData");
//        expectedResult.setTags("tags");
//        expectedResult.setRecoveryFrom(0L);
//        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        expectedResult.setPreFollowStaffId(0L);
//        expectedResult.setPreFollowStatus(0);
//        expectedResult.setType(0);
//        expectedResult.setCreateStaffId(0L);
//        expectedResult.setLevel("level");
//        expectedResult.setRecoveryFromIds("");
//
//        // Run the test
//        final Leads result = leadsManagerImplUnderTest.getById(0, 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    public void testTransferLeads1() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> leadsList = Arrays.asList(leads);
//        final LeadsHistoryDistribute leadsHistoryDistribute = new LeadsHistoryDistribute();
//        leadsHistoryDistribute.setId(0L);
//        leadsHistoryDistribute.setBid(0);
//        leadsHistoryDistribute.setLeadsId(0L);
//        leadsHistoryDistribute.setDistributeStaffId(0L);
//        leadsHistoryDistribute.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        final List<LeadsHistoryDistribute> historyDistributes = Arrays.asList(leadsHistoryDistribute);
//
//        // Run the test
//        final Integer result = leadsManagerImplUnderTest.transferLeads(leadsList, historyDistributes, 0L,
//                "targetStaffName", 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0);
//    }
//
//    @Test
//    public void testTransferLeads2() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> leadsList = Arrays.asList(leads);
//        final StaffDto targetStaff = new StaffDto();
//        targetStaff.setId(10L);
//        targetStaff.setCustomerId(0L);
//        targetStaff.setName("name");
//        targetStaff.setGender(0);
//        targetStaff.setQyUserId("qyUserId");
//        targetStaff.setQyQrCode("qyQrCode");
//        targetStaff.setMobile("mobile");
//        targetStaff.setDepartmentId(0L);
//        targetStaff.setPosition("position");
//        targetStaff.setStatus(0);
//        targetStaff.setPassword("password");
//        targetStaff.setCreateTime(0L);
//        targetStaff.setUpdateTime(0L);
//        targetStaff.setBid(0);
//        targetStaff.setGeoLon("geoLon");
//
//        // Configure LeadsFollowFactory.createFollow(...).
//        final LeadsFollow leadsFollow = LeadsFollow.builder()
//                .bid(0)
//                .leadsId(0L)
//                .staffId(0L)
//                .userId(0L)
//                .followType(0)
//                .followContent("followInfo")
//                .leadsStatus(0)
//                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .build();
//        when(mockLeadsFollowFactory.createFollow(LeadsTransferEnum.STAFF_TRANSFER)).thenReturn(leadsFollow);
//
//        // Run the test
//        final LeadsTransferResultDto result = leadsManagerImplUnderTest.transferLeads(leadsList, targetStaff,
//                LeadsTransferEnum.STAFF_TRANSFER);
//
//        // Verify the results
//        Assert.assertNotNull(result);
//        Assert.assertEquals(result.getTransferLeadsList().get(0).getDistributeStaffId(), targetStaff.getId());
//        Assert.assertEquals(result.getTransferLeadsHistoryDistributeList().get(0).getDistributeStaffId(), targetStaff.getId());
//    }
//
//    @Test
//    public void testListByPreFollowStaffIds() {
//        // Setup
//        final GetPreFollowStaffReportRequest request = new GetPreFollowStaffReportRequest();
//        request.setBid(0);
//        request.setOperatorId(0L);
//        request.setStartTime("startTime");
//        request.setEndTime("endTime");
//        request.setId(0L);
//
//        // Run the test
//        final List<PreFollowStaffReportDto> result = leadsManagerImplUnderTest.listByPreFollowStaffIds(request,
//                Arrays.asList(0L));
//
//        // Verify the results
//    }
//
//    @Test
//    public void testImportLeadsAndUpdateLeadsBatch() {
//        // Setup
//        final LeadsInformationExtend leadsInformationExtend = new LeadsInformationExtend();
//        leadsInformationExtend.setId(0L);
//        leadsInformationExtend.setBid(0);
//        leadsInformationExtend.setCustomerId(0L);
//        leadsInformationExtend.setCustomerUid(0L);
//        leadsInformationExtend.setChannelId(0L);
//        leadsInformationExtend.setName("name");
//        leadsInformationExtend.setMobile("mobile");
//        leadsInformationExtend.setWeChat("weChat");
//        leadsInformationExtend.setStatus(0);
//        leadsInformationExtend.setContactIn24(0);
//        leadsInformationExtend.setProvinceId(0);
//        leadsInformationExtend.setProvinceName("provinceName");
//        leadsInformationExtend.setCityId(0);
//        leadsInformationExtend.setCityName("cityName");
//        leadsInformationExtend.setAreaId(0);
//        leadsInformationExtend.setAreaName("areaName");
//        leadsInformationExtend.setAddress("address");
//        leadsInformationExtend.setChannel(0);
//        leadsInformationExtend.setChannelType(0);
//        leadsInformationExtend.setChannelSource(0);
//        leadsInformationExtend.setOrderAccount("orderAccount");
//        leadsInformationExtend.setOrderSn("orderSn");
//        leadsInformationExtend.setGoodsName("goodsName");
//        leadsInformationExtend.setGoodsLink("goodsLink");
//        leadsInformationExtend.setGoodsNum(0);
//        leadsInformationExtend.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setPayAmount(new BigDecimal("0.00"));
//        leadsInformationExtend.setOrderMessage("orderMessage");
//        leadsInformationExtend.setRemark("remark");
//        leadsInformationExtend.setTpLeadsId("externalId");
//        leadsInformationExtend.setPromotionName("promotionName");
//        leadsInformationExtend.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setExpectIn(0);
//        leadsInformationExtend.setStyle(0);
//        leadsInformationExtend.setBatchId(0L);
//        leadsInformationExtend.setDistributeStaffId(0L);
//        leadsInformationExtend.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setErrorMsg("followInfo");
//        leadsInformationExtend.setLastFollowId(0L);
//        leadsInformationExtend.setExtData("extData");
//        leadsInformationExtend.setTags("tags");
//        leadsInformationExtend.setRecoveryFrom(0L);
//        leadsInformationExtend.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsInformationExtend.setPreFollowStaffId(0L);
//        leadsInformationExtend.setPreFollowStatus(0);
//        leadsInformationExtend.setType(0);
//        leadsInformationExtend.setCreateStaffId(0L);
//        leadsInformationExtend.setLevel("level");
//        leadsInformationExtend.setRecoveryFromIds("");
//        leadsInformationExtend.setExternalId("externalId");
//        leadsInformationExtend.setGender(0);
//        leadsInformationExtend.setCampaignId("campaignId");
//        leadsInformationExtend.setCampaignName("campaignName");
//        leadsInformationExtend.setAccountId("accountId");
//        leadsInformationExtend.setAccountName("accountName");
//        leadsInformationExtend.setSubmitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        final List<LeadsInformationExtend> leadsInformationExtends = Arrays.asList(leadsInformationExtend);
//        final LeadsBatch leadsBatch = new LeadsBatch();
//        leadsBatch.setId(0L);
//        leadsBatch.setBid(0);
//        leadsBatch.setChannel(0);
//        leadsBatch.setFileUrl("fileUrl");
//        leadsBatch.setFileType(0);
//        leadsBatch.setProcessStatus(0);
//        leadsBatch.setSuccessCount(0);
//        leadsBatch.setErrorCount(0);
//        leadsBatch.setStaffId(0L);
//        leadsBatch.setErrorFileUrl("errorFileUrl");
//        leadsBatch.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsBatch.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//
//        // Run the test
//        final Set<Long> result = leadsManagerImplUnderTest.importLeadsAndUpdateLeadsBatch(leadsInformationExtends,
//                leadsBatch, 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(new HashSet<>(Arrays.asList(0L)));
//    }
//
//    @Test
//    public void testBatchSaveLeads() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> leadsList = Arrays.asList(leads);
//
//        // Run the test
//        final int result = leadsManagerImplUnderTest.batchSaveLeads(leadsList, 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0);
//    }
//
//    @Test
//    public void testUpdateStatus() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//
//        // Run the test
//        leadsManagerImplUnderTest.updateStatus(leads, 0L, "followInfo", 0);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testPageStatistics() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final QueryWrapper<Leads> wrapper = new QueryWrapper<>(leads);
//
//        // Run the test
//        final List<LeadsStatisticsDto> result = leadsManagerImplUnderTest.pageStatistics(wrapper);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testStrToIds() {
//        assertThat(leadsManagerImplUnderTest.strToIds(0L, "recoveryFromIds"))
//                .isEqualTo(new HashSet<>(Arrays.asList(0L)));
//        assertThat(leadsManagerImplUnderTest.strToIds(0L, "recoveryFromIds")).isEqualTo(Collections.emptySet());
//    }
//
//    @Test
//    public void testSelectBadCustomerLeads() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> expectedResult = Arrays.asList(leads);
//
//        // Run the test
//        final List<Leads> result = leadsManagerImplUnderTest.selectBadCustomerLeads(0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    public void testListByKeyword() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> expectedResult = Arrays.asList(leads);
//
//        // Run the test
//        final List<Leads> result = leadsManagerImplUnderTest.listByKeyword(0, Arrays.asList("value"),
//                new HashSet<>(Arrays.asList(0L)));
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    public void testFindLeadsByClient() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> expectedResult = Arrays.asList(leads);
//
//        // Run the test
//        final List<Leads> result = leadsManagerImplUnderTest.findLeadsByClient(0, 0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    public void testDoTransferLeads() {
//        // Setup
//        final LeadsTransferResultDto leadsTransferResultDto = new LeadsTransferResultDto();
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        leadsTransferResultDto.setTransferLeadsList(Arrays.asList(leads));
//        leadsTransferResultDto.setTransferLeadsFollowList(Arrays.asList(LeadsFollow.builder()
//                .bid(0)
//                .leadsId(0L)
//                .staffId(0L)
//                .userId(0L)
//                .followType(0)
//                .followContent("followInfo")
//                .leadsStatus(0)
//                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .build()));
//        final LeadsHistoryDistribute leadsHistoryDistribute = new LeadsHistoryDistribute();
//        leadsHistoryDistribute.setBid(0);
//        leadsHistoryDistribute.setLeadsId(0L);
//        leadsHistoryDistribute.setDistributeStaffId(0L);
//        leadsHistoryDistribute.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leadsTransferResultDto.setTransferLeadsHistoryDistributeList(Arrays.asList(leadsHistoryDistribute));
//
//        // Run the test
//        final Boolean result = leadsManagerImplUnderTest.doTransferLeads(leadsTransferResultDto);
//
//        // Verify the results
//        assertThat(result).isFalse();
//    }
//
//    @Test
//    public void testGetByIds() {
//        // Setup
//        final Leads leads = new Leads();
//        leads.setId(0L);
//        leads.setBid(0);
//        leads.setCustomerId(0L);
//        leads.setCustomerUid(0L);
//        leads.setChannelId(0L);
//        leads.setName("name");
//        leads.setMobile("mobile");
//        leads.setWeChat("weChat");
//        leads.setStatus(0);
//        leads.setContactIn24(0);
//        leads.setProvinceId(0);
//        leads.setProvinceName("provinceName");
//        leads.setCityId(0);
//        leads.setCityName("cityName");
//        leads.setAreaId(0);
//        leads.setAreaName("areaName");
//        leads.setAddress("address");
//        leads.setChannel(0);
//        leads.setChannelType(0);
//        leads.setChannelSource(0);
//        leads.setOrderAccount("orderAccount");
//        leads.setOrderSn("orderSn");
//        leads.setGoodsName("goodsName");
//        leads.setGoodsLink("goodsLink");
//        leads.setGoodsNum(0);
//        leads.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPayAmount(new BigDecimal("0.00"));
//        leads.setOrderMessage("orderMessage");
//        leads.setRemark("remark");
//        leads.setTpLeadsId("externalId");
//        leads.setPromotionName("promotionName");
//        leads.setRegistryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setExpectIn(0);
//        leads.setStyle(0);
//        leads.setBatchId(0L);
//        leads.setDistributeStaffId(0L);
//        leads.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setDistributeFollowTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPushBackTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setErrorMsg("followInfo");
//        leads.setLastFollowId(0L);
//        leads.setExtData("extData");
//        leads.setTags("tags");
//        leads.setRecoveryFrom(0L);
//        leads.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
//        leads.setPreFollowStaffId(0L);
//        leads.setPreFollowStatus(0);
//        leads.setType(0);
//        leads.setCreateStaffId(0L);
//        leads.setLevel("level");
//        leads.setRecoveryFromIds("");
//        final List<Leads> expectedResult = Arrays.asList(leads);
//
//        // Run the test
//        final List<Leads> result = leadsManagerImplUnderTest.getByIds(new HashSet<>(Arrays.asList(0L)));
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//}

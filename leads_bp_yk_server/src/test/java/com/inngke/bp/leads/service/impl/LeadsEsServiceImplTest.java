package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.LeadsEsBatchRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.response.EsDocsResponse;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by emiya on 2022/6/15 22:28
 *
 * <AUTHOR>
 * @date 2022/6/15 22:28
 */
public class LeadsEsServiceImplTest extends BaseJunitTest {
    @Autowired
    private LeadsEsService leadsEsService;

    @Test
    public void testGetBatchDoc() {
        LeadsEsBatchRequest request = new LeadsEsBatchRequest();
        request.setLastLeadsId(0L);
        request.setPageSize(1000);
        BaseResponse<EsDocsResponse<LeadsEsDto>> resp = leadsEsService.getBatchDoc(request);
        Assert.assertNotNull(resp);

    }
}
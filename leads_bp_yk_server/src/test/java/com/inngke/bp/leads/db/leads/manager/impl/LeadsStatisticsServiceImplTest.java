package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.StaffLeadsCountRequest;
import com.inngke.bp.leads.dto.response.StaffLeadsCountDto;
import com.inngke.bp.leads.service.LeadsStatisticsService;
import com.inngke.common.dto.response.BaseResponse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/17 5:16 PM
 */
public class LeadsStatisticsServiceImplTest extends BaseJunitTest {
    @Autowired
    private LeadsStatisticsService leadsStatisticsService;

    @Test
    public void testGetStaffLeadsCount() {
        StaffLeadsCountRequest request = new StaffLeadsCountRequest();
        request.setDate(null);
        request.setOperatorId(0L);
        request.setBid(1);

        BaseResponse<List<StaffLeadsCountDto>> resp = leadsStatisticsService.getStaffLeadsCount(request);
        System.out.println(resp.getCode());
    }
}
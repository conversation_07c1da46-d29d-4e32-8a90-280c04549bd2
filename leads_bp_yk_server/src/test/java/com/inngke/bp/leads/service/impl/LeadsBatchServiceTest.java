package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.LeadsDraftExcelDto;
import com.inngke.bp.leads.dto.request.LeadsBatchGetRequest;
import com.inngke.bp.leads.dto.request.LeadsBatchImportRequest;
import com.inngke.bp.leads.dto.request.LeadsBatchListRequest;
import com.inngke.bp.leads.dto.response.LeadsBatchDto;
import com.inngke.bp.leads.dto.response.LeadsBatchImportDto;
import com.inngke.bp.leads.dto.response.LeadsDraftDto;
import com.inngke.bp.leads.service.LeadsBatchService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class LeadsBatchServiceTest extends BaseJunitTest {

    @Autowired
    private LeadsBatchService leadsBatchService;

    @Test
    public void testCreate() {
        LeadsBatchImportRequest request = new LeadsBatchImportRequest();
        request.setBid(1);
        request.setFileUrl("https://static.inngke.com/file/%E5%B9%BF%E5%91%8A%E6%8A%95%E6%94%BE%E8%8E%B7%E5%8F%96%E7%BA%BF%E7%B4%A2%E6%A8%A1%E6%9D%BF(1).xlsx");

        BaseResponse<Long> resp = leadsBatchService.create(request);
        Assert.assertNotNull("结果不应该为空", resp);
    }

    @Test
    public void testGet() {
        LeadsBatchGetRequest request = new LeadsBatchGetRequest();
        request.setBid(1);
        request.setLeadsBatchId(1l);

        BaseResponse<LeadsBatchDto> resp = leadsBatchService.get(request);
        Assert.assertNotNull("结果不应该为空", resp);
    }

    @Test
    public void testGetLeadsDraftList() {
        LeadsBatchListRequest request = new LeadsBatchListRequest();
        request.setPageNo(1);
        request.setPageSize(20);
        request.setLeadsBatchId(6L);

        BaseResponse<BasePaginationResponse<LeadsDraftDto>> resp = leadsBatchService.getLeadsDraftList(request);
        Assert.assertNotNull("结果不应该为空", resp);
        Assert.assertEquals("code应该为0", 0, resp.getCode().intValue());
    }

    @Test
    public void testImportLeadsBatch() {
        LeadsBatchGetRequest request = new LeadsBatchGetRequest();
        request.setBid(1134);
        request.setOperatorId(1024L);
        request.setLeadsBatchId(6L);

        BaseResponse<LeadsBatchImportDto> resp = leadsBatchService.importLeadsBatch(request);
        Assert.assertNotNull("结果不应该为空", resp);
    }

}

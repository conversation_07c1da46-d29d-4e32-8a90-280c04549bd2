package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.dto.request.LeadsDepStaffStatisticsQuery;
import com.inngke.bp.leads.dto.response.LeadsDepartmentStaffStatisticsDto;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.pulsar.shade.org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/9/28 15:53
 **/
public class LeadsServiceTest extends BaseJunitTest {

    @Autowired
    private LeadsService leadsService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Test
    public void getLeadsStatisticsList() {
        LeadsDepStaffStatisticsQuery leadsDepStaffStatisticsQuery = new LeadsDepStaffStatisticsQuery();
        leadsDepStaffStatisticsQuery.setBid(7);
        leadsDepStaffStatisticsQuery.setStaffId(1505L);
        leadsDepStaffStatisticsQuery.setDepartmentId(4410L);
        BaseResponse<LeadsDepartmentStaffStatisticsDto> response = leadsService.getLeadsStatisticsList(leadsDepStaffStatisticsQuery);
        LeadsDepartmentStaffStatisticsDto data = response.getData();
        System.out.println(data);
    }

    @Test
    public void getCountMapByDeptIds() {
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(7);
        baseIdsRequest.setIds(Arrays.asList(4384L));
        Map<Long, Integer> countMapByDeptIds = staffClientForLeads.getCountMapByDeptIds(baseIdsRequest);
        System.out.println(countMapByDeptIds);
    }

}

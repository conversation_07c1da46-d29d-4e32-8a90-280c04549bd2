//package com.inngke.bp.leads.service;
//
//import com.google.common.collect.Lists;
//import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;
//import com.inngke.bp.leads.db.leads.manager.LeadsManager;
//import com.inngke.bp.leads.db.leads.manager.LeadsPreFollowConfigManager;
//import com.inngke.bp.leads.dto.request.GetPreFollowStaffReportRequest;
//import com.inngke.bp.leads.dto.request.GetPreFollowStaffRequest;
//import com.inngke.bp.leads.dto.response.PreFollowStaffReportDto;
//import com.inngke.bp.leads.dto.response.SimplePreFollowStaffDto;
//import com.inngke.bp.leads.service.impl.PreFollowStaffServiceImpl;
//import com.inngke.bp.user.dto.response.StaffDto;
//import com.inngke.common.dto.response.BaseResponse;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.when;
//
//@RunWith(SpringRunner.class)
//public class PreFollowStaffServiceTest {
//
//    private final String deptName = "测试";
//    private final long staffId = 357L;
//    private final String mobile = "123";
//    private final String staffName = "姓名";
//    private final int dealCount = 1;
//    private final String totalAmount = "100";
//    private final int bid = 7;
//    private final String startTime = "2018-08-02";
//    private final String endTime = "2023-08-02";
//    @InjectMocks
//    private PreFollowStaffService preFollowStaffService = new PreFollowStaffServiceImpl();;
//
//    @Mock
//    private LeadsPreFollowConfigManager leadsPreFollowConfigManager;
//
//    @Mock
//    private GuideGetService guideGetService;
//
//    @Mock
//    protected LeadsManager leadsManager;
//
//    @Mock
//    private QyWxStaffService qyWxStaffService;
//
//
//    @Before
//    public void init() {
//        LeadsPreFollowConfig leadsPreFollowConfig = new LeadsPreFollowConfig();
//        leadsPreFollowConfig.setStaffId(staffId);
//        when(leadsPreFollowConfigManager.list(any())).thenReturn(Lists.newArrayList(leadsPreFollowConfig));
//        GuideDto guideDto = new GuideDto();
//        guideDto.setStaffId(staffId);
//        guideDto.setMobile(mobile);
//        guideDto.setStaffName(staffName);
//        guideDto.setDeptName(deptName);
//        when(guideGetService.getList(Mockito.argThat(i -> {
//            if (i.getStaffId().contains(staffId)) {
//                return true;
//            }
//            return false;
//        }))).thenReturn(BaseResponse.success(Lists.newArrayList(guideDto)));
//        PreFollowStaffReportDto preFollowStaffReportDto = new PreFollowStaffReportDto();
//        preFollowStaffReportDto.setStaffId(staffId);
//        preFollowStaffReportDto.setDealCount(dealCount);
//        preFollowStaffReportDto.setDealTotalAmount(totalAmount);
//        when(leadsManager.listByPreFollowStaffIds(Mockito.argThat(i -> {
//            if (i.getBid().equals(bid)) {
//                return true;
//            }
//            return false;
//        }),Mockito.argThat(i -> {
//            if (i.get(0).equals(staffId)) {
//                return true;
//            }
//            return false;
//        }))).thenReturn(Lists.newArrayList(preFollowStaffReportDto));
//    }
//
//    @Test
//    public void getPreFollowStaffReport() {
//        GetPreFollowStaffReportRequest getPreFollowStaffReportRequest = new GetPreFollowStaffReportRequest();
//        getPreFollowStaffReportRequest.setBid(bid);
//        getPreFollowStaffReportRequest.setStartTime(startTime);
//        getPreFollowStaffReportRequest.setEndTime(endTime);
//        BaseResponse<List<PreFollowStaffReportDto>> response = preFollowStaffService.getPreFollowStaffReport(getPreFollowStaffReportRequest);
//        PreFollowStaffReportDto preFollowStaffReportDto = response.getData().get(0);
//        Assert.assertTrue(preFollowStaffReportDto.getStaffId().equals(staffId));
//        Assert.assertTrue(preFollowStaffReportDto.getDealCount().equals(dealCount));
//        Assert.assertTrue(preFollowStaffReportDto.getMobile().equals(mobile));
//        Assert.assertTrue(preFollowStaffReportDto.getDeptName().equals(deptName));
//        Assert.assertTrue(preFollowStaffReportDto.getDealTotalAmount().equals(totalAmount));
//    }
//
//    @Before
//    public void getQyWxStaffSimpleInfosInit() {
//        StaffDto staffDto = new StaffDto();
//        staffDto.setId(staffId);
//        List<StaffDto> staffDtoList = Lists.newArrayList(staffDto);
//        when(qyWxStaffService.getQyWxStaffSimpleInfos(Mockito.argThat(i -> {
//            if (i.getMobile().equals(mobile) && i.getBid().equals(bid)) {
//                return true;
//            }
//            return false;
//        }))).thenReturn(staffDtoList);
//    }
//
//    @Before
//    public void leadsPreFollowConfigManagerInit() {
//        LeadsPreFollowConfig leadsPreFollowConfig = getLeadsPreFollowConfig();
//        when(leadsPreFollowConfigManager.getByStaffId(bid, staffId)).thenReturn(leadsPreFollowConfig);
//    }
//
//    private LeadsPreFollowConfig getLeadsPreFollowConfig() {
//        LeadsPreFollowConfig leadsPreFollowConfig = new LeadsPreFollowConfig();
//        leadsPreFollowConfig.setId(1L);
//        leadsPreFollowConfig.setName("");
//        leadsPreFollowConfig.setBid(0);
//        leadsPreFollowConfig.setStaffId(0L);
//        leadsPreFollowConfig.setChannelIds("");
//        leadsPreFollowConfig.setRegionIds("");
//        leadsPreFollowConfig.setCreateTime(LocalDateTime.now());
//        leadsPreFollowConfig.setUpdateTime(LocalDateTime.now());
//        return leadsPreFollowConfig;
//    }
//
//    @Test
//    public void getPreFollowStaffTest() {
//        GetPreFollowStaffRequest getPreFollowStaffRequest = new GetPreFollowStaffRequest();
//        getPreFollowStaffRequest.setBid(bid);
//        getPreFollowStaffRequest.setMobile(mobile);
//        BaseResponse<SimplePreFollowStaffDto> response = preFollowStaffService.getPreFollowStaff(getPreFollowStaffRequest);
//        Assert.assertTrue(getLeadsPreFollowConfig().getStaffId().equals(response.getData().getStaffId()));
//    }
//}
package com.inngke.bp.leads.mq;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.mq.message.customer.CusromerChangeMessageDto;
import com.inngke.bp.leads.mq.message.leads.LeadsFollowMessage;
import com.inngke.bp.leads.mq.message.order.OrderChangeMessageDto;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.service.MqService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class LeadsCustomerChangeTest extends BaseJunitTest {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @Autowired
    private JsonService jsonService;


    @Test
    public void testProcess() throws InterruptedException {
        MqSendRequest mqSendRequest = new MqSendRequest();
//
//        CusromerChangeMessageDto cusromerChangeMessageDto = new CusromerChangeMessageDto();
//
//        cusromerChangeMessageDto.setBid(1);
//        cusromerChangeMessageDto.setEvent(4);
//        cusromerChangeMessageDto.setUid(1598L);
//        cusromerChangeMessageDto.setOperatorId(0L);
//        cusromerChangeMessageDto.setMobile("15918756309");

//        OrderChangeMessageDto orderChangeMessageDto = new OrderChangeMessageDto();
//        orderChangeMessageDto.setBid(1);
//        orderChangeMessageDto.setOrderType(2);
//        orderChangeMessageDto.setEvent(3);
////        orderChangeMessageDto.setOperatorId(1597L);
//        orderChangeMessageDto.setMobile("13223232323");

        LeadsFollowMessage leadsFollowMessage = new LeadsFollowMessage();
        leadsFollowMessage.setLeadsIds(Lists.newArrayList(215L,216L));
        leadsFollowMessage.setStaffId(328L);
        leadsFollowMessage.setOperatorId(0L);
        leadsFollowMessage.setType(2);
        leadsFollowMessage.setBid(1);

        mqSendRequest.setBid(1);
        mqSendRequest.setTopic("test_only");
        mqSendRequest.setPayload(jsonService.toJson(leadsFollowMessage));
        mqService.send(mqSendRequest);


    }
}

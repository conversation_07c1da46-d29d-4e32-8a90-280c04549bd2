package com.inngke.bp.leads.query;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.LeadsQuery;
import com.inngke.bp.leads.dto.response.LeadsListVo;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.common.dto.response.BaseResponse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @projectName: leads_bp_yk
 * @package: com.inngke.bp.leads.query
 * @className: LeadTest
 * @author: Cairunlin
 * @description:
 * @date: 2022/4/12 12:09
 * @version: 1.0
 */
public class LeadTest extends BaseJunitTest {

    @Autowired
    private LeadsService leadsService;


    @Test
    public void test() {
        LeadsQuery leadsQuery = new LeadsQuery();
        leadsQuery.setBid(7);
        leadsQuery.setLastUpdateTime(1649728980000L);
        BaseResponse<LeadsListVo> search = leadsService.search(leadsQuery);

        System.out.println(search);
    }
}

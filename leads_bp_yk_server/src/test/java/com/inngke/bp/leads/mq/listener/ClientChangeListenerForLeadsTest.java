package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.LeadsTransferResultDto;
import com.inngke.bp.leads.enums.LeadsTransferEnum;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.service.JsonService;
import org.apache.pulsar.client.api.Message;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ClientChangeListenerForLeadsTest {

    @Mock
    private JsonService mockJsonService;
    @Mock
    private LeadsManager mockLeadsManager;
    @Mock
    private StaffClientForLeads mockStaffClientForLeads;

    @InjectMocks
    private ClientChangeListenerForLeads clientTransferListenerOfLeadsUnderTest;

    @Test
    public void testProcess() {
        // Setup
        final Message<byte[]> message = null;
        final ClientTransferMessage clientTransferMessage = new ClientTransferMessage();
        clientTransferMessage.setBid(7);
        clientTransferMessage.setSourceStaffId(1000L);
        clientTransferMessage.setTargetStaffId(2000L);
        clientTransferMessage.setId(100L);
        clientTransferMessage.setEvent(2);
        clientTransferMessage.setBusiness("10");

        when(mockJsonService.toJson(any(Serializable.class))).thenReturn("result");

        // Configure StaffClientForLeads.getStaffById(...).
        final StaffDto staffDto = new StaffDto();
        staffDto.setId(0L);
        staffDto.setCustomerId(0L);
        staffDto.setName("name");
        staffDto.setGender(0);
        staffDto.setQyUserId("qyUserId");
        staffDto.setQyQrCode("qyQrCode");
        staffDto.setMobile("mobile");
        staffDto.setDepartmentId(0L);
        staffDto.setPosition("position");
        staffDto.setStatus(0);
        staffDto.setPassword("password");
        staffDto.setCreateTime(0L);
        staffDto.setUpdateTime(0L);
        staffDto.setBid(0);
        staffDto.setGeoLon("geoLon");
        when(mockStaffClientForLeads.getStaffById(7, 2000L)).thenReturn(staffDto);

        // Configure LeadsManager.findLeadsByClient(...).
        final Leads leads = new Leads();
        leads.setId(0L);
        leads.setBid(0);
        leads.setCustomerId(0L);
        leads.setCustomerUid(0L);
        leads.setChannelId(0L);
        leads.setClientId(0L);
        leads.setName("name");
        leads.setMobile("mobile");
        leads.setWeChat("weChat");
        leads.setStatus(0);
        leads.setContactIn24(0);
        leads.setProvinceId(0);
        leads.setProvinceName("provinceName");
        leads.setCityId(0);
        leads.setCityName("cityName");
        final List<Leads> leadsList = Arrays.asList(leads);
        when(mockLeadsManager.findLeadsByClient(7, 100L)).thenReturn(leadsList);

        // Configure LeadsManager.transferLeads(...).
        final LeadsTransferResultDto leadsTransferResultDto = new LeadsTransferResultDto();
        final Leads leads1 = new Leads();
        leads1.setId(0L);
        leads1.setBid(0);
        leads1.setCustomerId(0L);
        leads1.setCustomerUid(0L);
        leads1.setChannelId(0L);
        leads1.setClientId(0L);
        leads1.setName("name");
        leads1.setMobile("mobile");
        leads1.setWeChat("weChat");
        leads1.setStatus(0);
        leads1.setContactIn24(0);
        leads1.setProvinceId(0);
        leads1.setProvinceName("provinceName");
        leads1.setCityId(0);
        leadsTransferResultDto.setTransferLeadsList(Arrays.asList(leads1));
        when(mockLeadsManager.transferLeads(leadsList, staffDto,
                LeadsTransferEnum.CLIENT_TRANSFER)).thenReturn(leadsTransferResultDto);

        when(mockLeadsManager.doTransferLeads(any(LeadsTransferResultDto.class))).thenReturn(false);

        // Run the test
//        clientTransferListenerOfLeadsUnderTest.process(message, clientTransferMessage);

        // Verify the results
    }

    @Test
    public void testProcess_LeadsManagerFindLeadsByClientReturnsNoItems() {
        // Setup
        final Message<byte[]> message = null;
        final ClientTransferMessage clientTransferMessage = new ClientTransferMessage();
        clientTransferMessage.setBid(0);
        clientTransferMessage.setSourceStaffId(0L);
        clientTransferMessage.setTargetStaffId(0L);
        clientTransferMessage.setId(0L);
        clientTransferMessage.setEvent(0);
        clientTransferMessage.setBusiness("business");

        when(mockJsonService.toJson(any(Serializable.class))).thenReturn("result");

        // Configure StaffClientForLeads.getStaffById(...).
        final StaffDto staffDto = new StaffDto();
        staffDto.setId(0L);
        staffDto.setCustomerId(0L);
        staffDto.setName("name");
        staffDto.setGender(0);
        staffDto.setQyUserId("qyUserId");
        staffDto.setQyQrCode("qyQrCode");
        staffDto.setMobile("mobile");
        staffDto.setDepartmentId(0L);
        staffDto.setPosition("position");
        staffDto.setStatus(0);
        staffDto.setPassword("password");
        staffDto.setCreateTime(0L);
        staffDto.setUpdateTime(0L);
        staffDto.setBid(0);
        staffDto.setGeoLon("geoLon");
        when(mockStaffClientForLeads.getStaffById(0, 0L)).thenReturn(staffDto);

        when(mockLeadsManager.findLeadsByClient(0, 0L)).thenReturn(Collections.emptyList());

        // Configure LeadsManager.transferLeads(...).
        final LeadsTransferResultDto leadsTransferResultDto = new LeadsTransferResultDto();
        final Leads leads = new Leads();
        leads.setId(0L);
        leads.setBid(0);
        leads.setCustomerId(0L);
        leads.setCustomerUid(0L);
        leads.setChannelId(0L);
        leads.setClientId(0L);
        leads.setName("name");
        leads.setMobile("mobile");
        leads.setWeChat("weChat");
        leads.setStatus(0);
        leads.setContactIn24(0);
        leads.setProvinceId(0);
        leads.setProvinceName("provinceName");
        leads.setCityId(0);
        leadsTransferResultDto.setTransferLeadsList(Arrays.asList(leads));
        when(mockLeadsManager.transferLeads(Arrays.asList(new Leads()), new StaffDto(),
                LeadsTransferEnum.CLIENT_TRANSFER)).thenReturn(leadsTransferResultDto);

        when(mockLeadsManager.doTransferLeads(any(LeadsTransferResultDto.class))).thenReturn(false);

        // Run the test
//        clientTransferListenerOfLeadsUnderTest.process(message, clientTransferMessage);

        // Verify the results
    }
}

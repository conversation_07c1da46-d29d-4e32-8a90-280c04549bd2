package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadTransferRequest;
import com.inngke.bp.leads.dto.request.LeadsAddIncludeStaffRequest;
import com.inngke.bp.leads.dto.request.LeadsDepEsGetRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.es.service.EsDocService;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.commons.compress.utils.Lists;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/17 9:13 PM
 */
public class LeadsChangeServiceImplTest extends BaseJunitTest {

    @Autowired
    private LeadsChangeServiceImpl leadsChangeService;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private EsDocService esDocService;

    @Test
    public void testSendWxPubMessage() {
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                .select(Leads.DISTRIBUTE_STAFF_ID)
        );

        leadsChangeService.sendWxPubMessage(1, 0L, leadsList,null,1);
    }

    public BaseResponse<List<LeadsDto>> test(LeadsDepEsGetRequest request) {
        Integer bid = request.getBid();
        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("bid", bid));
        //匹配部门
        ArrayList<Long> objects = Lists.newArrayList();
        objects.add(26L);
        qb.must(QueryBuilders.termsQuery("deptIds", objects));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(qb);
        SearchRequest source = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);
        SearchResponse resp = esDocService.search(source);
        if (resp == null || resp.getHits() == null) {
            throw new InngkeServiceException("查询线索失败，query=" + searchSourceBuilder + "，response=" + resp);
        }
//        List<LeadsDto> resultList = Lists.newArrayList();
//        if (resp.getHits().getTotalHits().value > 0) {
//            resp.getHits().forEach(hit -> resultList.add(jsonService.toObject(hit.getSourceAsString(), LeadsDto.class)));
//        }
        return null;
    }

    @Test
    public  void test() {
        LeadsDepEsGetRequest request = new LeadsDepEsGetRequest();
        request.setDepartmentId(16L);
        request.setBid(1);
        request.setOperatorId(1L);
        test(request);
    }

    @Test
    public void test1() {
        LeadsAddIncludeStaffRequest leadsAddIncludeStaffRequest = new LeadsAddIncludeStaffRequest();
        leadsAddIncludeStaffRequest.setBid(7);
        leadsAddIncludeStaffRequest.setAreaName("小店区");
        leadsAddIncludeStaffRequest.setOperatorId(0L);
        leadsAddIncludeStaffRequest.setProvinceName("山西省");
        leadsAddIncludeStaffRequest.setCityName("太原市");
        leadsAddIncludeStaffRequest.setDistributeStaffId(123L);
        leadsAddIncludeStaffRequest.setCustomerUid(0L);
        leadsAddIncludeStaffRequest.setCustomerId(0L);
        leadsAddIncludeStaffRequest.setDistributeStaffId(418L);
        leadsAddIncludeStaffRequest.setDistributeAgentId(0L);
        leadsAddIncludeStaffRequest.setName("测试");
        leadsAddIncludeStaffRequest.setMobile("1523232323");
        BaseResponse<LeadsDto> leadsDtoBaseResponse = leadsChangeService.addIncludeStaff(leadsAddIncludeStaffRequest);
        System.out.println(leadsDtoBaseResponse.toString());
    }

    @Test
    public void test2() {
        LeadTransferRequest leadTransferRequest = new LeadTransferRequest();
//        leadTransferRequest.setGuideId(2196L);
//        leadTransferRequest.setTargetGuideId(2046L);
        leadTransferRequest.setBid(7);
        leadTransferRequest.setOperatorId(999L);
        leadsChangeService.transfer(leadTransferRequest);
    }
}
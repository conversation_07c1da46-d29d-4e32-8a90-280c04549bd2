package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.LeadsRecoveryRequest;
import com.inngke.bp.leads.service.LeadsChangeService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.es.service.EsDocService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.ArrayList;

/**
 * 部门线索统计测试类
 * <AUTHOR>
 */
public class LeadsDepartmentStatisticsTest  extends BaseJunitTest {

    @Autowired
    private LeadsChangeService leadsChangeService;

    @Autowired
    private EsDocService esDocService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Test
    public void testDepartmentStatistics(){

        String department = "[\"1\",\"2\",\"23\",\"5\",\"3\",\"4\",\"6\",\"7\",\"24\",\"29\",\"31\",\"33\",\"26\",\"36\",\"43\",\"44\",\"45\",\"27\",\"28\",\"34\",\"30\",\"35\",\"37\",\"38\",\"39\",\"40\",\"42\",\"41\",\"25\",\"32\"]";
        String s = department.replaceAll("[|]", "");


    }

    @Test
    public void testLeadsRecovery(){
        LeadsRecoveryRequest request = new LeadsRecoveryRequest();
        request.setBid(1);
        request.setLeadsList(Lists.newArrayList(27L,28L));
        request.setOperatorId(0L);
        BaseResponse<Boolean> booleanBaseResponse = leadsChangeService.recoveryLeads(request);
    }

//    public static void main(String[] args) {
//        String department = "[\"1\",\"2\",\"23\",\"3\",\"4\",\"5\",\"6\",\"7\",\"24\"]";
//        department  = department.replaceAll("\\[|\\]|\"", "");
//        String[] split = department.split(",", -1);
//        List<Long> departmentList = Lists.newArrayList(split).stream().map(Long::parseLong).collect(Collectors.toList());
//
//    }


    @Test
    public void installPolymerizationSearch() throws IOException {

        BoolQueryBuilder qb = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("bid", 1)).must(QueryBuilders.rangeQuery("status").gt(0));
        ArrayList<Integer> integers = Lists.newArrayList(26, 36);
        qb.must(QueryBuilders.termsQuery("deptIds", integers));

        //匹配部门
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(qb).from(0).size(1000);
        SearchRequest source = new SearchRequest().indices("leads").source(searchSourceBuilder);
        // 总聚合分桶
        searchSourceBuilder
                .aggregation(
                        AggregationBuilders.terms("inTotal").field("deptIds").size(1000)
                                .subAggregation(
                                        // 总数
                                        AggregationBuilders.terms("totalCount")
                                )
                                .subAggregation(
                                        // 留资客户聚合
                                        AggregationBuilders.filter("elseCount",QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("status").gt(1)))
                                )
                );
        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
    }


}

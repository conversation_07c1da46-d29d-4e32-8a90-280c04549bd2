package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.core.converter.LeadsStatisticsConverter;
import com.inngke.bp.leads.dto.request.LeadsStatisticsQuery;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
import com.inngke.bp.leads.dto.response.LeadsStatisticsExcelDto;
import com.inngke.bp.leads.service.LeadsStatisticsService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class LeadsStatisticsServiceTest extends BaseJunitTest {
    @Autowired
    private LeadsStatisticsService leadsStatisticsService;

    @Test
    public void testGetLeadsStatistics() {
        LeadsStatisticsQuery request = new LeadsStatisticsQuery();
        request.setBid(1134);
        request.setOperatorId(1024l);

        BaseResponse<BasePaginationResponse<LeadsStatisticsDto>> response = leadsStatisticsService.getLeadsStatistics(request);
        Assert.assertEquals("code应该为0", 0, response.getCode().intValue());
        Assert.assertNotNull("结果不应该为空", response.getData());
        Assert.assertNotNull("列表不应该为空", response.getData().getList());
    }

    @Test
    public void testConvertToPercentage() {
        LeadsStatisticsQuery request = new LeadsStatisticsQuery();
        request.setBid(1134);
        request.setOperatorId(1024l);

        BaseResponse<BasePaginationResponse<LeadsStatisticsDto>> response = leadsStatisticsService.getLeadsStatistics(request);
        Assert.assertEquals("code应该为0", 0, response.getCode().intValue());
        Assert.assertNotNull("结果不应该为空", response.getData());
        Assert.assertNotNull("列表不应该为空", response.getData().getList());
        List<LeadsStatisticsDto> list = response.getData().getList();
        List<LeadsStatisticsExcelDto> excelDto = LeadsStatisticsConverter.toLeadsStatisticsExcelDto(list);
        Assert.assertNotNull("结果不应该为空", excelDto);
    }
}

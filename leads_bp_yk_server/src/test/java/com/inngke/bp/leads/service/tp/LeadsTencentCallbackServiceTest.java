package com.inngke.bp.leads.service.tp;
import com.tencent.ads.model.AuthorizerStruct;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.bp.leads.service.impl.tp.oauth.LeadsTpOauthTencentServiceImpl;
import com.inngke.common.service.JsonService;
import com.tencent.ads.ApiException;
import com.tencent.ads.model.OauthTokenResponseData;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;


public class LeadsTencentCallbackServiceTest extends BaseJunitTest {
    @Autowired
    private LeadsTpOauthTencentServiceImpl leadsTpOauthTencentService;

    @Autowired
    private JsonService jsonService;
    @Test
    public void handle() throws ApiException {
        OauthTokenResponseData oauthData = new OauthTokenResponseData();
        AuthorizerStruct authorizerStruct = new AuthorizerStruct();
        authorizerStruct.setAccountId(21441408L);
        oauthData.setAuthorizerInfo(authorizerStruct);
        oauthData.setAccessToken("d58ac6021c539d47a131d9e5400387ca");
        oauthData.setRefreshToken("");
        oauthData.setAccessTokenExpiresIn(0L);
        oauthData.setRefreshTokenExpiresIn(0L);

        leadsTpOauthTencentService.getLeadsTpAccountInfoWeChat(1,oauthData);
    }
}

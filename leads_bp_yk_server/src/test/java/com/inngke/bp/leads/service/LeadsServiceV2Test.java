package com.inngke.bp.leads.service;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.LeadsGetRequest;
import com.inngke.bp.leads.dto.response.MallOrderDto;
import com.inngke.common.dto.response.BaseResponse;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class LeadsServiceV2Test extends BaseJunitTest {

    @Autowired
    LeadsServiceV2 leadsServiceV2;

    @Test
    public void testGetMallOrderList() {
        LeadsGetRequest leadsGetRequest = new LeadsGetRequest();
        leadsGetRequest.setId(4836L);
        leadsGetRequest.setBid(1);
        BaseResponse<List<MallOrderDto>> mallOrderList = leadsServiceV2.getMallOrderList(leadsGetRequest);
        System.out.println(mallOrderList);
    }
}
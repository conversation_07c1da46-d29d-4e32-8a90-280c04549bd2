package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.dto.request.LeadsConfSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsConfDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

public class LeadsConfServiceImplTest extends BaseJunitTest {

    @Autowired
    private  LeadsConfServiceImpl leadsConfService;


    @Test
    public void testEnableLeads() {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setBid(1);
        request.setOperatorId(1L);

        BaseResponse<Boolean> resp = leadsConfService.enableLeads(request);

        Assert.assertNotNull("结果不应该为空", resp);
    }

    @Test
    public void testDisableLeads() {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setBid(1);
        request.setOperatorId(1L);

        BaseResponse<Boolean> resp = leadsConfService.disableLeads(request);

        Assert.assertNotNull("结果不应该为空", resp);
    }

    @Test
    public void testSetLeadsConf() {
        LeadsConfSaveRequest request = new LeadsConfSaveRequest();
        request.setBid(1);
        request.setOperatorId(1L);
        request.setEnable(true);
        request.setDistributeType(1);
        request.setForwardEnable(false);

        BaseResponse<LeadsConfDto> resp = leadsConfService.setLeadsConf(request);

        Assert.assertNotNull("结果不应该为空", resp);
    }

    @Test
    public void testGetLeadsConf() {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setBid(1);
        request.setOperatorId(1L);

        BaseResponse<LeadsConfDto> resp = leadsConfService.getLeadsConf(request);

        Assert.assertNotNull("结果不应该为空", resp);
        Assert.assertNotNull("线索id不能为空", resp.getData().getId());
    }
}

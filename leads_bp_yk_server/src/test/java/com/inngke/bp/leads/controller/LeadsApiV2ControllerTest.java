package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.BaseJunitTest;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class LeadsApiV2ControllerTest extends BaseJunitTest {

    @Autowired
    private LeadsManager leadsManager;

    @Test
    public void updateLeadsTest() {
        Leads leads = new Leads();
        // test id = 189
        leads.setId(189L);
        leads.setPayTime(null);
        leadsManager.updateLeads(leads, null, null);
    }

}

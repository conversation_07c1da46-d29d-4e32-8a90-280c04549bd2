# mybatis-plus-generator的配置.

globalConfig:
  author: xuwen<PERSON>
  open: false
  idType: AUTO
  enableCache: false
  activeRecord: false
  baseResultMap: true
  baseColumnList: true
  swagger2: false
  fileOverride: true
  mapperName: '%sDao'
  serviceName: '%sManager'
  serviceImplName: '%sManagerImpl'
dataSourceConfig:
  url: ***************************************************************************************************
  driverName: com.mysql.jdbc.Driver
  username: yk_root
  password: M22YjT@zH1Yzdplk
packageConfig:
  parent: com.inngke.bp.leads
  entity: db.leads.entity
  service: db.leads.manager
  serviceImpl: db.leads.manager.impl
  mapper: db.leads.dao
  xml: db.leads.mapper
#  controller: controller
  pathInfo:
    entity_path: src/main/java/com/inngke/bp/leads/db/leads/entity
    service_path: src/main/java/com/inngke/bp/leads/db/leads/manager
    service_impl_path: src/main/java/com/inngke/bp/leads/db/leads/manager/impl
    mapper_path: src/main/java/com/inngke/bp/leads/db/leads/dao
    controller_path: src/main/java/com/inngke/bp/leads/controller
strategyConfig:
  naming: underline_to_camel
  columnNaming: underline_to_camel
  entityLombokModel: true
  entityColumnConstant: true
  restControllerStyle: true
  superMapperClass: com.baomidou.mybatisplus.core.mapper.BaseMapper
  superServiceClass: com.baomidou.mybatisplus.extension.service.IService
  superServiceImplClass: com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
#  tablePrefix:
#    - ***
  include:
#    - leads
#    - leads_conf
#    - leads_distribute_conf
#    - leads_log
#    - leads_draft
#    - leads_batch
#    - leads_follow
#    - leads_history_distribute
#    - leads_ext_information
#    - leads_tp_log
#    - leads_tp_pull_condition
#    - leads_tp_oauth
#    - allocation_staff_rule
#    - leads_event_conf
#    - leads_event_log
#    - leads_channel
#    - leads_back_reason
#    - leads_invalid_reason
#    - leads_invalid_reason
#    - leads_push_back_log
#    - leads_distribute_conf_permissions
#    - leads_distribute_channel_conf
#    - leads_distribute_forward_conf
    - leads_transfer_rollback_todo

server:
  port: 60497  #HTTP服务端口号
  dubboPort: 60297 #Dubbo服务端口
spring:
  profiles:
    active: dev
  application:
    name: leads_bp_yk
  cloud:
    nacos:
      config:
        namespace: CONF-${ENV:DEV}
        server-addr: nacos:8848 # 注册中心地址，注意必须带端口号！
        file-extension: yaml
        shared-configs[0]:
          dataId: redis_conf.yaml
        shared-configs[1]:
          dataId: dubbo_conf.yaml
        shared-configs[2]:
          dataId: privatization_conf.yaml
        shared-configs[3]:
          dataId: common_conf.yaml
        shared-configs[4]:
          dataId: tdmq_conf.yaml
      discovery:
        server-addr: nacos:8848 # 注册中心地址，注意必须带端口号！
        group: ${ENV:DEV} 

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
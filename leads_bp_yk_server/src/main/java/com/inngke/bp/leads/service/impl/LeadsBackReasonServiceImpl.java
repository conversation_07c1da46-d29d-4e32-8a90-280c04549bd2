package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsBackReason;
import com.inngke.bp.leads.db.leads.entity.LeadsInvalidReason;
import com.inngke.bp.leads.db.leads.manager.LeadsBackReasonManager;
import com.inngke.bp.leads.dto.request.LeadsBackReasonMoveSortRequest;
import com.inngke.bp.leads.dto.request.LeadsBackReasonSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsBackReasonDto;
import com.inngke.bp.leads.service.LeadsBackReasonInitService;
import com.inngke.bp.leads.service.LeadsBackReasonService;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.ds.annotation.DS;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.consts.LeadsServiceConsts.LOCK_PREFIX;

@Service
@DubboService(version = "1.0.0")
@Slf4j
public class LeadsBackReasonServiceImpl implements LeadsBackReasonService {

    private static final String LOCK = LOCK_PREFIX + "LeadsBackReason_%s";

    @Autowired
    private LeadsBackReasonManager leadsBackReasonManager;

    @Autowired
    private LeadsBackReasonInitService leadsBackReasonInitService;

    @Autowired
    private LockService lockService;


    @Override
    public BaseResponse<List<LeadsBackReasonDto>> getList(BaseBidRequest request) {
        List<LeadsBackReason> leadsBackReasonList = getLeadsBackReasons(request);

        if (ObjectUtils.isEmpty(leadsBackReasonList)) {
            leadsBackReasonInitService.init();
            leadsBackReasonList = getLeadsBackReasons(request);
        }

        return BaseResponse.success(leadsBackReasonList.stream().map(this::toLeadsBackReasonDto).collect(Collectors.toList()));
    }

    public List<LeadsBackReason> getLeadsBackReasons(BaseBidRequest request) {
        List<LeadsBackReason> leadsBackReasonList = leadsBackReasonManager.list(Wrappers.<LeadsBackReason>query()
                .eq(LeadsBackReason.BID, request.getBid())
                .eq(LeadsBackReason.ENABLE, 1)
                .orderByAsc(LeadsBackReason.SORT));
        return leadsBackReasonList;
    }

    @Override
    public BaseResponse save(LeadsBackReasonSaveRequest request) {
        Integer bid = request.getBid();
        Long id = request.getId();
        Lock lock = lockService.getLock(String.format(LOCK, bid), 60);
        if (lock == null) {
            return BaseResponse.error("同时操作中，请稍后刷新下再操作！");
        }

        try {
            checkName(request);
            if (Objects.isNull(id)) {
                LeadsBackReason leadsInvalidReason = toLeadsBackReason(request);
                //保存
                save(bid, leadsInvalidReason);
                return BaseResponse.success();
            }else {
                LeadsBackReason getLeadsInvalidReason = leadsBackReasonManager.getOne(getQueryWrapper(bid, id));
                if (Objects.isNull(getLeadsInvalidReason)) {
                    return BaseResponse.error("数据不存在！");
                }
                //更新
                update(request, bid);
            }
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        return BaseResponse.success();
    }

    private void update(LeadsBackReasonSaveRequest request, Integer bid) {
        leadsBackReasonManager.update(Wrappers.<LeadsBackReason>update()
                .eq(LeadsBackReason.BID, bid)
                .eq(LeadsBackReason.ID, request.getId())
                .set(!ObjectUtils.isEmpty(request.getReason()),LeadsBackReason.REASON, request.getReason())
                .set(!ObjectUtils.isEmpty(request.getEnable()),LeadsBackReason.ENABLE, request.getEnable()));
    }

    private QueryWrapper<LeadsBackReason> getQueryWrapper(Integer bid, Long id) {
        QueryWrapper<LeadsBackReason> queryWrapper = Wrappers.<LeadsBackReason>query()
                .eq(LeadsBackReason.BID, bid)
                .eq(LeadsInvalidReason.ID, id)
                .ne(LeadsInvalidReason.ENABLE, -1);
        return queryWrapper;
    }

    private void save(Integer bid, LeadsBackReason leadsInvalidReason) {
        LeadsBackReason latestLeadsInvalidReason = leadsBackReasonManager.getOne(Wrappers.<LeadsBackReason>query()
                .eq(LeadsBackReason.BID, bid)
                .orderByDesc(LeadsBackReason.CREATE_TIME,LeadsBackReason.ID).last("limit 1"));
        if (!ObjectUtils.isEmpty(latestLeadsInvalidReason)
                && !ObjectUtils.isEmpty(latestLeadsInvalidReason.getSort())) {
            leadsInvalidReason.setSort(latestLeadsInvalidReason.getSort() + 1);
        }
        leadsInvalidReason.setId(SnowflakeHelper.getId());
        leadsBackReasonManager.save(leadsInvalidReason);
    }

    private LeadsBackReason toLeadsBackReason(LeadsBackReasonSaveRequest request) {
        LeadsBackReason leadsInvalidReason = new LeadsBackReason();
        leadsInvalidReason.setBid(request.getBid());
        leadsInvalidReason.setId(request.getId());
        leadsInvalidReason.setReason(request.getReason());
        leadsInvalidReason.setEnable(request.getEnable());

        return leadsInvalidReason;
    }

    private void checkName(LeadsBackReasonSaveRequest request) {
        Integer bid = request.getBid();
        Long id = request.getId();
        String reason = request.getReason();
        if (StringUtils.isEmpty(reason)) {
            return;
        }
        QueryWrapper<LeadsBackReason> queryWrapper = Wrappers.<LeadsBackReason>query()
                .in(LeadsBackReason.BID, bid)
                .eq(LeadsBackReason.REASON, reason)
                .ne(LeadsBackReason.ENABLE, -1);
        if (!Objects.isNull(id)) {
            queryWrapper.ne(LeadsBackReason.ID, id);
        }
        int count = leadsBackReasonManager.count(queryWrapper);
        if (count > 0) {
            throw new InngkeServiceException("原因名称不能重复");
        }
    }

    private LeadsBackReasonDto toLeadsBackReasonDto(LeadsBackReason leadsBackReason) {
        LeadsBackReasonDto leadsBackReasonDto = new LeadsBackReasonDto();
        leadsBackReasonDto.setReason(leadsBackReason.getReason());
        leadsBackReasonDto.setId(leadsBackReason.getId());

        return leadsBackReasonDto;
    }

    @Override
    public BaseResponse switchSort(LeadsBackReasonMoveSortRequest request) {
        Integer bid = request.getBid();
        Lock lock = lockService.getLock(String.format(LOCK, bid), 60);
        if (lock == null) {
            return BaseResponse.error("同时操作中，请稍后刷新下再操作！");
        }
        try {
            Long prevId = request.getPrevId();
            Long nextId = request.getNextId();

            LeadsBackReason prevLeadsBackReason = leadsBackReasonManager.getOne(getQueryWrapper(bid, prevId));
            LeadsBackReason nextLeadsBackReason = leadsBackReasonManager.getOne(getQueryWrapper(bid, nextId));
            if (Objects.isNull(prevLeadsBackReason) || Objects.isNull(nextLeadsBackReason)) {
                return BaseResponse.error("数据不存在！");
            }

            leadsBackReasonManager.moveSort(bid, prevLeadsBackReason, nextLeadsBackReason);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        return BaseResponse.success();
    }
}

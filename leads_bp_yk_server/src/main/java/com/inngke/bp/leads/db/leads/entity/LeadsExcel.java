/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id
     */
    private Long customerId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 线索状态： -4=删除 -3=无效线索 -2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交
     * @see LeadsStatusEnum
     */
    private Integer status;

    /**
     * 省份ID，0表示未匹配
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    private Integer channel;

    /**
     * 下单账号
     */
    private String orderAccount;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 订购商品数量
     */
    private Integer goodsNum;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款金额
     */
    private BigDecimal payAmount;

    /**
     * 订单留言
     */
    private String orderMessage;

    /**
     * 其它备注
     */
    private String remark;

    /**
     * 外部平台线索ID
     */
    private String tpLeadsId;

    /**
     * 广告活动名称
     */
    private String promotionName;

    /**
     * 报名时间
     */
    private LocalDateTime registryTime;

    /**
     * 需求时间：0=未指定 1=一个月内 2=三个月内 3=六个月内 4=一年内 5=一年以上
     */
    private Integer expectIn;

    /**
     * 装修风格，详见枚举
     */
    private Integer style;

    /**
     * 批次ID，0表示非批次导入
     */
    private Long batchId;

    /**
     * 分配的经销商ID，即shop_agent_id
     */
    private Long distributeAgentId;

    /**
     * 分配给哪个员工，即staffId
     */
    private Long distributeStaffId;

    /**
     * 分配时间
     */
    private LocalDateTime distributeTime;

    /**
     * 异常原因
     */
    private String errorMsg;

    /**
     * 最近一条待跟进记录ID
     */
    private Long lastFollowId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String CUSTOMER_ID = "customer_id";

    public static final String NAME = "name";

    public static final String MOBILE = "mobile";

    public static final String STATUS = "status";

    public static final String PROVINCE_ID = "province_id";

    public static final String PROVINCE_NAME = "province_name";

    public static final String CITY_ID = "city_id";

    public static final String CITY_NAME = "city_name";

    public static final String AREA_ID = "area_id";

    public static final String AREA_NAME = "area_name";

    public static final String ADDRESS = "address";

    public static final String CHANNEL = "channel";

    public static final String ORDER_ACCOUNT = "order_account";

    public static final String ORDER_SN = "order_sn";

    public static final String GOODS_NAME = "goods_name";

    public static final String GOODS_NUM = "goods_num";

    public static final String PAY_TIME = "pay_time";

    public static final String PAY_AMOUNT = "pay_amount";

    public static final String ORDER_MESSAGE = "order_message";

    public static final String REMARK = "remark";

    public static final String TP_LEADS_ID = "tp_leads_id";

    public static final String PROMOTION_NAME = "promotion_name";

    public static final String REGISTRY_TIME = "registry_time";

    public static final String EXPECT_IN = "expect_in";

    public static final String STYLE = "style";

    public static final String BATCH_ID = "batch_id";

    public static final String DISTRIBUTE_AGENT_ID = "distribute_agent_id";

    public static final String DISTRIBUTE_STAFF_ID = "distribute_staff_id";

    public static final String DISTRIBUTE_TIME = "distribute_time";

    public static final String ERROR_MSG = "error_msg";

    public static final String LAST_FOLLOW_ID = "last_follow_id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsStatisticsBaseInfoDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsEsInfoGetService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.es.service.EsDocService;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

/**
 * 线索es获取数据实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LeadsEsInfoGetServiceImpl implements LeadsEsInfoGetService {

    @Autowired
    private EsDocService esDocService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    private static final String LEADS_ES_INDEX = "leads";

    private static final String STATUS = "status";

    private static final String CLIENT_ID = "clientId";

    /**
     * 根据部门编号集合查询该部门以及子部门的所有线索
     * @param bid 商户编号
     * @param depIds 部门集合
     * @param belongingDepartmentIds 有权限管理的部门
     * @return 聚合数据map
     * @throws IOException 异常
     */
    @Override
    public BaseResponse<Map<Long, LeadsStatisticsBaseInfoDto>> getDepLeadsListIncludeSubsByDepId(Integer bid,List<Long> depIds,List<Long> belongingDepartmentIds, Long startDistributeTime, Long endDistributeTime) {
        Map<Long, LeadsStatisticsBaseInfoDto> polymerEventData = getEventPolymerizeData(bid, depIds, "followDepartmentIds", belongingDepartmentIds, startDistributeTime, endDistributeTime);
        Map<Long, LeadsStatisticsBaseInfoDto> polymerData = getPolymerizeData(bid, depIds, "deptIds", belongingDepartmentIds, startDistributeTime, endDistributeTime);
        return BaseResponse.success(mergerPolymerDataAndEventData(polymerData, polymerEventData));
    }

    @Override
    public BaseResponse<Map<Long, LeadsStatisticsBaseInfoDto>> getLeadsListByStaffIds(Integer bid,List<Long> staffIds,List<Long> belongingDepartmentIds, Long startDistributeTime, Long endDistributeTime) {
        Map<Long, LeadsStatisticsBaseInfoDto> polymerEventData = getEventPolymerizeData(bid, staffIds, "followStaffId", belongingDepartmentIds, startDistributeTime, endDistributeTime);
        Map<Long, LeadsStatisticsBaseInfoDto> polymerData = getPolymerizeData(bid, staffIds, "distributeStaffId", belongingDepartmentIds, startDistributeTime, endDistributeTime);
        return BaseResponse.success(mergerPolymerDataAndEventData(polymerData, polymerEventData));
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> getEventPolymerizeData(Integer bid, List<Long> queryIds,String matchField, List<Long> belongingDepartmentIds, Long startDistributeTime, Long endDistributeTime) {
        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery(matchField, queryIds))
                .must(QueryBuilders.termQuery("bid", bid))
                .must(QueryBuilders.rangeQuery("createTime").gte(startDistributeTime).lte(endDistributeTime));
        ;
        if(!CollectionUtils.isEmpty(belongingDepartmentIds)){
            qb.must(QueryBuilders.termsQuery("followDepartmentId",belongingDepartmentIds));
        }

        //匹配部门
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(qb).size(0);
        searchSourceBuilder.aggregation(
                AggregationBuilders.terms("inTotal").field(matchField)
                        .subAggregation(
                                AggregationBuilders.filter("deposit_filter", QueryBuilders.termQuery("eventId", 15))
                                        .subAggregation(AggregationBuilders.cardinality("distinct_count").field("leadsId")
                                        )
                        ).size(1000)


        );

        SearchRequest searchRequest = new SearchRequest()
                .indices("leads-event-log")
                .source(searchSourceBuilder);
        log.info("getEventPolymerizeData {}", searchRequest.source());
        SearchResponse searchResponse;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }
        return installPolymerizeEventData(searchResponse);
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> mergerPolymerDataAndEventData(Map<Long, LeadsStatisticsBaseInfoDto> polymerData, Map<Long, LeadsStatisticsBaseInfoDto> polymerEventData) {
        if (CollectionUtils.isEmpty(polymerEventData)) {
            return polymerData;
        }
        if (CollectionUtils.isEmpty(polymerData)) {
            return polymerEventData;
        }
        Map<Long, LeadsStatisticsBaseInfoDto> mergerData = Maps.newHashMap();
        for (Map.Entry<Long, LeadsStatisticsBaseInfoDto> entry : polymerData.entrySet()) {
            LeadsStatisticsBaseInfoDto mergerNode = entry.getValue();
            LeadsStatisticsBaseInfoDto polymerEventNode = polymerEventData.get(entry.getKey());
            mergerNode.setDepositCount(Optional.ofNullable(polymerEventNode).map(LeadsStatisticsBaseInfoDto::getDepositCount).orElse(0));
            mergerData.put(entry.getKey(), mergerNode);
        }

        return mergerData;
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> installPolymerizeEventData(SearchResponse searchResponse) {
        List<? extends Terms.Bucket> buckets = ((Terms) searchResponse.getAggregations().get("inTotal")).getBuckets();
        Map<Long, LeadsStatisticsBaseInfoDto> commonFieldMap = new HashMap<>();

        for (Terms.Bucket bucket : buckets) {
            processBucket(bucket, commonFieldMap);
        }

        return commonFieldMap;
    }

    private void processBucket(Terms.Bucket bucket, Map<Long, LeadsStatisticsBaseInfoDto> commonFieldMap) {
        Aggregations aggregations = bucket.getAggregations();
        if (Objects.isNull(aggregations)) {
            return;
        }

        ParsedFilter parsedFilter = (ParsedFilter) aggregations.get("deposit_filter");
        if (Objects.isNull(parsedFilter)) {
            return;
        }

        Aggregations subAggregation = parsedFilter.getAggregations();
        if (Objects.isNull(subAggregation)) {
            return;
        }

        ParsedCardinality distinctCount = subAggregation.get("distinct_count");
        if (Objects.isNull(distinctCount)) {
            return;
        }

        long depositCount = distinctCount.getValue();

        LeadsStatisticsBaseInfoDto leadsStatisticsBaseInfoDto = new LeadsStatisticsBaseInfoDto();
        leadsStatisticsBaseInfoDto.setDepositCount(Long.valueOf(depositCount).intValue());
        commonFieldMap.put(bucket.getKeyAsNumber().longValue(), leadsStatisticsBaseInfoDto);
    }


    private Map<Long, LeadsStatisticsBaseInfoDto> getPolymerizeData(Integer bid, List<Long> queryIds,String matchField,List<Long> belongingDepartmentIds, Long startDistributeTime, Long endDistributeTime) {

        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery(matchField, queryIds))
                .must(QueryBuilders.termQuery("bid", bid))
                .must(QueryBuilders.rangeQuery("distributeTime").gte(startDistributeTime).lte(endDistributeTime));
        if(!CollectionUtils.isEmpty(belongingDepartmentIds)){
            qb.must(QueryBuilders.termsQuery("belongingDepartmentIds",belongingDepartmentIds));
        }

        //匹配部门
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(qb);
        // 总聚合分桶
        searchSourceBuilder
                .aggregation(
                        AggregationBuilders.terms("inTotal").field(matchField).size(10000)
                                .subAggregation(
                                        // 总数
                                        AggregationBuilders.filter("totalCount",QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(STATUS, LeadsStatusEnum.getAllocatedLeadsStatus())))
                                )
                                //LeadsStatusEnum.DISTRIBUTED.getStatus()
                                .subAggregation(
                                        // 待联系
                                        AggregationBuilders.filter("distributedCount",QueryBuilders.boolQuery().must(QueryBuilders.termQuery(STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())))
                                )
                                .subAggregation(
                                        // 已成交
                                        AggregationBuilders.filter("dealCount",QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.HAS_STORE_ORDER, 1)).mustNot(QueryBuilders.termsQuery(STATUS, LeadsStatusEnum.getNotStatisticsLeadsStatus())))
                                )
                                .subAggregation(
                                        // 联系中
                                        AggregationBuilders.filter("inContactCount",QueryBuilders.boolQuery()
                                                .must(QueryBuilders.rangeQuery(STATUS)
                                                        .gte(LeadsStatusEnum.CONTACTED.getStatus())
                                                        .lte(LeadsStatusEnum.INSTALLED.getStatus())
                                                )
                                        )
                                )
                                .subAggregation(
                                        // 24小时内联系
                                        //AggregationBuilders.filter("contactCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery("contactIn24", 1)).must(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(LeadsStatusEnum.CONTACTED.getStatus()))))
                                        AggregationBuilders.filter("contactCount", QueryBuilders.boolQuery()
                                                .must(QueryBuilders.rangeQuery("firstCallIntervalTime").lte(24 * 60 * 60))
                                                .must(QueryBuilders.rangeQuery(STATUS).gt(0)))
                                )
                                .subAggregation(
                                        // 线索转交数
                                        AggregationBuilders.filter("clientCount",QueryBuilders.boolQuery()
                                                .must(QueryBuilders.rangeQuery(CLIENT_ID)
                                                        .gt(0)
                                                )
                                        )
                                )
                );
        SearchRequest searchRequest = new SearchRequest()
                .indices(LEADS_ES_INDEX)
                .source(searchSourceBuilder);
        log.info("getPolymerizeData {}", searchRequest);
        SearchResponse searchResponse;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }

        return installPolymerizeData(searchResponse);
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> installPolymerizeData(SearchResponse searchResponse){
        List<? extends Terms.Bucket> inTotal = ((Terms) searchResponse.getAggregations().get("inTotal")).getBuckets();
        Map<Long, LeadsStatisticsBaseInfoDto> commonFieldMap = new HashMap<>();
        inTotal.forEach(ele->{
            Aggregations currentAggregations = ele.getAggregations();
            ParsedFilter totalCount = currentAggregations.get("totalCount");
            //待联系
            ParsedFilter distributedCount = currentAggregations.get("distributedCount");
            //已成交
            ParsedFilter inContactCount = currentAggregations.get("inContactCount");
            //联系中
            ParsedFilter dealCount = currentAggregations.get("dealCount");
            //24小时内联系
            ParsedFilter contactCount = currentAggregations.get("contactCount");
            //转客户数
            ParsedFilter clientCount = currentAggregations.get("clientCount");
            LeadsStatisticsBaseInfoDto leadsStatisticsBaseInfoDto = new LeadsStatisticsBaseInfoDto();
            leadsStatisticsBaseInfoDto.setTotalCount(Optional.of(totalCount.getDocCount()).orElse(0L).intValue());
            leadsStatisticsBaseInfoDto.setToBeContactedCount(Optional.of(distributedCount.getDocCount()).orElse(0L).intValue());
            leadsStatisticsBaseInfoDto.setInContactCount(Optional.of(inContactCount.getDocCount()).orElse(0L).intValue());
            leadsStatisticsBaseInfoDto.setDealCount(Optional.of(dealCount.getDocCount()).orElse(0L).intValue());
            leadsStatisticsBaseInfoDto.setContactTimelyCount(Optional.of(contactCount.getDocCount()).orElse(0L).intValue());
            leadsStatisticsBaseInfoDto.setClientCount(Optional.of(clientCount.getDocCount()).orElse(0L).intValue());
            commonFieldMap.put((Long)(ele).getKey(),leadsStatisticsBaseInfoDto);
        });
        return commonFieldMap;
    }

}

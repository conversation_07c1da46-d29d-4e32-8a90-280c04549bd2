package com.inngke.bp.leads.mq.message.leads;

import com.inngke.common.mq.dto.BaseMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/18 8:17 PM
 */
public class LeadsFollowMessage extends BaseMessage {


    /**
     * 线索跟进主键
     */
    private Long id;

    /**
     * 需要跟进的员工ID
     */
    private Long staffId;

    /**
     * 线索编号集合
     */
    @Deprecated(since = "通过mq实现线索跟进通知废弃")
    private List<Long> leadsIds;

    /**
     * 提醒类型
     * 1小时超时提醒 ：1 ，24小时超市提醒 ：2
     */
    @Deprecated(since = "通过mq实现线索跟进通知废弃")
    private Integer type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<Long> getLeadsIds() {
        return leadsIds;
    }

    public void setLeadsIds(List<Long> leadsIds) {
        this.leadsIds = leadsIds;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}

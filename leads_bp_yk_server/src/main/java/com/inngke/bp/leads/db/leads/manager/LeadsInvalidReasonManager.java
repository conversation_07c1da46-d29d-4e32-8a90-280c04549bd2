/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.db.leads.entity.LeadsInvalidReason;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface LeadsInvalidReasonManager extends IService<LeadsInvalidReason> {

    void moveSort(Integer bid, LeadsInvalidReason prevLeadsInvalidReason, LeadsInvalidReason nextLeadsInvalidReason);

    LeadsInvalidReason getById(Integer bid, Long id);
}

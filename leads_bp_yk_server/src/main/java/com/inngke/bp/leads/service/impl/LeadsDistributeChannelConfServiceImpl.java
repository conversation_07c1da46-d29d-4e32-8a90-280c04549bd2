package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeChannelConf;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeChannelConfManager;
import com.inngke.bp.leads.dto.request.AddDistributeChannelConfRequest;
import com.inngke.bp.leads.dto.request.LeadsChannelListRequest;
import com.inngke.bp.leads.dto.request.UpdateDistributeChannelConfRequest;
import com.inngke.bp.leads.dto.response.DistributeChannelConfDto;
import com.inngke.bp.leads.dto.response.LeadsChannelDto;
import com.inngke.bp.leads.dto.response.LeadsChannelValueDto;
import com.inngke.bp.leads.service.LeadsChannelService;
import com.inngke.bp.leads.service.LeadsDistributeChannelConfService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class LeadsDistributeChannelConfServiceImpl implements LeadsDistributeChannelConfService {

    @Resource
    private LeadsDistributeChannelConfManager leadsDistributeChannelConfManager;
    @Resource
    private LeadsChannelService leadsChannelService;

    @Override
    public BaseResponse<List<DistributeChannelConfDto>> getList(BaseBidRequest request) {
        Map<Integer, String> leadsChannelMap = getLeadsChannelMap(request.getBid());

        return BaseResponse.success(leadsDistributeChannelConfManager.list(
                Wrappers.<LeadsDistributeChannelConf>query().eq(LeadsDistributeChannelConf.BID, request.getBid())
        ).stream().map(leadsDistributeChannelConf ->
                toDistributeChannelConfDto(leadsDistributeChannelConf, leadsChannelMap)
        ).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<Boolean> addDistributeChannelConf(AddDistributeChannelConfRequest request) {
        if (CollectionUtils.isEmpty(request.getChannelIds())){
            return BaseResponse.error("请选择线索渠道");
        }
        LeadsDistributeChannelConf leadsDistributeChannelConf = new LeadsDistributeChannelConf();
        leadsDistributeChannelConf.setId(SnowflakeHelper.getId());
        leadsDistributeChannelConf.setBid(request.getBid());
        leadsDistributeChannelConf.setName(request.getName());
        leadsDistributeChannelConf.setChannelIds(Joiner.on(InngkeAppConst.COMMA_STR).join(request.getChannelIds()));
        leadsDistributeChannelConf.setCreateTime(LocalDateTime.now());

        boolean existName = leadsDistributeChannelConfManager.count(
                Wrappers.<LeadsDistributeChannelConf>query()
                        .eq(LeadsDistributeChannelConf.BID, request.getBid())
                        .eq(LeadsDistributeChannelConf.NAME, request.getName())
        ) > 0;

        if (existName){
            return BaseResponse.error("名称已存在");
        }

        if (leadsDistributeChannelConfManager.save(leadsDistributeChannelConf)) {
            return BaseResponse.success(true);
        }
        return BaseResponse.error("保存失败");
    }

    @Override
    public BaseResponse<Boolean> updateDistributeChannelConf(UpdateDistributeChannelConfRequest request) {
        if (CollectionUtils.isEmpty(request.getChannelIds())){
            return BaseResponse.error("请选择线索渠道");
        }
        LeadsDistributeChannelConf leadsDistributeChannelConf = new LeadsDistributeChannelConf();
        leadsDistributeChannelConf.setId(request.getId());
        leadsDistributeChannelConf.setName(request.getName());
        leadsDistributeChannelConf.setChannelIds(Joiner.on(InngkeAppConst.COMMA_STR).join(request.getChannelIds()));

        if (leadsDistributeChannelConfManager.updateById(leadsDistributeChannelConf)) {
            return BaseResponse.success(true);
        }
        return BaseResponse.error("更新失败");
    }

    private DistributeChannelConfDto toDistributeChannelConfDto(LeadsDistributeChannelConf leadsDistributeChannelConf, Map<Integer, String> leadsChannelMap) {
        DistributeChannelConfDto distributeChannelConfDto = new DistributeChannelConfDto();
        distributeChannelConfDto.setId(leadsDistributeChannelConf.getId());
        distributeChannelConfDto.setName(leadsDistributeChannelConf.getName());
        distributeChannelConfDto.setChannelIds(
                Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(leadsDistributeChannelConf.getChannelIds()))
                        .stream().map(Long::valueOf).collect(Collectors.toList())
        );
        distributeChannelConfDto.setChannelNameList(
                distributeChannelConfDto.getChannelIds().stream().map(Long::intValue).map(leadsChannelMap::get)
                        .filter(Objects::nonNull).collect(Collectors.toList())
        );
        return distributeChannelConfDto;
    }

    private Map<Integer,String> getLeadsChannelMap(Integer bid){
        LeadsChannelListRequest request = new LeadsChannelListRequest();
        request.setStatus(1);
        request.setBid(bid);

        return Optional.ofNullable(leadsChannelService.getValueList(request)).map(BaseResponse::getData).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(LeadsChannelValueDto::getValue,LeadsChannelValueDto::getName));
    }

}

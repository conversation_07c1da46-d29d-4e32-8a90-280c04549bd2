package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线索前置分配客服配置表
 *
 * <AUTHOR>
 * @since 2022/9/8
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsPreFollowConfig implements Serializable {

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 客服ID,即staffId
     */
    private Long staffId;

    /**
     * 渠道Ids,用逗号隔开
     */
    private String channelIds;

    /**
     * 区域Ids,用逗号隔开
     */
    private String regionIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String BID = "bid";

    public static final String STAFF_ID = "staff_id";

    public static final String CHANNEL_IDS = "channel_ids";

    public static final String REGION_IDS = "region_ids";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";


}

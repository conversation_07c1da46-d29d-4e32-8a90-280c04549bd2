/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsEventConf;
import com.inngke.bp.leads.db.leads.dao.LeadsEventConfDao;
import com.inngke.bp.leads.db.leads.manager.LeadsEventConfManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线索事件配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsEventConfManagerImpl extends ServiceImpl<LeadsEventConfDao, LeadsEventConf> implements LeadsEventConfManager {

}

package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.tp.*;
import com.inngke.bp.leads.dto.response.tp.LeadsTpPullConditionDto;
import com.inngke.bp.leads.dto.response.tp.TpAccountInfoDto;
import com.inngke.bp.leads.dto.response.tp.TpLaunchOauthDataDto;
import com.inngke.bp.leads.service.impl.tp.pull.LeadsPullServiceFactory;
import com.inngke.bp.leads.service.tp.LeadsTpOauthService;
import com.inngke.bp.leads.service.tp.LeadsTpPullConditionService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索-第三方
 * @date 2022/4/19 14:41
 */
@RestController
@RequestMapping("/api/leads-tp")
public class LeadsTpApiController {

    @Autowired
    private LeadsTpPullConditionService leadsTpPullConditionService;

    @Autowired
    private LeadsTpOauthService leadsTpOauthService;

    @Autowired
    private LeadsPullServiceFactory leadsPullServiceFactory;

    /**
     * 获取线索拉取条件
     *
     * @param bid
     * @param operatorId
     * @return
     */
    @GetMapping("/condition")
    public BaseResponse<List<LeadsTpPullConditionDto>> getLeadsTpPullConditionList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId) {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsTpPullConditionService.getLeadsTpPullConditionList(request);
    }

    /**
     * 保存线索拉取条件
     *
     * @param bid
     * @param operatorId
     * @return
     */
    @PostMapping("/condition")
    public BaseResponse<List<LeadsTpPullConditionDto>> saveLeadsTpPullConditionList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody SaveLeadsTpPullConditionRequest request) {
        request.setBid(bid);
        request.setOperatorId(operatorId);

        return leadsTpPullConditionService.saveLeadsTpPullConditionList(request);
    }

    /**
     * 获取已授权的第三方信息
     *
     * @param bid
     * @param operatorId
     * @return
     */
    @GetMapping("/already-oauth")
    public BaseResponse<List<TpAccountInfoDto>> getAlreadyOauthList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId) {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsTpOauthService.getAlreadyOauthList(request);
    }

    @PutMapping("/setting/operator-staff")
    public BaseResponse<Boolean> settingOperatorStaff(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody AccountSettingOperatorStaffRequest request
    ) {
        request.setBid(jwtPayload.getBid());
        request.setOperatorStaffId(jwtPayload.getSid());

        return leadsTpOauthService.settingOperatorStaff(request);
    }

    /**
     * 已授权的第三方账号解绑
     */
    @PutMapping("/unbind-oauth")
    public BaseResponse<Boolean> unbindOauth(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated UnbindOauthRequest request) {
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsTpOauthService.unbindOauth(request);
    }

    /**
     * 获取发起授权数据
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return
     */
    @GetMapping("/oauth-data")
    public BaseResponse<TpLaunchOauthDataDto> getLaunchOauthData(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            GetTpLaunchOauthDataRequest request) {

        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsTpOauthService.getLaunchOauthData(request);
    }

    /**
     * 第三方回调接口
     *
     * @param bid
     * @param type
     * @param authorizationCode
     * @return
     */
    @GetMapping("/oauth/callback/bid/{bid:\\d+}/type/{type:\\d+}")
    public BaseResponse<Boolean> oauthCallback(
            @RequestHeader int bid,
            @PathVariable("type") int type,
            @RequestParam("authorization_code") String authorizationCode
    ) {

        OauthCallbackRequest request = new OauthCallbackRequest();
        request.setOperatorId(0L);
        request.setType(type);
        request.setBid(bid);
        request.setAuthorizationCode(authorizationCode);

        return leadsTpOauthService.oauthCallback(request);
    }

    /**
     * 拉取线索 type:1腾讯 2:飞鱼
     *
     * @param bid
     * @param type
     */
    @GetMapping("pull/leads/{type}")
    public void pullFlyFishLeads(
            @RequestHeader int bid,
            @PathVariable("type") Integer type,
            @RequestParam("startTime") String startTime
    ){
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setBid(bid);

        leadsPullServiceFactory.getInstance(type).pull(startTime,request);
    }


}

package com.inngke.bp.leads.mq.process.leads;

import com.inngke.bp.distribute.dto.request.DistributorCustomerRequest;
import com.inngke.bp.distribute.dto.response.DistributorLeadDto;
import com.inngke.bp.leads.client.DistributorCustomerServiceClientForLeads;
import com.inngke.bp.leads.client.DistributorServiceClientForLeads;
import com.inngke.bp.leads.core.constant.MQTopicConstant;
import com.inngke.bp.leads.dto.RewardEventDto;
import com.inngke.bp.leads.mq.listener.LeadsChangeListener;
import com.inngke.bp.leads.mq.message.leads.LeadsChangeMessage;
import com.inngke.bp.leads.mq.process.LeadsProcess;
import com.inngke.bp.plus.enums.RewardEventEnum;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.request.BatchMqSendRequest;
import com.inngke.ip.common.service.MqService;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class RewardEventProcess implements LeadsProcess {
    private static final Logger logger = LoggerFactory.getLogger(RewardEventProcess.class);
    @Autowired
    private DistributorServiceClientForLeads distributorServiceClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @Autowired
    private JsonService jsonService;

    @Override
    public int type() {
        return 10;
    }


    @Override
    public void process(LeadsChangeMessage message) {
        List<Long> leadsIds = message.getLeadsId();
        Integer bid = message.getBid();
        List<DistributorLeadDto> customerIdByLeads = distributorServiceClientForLeads.getCustomerIdByLeads(leadsIds, bid);
        if (CollectionUtils.isEmpty(customerIdByLeads)){
            logger.warn("处理奖励事件，获取到线索关联报备客户信息为空，leadsIds={}，bid={}",leadsIds,bid);
            return;
        }
        //发送奖励事件mq
        List<String> playloads = getplayloads(bid, customerIdByLeads);
        BatchMqSendRequest batchMqSendRequeste = new BatchMqSendRequest();
        batchMqSendRequeste.setBid(bid);
        batchMqSendRequeste.setTopic(MQTopicConstant.REWARD_EVNT_TOPIC);
        batchMqSendRequeste.setPayloads(playloads);
        batchMqSendRequeste.setOperatorId(0L);
        BaseResponse<Boolean> response = mqService.batchSend(batchMqSendRequeste);
        if (!BaseResponse.responseSuccessWithNonNullData(response)){
            logger.warn("发送mq失败!playload={}",playloads);
        }else {
            logger.info("线索变更触发奖励事件，发送mq成功！");
        }

    }

    private List<String> getplayloads(Integer bid, List<DistributorLeadDto> customerIdByLeads) {
        List<String> playloads = Lists.newArrayList();
        customerIdByLeads.forEach(
                customer ->{
                    RewardEventDto rewardEventDto = new RewardEventDto();
                    rewardEventDto.setEvent(RewardEventEnum.TRANSFER_CUSTOMER.getCode());
                    rewardEventDto.setBid(bid);
                    rewardEventDto.setGuideId(customer.getGuideId());
                    rewardEventDto.setDistributorId(customer.getDistributorId());
                    rewardEventDto.setLeadsId(customer.getLeadsId());
                    playloads.add(jsonService.toJson(rewardEventDto));
                }
        );
        return playloads;
    }
}

package com.inngke.bp.leads.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.service.LeadsFollowTimeService;
import com.inngke.common.core.config.privatization.PrivatizationDb;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.LockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class LeadsFollowTimeSchedule {

    private static final String REDIS_KEY = LeadsServiceConsts.APP_ID + ":leadsFollowTime:";
    private static final String LOCK_KEY = LeadsServiceConsts.APP_ID + ":lock:leadsFollowTime";
    private static final int BATCH_SIZE = 2000;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LockService lockService;

    @Autowired
    private LeadsFollowTimeService leadsFollowTimeService;

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    /**
     * 执行完后间隔61秒执行一次
     */
    @Scheduled(fixedRate = 61000)
    public void updateLeadsFollowTime() {
        Lock lock = lockService.getLock(LOCK_KEY, 300);
        if (lock == null) {
            return;
        }
        try {
            databasePrivatizationService.accept(this::doUpdateOrSaveLeadsFollowTime);
        } finally {
            lock.unlock();
        }
    }

    private void doUpdateOrSaveLeadsFollowTime(PrivatizationDb privatizationDb) {
        ValueOperations valOps = redisTemplate.opsForValue();
        String key = REDIS_KEY + privatizationDb.getCode();
        Number leadsFollowId = (Number) valOps.get(key);
        if (Objects.nonNull(leadsFollowId)) {
            leadsFollowId = leadsFollowId.longValue();
        }
        while (true) {
            List<LeadsFollow> list = leadsFollowManager.list(
                    Wrappers.<LeadsFollow>query()
                            .gt(Objects.nonNull(leadsFollowId), LeadsFollow.ID, leadsFollowId)
                            //不查询 follow_content 和 follow_images字段
                            .select(LeadsFollow.class, info -> !info.getColumn().equals(LeadsFollow.FOLLOW_CONTENT) && !info.getColumn().equals(LeadsFollow.FOLLOW_IMAGES))
                            .last("LIMIT " + BATCH_SIZE)
            );
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            leadsFollowTimeService.updateOrSaveLeadsFollowTime(list);
            leadsFollowId = list.get(list.size() - 1).getId();
            if (list.size() < BATCH_SIZE) {
                break;
            }
        }
        if (Objects.nonNull(leadsFollowId)) {
            valOps.set(key, leadsFollowId, 30, TimeUnit.DAYS);
        }
    }
}

package com.inngke.bp.leads.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/22 17:28
 */
@NoArgsConstructor
@Data
public class GetFlyFishAdvertiserRequest implements Serializable {

    @JsonProperty("advertiser_ids")
    private List<Long> advertiserIds;

    @JsonProperty("fields")
    private List<String> fields;
}

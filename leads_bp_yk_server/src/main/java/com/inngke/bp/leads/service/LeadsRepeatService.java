package com.inngke.bp.leads.service;

import com.inngke.bp.leads.db.leads.entity.Leads;

import java.util.List;
import java.util.Set;

/**
 * 线索重复
 *
 * <AUTHOR>
 * @date 2023/2/8 11:30
 */
public interface LeadsRepeatService {

    /**
     * 判断线索重复
     * @param bid
     * @param mobile
     * @param weChat
     * @param excludeIds
     * @return
     */
    List<Leads> checkRepeat(Integer bid, String mobile, String weChat, Set<Long> excludeIds);
}

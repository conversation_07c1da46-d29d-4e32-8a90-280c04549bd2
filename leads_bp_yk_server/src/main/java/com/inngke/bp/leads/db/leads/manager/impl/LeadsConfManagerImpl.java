/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsConf;
import com.inngke.bp.leads.db.leads.dao.LeadsConfDao;
import com.inngke.bp.leads.db.leads.manager.LeadsConfManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线索配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsConfManagerImpl extends ServiceImpl<LeadsConfDao, LeadsConf> implements LeadsConfManager {

}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索扩展表-信息类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsExtInformation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 外部ID
     */
    private String externalId;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 性别 1:男 2:女
     */
    private Integer gender;

    /**
     * 计划ID
     */
    private String campaignId;

    /**
     * 计划名称
     */
    private String campaignName;

    /**
     * 广告主ID 腾讯,飞鱼用户ID
     */
    private String accountId;

    /**
     * 广告主名称
     */
    private String accountName;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String EXTERNAL_ID = "external_id";

    public static final String OPEN_ID = "open_id";

    public static final String GENDER = "gender";

    public static final String AGE = "age";

    public static final String QQ = "qq";

    public static final String EMAIL = "email";

    public static final String CAMPAIGN_ID = "campaign_id";

    public static final String CAMPAIGN_NAME = "campaign_name";

    public static final String ACCOUNT_ID = "account_id";

    public static final String ACCOUNT_NAME = "account_name";

    public static final String SUBMIT_TIME = "submit_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

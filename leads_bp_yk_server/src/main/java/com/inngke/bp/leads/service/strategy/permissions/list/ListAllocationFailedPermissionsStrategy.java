package com.inngke.bp.leads.service.strategy.permissions.list;

import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 分配失败
 */
@Component
public class ListAllocationFailedPermissionsStrategy extends ListPermissionsStrategyAbstract {

    /**
     * 可以看到自己创建的、自己所管理部门的员工创建的
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    @Override
    protected void otherPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultOtherPermissionsHandle(request, queryBuilder);
    }

    /**
     * 可以看到自己创建的、自己所管理部门的员工创建的、对接第三方系统的线索数据
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    @Override
    protected void merchantManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultMerchantManagePermissionsHandle(request,queryBuilder);
    }
}

package com.inngke.bp.leads.dto;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class DistributeStaffState implements Serializable {

    private List<Leads> leadsList;

    private List<LeadsFollow> autoForwardLeadsFollowList;

    private Map<Integer, List<Long>> channelConfIdsMap;

    private boolean finalSaveIndex;
}

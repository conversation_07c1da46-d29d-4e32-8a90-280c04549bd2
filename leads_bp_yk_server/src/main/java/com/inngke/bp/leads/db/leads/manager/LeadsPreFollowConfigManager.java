package com.inngke.bp.leads.db.leads.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/9/8
 **/
public interface LeadsPreFollowConfigManager extends IService<LeadsPreFollowConfig> {
    LeadsPreFollowConfig getByStaffId(Integer bid,Long staffId);

    List<LeadsPreFollowConfig> getByStaffIds(Integer bid, Set<Long> staffIds);
}

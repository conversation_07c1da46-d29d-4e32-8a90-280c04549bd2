/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.dao.LeadsPushBackLogDao;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPushBackLogManager;
import com.inngke.bp.leads.dto.CommitLeadsPushBackLogDto;
import com.inngke.bp.leads.dto.RejectPushBackDto;
import com.inngke.common.ds.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 * 线索退回记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
@Slf4j
public class LeadsPushBackLogManagerImpl extends ServiceImpl<LeadsPushBackLogDao, LeadsPushBackLog> implements LeadsPushBackLogManager {
    @Resource
    private LeadsManager leadsManager;

    @Override
    public LeadsPushBackLog findLeadsPushBackLogById(Integer bid, Long id) {
        if (Objects.isNull(bid) || Objects.isNull(id)) {
            return null;
        }
        return this.getBaseMapper().selectOne(
                Wrappers.<LeadsPushBackLog>query()
                        .eq(LeadsPushBackLog.BID, bid)
                        .eq(LeadsPushBackLog.ID, id)
        );
    }

    @Override
    public LeadsPushBackLog findLastOneLeadsPushBackLogByLeadsId(Integer bid, Long leadsId, Integer isReject) {
        if (Objects.isNull(bid) || Objects.isNull(leadsId)) {
            return null;
        }

        return this.getBaseMapper().selectOne(
                Wrappers.<LeadsPushBackLog>query()
                        .eq(LeadsPushBackLog.BID, bid)
                        .eq(LeadsPushBackLog.LEADS_ID, leadsId)
                        .eq(Objects.nonNull(isReject), LeadsPushBackLog.IS_REJECT, isReject)
                        .orderByDesc(LeadsPushBackLog.PUSH_BACK_TIME)
                        .last("LIMIT 1")
        );
    }

    @Override
    public List<LeadsPushBackLog> batchFindLastOneLeadsPushBackLogByLeadsId(Integer bid, Set<Long> leadsIds, Integer isReject) {
        if (Objects.isNull(bid) || CollectionUtils.isEmpty(leadsIds)) {
            return Lists.newArrayList();
        }

        return this.getBaseMapper().selectList(
                Wrappers.<LeadsPushBackLog>query()
                        .eq(LeadsPushBackLog.BID, bid)
                        .in(LeadsPushBackLog.LEADS_ID, leadsIds)
                        .eq(Objects.nonNull(isReject), LeadsPushBackLog.IS_REJECT, isReject)
                        .orderByDesc(LeadsPushBackLog.PUSH_BACK_TIME)
                        .select(
                                LeadsPushBackLog.ID,
                                LeadsPushBackLog.BID,
                                LeadsPushBackLog.LEADS_ID,
                                LeadsPushBackLog.LEADS_SNAPSHOT,
                                LeadsPushBackLog.LEADS_SNAPSHOT,
                                LeadsPushBackLog.PUSH_BACK_REASON_IMAGES,
                                "MAX(" +LeadsPushBackLog.PUSH_BACK_TIME+ ") AS pushBackTime"
                        )
                        .groupBy(LeadsPushBackLog.LEADS_ID)
        );
    }

    @Override
    public Long commitPushBackLog(CommitLeadsPushBackLogDto commitLeadsPushBackLog) {
        if (Objects.isNull(commitLeadsPushBackLog)) {
            return null;
        }
        LeadsPushBackLog leadsPushBackLog = commitLeadsPushBackLog.convertLeadsPushBackLog();
        int insert = this.getBaseMapper().insert(leadsPushBackLog);
        return insert > 0 ? leadsPushBackLog.getId() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long rejectPushBack(RejectPushBackDto rejectPushBack) {
        if (Objects.isNull(rejectPushBack)) {
            return null;
        }
        LeadsPushBackLog leadsPushBackLog = this.findLeadsPushBackLogById(rejectPushBack.getBid(), rejectPushBack.getPushBackLogId());
        if (Objects.isNull(leadsPushBackLog)) {
            log.info("未找到退回记录：id={}", rejectPushBack.getPushBackLogId());
            return null;
        }
        if (leadsPushBackLog.getIsReject() == 1) {
            log.info("退回记录已被驳回：id={}", rejectPushBack.getPushBackLogId());
            return null;
        }
        LeadsPushBackLog pushBackLog = rejectPushBack.convertLeadsPushBackLog();
        int row = this.getBaseMapper().updateById(pushBackLog);
        if (row > 0) {
            leadsPushBackLog = this.findLeadsPushBackLogById(rejectPushBack.getBid(), rejectPushBack.getPushBackLogId());
            return leadsManager.rejectPushBackRollbackLeads(leadsPushBackLog);
        }
        return pushBackLog.getId();

    }
}

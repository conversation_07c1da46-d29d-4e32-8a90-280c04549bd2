package com.inngke.bp.leads.service;

import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.dto.request.LeadsFollowTimeByDepartmentRequest;
import com.inngke.bp.leads.dto.request.LeadsFollowTimeByStaffRequest;
import com.inngke.bp.leads.dto.request.LeadsOrganizationChannelRequest;
import com.inngke.bp.leads.dto.request.LeadsOrganizationPreFollowRequest;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.dto.response.LeadsOrganizationPreFollowDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.HashMap;
import java.util.List;

public interface LeadsFollowTimeService {

    /**
     *全量同步线索跟进
     * @return
    */
    BaseResponse<Void> allSync(int bid);

    /**
     *更新LeadsFollowTime
     * @param	list
     * @return void
    */
    void updateOrSaveLeadsFollowTime(List<LeadsFollow> list);

    /**
     * 员工维度统计
     * @param query
     * @return
     */
    BaseResponse<BasePaginationResponse<LeadsFollowTimeByStaffResponse>> listByStaff(LeadsFollowTimeByStaffRequest query);

    /**
     * 组织维度-客服明细详情
     */
    BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByStaffDetail(LeadsFollowTimeByStaffRequest query);

    /**
     * 部门维度统计
     * @param query
     * @return
     */
    BaseResponse<List<LeadsFollowTimeByStaffResponse>> listByDepartment(LeadsFollowTimeByDepartmentRequest query);


    /**
     * 转化报表-组织维度部门详情
     */
    BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByDepartmentDetail(LeadsFollowTimeByDepartmentRequest query);


    BaseResponse<List<LeadsFollowTimeByStaffResponse>> listByDepartment(LeadsFollowTimeByDepartmentRequest query, HashMap<Long, String> departIdAndNameAll);

    /**
     * 部门导出
     */
    BaseResponse<List<LeadsFollowTimeByStaffResponse>> exportDepartment(LeadsFollowTimeByDepartmentRequest query);

    BaseResponse<List<LeadsFollowTimeByStaffResponse>> exportDepartmentV2(LeadsFollowTimeByDepartmentRequest query);

    /**
     * 组织维度-客服报表
     */
    BaseResponse<List<LeadsOrganizationPreFollowDto>> listByPreFollow(LeadsOrganizationPreFollowRequest query);

    /**
     * 组织维度-客服报表指标明细
     */
    BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByPreFollowDetail(LeadsOrganizationPreFollowRequest query);

    /**
     * 组织维度-渠道来源指标明细
     */
    BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByChannelDetail(LeadsOrganizationChannelRequest query);
}

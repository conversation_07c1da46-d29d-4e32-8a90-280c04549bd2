package com.inngke.bp.leads.notify.builder;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.notify.context.AdminNotifyNotContactGuidesContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AdminNotifyNotContactGuidesMessageBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/21 15:37
 */
@Component(value = "admin_notify_not_contact_guides")
@Slf4j
public class AdminNotifyNotContactGuidesMessageBuilder extends TemplateMessageContentBuilder<AdminNotifyNotContactGuidesContext> {


    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(AdminNotifyNotContactGuidesContext ctx) {

        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);

        Long targetStaffId = ctx.getTargetStaffId();

        String qyUserId = ctx.getQyUserId();

        String name = "";
        String mobiles = "";
        String createTime = "";

        List<String> mobileList = Lists.newArrayList();
        if (StringUtils.isBlank(qyUserId)) {
            name = "您有" + ctx.getSize() + "条线索未跟进";
            mobiles = ctx.getLeadsList().stream().findFirst().map(LeadsEsDto::getMobile).map(this::cellPhoneNumberDesensitization).orElse("") + "..";

        } else {

            List<String> nameList = ctx.getLeadsList().stream().map(LeadsEsDto::getName).filter(com.inngke.common.utils.StringUtils::isNotBlank)
                    .limit(2).collect(Collectors.toList());

            String extName = ctx.getSize() > 2 && nameList.size() > 1 ? "..." : "";
            name = nameList.size() > 1 ? Joiner.on("，").join(nameList) + extName :
                    nameList.stream().findFirst().orElse(InngkeAppConst.EMPTY_STR);

            String extMobile = ctx.getSize() > 2 ? "..." : "";
            mobileList = ctx.getLeadsList().stream().map(LeadsEsDto::getMobile).filter(com.inngke.common.utils.StringUtils::isNotBlank)
                    .limit(2).map(this::cellPhoneNumberDesensitization).collect(Collectors.toList());
            mobiles = mobileList.size() > 1 ? Joiner.on("，").join(mobileList) + extMobile :
                    mobileList.stream().findFirst().orElse("未知") + "..";

            if (ctx.getSize() == 1) {
                LeadsEsDto leadsEsDto = ctx.getLeadsList().get(0);
                createTime = DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(leadsEsDto.getDistributeTime()), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
            } else {
                createTime = DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(ctx.getLeadsList().get(ctx.getLeadsList().size() - 1).getDistributeTime()), DateTimeUtils.YYYY_MM_DD_HH_MM_SS)
                        + "~" +
                        DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(ctx.getLeadsList().get(0).getDistributeTime()), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
            }

        }

        return builder
                .setTitle("你有" + ctx.getSize() + "条线索未跟进！")
                .setVar("name", name)
                .setVar("mobile", StringUtils.isBlank(mobiles) ? "-" : mobiles)
                .setVar("followType", "跟进提醒")
                .setVar("followContent", "请及时跟进处理")
                // 企业微信
                .setVar("createTime", createTime)
                .setVar("content", "请及时跟进处理以免错过最佳跟进时间!");
    }

    private String cellPhoneNumberDesensitization(String mobile){
        if (com.inngke.common.utils.StringUtils.isBlank(mobile)){
            return mobile;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}

package com.inngke.bp.leads.service.message.sender;

import com.inngke.bp.leads.client.MqServiceForLeads;
import com.inngke.bp.leads.client.ShortLinkServiceForLeads;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.ip.common.dto.request.ShortLinkGenerateRequest;
import com.inngke.ip.common.dto.response.ShortLinkGenerateDto;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-07-10 17:05
 **/
@Service
public class LeadsDailyNotifySenderService extends MessageSenderServiceAbs {

    private static final Logger logger = LoggerFactory.getLogger(LeadsDailyNotifySenderService.class);


    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    @Autowired
    private ShortLinkServiceForLeads shortLinkServiceForLeads;

    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.LEADS_DAILY_NOTIFY;
    }

    @Override
    public void init(MessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(MessageContext ctx) {
        int bid = ctx.getBid();
        String mobile = ctx.getMobile();
        Integer count = ctx.getCount();

        ShortLinkGenerateRequest shortLinkGenerateRequest = new ShortLinkGenerateRequest();
        shortLinkGenerateRequest.setBid(bid);
        if (count.equals(1)) {
            // id=26950&staffId=1284&type=undefined
            shortLinkGenerateRequest.setPagePath("Clue/Clue/Cluetit/Cluetit");
            shortLinkGenerateRequest.setPageRequest("id=" + ctx.getLeads().getId());
        } else {
            shortLinkGenerateRequest.setPagePath("Clue/Clue/myClue/myClue");
            shortLinkGenerateRequest.setPageRequest("staffId" + ctx.getLeads().getDistributeStaffId());
        }

        ShortLinkGenerateDto generate = shortLinkServiceForLeads.generate(shortLinkGenerateRequest);

        return getTemplateRequestBuilder(ctx)
                .setVar("leadsCount", String.valueOf(ctx.getCount()))
                .setVar("leadsLink", generate.getCode())
                .setVar("enterpriseName", ctx.getEnterpriseName())
                .setMobile(mobile)
                .build();
    }
}

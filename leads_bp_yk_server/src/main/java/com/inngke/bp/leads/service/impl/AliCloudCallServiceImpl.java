package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.dto.request.AliCloudCallCenterMessageRequest;
import com.inngke.bp.leads.mq.message.reach.AliCloudCallDto;
import com.inngke.bp.leads.service.AliCloudCallService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 根据阿里云呼叫中心信息更新线索内容
 * <AUTHOR>
 * @since 2023-3-15 11:29:32
 */
@Service
public class AliCloudCallServiceImpl implements AliCloudCallService {

    @Autowired
    private LeadsServiceV2 leadsServiceV2;


    @Override
    public BaseResponse updateClueInfoByAliCloudCallCenterMessage(AliCloudCallDto aliCloudCallDto) {
        AliCloudCallCenterMessageRequest aliCloudCallCenterMessageRequest
                = new AliCloudCallCenterMessageRequest();
        aliCloudCallCenterMessageRequest.setCallType(aliCloudCallDto.getCallType());
        aliCloudCallCenterMessageRequest.setCallDuration(aliCloudCallDto.getCallDuration());
        aliCloudCallCenterMessageRequest.setBid(aliCloudCallDto.getBid());
        aliCloudCallCenterMessageRequest.setLeadsId(aliCloudCallDto.getLeadsId());
        aliCloudCallCenterMessageRequest.setFollowTime(aliCloudCallDto.getFollowTime());
        aliCloudCallCenterMessageRequest.setConnected(aliCloudCallDto.getConnected());
        aliCloudCallCenterMessageRequest.setReason(aliCloudCallDto.getReason());
        aliCloudCallCenterMessageRequest.setStaffId(aliCloudCallDto.getStaffId());
        BaseResponse baseResponse = leadsServiceV2.updateLeadsInfoByAliCloudCallCenterMessage(aliCloudCallCenterMessageRequest);
        if (!BaseResponse.responseSuccess(baseResponse)){
            return BaseResponse.error(baseResponse.getMsg());
        }
        return BaseResponse.success();
    }
}

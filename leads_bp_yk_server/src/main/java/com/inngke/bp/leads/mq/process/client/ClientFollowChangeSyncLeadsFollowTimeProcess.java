package com.inngke.bp.leads.mq.process.client;

import com.inngke.bp.client.dto.response.client.ClientFollowDto;
import com.inngke.bp.leads.client.ClientGetClientFollowForLeads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ClientFollowChangeSyncLeadsFollowTimeProcess implements ClientFollowChangeProcess{

    @Autowired
    private ClientGetClientFollowForLeads clientGetClientFollowForLeads;

    @Autowired
    private LeadsManager leadsManager;


    @Override
    public void process(EntityChangeMessageDto message) {
        ClientFollowDto clientFollow = clientGetClientFollowForLeads.getById(message.getBid(), message.getId());
        if (Objects.isNull(clientFollow)){
            return;
        }

        leadsManager.updateLeadsFollowTimeByClientId(
                message.getBid(), clientFollow.getClientId(),
                DateTimeUtils.MillisToLocalDateTime(clientFollow.getCreateTime())
        );
    }
}

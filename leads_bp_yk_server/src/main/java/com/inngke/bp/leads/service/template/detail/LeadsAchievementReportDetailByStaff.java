package com.inngke.bp.leads.service.template.detail;

import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.service.template.leadsAchievement.LeadsAchievementReportContext;
import com.inngke.common.exception.InngkeServiceException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 14:16
 */
@Component
public class LeadsAchievementReportDetailByStaff extends LeadsAchievementDetail {

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;


    @Override
    public String getDimensionCode() {
        return "staffDetail";
    }


    @Override
    protected void checkPara(LeadsAchievementReportContext context) {
        if (context.getSelectStaffId() == null) {
            throw new InngkeServiceException("selectStaffId不能为空");
        }
    }

    @Override
    protected void afterQueryBuildPlugin(LeadsAchievementReportContext context) {
        BoolQueryBuilder qb = context.getBoolQueryBuilder();
        qb.mustNot(QueryBuilders.termQuery("followStaffId", 0L));
        if (Objects.nonNull(context.getSelectStaffId())) {
            qb.must(QueryBuilders.termQuery("followStaffId", context.getSelectStaffId()));
        }
        if (Objects.nonNull(context.getSelectDeptId())) {
            Set<Long> childrenDepartment = departmentClientForLeads.getChildrenDepartment(context.getBid(), context.getSelectDeptId());
            childrenDepartment.add(context.getSelectDeptId());
            qb.must(QueryBuilders.termsQuery("followDepartmentId", childrenDepartment));
        }
        Set<Long> manageDepartment = getManageDepartment(context.getBid(), null, context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("followDepartmentId", manageDepartment));
    }

    @Override
    protected void setTimeAndAmountBoolQueryBuilder(BoolQueryBuilder qb, LeadsAchievementReportContext context) {
        qb.must(QueryBuilders.termQuery("followStaffId", context.getSelectStaffId()));

        Set<Long> manageDepartment = getManageDepartment(context.getBid(), null, context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("followDepartmentId", manageDepartment));
    }
}

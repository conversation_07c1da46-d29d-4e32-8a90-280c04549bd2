package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * Created by emiya on 2022/6/15 17:54
 *
 * <AUTHOR>
 * @date 2022/6/15 17:54
 */
@Component(value = "LeadsDepositAmountStrategy")
public class LeadsDepositAmountStrategy implements LeadsFollowTimeStrategy {



    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.sum(
                        "depositAmount"
                ).field("depositAmount")
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedSum sum = bucketAggregations.get("depositAmount");
        double depositAmount = sum.getValue();
        dto.setDepositAmount(Optional.ofNullable(BigDecimal.valueOf(depositAmount).setScale(0, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO));
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        BigDecimal depositAmount = dtos.stream().filter(t -> t.getDepositAmount() != null).map(LeadsFollowTimeByStaffResponse::getDepositAmount).reduce(BigDecimal::add).get();
        dto.setDepositAmount(depositAmount.setScale(0, RoundingMode.HALF_UP));
    }
}

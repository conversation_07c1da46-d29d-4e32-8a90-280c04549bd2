package com.inngke.bp.leads.db.leads.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/3/10 9:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LeadsInformationExtend extends Leads{

    /**
     * 外部ID
     */
    private String externalId;

    /**
     * 性别 1:男 2:女
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 计划ID
     */
    private String campaignId;

    /**
     * 计划名称
     */
    private String campaignName;

    /**
     * 广告主ID 腾讯,飞鱼用户ID
     */
    private String accountId;

    /**
     * 广告主名称
     */
    private String accountName;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
}

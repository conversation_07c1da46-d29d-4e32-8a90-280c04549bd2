package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.ChannelClientForLeads;
import com.inngke.bp.leads.client.RbacClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConf;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConfPermissions;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfManager;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfPermissionsManager;
import com.inngke.bp.leads.dto.LeadsRegionExcelDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.DistributeConfMapDto;
import com.inngke.bp.leads.dto.response.RegionConfDto;
import com.inngke.bp.leads.dto.response.StaffSimpleInfoDto;
import com.inngke.bp.leads.service.LeadsDistributeConfService;
import com.inngke.bp.leads.service.schedule.LeadsRegionCache;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.bp.organize.dto.response.channel.ChannelDto;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.ExcelUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.request.RegionGetRequest;
import com.inngke.ip.common.dto.response.RegionDto;
import com.inngke.ip.common.service.RegionService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.client.RbacClientForLeads.MERCHANT_MANAGER;

/**
 * <AUTHOR>
 * @since 2021/9/7 4:37 PM
 */
@DubboService(version = "1.0.0")
public class LeadsDistributeConfServiceImpl implements LeadsDistributeConfService {
    private static final Logger logger = LoggerFactory.getLogger(LeadsDistributeConfServiceImpl.class);

    @Autowired
    private LeadsRegionCache regionCache;

    @Autowired
    private LeadsDistributeConfManager leadsDistributeConfManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private LeadsDistributeConfPermissionsManager leadsDistributeConfPermissionsManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private RegionService regionService;

    @Autowired
    private ChannelClientForLeads channelClientForLeads;


    /**
     * 获取行政区域树
     *
     * @param request 请求
     * @return 行政区域树
     */
    @Override
    public BaseResponse<List<RegionConfDto>> getRegionTree(GetLeadsDistributeConfRequest request) {
        List<LeadsDistributeConf> leadsDistributeConfList = leadsDistributeConfManager.list(
                Wrappers.<LeadsDistributeConf>query()
                        .eq(LeadsDistributeConf.BID, request.getBid())
                        .eq(LeadsDistributeConf.CHANNEL_ID, Optional.ofNullable(request.getChannelId()).orElse(0L))
                        .select(LeadsDistributeConf.STAFF_IDS, LeadsDistributeConf.REGION_ID)
        );
        //设置地区信息
        RegionDto region = getRegion(0); //获取全部数据
        assert region != null;
        List<RegionConfDto> provinces = setRegionInfo(region);

        if (CollectionUtils.isEmpty(leadsDistributeConfList)) {
            return BaseResponse.success(filterPermissions(provinces, request.getBid(), request.getOperatorId()));
        }
        //获取员工和经销商对应信息
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(request.getBid());

        List<Long> staffIds = leadsDistributeConfList.stream().map(
                leadsDistributeConf -> Lists.newArrayList(leadsDistributeConf.getStaffIds().split(","))
        ).flatMap(Collection::stream).map(Long::parseLong).collect(Collectors.toList());
        baseIdsRequest.setIds(staffIds);

        List<StaffIdAndAgentIdDto> staffAndAgentInfos = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        //获取地区ids
        Map<Long, StaffIdAndAgentIdDto> staffAndAgentInfoMap = staffAndAgentInfos.stream().collect(Collectors.toMap(StaffIdAndAgentIdDto::getId, Function.identity()));
        Map<Integer, List<StaffIdAndAgentIdDto>> regionIdAndStaffInfoMap = Maps.newHashMap();
        leadsDistributeConfList.forEach(leadsDistributeConf -> {
            List<String> staffids = Splitter.on(InngkeAppConst.COMMA_STR).trimResults().splitToList(leadsDistributeConf.getStaffIds());
            List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtos = Lists.newArrayList();
            staffids.forEach(staffid -> {
                StaffIdAndAgentIdDto staffIdAndAgentIdDto = staffAndAgentInfoMap.get(Long.parseLong(staffid));
                staffIdAndAgentIdDtos.add(staffIdAndAgentIdDto);
            });
            regionIdAndStaffInfoMap.put(leadsDistributeConf.getRegionId(), staffIdAndAgentIdDtos);
        });


        //设置员工信息
        provinces.forEach(
                province -> {
                    if (regionIdAndStaffInfoMap.containsKey(province.getId())) {
                        setStaffInfo(regionIdAndStaffInfoMap.get(province.getId()), province);
                    }
                    province.getChildren().forEach(

                            city -> {
                                if (regionIdAndStaffInfoMap.containsKey(city.getId())) {
                                    setStaffInfo(regionIdAndStaffInfoMap.get(city.getId()), city);
                                }

                                city.getChildren().forEach(area -> {
                                    if (regionIdAndStaffInfoMap.containsKey(area.getId())) {
                                        setStaffInfo(regionIdAndStaffInfoMap.get(area.getId()), area);
                                    }
                                });
                            });
                }
        );
        return BaseResponse.success(filterPermissions(provinces, request.getBid(), request.getOperatorId()));
    }

    /**
     * 过滤没有权限的地区
     */
    private List<RegionConfDto> filterPermissions(List<RegionConfDto> provinces, Integer bid,Long cid){
        StaffDto staff = staffClientForLeads.getByCustomerId(bid,cid);
        if (Objects.isNull(staff)){
            return Lists.newArrayList();
        }

        Set<String> userRoleCode = rbacClientForLeads.getUserRoleCode(bid, staff.getId());
        if (CollectionUtils.isEmpty(userRoleCode)){
            return Lists.newArrayList();
        }

        if (userRoleCode.contains(MERCHANT_MANAGER)){
            return provinces;
        }

        Map<Long,List<Integer>> staffRegionMap = leadsDistributeConfPermissionsManager.getStaffPermissionsMap(bid);
        List<Integer> staffRegions = staffRegionMap.get(staff.getId());
        if (CollectionUtils.isEmpty(staffRegions)){
            return Lists.newArrayList();
        }

        return provinces.stream().filter(province-> staffRegions.contains(province.getId())).collect(Collectors.toList());
    }


    private List<RegionConfDto> setRegionInfo(RegionDto region) {
        List<RegionDto> children = region.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return Lists.newArrayList();
        }
        return children.stream().map(regionInfo -> {
            RegionConfDto regionConfDto = new RegionConfDto();
            regionConfDto.setId(regionInfo.getId());
            regionConfDto.setName(regionInfo.getFullName());
            if (regionInfo.getLevel() != 3) {
                List<RegionConfDto> childrenRegion = setRegionInfo(regionInfo);
                if (!CollectionUtils.isEmpty(childrenRegion)) {
                    regionConfDto.setChildren(childrenRegion);
                }
            }
            return regionConfDto;
        }).collect(Collectors.toList());
    }


    /**
     * 设置员工信息
     *
     * @param staffIdAndAgentIdDtoList 经销商ID与员工ID
     */
    private void setStaffInfo(List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtoList, RegionConfDto regionConfDto) {
        List<StaffSimpleInfoDto> staffSimpleInfoDtoList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(staffIdAndAgentIdDtoList) && staffIdAndAgentIdDtoList.size() != 0) {
            staffIdAndAgentIdDtoList.forEach(staffInfo -> {
                if (staffInfo == null) {
                    return;
                }
                StaffSimpleInfoDto staffSimpleInfoDto = new StaffSimpleInfoDto();
                staffSimpleInfoDto.setId(staffInfo.getId());
                if (!StringUtils.isEmpty(staffInfo.getStaffName())) {
                    staffSimpleInfoDto.setName(staffInfo.getStaffName());
                }
                if (!StringUtils.isEmpty(staffInfo.getAgentName())) {
                    staffSimpleInfoDto.setAgentName(staffInfo.getAgentName());
                }
                staffSimpleInfoDto.setId(staffInfo.getId());
                staffSimpleInfoDtoList.add(staffSimpleInfoDto);
            });
        }

        if (!CollectionUtils.isEmpty(staffSimpleInfoDtoList)) {
            regionConfDto.setStaffList(staffSimpleInfoDtoList);
        }

    }


    /**
     * 保存区域设置
     *
     * @param request 保存请求参数
     * @return
     */
    @Override
    public BaseResponse<Boolean> save(RegionConfSaveRequest request) {
        Set<Integer> regionIds = request.getRegionIds();
        Set<Long> staffIds = request.getStaffIds();
        if (regionIds.isEmpty()) {
            return BaseResponse.error("区域选项不能为空");
        }
        if (staffIds.isEmpty()) {
            return BaseResponse.error("设置员工选项不能为空");
        }
        Integer regionId = regionIds.iterator().next();
        String staffId = StringUtils.join(staffIds, ",");
        Long channelId = Optional.ofNullable(request.getChannelId()).orElse(0L);
        Integer bid = request.getBid();

        AsyncUtils.runAsync(() -> {
            RegionDto region = this.getRegion(bid, regionId);
            //获取已经存在的配置
            LeadsDistributeConf alreadyExist = isSelected(bid, regionId, channelId);
            //删除所有子节点包括自身
            this.clearChildrenNode(bid, region, channelId);

            select(bid, region, channelId, staffId, alreadyExist);
        });

        return BaseResponse.success(true);
    }

    private void select(Integer bid, RegionDto region, Long channelId, String staffId, LeadsDistributeConf alreadyExist) {
        Integer regionId = region.getId();
        Boolean isEnd = parentToChill(bid, region, regionId, staffId, channelId, null);
        if (Boolean.TRUE.equals(isEnd)) {
            return;
        }
        Boolean hasParentSelected = chillToParent(bid, region, staffId, channelId, alreadyExist, false);
        if (!hasParentSelected) {
            this.saveRegion(bid, regionId, staffId, channelId, alreadyExist);
        }
    }

    private Boolean parentToChill(Integer bid, RegionDto region, Integer regionId,
                                  String staffId, Long channelId, RegionDto chillRegion) {
        RegionDto parentRegion = this.getRegion(bid, region.getParentId());
        if (parentRegion != null) {
            Boolean isEnd = parentToChill(bid, parentRegion, regionId, staffId, channelId, region);
            if (isEnd) {
                return true;
            }
        }

        if (!region.getId().equals(regionId) && chillRegion != null) {
            LeadsDistributeConf selected = isSelected(bid, region.getId(), channelId);
            if (selected == null) {
                return false;
            }
            if (selected.getStaffIds().equals(staffId)) {
                return true;
            } else {
                this.clearChildrenNode(bid, region, channelId);
                List<LeadsDistributeConf> chillDistributeConf = region.getChildren().stream().map(chill -> {
                    if (!chill.getId().equals(regionId)) {
                        LeadsDistributeConf leadsDistributeConf = new LeadsDistributeConf();
                        leadsDistributeConf.setBid(bid);
                        leadsDistributeConf.setChannelId(channelId);
                        leadsDistributeConf.setRegionId(chill.getId());
                        leadsDistributeConf.setStaffIds(selected.getStaffIds());
                        leadsDistributeConf.setCreateTime(LocalDateTime.now());
                        return leadsDistributeConf;
                    }
                    return null;
                }).collect(Collectors.toList());
                chillDistributeConf.removeIf(Objects::isNull);
                this.leadsDistributeConfManager.saveBatch(chillDistributeConf);

                return false;
            }
        }

        return false;
    }

    private LeadsDistributeConf isSelected(Integer bid, Integer regionId, Long channelId) {
        return this.leadsDistributeConfManager.getOne(new QueryWrapper<LeadsDistributeConf>()
                .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                .eq(LeadsDistributeConf.BID, bid)
                .eq(LeadsDistributeConf.REGION_ID, regionId)
        );
    }

    private Boolean chillToParent(Integer bid, RegionDto region, String staffId
            , Long channelId, LeadsDistributeConf alreadyExist, Boolean hasParentSelected) {
        RegionDto parentRegion = this.getRegion(bid, region.getParentId());
        if (parentRegion == null || parentRegion.getLevel() == 0) {
            return false;
        }

        int count = this.leadsDistributeConfManager.count(new QueryWrapper<LeadsDistributeConf>()
                .eq(LeadsDistributeConf.BID, bid)
                .eq(LeadsDistributeConf.STAFF_IDS, staffId)
                .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                .in(LeadsDistributeConf.REGION_ID, parentRegion.getChildren().stream()
                        .map(RegionDto::getId)
                        .collect(Collectors.toSet()
                        )
                )
        );

        if (count != 0 && (parentRegion.getChildren().size() - 1 == count || parentRegion.getChildren().size() == count)) {
            this.clearChildrenNode(bid, parentRegion, channelId);

            this.saveRegion(bid, parentRegion.getId(), staffId, channelId, alreadyExist);

            hasParentSelected = true;
        }

        this.chillToParent(bid, parentRegion, staffId, channelId, alreadyExist, hasParentSelected);

        return hasParentSelected;
    }

    private void clearChildrenNode(Integer bid, RegionDto region, Long channelId) {
        if (CollectionUtils.isEmpty(region.getChildren())) {
            this.leadsDistributeConfManager.remove(new QueryWrapper<LeadsDistributeConf>()
                    .eq(LeadsDistributeConf.BID, bid)
                    .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                    .eq(LeadsDistributeConf.REGION_ID, region.getId()));
            return;
        }

        Set<Integer> allChillIds = region.getChildren().stream().map(RegionDto::getId).collect(Collectors.toSet());
        allChillIds.add(region.getId());
        region.getChildren().forEach(chill -> {
            fillAllChillIds(bid, chill, allChillIds);
        });

        this.leadsDistributeConfManager.remove(new QueryWrapper<LeadsDistributeConf>()
                .eq(LeadsDistributeConf.BID, bid)
                .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                .in(LeadsDistributeConf.REGION_ID, allChillIds));
    }

    private void fillAllChillIds(Integer bid, RegionDto region, Set<Integer> allChillIds) {
        if (region.getLevel() < 3) {
            region = this.getRegion(bid, region.getId());
            region.getChildren().forEach(chill -> {
                fillAllChillIds(bid, chill, allChillIds);
                allChillIds.add(chill.getId());
            });
        }
    }

    /**
     * 保存区域接收人配置
     *
     * @param bid          bid
     * @param regionId     区域ID
     * @param staffIds     员工
     * @param channelId    渠道
     * @param alreadyExist 已存在的配置
     */
    private void saveRegion(Integer bid, Integer regionId, String staffIds, Long channelId, LeadsDistributeConf alreadyExist) {
        LeadsDistributeConf leadsDistributeConf = new LeadsDistributeConf();
        leadsDistributeConf.setBid(bid);
        leadsDistributeConf.setRegionId(regionId);
        leadsDistributeConf.setStaffIds(staffIds);
        leadsDistributeConf.setChannelId(channelId);

        if (!ObjectUtils.isEmpty(alreadyExist)) {
            Long newStaffId = calculateTheNewIndex(sortStaffIds(staffIds), sortStaffIds(alreadyExist.getStaffIds())
                    , alreadyExist.getIndexes());
            leadsDistributeConf.setIndexes(newStaffId);
        }

        leadsDistributeConf.setCreateTime(LocalDateTime.now());

        this.leadsDistributeConfManager.save(leadsDistributeConf);
    }

    private Set<String> sortStaffIds(String ids) {
        ArrayList<String> staffIds = Lists.newArrayList(ids.split(InngkeAppConst.COMMA_STR));

        return Sets.newTreeSet(staffIds);
    }

    /**
     * 计算新的分配索引
     *
     * @param staffList    新的员工集合
     * @param oldStaffList 旧的员工集合
     * @param oldIndex     旧的分配索引
     * @return 新的分配索引
     */
    private Long calculateTheNewIndex(Set<String> staffList, Set<String> oldStaffList, Long oldIndex) {
        if (staffList.contains(String.valueOf(oldIndex))) {
            return oldIndex;
        }

        Set<String> beMixed = new HashSet<>();
        beMixed.addAll(staffList);
        beMixed.retainAll(oldStaffList);

        long newIndex = 0L;

        for (String staffId : beMixed) {
            if (Long.parseLong(staffId) >= oldIndex) {
                break;
            }
            newIndex = Long.parseLong(staffId);
        }

        return newIndex;
    }

    private RegionDto getRegion(Integer bid, Integer regionId) {
        RegionGetRequest regionGetRequest = new RegionGetRequest();
        regionGetRequest.setBid(bid);
        regionGetRequest.setId(regionId);
        regionGetRequest.setWithChildren(true);

        return regionService.getRegion(regionGetRequest).getData();
    }


    private RegionDto getRegion(Integer regionId) {
        if (regionId == 1) {
            return null;
        }
        //组装地区数据
        Map<Integer, RegionDto> provinceNumMap = regionCache.getProvinceNumMap();
        Map<Integer, RegionDto> cityNumMap = regionCache.getCityNumMap();
        Map<Integer, RegionDto> areaNumMap = regionCache.getAreaNumMap();

        boolean level1 = provinceNumMap.containsKey(regionId);

        boolean level2 = cityNumMap.containsKey(regionId);

        boolean level3 = areaNumMap.containsKey(regionId);

        if (level3) {
            return areaNumMap.get(regionId);
        }

        if (level2) {
            RegionDto regionDto = cityNumMap.get(regionId);
            regionDto.setChildren(
                    areaNumMap.values().stream().filter(v -> v.getParentId().equals(regionId)).collect(Collectors.toList())
            );
            return regionDto;
        }

        if (level1) {
            RegionDto regionDto = provinceNumMap.get(regionId);
            cityNumMap.values().stream().filter(v -> v.getParentId().equals(regionId)).collect(Collectors.toList()).stream().forEach(
                    city -> {
                        city.setChildren(areaNumMap.values().stream().filter(v -> v.getParentId().equals(city.getId())).collect(Collectors.toList()));
                    }
            );
            regionDto.setChildren(cityNumMap.values().stream().filter(v -> v.getParentId().equals(regionId)).collect(Collectors.toList()));
            return regionDto;
        }

        //默认返回顶节点
        RegionDto china = new RegionDto();
        china.setId(1);
        provinceNumMap.values().stream().filter(v -> v.getParentId().equals(1)).collect(Collectors.toList()).stream().forEach(
                province -> {
                    cityNumMap.values().forEach(
                            city -> {
                                city.setChildren(areaNumMap.values().stream().filter(v -> v.getParentId().equals(city.getId())).collect(Collectors.toList()));
                            }
                    );
                    province.setChildren(cityNumMap.values().stream().filter(v -> v.getParentId().equals(province.getId())).collect(Collectors.toList()));

                }
        );
        china.setChildren(provinceNumMap.values().stream().filter(v -> v.getParentId().equals(1)).collect(Collectors.toList()));
        china.setName("中国");
        china.setFullName("中华人民共和国");
        china.setLevel(0);
        china.setParentId(0);
        return china;
    }

    /**
     * 清除区域接收人
     *
     * @param request
     * @return
     */
    @Override
    public BaseResponse<Boolean> clear(RegionConfClearRequest request) {
        Integer regionId = request.getRegionId();
        Integer bid = request.getBid();

        RegionDto region = this.getRegion(regionId);
        if (region == null) {
            return BaseResponse.success(true);
        }
        if (CollectionUtils.isEmpty(region.getChildren())) {
            clearParentNode(bid, region, Optional.ofNullable(request.getChannelId()).orElse(0L));
        } else {
            clearChildrenNode(bid, region, Optional.ofNullable(request.getChannelId()).orElse(0L));
        }

        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<DistributeConfMapDto> getDistributeMap(GetDistributeMapRequest request) {
        List<LeadsDistributeConf> list = leadsDistributeConfManager.list(
                Wrappers.<LeadsDistributeConf>query()
                        .eq(LeadsDistributeConf.BID, request.getBid())
                        .in(LeadsDistributeConf.REGION_ID, request.getRegionIds())
                        .select(LeadsDistributeConf.REGION_ID, LeadsDistributeConf.STAFF_IDS)
        );
        Map<Integer, Long> distributeMap = Maps.newHashMap();

        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            distributeMap = list.stream().collect(Collectors.toMap(
                    LeadsDistributeConf::getRegionId, item -> Long.parseLong(item.getStaffIds().split(InngkeAppConst.COMMA_STR)[0]))
            );
        }

        DistributeConfMapDto distributeMapDto = new DistributeConfMapDto();
        distributeMapDto.setDistributeMap(distributeMap);

        return BaseResponse.success(distributeMapDto);
    }

    @Override
    public BaseResponse<List<String>> batchImport(LeadsBatchImportRequest request) {
        ExcelUtils<LeadsRegionExcelDto> list = new ExcelUtils<>(LeadsRegionExcelDto.class);
        List<LeadsRegionExcelDto> leadsDraftExcelDtoList;
        try {
            leadsDraftExcelDtoList = list.importExcelByUrl(request.getFileUrl(), 1);
        } catch (Exception e) {
            throw new InngkeServiceException("读取Excel失败", e);
        }

        List<String> errorList = Lists.newArrayList();
        Map<String, Long> staffMobileIdMap = getStaffMap(request.getBid(), leadsDraftExcelDtoList);
        Map<String, Integer> channelNameIdMap = getChannelMap(request.getBid());

        leadsDraftExcelDtoList.forEach(leadsRegionExcelDto -> {
            if (Objects.isNull(leadsRegionExcelDto)) {
                return;
            }

            List<String> reasonList = checkData(staffMobileIdMap, channelNameIdMap,leadsRegionExcelDto);
            if (!CollectionUtils.isEmpty(reasonList)) {
                String reason = StringUtils.join(reasonList, ",");
                errorList.add("导入员工" + leadsRegionExcelDto.getStaffName() + "失败,原因：" + reason);
                return;
            }

            //获取员工
            TreeSet<Long> staffIds = getStaffId(staffMobileIdMap, leadsRegionExcelDto.getStaffMobile());

            RegionConfSaveRequest regionConfSaveRequest = new RegionConfSaveRequest();
            regionConfSaveRequest.setBid(request.getBid());
            regionConfSaveRequest.setStaffIds(staffIds);

            //设置渠道
            if (!StringUtils.isEmpty(leadsRegionExcelDto.getChannelName())) {
                regionConfSaveRequest.setChannelId(channelNameIdMap.get(leadsRegionExcelDto.getChannelName()).longValue());
            }

            //设置区域
            Set<Integer> regionId = getRegionId(leadsRegionExcelDto);
            if (CollectionUtils.isEmpty(regionId)) {
                return;
            }
            regionConfSaveRequest.setRegionIds(regionId);

            save(regionConfSaveRequest);
        });

        return BaseResponse.success(errorList);
    }

    private TreeSet<Long> getStaffId(Map<String, Long> staffMobileIdMap, String mobile) {
        TreeSet<Long> staffIds = new TreeSet<>();
        List<String> staffMobiles = Arrays.asList(StringUtils.split(mobile, ","));
        staffMobiles.forEach(staffMobile -> {
            Long staffId = staffMobileIdMap.get(staffMobile);
            if (Objects.nonNull(staffIds)) {
                staffIds.add(staffId);
            }
        });
        return staffIds;
    }

    private Set<Integer> getRegionId(LeadsRegionExcelDto leadsRegionExcelDto) {
        if (!StringUtils.isEmpty(leadsRegionExcelDto.getArea())) {
            return Sets.newHashSet(regionCache.getAreaByParentNameAndAreaName(leadsRegionExcelDto.getCity(),leadsRegionExcelDto.getArea()).getId());
        }

        if (!StringUtils.isEmpty(leadsRegionExcelDto.getCity())) {
            return Sets.newHashSet(regionCache.getCityNameMap().get(leadsRegionExcelDto.getCity()).getId());
        }

        if (!StringUtils.isEmpty(leadsRegionExcelDto.getProvince())) {
            return Sets.newHashSet(regionCache.getProvinceNameMap().get(leadsRegionExcelDto.getProvince()).getId());
        }

        return Sets.newHashSet();
    }


    private List<String> checkData(Map<String, Long> staffMobileIdMap, Map<String, Integer> channelNameIdMap, LeadsRegionExcelDto leadsRegionExcelDto) {
        List<String> errorList = Lists.newArrayList();
        //根据手机查询staff
        List<String> staffMobiles = Arrays.asList(StringUtils.split(leadsRegionExcelDto.getStaffMobile(), ","));
        staffMobiles.forEach(staffMobile -> {
            if (Objects.isNull(staffMobileIdMap.get(staffMobile))) {
                errorList.add(buildReason(staffMobile));
            }
        });

        //根据渠道名查询id
        if (!StringUtils.isEmpty(leadsRegionExcelDto.getChannelName())
                && Objects.isNull(channelNameIdMap.get(leadsRegionExcelDto.getChannelName()))) {
            errorList.add(buildReason(leadsRegionExcelDto.getChannelName()));
        }
        //根据区名查询code
        if (!StringUtils.isEmpty(leadsRegionExcelDto.getProvince())
                &&!StringUtils.isEmpty(leadsRegionExcelDto.getCity())
                &&!StringUtils.isEmpty(leadsRegionExcelDto.getArea())
                && Objects.isNull(regionCache.getAreaByParentNameAndAreaName(leadsRegionExcelDto.getCity(),leadsRegionExcelDto.getArea()))) {
            errorList.add(buildReason(leadsRegionExcelDto.getArea()));
        }
        //根据市名查询code
        if (StringUtils.isEmpty(leadsRegionExcelDto.getArea())
                &&!StringUtils.isEmpty(leadsRegionExcelDto.getProvince())
                &&!StringUtils.isEmpty(leadsRegionExcelDto.getCity())
                && Objects.isNull(regionCache.getCityNameMap().get(leadsRegionExcelDto.getCity()))) {
            errorList.add(buildReason(leadsRegionExcelDto.getCity()));
        }

        //根据省名查询code
        if (StringUtils.isEmpty(leadsRegionExcelDto.getCity())
                &&!StringUtils.isEmpty(leadsRegionExcelDto.getProvince())
                && Objects.isNull(regionCache.getProvinceNameMap().get(leadsRegionExcelDto.getProvince()))) {
            errorList.add(buildReason(leadsRegionExcelDto.getProvince()));
        }
        return errorList;
    }

    private Map<String, Long> getStaffMap(Integer bid, List<LeadsRegionExcelDto> leadsDraftExcelDtoList) {
        return staffClientForLeads.getStaffByMobileList(bid, getMobiles(leadsDraftExcelDtoList))
                .stream().collect(Collectors.toMap(UserStaffDto::getMobile, UserStaffDto::getId));
    }

    private Map<String, Integer> getChannelMap(Integer bid) {
        return channelClientForLeads.getAllChannel(bid)
                .stream().collect(Collectors.toMap(ChannelDto::getName, ChannelDto::getId, (i1, i2) -> i1));
    }

    private String buildReason(String reason) {
        return reason + "无法匹对";
    }

    private List<String> getMobiles(List<LeadsRegionExcelDto> leadsDraftExcelDtoList) {
        return leadsDraftExcelDtoList.stream().map(LeadsRegionExcelDto::getStaffMobile).flatMap(v -> Arrays.stream(v.split(","))).collect(Collectors.toList());
    }

    private Boolean clearParentNode(Integer bid, RegionDto region, Long channelId) {
        if (region.getParentId() != null) {
            RegionDto parent = getRegion(region.getParentId());
            if (!Objects.isNull(parent)) {
                Boolean parentChecked = clearParentNode(bid, parent, channelId);
                if (parentChecked) {
                    if (region.getChildren() != null) {
                        LeadsDistributeConf conf = leadsDistributeConfManager.getOne(new QueryWrapper<LeadsDistributeConf>()
                                .eq(LeadsDistributeConf.BID, bid)
                                .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                                .eq(LeadsDistributeConf.REGION_ID, region.getId()));
                        saveChildren(region, conf, channelId);
                    }
                    leadsDistributeConfManager.remove(new QueryWrapper<LeadsDistributeConf>()
                            .eq(LeadsDistributeConf.BID, bid)
                            .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                            .eq(LeadsDistributeConf.REGION_ID, region.getId()));

                    return true;
                }
            }
        }

        LeadsDistributeConf conf = leadsDistributeConfManager.getOne(new QueryWrapper<LeadsDistributeConf>()
                .eq(LeadsDistributeConf.BID, bid)
                .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                .eq(LeadsDistributeConf.REGION_ID, region.getId()));

        //将所有子节点设置为当前节点的员工
        if (!Objects.isNull(conf)) {
            saveChildren(region, conf, channelId);
            leadsDistributeConfManager.remove(new QueryWrapper<LeadsDistributeConf>()
                    .eq(LeadsDistributeConf.BID, bid)
                    .eq(LeadsDistributeConf.CHANNEL_ID, channelId)
                    .eq(LeadsDistributeConf.REGION_ID, region.getId()));
            return true;
        }

        return false;
    }

    private void saveChildren(RegionDto region, LeadsDistributeConf conf, Long channelId) {
        List<LeadsDistributeConf> collect = null;
        if (!CollectionUtils.isEmpty(region.getChildren())) {
            collect = region.getChildren().stream().map(children -> {
                LeadsDistributeConf chillConf = new LeadsDistributeConf();
                chillConf.setBid(conf.getBid());
                chillConf.setStaffIds(conf.getStaffIds());
                chillConf.setRegionId(children.getId());
                chillConf.setChannelId(channelId);
                chillConf.setCreateTime(LocalDateTime.now());
                return chillConf;
            }).collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(collect)) {
            leadsDistributeConfManager.saveBatch(collect);
        }
    }

    private Boolean staffIdsIsEqually(String staffIds, String newStaffIds) {
        ArrayList<String> newStaffIdsList = Lists.newArrayList(newStaffIds.split(","));
        ArrayList<String> staffIdsList = Lists.newArrayList(staffIds.split(","));
        Collections.sort(newStaffIdsList);
        Collections.sort(staffIdsList);
        return newStaffIdsList.equals(staffIdsList);
    }
}

package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/9/20
 **/
public class LeadsOrganizationPreFollowDto implements Serializable {
    /**
     * 员工主键
     */
    private Long staffId;
    /**
     * 员工姓名
     */
    private String staffName;
    /**
     * 员工手机号码
     */
    private String mobile;

    /**
     * 部门名称
     */
    private String departmentName = "";

    /**
     * 总客户数
     */
    private Long customerCount = 0L;
    /**
     * 已跟进数
     */
    private Long hasFollowNum = 0L;
    /**
     * 待跟进数
     */
    private Long hasNotFollowNum = 0L;
    /**
     * 下发数
     */
    private Long hasDistributeNum = 0L;
    /**
     * 待下发数 总客户数—下发数
     */
    private Long waitDistributeNum = 0L;
    /**
     * 下发率 下发数/总客户数
     */
    private String distributeRate = "0%";


    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getCustomerCount() {
        return customerCount;
    }

    public void setCustomerCount(Long customerCount) {
        this.customerCount = customerCount;
    }

    public Long getHasFollowNum() {
        return hasFollowNum;
    }

    public void setHasFollowNum(Long hasFollowNum) {
        this.hasFollowNum = hasFollowNum;
    }

    public Long getHasNotFollowNum() {
        return hasNotFollowNum;
    }

    public void setHasNotFollowNum(Long hasNotFollowNum) {
        this.hasNotFollowNum = hasNotFollowNum;
    }

    public Long getHasDistributeNum() {
        return hasDistributeNum;
    }

    public void setHasDistributeNum(Long hasDistributeNum) {
        this.hasDistributeNum = hasDistributeNum;
    }

    public Long getWaitDistributeNum() {
        return waitDistributeNum;
    }

    public void setWaitDistributeNum(Long waitDistributeNum) {
        this.waitDistributeNum = waitDistributeNum;
    }

    public String getDistributeRate() {
        return distributeRate;
    }

    public void setDistributeRate(String distributeRate) {
        this.distributeRate = distributeRate;
    }
}

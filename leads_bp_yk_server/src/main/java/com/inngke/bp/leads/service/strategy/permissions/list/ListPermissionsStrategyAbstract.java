package com.inngke.bp.leads.service.strategy.permissions.list;

import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;
import java.util.Set;

@Component
public abstract class ListPermissionsStrategyAbstract implements ListPermissionsStrategy {

    /**
     * 客资清理客服
     */
    private static final String CUSTOMER_ROLE_CODE = "customer";

    /**
     * 企业管理员
     */
    private static final String MERCHANT_MANAGER = "merchant_manager";

    private static final String CUSTOMER_MANAGER = "customer_manager";

    @Autowired
    protected StaffClientForLeads staffClientForLeads;

    @Autowired
    protected DepartmentClientForLeads departmentClientForLeads;

    /**
     *
     * @param request       查询参数
     * @param queryBuilder  Es查询条件
     * @param userRoleCodes 用户角色code码
     */
    @Override
    public void handle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder, Set<String> userRoleCodes) {
        //判断是否拥有企业管理员角色
        if (isManager(userRoleCodes)) {
            //企业管理员角色权限
            merchantManagePermissionsHandle(request, queryBuilder);
        } else if(isCustomerRole(userRoleCodes)){
            //客资清洗角色权限
            customerPermissionsHandle(request, queryBuilder);
        } else if (isCustomerManager(userRoleCodes)) {
            customerManagePermissionsHandle(request, queryBuilder);
        }else {
            //其它角色权限
            otherPermissionsHandle(request, queryBuilder);
        }
    }

    private boolean isCustomerManager(Set<String> userRoleCodes) {
        return userRoleCodes.contains(CUSTOMER_MANAGER);
    }

    private boolean isManager(Set<String> userRoleCodes) {
        return userRoleCodes.contains(MERCHANT_MANAGER);
    }

    /**
     * 获取员工管理部门
     */
    protected Set<Long> getStaffManageDepartmentIds(Integer bid, Long sid) {
        //获取员工管理部门
        return staffClientForLeads.getStaffManageDepartmentIds(bid, sid);
    }

    protected Boolean isCustomerRole(Set<String> userRoles) {
        return userRoles.contains(CUSTOMER_ROLE_CODE);
    }

    /**
     * 其它角色 自己创建的和自己所管理部门的员工创建的 (默认规则)
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    protected void defaultOtherPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        BoolQueryBuilder sonQueryBuilder = getDefaultPermissionsBuilder(request, queryBuilder);

        queryBuilder.must(sonQueryBuilder);
    }

    /**
     * 默认的管理员数据权限
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    protected void defaultMerchantManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder){
        BoolQueryBuilder sonQueryBuilder = getDefaultPermissionsBuilder(request, queryBuilder);
        //系统对接的
        sonQueryBuilder.should(QueryBuilders.termQuery(LeadsEsDto.CREATE_DEPARTMENT_ID,0L))
                        .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID,0L));

        queryBuilder.must(sonQueryBuilder);
    }

    protected void defaultCustomerManagerPermissionHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        Set<Long> staffManageDepartmentIds = getStaffManageDepartmentIds(request.getBid(), request.getSid());

        StaffDto staffById = staffClientForLeads.getStaffById(request.getBid(), request.getSid());
        if (Objects.nonNull(staffById)) {
            // 自己所在部门
            staffManageDepartmentIds.add(staffById.getDepartmentId());
        }
        BoolQueryBuilder sonQueryBuilder = QueryBuilders.boolQuery();
        //系统对接的
        sonQueryBuilder.should(QueryBuilders.termQuery(LeadsEsDto.CREATE_DEPARTMENT_ID,0L))
                .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID,0L));
        //自己所管理部门的员工创建的
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            sonQueryBuilder.should(QueryBuilders.termsQuery(LeadsEsDto.CREATE_DEPARTMENT_ID, staffManageDepartmentIds));
        }
        queryBuilder.must(sonQueryBuilder);
    }

    /**
     * 默认数据权限 可以看到自己创建的,自己所管理部门的员工创建的;
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     * @return BoolQueryBuilder
     */
    protected BoolQueryBuilder getDefaultPermissionsBuilder(SearchLeadsRequest request, BoolQueryBuilder queryBuilder){
        Set<Long> staffManageDepartmentIds = getStaffManageDepartmentIds(request.getBid(), request.getSid());

        //自己创建的
        BoolQueryBuilder sonQueryBuilder = QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getSid()));

        //自己所管理部门的员工创建的
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            sonQueryBuilder.should(QueryBuilders.termsQuery(LeadsEsDto.CREATE_DEPARTMENT_ID, staffManageDepartmentIds));
        }

        return sonQueryBuilder;
    }


    /**
     * 客服: 可以看到自己创建的线索数据和自己跟进的线索
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    protected void customerPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        queryBuilder.must(QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getSid()))
                .should(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, request.getSid()))
        );
    }

    /**
     * 其他角色
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    protected abstract void otherPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder);

    /**
     * 企业管理员
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    protected abstract void merchantManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder);

    protected void customerManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        // 使用企业管理员的默认权限，需要特殊权限在子类中重写该方法
        Set<Long> staffManageDepartmentIds = getStaffManageDepartmentIds(request.getBid(), request.getSid());

        StaffDto staffById = staffClientForLeads.getStaffById(request.getBid(), request.getSid());
        if (Objects.nonNull(staffById)) {
            // 自己所在部门
            staffManageDepartmentIds.add(staffById.getDepartmentId());
        }
        BoolQueryBuilder sonQueryBuilder = QueryBuilders.boolQuery();
        //自己所管理部门的员工创建的
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            sonQueryBuilder.should(QueryBuilders.termsQuery(LeadsEsDto.CREATE_DEPARTMENT_ID, staffManageDepartmentIds));
        }
        queryBuilder.must(sonQueryBuilder);
    }
}

package com.inngke.bp.leads.notify.context;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.ApplicationContextGetBeanHelper;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.StaffLeadsCountDto;
import com.inngke.bp.leads.notify.builder.LeadsDistributeContentBuilder;
import com.inngke.bp.leads.service.enums.LeadsNotifyMessageEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.context.NotifyMessageContext;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * LeadsDistributeContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/16 17:57
 */
public class LeadsDistributeMessageContext implements NotifyMessageContext {
    private int bid;

    private final LeadsNotifyMessageEnum notifyEnum = LeadsNotifyMessageEnum.DISTRIBUTE_LEADS;


    private Long targetStaffId;

    private String qyWxUserId;

    private String wxPubOpenId;
    private String name;

    private String mobile;

    private String formatTime;

    private Map<String, String> pathParams;
    private String remark;

    private StaffLeadsCountDto staffLeadsCount;

    private Integer leadsType;

    @Override
    public int getBid() {
        return this.bid;
    }

    @Override
    public String getMsgTypeCode() {
        return notifyEnum.getTemplateCode();
    }

    @Override
    public String getPath() {
        return notifyEnum.getPath();
    }

    @Override
    public Long getTargetStaffId() {
        return this.targetStaffId;
    }

    @Override
    public Map<String, String> getPathParams() {
        return pathParams;
    }

    @Override
    public String getQyUserId() {
        return this.qyWxUserId;
    }

    @Override
    public String getWxPubOpenId() {
        return this.wxPubOpenId;
    }

    @Override
    public TemplateMessageContentBuilder<? extends NotifyMessageContext> getMessageBuilder() {
        return ApplicationContextGetBeanHelper.getBean(LeadsDistributeContentBuilder.class);
    }

    public StaffLeadsCountDto getStaffLeadsCount() {
        return staffLeadsCount;
    }

    public void setBid(int bid) {
        this.bid = bid;
    }

    public LeadsNotifyMessageEnum getNotifyEnum() {
        return notifyEnum;
    }

    public String getName() {
        if (StringUtils.isBlank(qyWxUserId)) {
            return "有" + staffLeadsCount.getCount() + "条新的线索分配给你";
        } else {
            return staffLeadsCount.getMessageName();
        }
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        if (StringUtils.isNotBlank(qyWxUserId)) {
            return staffLeadsCount.getMobile();
        } else {
            String[] split = staffLeadsCount.getMobile().split(",");
            return split[split.length - 1];
        }
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFormatTime() {
        return formatTime;
    }

    public void setFormatTime(String formatTime) {
        this.formatTime = formatTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getLeadsType() {
        return leadsType;
    }

    public static LeadsDistributeMessageContext init(int bid, long leadsId, StaffLeadsCountDto leadsCount, Integer leadsType) {
        LeadsManager leadsManager = ApplicationContextGetBeanHelper.getBean(LeadsManager.class);
        Leads leads = leadsManager.getById(bid, leadsId);

        LeadsDistributeMessageContext context = new LeadsDistributeMessageContext();
        if (Objects.nonNull(leads)) {
            context.bid = bid;
            context.name = leads.getName();
            context.mobile = leads.getMobile();
            context.formatTime = DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
            context.targetStaffId = leads.getDistributeStaffId();
            context.staffLeadsCount = leadsCount;
        }
        context.leadsType = leadsType;
        StaffClientForLeads staffClient = ApplicationContextGetBeanHelper.getBean(StaffClientForLeads.class);
        StaffDto staff = staffClient.getStaffById(bid, context.targetStaffId);
        if (Objects.nonNull(staff)) {
            if (StringUtils.isNotBlank(staff.getQyUserId())) {
                context.qyWxUserId = staff.getQyUserId();
            }

            CustomerGetServiceClientForLeads customerClient = ApplicationContextGetBeanHelper.getBean(CustomerGetServiceClientForLeads.class);
            CustomerDto customer = customerClient.getCustomerById(bid, staff.getCustomerId());
            if (Objects.nonNull(customer)) {
                context.wxPubOpenId = customer.getWxPubOpenId();
            }
        }
        context.pathParams = buildPathParams(context, leads, leadsCount);
        return context;
    }

    public static LeadsDistributeMessageContext init(int bid, long leadsId, StaffLeadsCountDto leadsCount) {
        return init(bid, leadsId, leadsCount, null);
    }

    private static Map<String, String> buildPathParams(LeadsDistributeMessageContext context, Leads leads, StaffLeadsCountDto leadsCount) {
//        Map<String, String> params = Maps.newHashMap();
//        if (Objects.isNull(leads)) {
//            return params;
//        }
//        params.put("staffId", String.valueOf(leads.getDistributeStaffId()));
//        params.put("mes_type", StringUtils.isNotBlank(context.getQyUserId()) ? "qy_wx" : "wx_pub");
//        params.put("id", String.valueOf(leads.getId()));

        int size = leadsCount.getCount();

        Map<String, String> params = Maps.newHashMap();
        params.put("umaEvent", "clueRemind");
        params.put("staffId", String.valueOf(context.getTargetStaffId()));

        if (size == 1) {
            params.put("leadsId", leads.getId().toString());
            params.put("id", leads.getId().toString());
        } else {
            params.put("secondStatus", "1");
        }

        return params;
    }
}

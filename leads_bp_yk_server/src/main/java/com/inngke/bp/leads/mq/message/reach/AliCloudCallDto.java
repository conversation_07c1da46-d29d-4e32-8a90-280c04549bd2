package com.inngke.bp.leads.mq.message.reach;
import com.inngke.common.mq.dto.BaseMessage;

import java.io.Serializable;

/**
 * 阿里云云呼叫:线索更新Dto
 */
public class AliCloudCallDto extends BaseMessage {

    /**
     * 商户id
     */
    private Integer bid;

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 呼叫类型   0:呼入    1:呼出   (2:双呼,3:加密通话,4:内部呼叫,5:预测式外呼,6:会议)
     */
    private Integer callType;

    /**
     * 未接通原因
     */
    private String reason;

    /**
     * 是否接通(0:未接通 1:接通)
     */
    private Integer connected;


    /**
     *  跟进时间(毫秒)
     */
    private Long followTime;

    /**
     * 通话时间 (秒)
     */
    private Long callDuration;

    /**
     * 员工Id
     */
    private Long staffId;

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getFollowTime() {
        return followTime;
    }

    public void setFollowTime(Long followTime) {
        this.followTime = followTime;
    }

    public Long getCallDuration() {
        return callDuration;
    }

    public void setCallDuration(Long callDuration) {
        this.callDuration = callDuration;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Integer getCallType() {
        return callType;
    }

    public void setCallType(Integer callType) {
        this.callType = callType;
    }


    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getConnected() {
        return connected;
    }

    public void setConnected(Integer connected) {
        this.connected = connected;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }
}

package com.inngke.bp.leads.mq.process;

import com.inngke.bp.leads.mq.message.customer.CusromerChangeMessageDto;

/**
 * <AUTHOR>
 * @since 2021/9/17 14:33
 */
public interface CustomerProcess {

    /**
     * 是否需要处理
     *
     * @param message 消息体
     * @return 是否需要继续处理
     */
    boolean match(CusromerChangeMessageDto message);

    /**
     * 处理逻辑
     * @param message 消息体
     */
    void process(CusromerChangeMessageDto message);
}

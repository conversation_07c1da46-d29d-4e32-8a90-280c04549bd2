package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.LeadsConfSaveRequest;
import com.inngke.bp.leads.dto.request.MultiSetLeadsTpConfRequest;
import com.inngke.bp.leads.dto.request.SetLeadsTpConfRequest;
import com.inngke.bp.leads.dto.response.LeadsConfDto;
import com.inngke.bp.leads.dto.response.tp.TpConfigDto;
import com.inngke.bp.leads.service.LeadsConfService;
import com.inngke.bp.leads.service.tp.LeadsTpConfigService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索配置
 * @since 2021/9/7 3:49 PM
 */
@RestController
@RequestMapping("/api/leads/conf")
public class LeadsConfApiController {
    @Autowired
    private LeadsConfService leadsConfService;

    @Autowired
    private LeadsTpConfigService leadsTpConfigService;

    /**
     * 开启线索功能
     *
     * @param id         需要开启的商户ID
     * @param bid        操作者归属商户ID
     * @param operatorId 操作者ID
     * @return 是否开启成功
     */
    @PostMapping("/{id:\\d+}")
    public BaseResponse<Boolean> enableLeads(
            @PathVariable int id,
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setOperatorId(operatorId);
        request.setBid(id);
        return leadsConfService.enableLeads(request);
    }

    /**
     * 关闭线索功能
     *
     * @param id         需要开启的商户ID
     * @param bid        操作者归属商户ID
     * @param operatorId 操作者ID
     * @return 是否关闭成功
     */
    @DeleteMapping("/{id:\\d+}")
    public BaseResponse<Boolean> disableLeads(
            @PathVariable int id,
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setOperatorId(operatorId);
        request.setBid(id);
        return leadsConfService.disableLeads(request);
    }

    /**
     * 设置线索配置
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    配置请求
     * @return 保存成功后的线索配置
     */
    @PostMapping("/setting")
    public BaseResponse<LeadsConfDto> setLeadsConf(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated LeadsConfSaveRequest request
    ) {
        request.setOperatorId(operatorId);
        request.setBid(request.getId());
        return leadsConfService.setLeadsConf(request);
    }

    /**
     * 获取线索配置
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return 线索配置
     */
    @GetMapping("/{id:\\d+}")
    public BaseResponse<LeadsConfDto> getLeadsConf(
            @PathVariable int id,
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setOperatorId(operatorId);
        request.setBid(id);
        return leadsConfService.getLeadsConf(request);
    }

    /**
     * 获取线索第三方平台配置
     *
     * @param bid
     * @return
     */
    @GetMapping("/tp")
    public BaseResponse<TpConfigDto> getTpConfig(
            @RequestHeader(InngkeApiConst.STR_BID) int bid
    ) {
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        return leadsTpConfigService.getTpConfig(request);
    }

    /**
     * 保存线索第三方平台配置
     *
     * @param bid
     * @return
     */
    @PostMapping("/tp")
    public BaseResponse<TpConfigDto> setTpConfig(
            @RequestHeader(InngkeApiConst.STR_BID) int bid,
            @RequestBody SetLeadsTpConfRequest request) {
        request.setBid(bid);

        return leadsTpConfigService.setTpConfig(request);
    }

    /**
     * 保存多个线索第三方平台配置
     *
     * @param bid
     * @return
     */
    @PostMapping("/tp/multi")
    public BaseResponse<TpConfigDto> multiSetTpConfig(
            @RequestHeader(InngkeApiConst.STR_BID) int bid,
            @RequestBody MultiSetLeadsTpConfRequest request) {
        request.setBid(bid);

        return leadsTpConfigService.multiSetTpConfig(request);
    }
}

package com.inngke.bp.leads.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/19 17:28
 */
public class RewardEventDto implements Serializable {
    /**
     * 商户号id
     */
    private Integer bid;

    /**
     * 奖励事件类型  1 成为合伙人；2 报备客户；3 报备有效；4 客户见面；5 客户下定；6 客户成交
     */
    private Integer event;

    /**
     * 合伙人id
     */
    private Long distributorId;

    /**
     * 合伙人导购Id
     */
    private Long guideId;

    /**
     * 线索id,报备客户时生成的客户线索
     */
    private Long leadsId;

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }

    public Long getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(Long distributorId) {
        this.distributorId = distributorId;
    }

    public Long getGuideId() {
        return guideId;
    }

    public void setGuideId(Long guideId) {
        this.guideId = guideId;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }
}

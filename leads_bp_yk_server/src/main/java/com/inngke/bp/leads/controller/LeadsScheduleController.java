package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.schedule.LeadsSchedule;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2023-07-25 14:35
 **/
@RestController
@RequestMapping("/api/leads-schedule")
@Slf4j
public class LeadsScheduleController {

    private static final Logger logger = LoggerFactory.getLogger(LeadsScheduleController.class);

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsSchedule leadsSchedule;


    @GetMapping("/leadsDailyNotify")
    public BaseResponse<Boolean> list(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload
    ) {
//        if (EnvUtils.getEnv() == EnvEnum.PROD) {
//            return BaseResponse.error("生产环境不允许调用");
//        }
        logger.info("请求每日通知:{}", jsonService.toJson(jwtPayload));
        leadsSchedule.leadsDailyNotify();
        return BaseResponse.success(true);
    }




}

package com.inngke.bp.leads.dto;

import com.inngke.bp.store.dto.response.StoreOrderDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/8
 **/
public class leadsMobileAndIdStoreOrderMapDto {
    /**
     * 手机号对于的订单
     */
    Map<String, List<StoreOrderDto>> mobileStoreOrder;

    /**
     * 线索Id对应的订单
     */
    Map<Long, List<StoreOrderDto>> LeadsIdStoreOrder;

    public Map<String, List<StoreOrderDto>> getMobileStoreOrder() {
        return mobileStoreOrder;
    }

    public void setMobileStoreOrder(Map<String, List<StoreOrderDto>> mobileStoreOrder) {
        this.mobileStoreOrder = mobileStoreOrder;
    }

    public Map<Long, List<StoreOrderDto>> getLeadsIdStoreOrder() {
        return LeadsIdStoreOrder;
    }

    public void setLeadsIdStoreOrder(Map<Long, List<StoreOrderDto>> leadsIdStoreOrder) {
        LeadsIdStoreOrder = leadsIdStoreOrder;
    }
}

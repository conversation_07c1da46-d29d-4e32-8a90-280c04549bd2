package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeOrderSuccessStrategy")
public class LeadsFollowTimeOrderSuccessStrategy implements LeadsFollowTimeStrategy {
    @Autowired
    private LeadsManager leadsManager;

    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateOrderSuccess(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateOrderSuccess",
                        //QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("orderAmount").gt(0))
                        QueryBuilders.boolQuery().should(QueryBuilders.rangeQuery("orderAmount").gt(0))
                                .should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.TRADED.getStatus()))
                                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.TRADED.getStatus()))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateOrderSuccess = bucketAggregations.get("stateOrderSuccess");
        long count = stateOrderSuccess.getDocCount();
        dto.setStateOrderSuccess((int)count);
        if(dto.getLeadsCount()==0){
            return;
        }

        // 获取有效客户数
        ParsedFilter contactFilter = bucketAggregations.get("validCount");
        long contactCount = contactFilter.getDocCount();
        if (contactCount > 0L) {
            String rate = BigDecimal.valueOf(count).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(contactCount),2, RoundingMode.HALF_DOWN).toString();
            dto.setOrderSuccessRate(rate+"%");
        }
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateOrderSuccess).reduce(Integer::sum).get();
        dto.setStateOrderSuccess(sum);
        if(dto.getStateAvail() == 0){
            return;
        }
        String rate = BigDecimal.valueOf(sum).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getStateAvail()),2, RoundingMode.HALF_UP).toString();
        dto.setOrderSuccessRate(rate+"%");
    }
}

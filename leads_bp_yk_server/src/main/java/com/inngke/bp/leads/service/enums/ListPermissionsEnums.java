package com.inngke.bp.leads.service.enums;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.strategy.permissions.list.*;

import java.util.List;
import java.util.Set;

public enum ListPermissionsEnums {
    /**
     * 分配失败
     */
    ALLOCATION_FAILED(LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(), null, ListAllocationFailedPermissionsStrategy.class),

    /**
     * 已分配
     */
    ASSIGNED(LeadsStatusEnum.getAllocatedLeadsStatus(), 5, ListAssignedPermissionsStrategy.class),

    /**
     * 客服接待
     */
    CUSTOMER(LeadsStatusEnum.PRE_FOLLOW.getStatus(), null, ListCustomerPermissionsStrategy.class),

    /**
     * 员工退回
     */
    PUSHBACK(LeadsStatusEnum.PUSH_BACK.getStatus(), null, ListPushBackPermissionsStrategy.class),

    /**
     * 未分配
     */
    UNASSIGNED(LeadsStatusEnum.TO_DISTRIBUTE.getStatus(), 4, ListUnassignedPermissionsStrategy.class),

    /**
     * 除开 -4,-5的线索状态
     */
    ALL(-99, 10, ListUnassignedPermissionsStrategy.class);


    private final List<Integer> status;

    private final Integer statusGroup;

    private final Class<?> strategyClazz;

    ListPermissionsEnums(Integer status, Integer statusGroup, Class<?> strategyClazz) {
        this.status = Lists.newArrayList(status);
        this.statusGroup = statusGroup;
        this.strategyClazz = strategyClazz;
    }

    ListPermissionsEnums(List<Integer> status, Integer statusGroup, Class<?> strategyClazz) {
        this.status = status;
        this.statusGroup = statusGroup;
        this.strategyClazz = strategyClazz;
    }


    public List<Integer> getStatus() {
        return status;
    }

    public Integer getStatusGroup() {
        return statusGroup;
    }

    public Class<?> getStrategyClazz() {
        return strategyClazz;
    }

    public static ListPermissionsEnums parse(Set<Integer> status, Integer statusGroup){
        for (ListPermissionsEnums listPermissionsEnums : ListPermissionsEnums.values()) {
            if (status == null || status.size() == 0){
                if ((statusGroup != null && statusGroup.equals(listPermissionsEnums.getStatusGroup()))){
                    return listPermissionsEnums;
                }
            }
            for (Integer s : status) {
                if (listPermissionsEnums.status.contains(s) ||
                        (statusGroup != null && statusGroup.equals(listPermissionsEnums.getStatusGroup()))){
                    return listPermissionsEnums;
                }
            }
        }

        return null;
    }
}

package com.inngke.bp.leads.service.impl;

import cn.hutool.core.lang.UUID;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.MerchantConfigClientForLeads;
import com.inngke.bp.leads.dto.platform.CustomerServiceAdInfoDto;
import com.inngke.bp.leads.dto.platform.CustomerServiceLeadsDto;
import com.inngke.bp.leads.dto.platform.CustomerServiceTagDto;
import com.inngke.bp.leads.dto.request.tp.FeiYuLeadsPushDto;
import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.bp.leads.dto.response.LeadsInformationDto;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import com.inngke.bp.leads.service.LeadsTpDtoTransformService;
import com.inngke.bp.leads.service.LeadsTpEnumParseServiceImpl;
import com.inngke.bp.leads.service.schedule.LeadsRegionCache;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.response.RegionDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线索数据转换
 *
 * <AUTHOR>
 * @date 2022/2/26 15:51
 */
@Service
public class LeadsTpDtoTransformServiceImpl implements LeadsTpDtoTransformService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpDtoTransformServiceImpl.class);

    @Autowired
    private LeadsTpEnumParseServiceImpl leadsTpEnumParseService;

    @Resource
    private MerchantConfigClientForLeads merchantConfigClientForLeads;

    @Autowired
    private LeadsRegionCache leadsRegionCache;

    @Autowired
    private JsonService jsonService;

    private final static Map<String, Integer> GENDER_NAME_MAP = new HashMap<String, Integer>() {{
        put("男", 1);
        put("女", 1);
    }};

    /**
     * 腾讯广告 线索转换
     *
     * @param tencentLeadsPushDto 腾讯广告推送dto
     * @return 标准信息线索dto
     */
    @Override
    public LeadsInformationDto transform(TencentLeadsPushDto tencentLeadsPushDto) {
        LeadsInformationDto leadsInformationDto = new LeadsInformationDto();
        leadsInformationDto.setExternalId(tencentLeadsPushDto.getLeadsId().toString());
        leadsInformationDto.setName(tencentLeadsPushDto.getLeadsName());
        leadsInformationDto.setMobile(tencentLeadsPushDto.getLeadsTel());
        leadsInformationDto.setWeChat(tencentLeadsPushDto.getLeadsWechat());
        leadsInformationDto.setAddress(Optional.ofNullable(tencentLeadsPushDto.getAddress()).orElse("未知"));
        leadsInformationDto.setAge(tencentLeadsPushDto.getAge());
        leadsInformationDto.setQq(tencentLeadsPushDto.getLeadsQq());
        leadsInformationDto.setEmail(tencentLeadsPushDto.getLeadsEmail());
        leadsInformationDto.setCampaignId(tencentLeadsPushDto.getCampaignId());
        leadsInformationDto.setCampaignName(tencentLeadsPushDto.getCampaignName());
        leadsInformationDto.setAccountId(tencentLeadsPushDto.getAccountId().toString());
        //性别
        leadsInformationDto.setGender(leadsTpEnumParseService
                .parseTencentGenderEnum(tencentLeadsPushDto.getLeadsGender()));
        //渠道来源
        leadsInformationDto.setChannel(LeadsChannelEnum.TENCENT_AD.getChannel());
        //线索数据来源
        leadsInformationDto.setChannelType(LeadsDataSourceEnum.TENCENT_AD.getCode());
        //线索填写来源
        LeadsInputSourceEnum leadsInputSourceEnum = leadsTpEnumParseService.paresTencentInputChannelEnum(tencentLeadsPushDto.getLeadsType());
        if (!ObjectUtils.isEmpty(leadsInputSourceEnum)) {
            leadsInformationDto.setChannelSource(leadsInputSourceEnum.getCode());
        }

        // 通过配置设置线索创建人
        if (1 == tencentLeadsPushDto.getAccess()) {
            leadsInformationDto.setCreateStaffId(tencentLeadsPushDto.getCreateStaffId());
        } else if (2 == tencentLeadsPushDto.getAccess()) {
            Long operatorStaffId = merchantConfigClientForLeads.getTencentLeadsOperatorStaffId(tencentLeadsPushDto.getBid());
            leadsInformationDto.setOperatorStaffId(operatorStaffId);
        }

        leadsInformationDto.setCreateTime(DateTimeUtils.getMilli(
                DateTimeUtils.toLocalDateTime(tencentLeadsPushDto.getLeadsActionTime())));
        leadsInformationDto.setSubmitTime(DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(tencentLeadsPushDto.getLeadsActionTime())));

        if (!StringUtils.isEmpty(tencentLeadsPushDto.getLeadsArea())){
            tencentLeadsPushDto.setAddress(tencentLeadsPushDto.getLeadsArea());
            return parseLeadsRegion(leadsInformationDto,tencentLeadsPushDto.getLeadsArea());
        }
        if (!StringUtils.isEmpty(tencentLeadsPushDto.getTelLocation())){
            tencentLeadsPushDto.setAddress(tencentLeadsPushDto.getTelLocation());
            ArrayList<String> locationArr = Lists.newArrayList(tencentLeadsPushDto.getTelLocation().split("-"));
            return parseLeadsRegion(leadsInformationDto,
                    locationArr.size() >= 1 ? locationArr.get(0) : null,
                    locationArr.size() >= 2 ? locationArr.get(1) : null,
                    locationArr.size() >= 3 ? locationArr.get(2) : null);
        }
        return parseLeadsRegion(leadsInformationDto,"" ,"","");
    }

    /**
     * 飞鱼 线索转换
     *
     * @param feiYuLeadsPushDto 飞鱼推送dto
     * @return 标准信息线索dto
     */
    @Override
    public LeadsInformationDto transform(FeiYuLeadsPushDto feiYuLeadsPushDto) {
        LeadsInformationDto leadsInformationDto = new LeadsInformationDto();
        leadsInformationDto.setExternalId(feiYuLeadsPushDto.getId());
        leadsInformationDto.setTpLeadsId(feiYuLeadsPushDto.getId());
        leadsInformationDto.setName(feiYuLeadsPushDto.getName());
        leadsInformationDto.setWeChat(feiYuLeadsPushDto.getWeiXin());
        leadsInformationDto.setAddress(feiYuLeadsPushDto.getAddress());
        leadsInformationDto.setGender(parseFlyFishGender(feiYuLeadsPushDto.getGender()));
        leadsInformationDto.setAge(feiYuLeadsPushDto.getAge());
        leadsInformationDto.setQq(feiYuLeadsPushDto.getQq());
        leadsInformationDto.setEmail(feiYuLeadsPushDto.getEmail());
        leadsInformationDto.setCampaignId(feiYuLeadsPushDto.getAdId());
        leadsInformationDto.setCampaignName(feiYuLeadsPushDto.getAdName());
        leadsInformationDto.setAccountId(feiYuLeadsPushDto.getAdvId());
        leadsInformationDto.setAccountName(feiYuLeadsPushDto.getAdvName());
        leadsInformationDto.setPromotionName(feiYuLeadsPushDto.getAdName());
        leadsInformationDto.setRemark(feiYuLeadsPushDto.getRemark());
        leadsInformationDto.setMobile(feiYuLeadsPushDto.getTelPhone());
        leadsInformationDto.setChannelType(LeadsDataSourceEnum.FLYING_FISH_CRM.getCode());

        leadsInformationDto.setChannelSource(LeadsDataSourceEnum.FLYING_FISH_CRM.getCode());
        LeadsChannelEnum leadsChannelEnum = leadsTpEnumParseService.paresFlyFishChannelEnum(feiYuLeadsPushDto.getClueSource());
        if (!ObjectUtils.isEmpty(leadsChannelEnum)) {
            leadsInformationDto.setChannel(leadsChannelEnum.getChannel());
        }
        LeadsInputSourceEnum leadsInputSourceEnum = leadsTpEnumParseService.paresFlyFishInputChannelEnum(feiYuLeadsPushDto.getClueType());
        if (!ObjectUtils.isEmpty(leadsInputSourceEnum)) {
            leadsInformationDto.setChannelSource(leadsInputSourceEnum.getCode());
        }

        // 通过配置设置线索创建人
        if (Objects.nonNull(feiYuLeadsPushDto.getAccess())){
            if (1 == feiYuLeadsPushDto.getAccess()) {
                leadsInformationDto.setCreateStaffId(feiYuLeadsPushDto.getCreateStaffId());
            } else if (2 == feiYuLeadsPushDto.getAccess()) {
                Long operatorStaffId = merchantConfigClientForLeads.getFlyFishLeadsOperatorStaffId(feiYuLeadsPushDto.getBid());
                leadsInformationDto.setCreateStaffId(operatorStaffId);
            }
        }

        leadsInformationDto.setSubmitTime(DateTimeUtils.getMilli(DateTimeUtils.MillisToLocalDateTime(
                Long.valueOf(feiYuLeadsPushDto.getCreateTime()))));
        leadsInformationDto.setCreateTime(Long.valueOf(feiYuLeadsPushDto.getCreateTime()));

        boolean isTrue = StringUtils.isEmpty(feiYuLeadsPushDto.getProvinceName()) &&
                StringUtils.isEmpty(feiYuLeadsPushDto.getCityName()) &&
                StringUtils.isEmpty(feiYuLeadsPushDto.getCountyName()) &&
                !StringUtils.isEmpty(feiYuLeadsPushDto.getLocation());
        if (isTrue) {
            String location = feiYuLeadsPushDto.getLocation();
            ArrayList<String> locationArr = Lists.newArrayList(location.split("\\+"));
            if (!CollectionUtils.isEmpty(locationArr)){
                feiYuLeadsPushDto.setProvinceName(locationArr.size() >= 1 ? locationArr.get(0) : null);
                feiYuLeadsPushDto.setCityName(locationArr.size() >= 2 ? locationArr.get(1) : null);
                feiYuLeadsPushDto.setCountyName(locationArr.size() >= 3 ? locationArr.get(2) : null);
            }
        }

        return parseLeadsRegion(leadsInformationDto, feiYuLeadsPushDto.getProvinceName()
                , feiYuLeadsPushDto.getCityName(), feiYuLeadsPushDto.getCountyName());
    }

    /**
     * AI客服 线索转换
     *
     * @param customerServiceLeadsDto Ai客服线索详情
     * @return 标准信息线索dto
     */
    @Override
    public LeadsInformationDto transform(CustomerServiceLeadsDto customerServiceLeadsDto) {
        LeadsInformationDto leadsInformationDto = new LeadsInformationDto();
        leadsInformationDto.setExternalId(customerServiceLeadsDto.getId());
        leadsInformationDto.setTpLeadsId(customerServiceLeadsDto.getId());
        if (StringUtils.isEmpty(customerServiceLeadsDto.getName())){
            leadsInformationDto.setName(genDefaultWxPubName());
        }else {
            leadsInformationDto.setName(customerServiceLeadsDto.getName());
        }
        leadsInformationDto.setMobile(customerServiceLeadsDto.getPhone());
        leadsInformationDto.setWeChat(customerServiceLeadsDto.getWeiXin());
        leadsInformationDto.setGender(customerServiceLeadsDto.getSex());
        leadsInformationDto.setRemark(customerServiceLeadsDto.getRemark());
        leadsInformationDto.setOpenId(customerServiceLeadsDto.getOpenid());

        leadsInformationDto.setChannelType(LeadsDataSourceEnum.AI_CUSTOMER_SERVICE.getCode());
        LeadsChannelEnum leadsChannelEnum = leadsTpEnumParseService.paresAiCustomerChannelEnum(customerServiceLeadsDto.getSource());
        if (!ObjectUtils.isEmpty(leadsChannelEnum)) {
            leadsInformationDto.setChannel(leadsChannelEnum.getChannel());
        }

        CustomerServiceAdInfoDto adInfo = customerServiceLeadsDto.getAdInfo();
        if (!ObjectUtils.isEmpty(adInfo)) {
            leadsInformationDto.setCampaignId(adInfo.getAdgroupId());
            leadsInformationDto.setCampaignName(adInfo.getAdgroupName());
            leadsInformationDto.setAccountId(adInfo.getCampaignId());
            leadsInformationDto.setAccountName(adInfo.getCampaignName());
        }

        leadsInformationDto.setAddress("");


        Long operatorStaffId = merchantConfigClientForLeads.getAiLeadsOperatorStaffId(BidUtils.getBid());
        leadsInformationDto.setCreateStaffId(operatorStaffId);

        //时间
        leadsInformationDto.setCreateTime(DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(customerServiceLeadsDto.getCreatedAt())));
        leadsInformationDto.setUpdateTime(DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(customerServiceLeadsDto.getUpdatedAt())));
//        leadsInformationDto.setTags(customerServiceLeadsDto.getTags().stream().map(CustomerServiceTagDto::getName).collect(Collectors.toList()));
        leadsInformationDto.setExternalTags(customerServiceLeadsDto.getTags().stream().map(CustomerServiceTagDto::getName).collect(Collectors.toList()));

        return parseLeadsRegion(leadsInformationDto, customerServiceLeadsDto.getProvince()
                , customerServiceLeadsDto.getCity(), customerServiceLeadsDto.getDistrict());
    }

    private LeadsInformationDto parseLeadsRegion(LeadsInformationDto leadsInformationDto,String address){
        Map<String, String> addressResolution = leadsRegionCache.addressResolution(address);
        return parseLeadsRegion(
                leadsInformationDto,
                addressResolution.get(LeadsRegionCache.STR_PROVINCE),
                addressResolution.get(LeadsRegionCache.STR_CITY),
                addressResolution.get(LeadsRegionCache.STR_COUNTY),
                address
        );
    }

    private LeadsInformationDto parseLeadsRegion(LeadsInformationDto leadsInformationDto
            , String provinceName, String cityName, String areaName) {

        leadsInformationDto.setProvinceId(0);
        leadsInformationDto.setCityId(0);
        leadsInformationDto.setAreaId(0);
        RegionDto province = leadsRegionCache.getProvince(provinceName);
        if (!ObjectUtils.isEmpty(province)) {
            leadsInformationDto.setProvinceId(province.getId());
            leadsInformationDto.setProvinceName(province.getFullName());
        }

        RegionDto city = leadsRegionCache.getCity(cityName);
        if (!ObjectUtils.isEmpty(city)) {
            leadsInformationDto.setCityId(city.getId());
            leadsInformationDto.setCityName(city.getFullName());
            RegionDto area = leadsRegionCache.getAreaByParentNameAndAreaName(city.getFullName(),
                    areaName);
            if (!ObjectUtils.isEmpty(area)) {
                leadsInformationDto.setAreaId(area.getId());
                leadsInformationDto.setAreaName(area.getFullName());
            }
        }

        //只要有一个没匹配上 尝试使用最长公串匹配
        if ((StringUtils.isBlank(leadsInformationDto.getAreaName()) && StringUtils.isNotBlank(areaName)) ||
                (StringUtils.isBlank(leadsInformationDto.getCityName()) && StringUtils.isNotBlank(cityName)) ||
                (StringUtils.isBlank(leadsInformationDto.getPromotionName()) && StringUtils.isNotBlank(provinceName))
        ){
            return analysisRegionLongestCommonSubstring(leadsInformationDto,provinceName + cityName + areaName);
        }

        return leadsInformationDto;
    }

    private LeadsInformationDto analysisRegionLongestCommonSubstring(LeadsInformationDto leadsInformationDto, String address){
        RegionDto region = leadsRegionCache.analysisRegion(address);
        if (Objects.isNull(region)){
            logger.info("匹配失败：null");
            return leadsInformationDto;
        }
        logger.info("匹配结果：{}", jsonService.toJson(region));
        leadsInformationDto.setProvinceId(region.getId());
        leadsInformationDto.setProvinceName(region.getFullName());

        if (!CollectionUtils.isEmpty(region.getChildren())){
            RegionDto city = region.getChildren().get(0);

            if (Objects.nonNull(city)){
                leadsInformationDto.setCityId(city.getId());
                leadsInformationDto.setCityName(city.getFullName());

                if (!CollectionUtils.isEmpty(city.getChildren())){
                    RegionDto area = city.getChildren().get(0);

                    leadsInformationDto.setAreaId(area.getId());
                    leadsInformationDto.setAreaName(area.getFullName());
                }
            }
        }

        return leadsInformationDto;
    }

    private LeadsInformationDto parseLeadsRegion(LeadsInformationDto leadsInformationDto
            , String provinceName, String cityName, String areaName,String address) {
        LeadsInformationDto leadsInformation = parseLeadsRegion(leadsInformationDto, provinceName, cityName, areaName);

        //省市有一个没匹配上尝试使用匹配解析
        if (Objects.isNull(leadsInformation.getProvinceId())
                || leadsInformation.getProvinceId() <=0
                || Objects.isNull(leadsInformation.getCityId())
                || leadsInformation.getCityId() <=0
        ){
            return analysisRegionLongestCommonSubstring(leadsInformationDto,address);
        }
        return leadsInformationDto;
    }

    private Integer parseFlyFishGender(String genderText) {
        if (GENDER_NAME_MAP.containsKey(genderText)) {
            return GENDER_NAME_MAP.get(genderText);
        }

        return 0;
    }

    private String genDefaultWxPubName(){
        return  "微信用户" + StringUtils.substring(UUID.randomUUID().toString(),0,6);
    }

    public static void main(String[] args) {
        System.out.println(StringUtils.substring(UUID.randomUUID().toString(), 0, 6));
    }
}

package com.inngke.bp.leads.db.leads.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsBatch;
import com.inngke.bp.leads.db.leads.entity.LeadsCallLog;
import com.inngke.common.ds.annotation.DS;

/**
 * <AUTHOR>
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsCallLogDao extends BaseMapper<LeadsCallLog> {
}

package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.utils.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeContactStrategy")
public class LeadsFollowTimeContactStrategy implements LeadsFollowTimeStrategy {
    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateContact(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateContact",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.rangeQuery("stateContact").gt(0))
                                .mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus()))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateContact = bucketAggregations.get("stateContact");
        long count = stateContact.getDocCount();
        dto.setStateContact((int)count);
        if(dto.getLeadsCount()==0){
            return;
        }
        String rate = BigDecimal.valueOf(count).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getLeadsCount()),2, BigDecimal.ROUND_HALF_UP).toString();
        dto.setContactRate(rate+"%");
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateContact).reduce(Integer::sum).get();
        dto.setStateContact(sum);
        if(dto.getLeadsCount()==0){
            return;
        }
        String rate = BigDecimal.valueOf(sum).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getLeadsCount()),2, BigDecimal.ROUND_HALF_UP).toString();
        dto.setContactRate(rate+"%");
    }
}

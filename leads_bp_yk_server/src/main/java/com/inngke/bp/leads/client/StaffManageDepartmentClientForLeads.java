package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.organize.service.StaffManageDepartmentService;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.bp.user.dto.request.staff.StaffGetRequest;
import com.inngke.bp.user.service.UserStaffService;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/8/10 18:15
 **/
@Component
@Slf4j
public class StaffManageDepartmentClientForLeads {



    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    StaffManageDepartmentService staffManageDepartmentService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private UserStaffService userStaffService;

    public Set<Long> getManageDepartmentByStaffId(BaseIdRequest request) {
        BaseResponse<Set<Long>> manageDepartmentByStaffId = staffManageDepartmentService.getManageDepartmentByStaffId(request);
        if (!BaseResponse.responseSuccessWithNonNullData(manageDepartmentByStaffId)) {
            log.error("获取员工管理部门失败 {}", request);
            throw new InngkeServiceException("获取员工数据失败！");
        }
        return manageDepartmentByStaffId.getData();
    }

    public Set<Long> getStaffManageDepartment(Integer bid, Long staffId) {

        BaseIdRequest request = new BaseIdRequest();
        request.setBid(bid);
        request.setId(staffId);
        BaseResponse<Set<Long>> response = staffManageDepartmentService.getManageDepartmentByStaffId(request);

        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("获取员工管理部门返回：{}", jsonService.toJson(response));
            return Sets.newHashSet();
        }
        return response.getData();
    }

    public Set<Long> getStaffManagerTopDepartmentIds(Integer bid, Long staffId){
        BaseIdRequest request = new BaseIdRequest();
        request.setId(staffId);
        request.setBid(bid);

        Set<Long> staffManageDepartment = getStaffManageDepartment(bid, staffId);

        if (CollectionUtils.isEmpty(staffManageDepartment)){
            return staffManageDepartment;
        }

        return departmentClientForLeads.filterDepartmentTopIds(bid, Lists.newArrayList(staffManageDepartment));
    }


    public List<UserStaffDto> getStaffsByEs(Integer bid, Long deptId, String keyword){
        StaffGetRequest staffGetRequest = new StaffGetRequest();
        staffGetRequest.setBid(bid);
        if (!ObjectUtils.isEmpty(keyword)) {
            staffGetRequest.setKeyword(keyword);
        }else {
            staffGetRequest.setDeptIds(Sets.newHashSet(deptId));
        }
        staffGetRequest.setPageSize(9999);
        BaseResponse<List<UserStaffDto>> response = userStaffService.getList(staffGetRequest);

        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("获取员工返回：{}", jsonService.toJson(response));
            return Lists.newArrayList();
        }

        return response.getData();
    }

    public Set<Long> getStaffManagerDepartmentIds(Integer bid, Long staffId){
        BaseIdRequest request = new BaseIdRequest();
        request.setId(staffId);
        request.setBid(bid);

        BaseResponse<Set<Long>> manageDepartment = staffManageDepartmentService.getManageDepartmentByStaffId(request);
        if (!BaseResponse.responseSuccessWithNonNullData(manageDepartment)) {
            log.info("getManageDepartmentByStaffId异常：{}", jsonService.toJson(manageDepartment));
            return Sets.newHashSet();
        }
        return manageDepartment.getData();
    }

}

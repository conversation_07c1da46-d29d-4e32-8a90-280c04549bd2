/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.bp.leads.dto.LeadsFollowBatchCountDTO;
import com.inngke.common.ds.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 线索跟进记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsFollowDao extends BaseMapper<LeadsFollow> {

    @Select("<script>SELECT leads_id, COUNT(*) AS followCount FROM leads_follow WHERE leads_id IN " +
            "<foreach item=\"item\" collection=\"leadsIds\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{item}</foreach> group by leads_id </script>")
    List<LeadsFollowBatchCountDTO> countBatch(@Param("leadsIds") Set<Long> leadsIds);

    @Select("<script>" +
            "SELECT\n" +
            "\tleads_id,\n" +
            "\tbid,\n" +
            "\tuser_id,\n" +
            "\tstaff_id,\n" +
            "\tfollow_content,\n" +
            "\tcreate_time \n" +
            "FROM\n" +
            "\t(\n" +
            "SELECT\n" +
            "\tleads_id,\n" +
            "\tbid,\n" +
            "\tuser_id,\n" +
            "\tfollow_content,\n" +
            "\tstaff_id,\n" +
            "\tcreate_time \n" +
            "FROM\n" +
            "\t`leads_follow` \n" +
            "WHERE\n" +
            "\tbid = #{bid} \n" +
            "and follow_content NOT REGEXP '处回收|转交' "+
            "and leads_id in \n" +
            "        <foreach item=\"item\" index=\"index\" collection=\"leadsIds\"\n" +
            "                    open=\"(\" separator=\",\" close=\")\">\n" +
            "            #{item}\n" +
            " </foreach>" +
            "ORDER BY\n" +
            "\tcreate_time DESC \n" +
            "\tLIMIT 9999 \n" +
            "\t) AS test \n" +
            "GROUP BY\n" +
            "\tleads_id" +
            "</script>")
    List<LeadsFollow> getLeadsLatestFollowList(@Param("bid") Integer bid, @Param("leadsIds") List<Long> leadsIds);

    @Select("<script>" +
            " SELECT " +
            "t1.* " +
            "FROM " +
            " leads_follow t1" +
            " INNER JOIN ( SELECT id  FROM leads_follow " +
            " where " +
            "<if test = 'isPC == false'>" +
            " follow_content NOT REGEXP '处回收|转交' and " +
            "</if>" +
            " leads_id in" +
            "<foreach item=\"item\" collection=\"leadsIds\" index=\"index\" open=\"(\" separator=\",\" close=\")\">#{item}</foreach>" +
            " GROUP BY leads_id) t2 " +            " ON t1.id = t2.id " +
            "</script>"
    )
    List<LeadsFollow> getLastFollowRecordList(@Param("leadsIds") List<Long> leadsIds, @Param("isPC") boolean isPC);
}

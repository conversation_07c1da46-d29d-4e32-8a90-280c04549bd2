package com.inngke.bp.leads.service.impl.tp.pull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.core.utils.TencentSdkUtils;
import com.inngke.bp.leads.db.leads.entity.LeadsTpLog;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.tencent.ads.ApiException;
import com.tencent.ads.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 13:55
 */
@Service
public class LeadsTpPullTencentServiceImpl extends LeadsTpPullServiceAbstract<LeadCluesGetListStruct, TencentLeadsPushDto>{

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpPullTencentServiceImpl.class);

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public LeadsTpTypeEnum getType() {
        return LeadsTpTypeEnum.TENCENT;
    }

    @Override
    protected List<LeadCluesGetListStruct> getTpLeadsList(String customStartTime, BaseBidOptRequest request) {
        List<LeadsTpOauth> allOauth = getAllLeadsTpOauth(request);


        List<LeadCluesGetListStruct> allLeadsInfo = Lists.newArrayList();

        for (LeadsTpOauth leadsTpOauth : allOauth) {
            TimeRange timeRange = new TimeRange();
            LocalDateTime startLocalDateTime = getStartTime();
            if (startLocalDateTime.isBefore(leadsTpOauth.getCreateTime())){
                startLocalDateTime = leadsTpOauth.getCreateTime();
            }
            if (!StringUtils.isEmpty(customStartTime)){
                startLocalDateTime = DateTimeUtils.toLocalDateTime(customStartTime);
            }
            timeRange.setStartTime(DateTimeUtils.getMilli(startLocalDateTime)/1000);
            timeRange.setEndTime(DateTimeUtils.getMilli(LocalDateTime.now())/1000);

            String accessToken = leadsTpOauth.getAccessToken();
            List<String> accountIds = getAllAccountId(request.getBid(), leadsTpOauth.getAccountId());
            LeadCluesGetRequest getLeadsRequest = new LeadCluesGetRequest();
            getLeadsRequest.setTimeType(TimeType.ACTION_TIME);
            getLeadsRequest.setTimeRange(timeRange);
            getLeadsRequest.setFiltering(Lists.newArrayList());
            getLeadsRequest.setLastSearchAfterValues(Lists.newArrayList());
            for (String accountId : accountIds) {
                getLeadsRequest.setAccountId(Long.valueOf(accountId));
                getLeadsRequest.setPage(1L);
                getLeadsRequest.setPageSize(100L);
                List<LeadCluesGetListStruct> leadsInfo;

                do {
                    try {
                        LeadCluesGetResponseData leadCluesGetResponseData =
                                TencentSdkUtils.getTencentAds(accessToken).leadClues().leadCluesGet(getLeadsRequest);
                        leadsInfo = leadCluesGetResponseData.getLeadsInfo();
                        if (!CollectionUtils.isEmpty(leadsInfo)){
                            int size = leadsInfo.size();

                            leadsInfo.stream().filter((this::isChangeData)).forEach(allLeadsInfo::add);

                            logger.info("过滤掉无变更线索前后数据量:过滤前{},过滤后{}", leadsInfo.size()
                                    , leadsInfo.size() - size);
                        }else {
                            logger.info("获取到线索数据为空");
                        }
                    }catch (ApiException e){
                        break;
                    }
                }while (!CollectionUtils.isEmpty(leadsInfo));
            }
        }

        return allLeadsInfo;
    }

    @Override
    protected void saveLog(Integer bid, List<LeadCluesGetListStruct> tpLeadsList) {
        List<LeadsTpLog> leadsTpLogList = Lists.newArrayList();

        tpLeadsList.forEach((tencentLeads) -> {
            LeadsTpLog leadsTpLog = new LeadsTpLog();
            leadsTpLog.setType(LeadsTpTypeEnum.FEI_YU.getCode());
            leadsTpLog.setContent(toJson(tencentLeads));
            leadsTpLog.setTpId(tencentLeads.getLeadsId().toString());
            leadsTpLog.setBid(bid);
            leadsTpLogList.add(leadsTpLog);
        });

        leadsTpLogManager.saveOrUpdateBatch(bid, leadsTpLogList);
    }

    public String toJson(LeadCluesGetListStruct obj) {
        if (obj == null) {
            return "";
        }
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            objectMapper.writeValue(out, obj);
        } catch (IOException e) {
            throw new InngkeServiceException("格式化失败", e);
        }
        byte[] data = out.toByteArray();
        return new String(data, StandardCharsets.UTF_8);
    }

    @Override
    protected String getLeadsId(LeadCluesGetListStruct leads) {
        return String.valueOf(leads.getLeadsId());
    }

    @Override
    protected Class<LeadCluesGetListStruct> getTpLeadsDtoClazz() {
        return LeadCluesGetListStruct.class;
    }

    @Override
    protected void saveLeads(Integer bid, List<TencentLeadsPushDto> tencentLeadsPushDtoList) {
        tencentLeadsPushDtoList.forEach(tencentLeads -> {
                    try {
                        leadsTpConserveService.conserve(bid, tencentLeads);
                    } catch (Exception e) {
                        logger.error("保存线索失败:{}", jsonService.toJson(tencentLeads), e);
                    }
                }
        );
    }

    @Override
    protected TencentLeadsPushDto toLeadsPushDto(LeadCluesGetListStruct pullLeads) {
        TencentLeadsPushDto tencentLeadsPushDto = new TencentLeadsPushDto();
        tencentLeadsPushDto.setLeadsId(pullLeads.getLeadsId());
        tencentLeadsPushDto.setClickId(pullLeads.getClickId());
        tencentLeadsPushDto.setAccountId(pullLeads.getAccountId());
        tencentLeadsPushDto.setAdvertiserId(pullLeads.getAccountId().toString());
        tencentLeadsPushDto.setAgencyId(pullLeads.getAgencyId());
        tencentLeadsPushDto.setAgencyName(pullLeads.getAgencyName());
        tencentLeadsPushDto.setWechatSpId(pullLeads.getWechatAppid());
        tencentLeadsPushDto.setCampaignId(String.valueOf(pullLeads.getCampaignId()));
        tencentLeadsPushDto.setCampaignName(pullLeads.getCampaignName());
        tencentLeadsPushDto.setAdgroupId(pullLeads.getAdgroupId());
        tencentLeadsPushDto.setAdgroupName(pullLeads.getAdgroupName());
        tencentLeadsPushDto.setCreativeId(pullLeads.getCreativeId());
        tencentLeadsPushDto.setCreativeName(pullLeads.getCreativeName());
        tencentLeadsPushDto.setAdId(String.valueOf(pullLeads.getAdId()));
        tencentLeadsPushDto.setAdName(pullLeads.getAdName());
        tencentLeadsPushDto.setAdCreativeId(String.valueOf(pullLeads.getAdcreativeId()));
        tencentLeadsPushDto.setAdCreativeName(pullLeads.getAdcreativeName());
        tencentLeadsPushDto.setPageId(pullLeads.getPageId());
        tencentLeadsPushDto.setPageUrl(pullLeads.getPageUrl());
        tencentLeadsPushDto.setLeadsType(pullLeads.getLeadsType().getValue());
        tencentLeadsPushDto.setLeadsTags(pullLeads.getLeadsTags());
        tencentLeadsPushDto.setLeadsSource(pullLeads.getLeadsSource());
        tencentLeadsPushDto.setLeadsPotentialScore(pullLeads.getLeadsPotentialScore());
        tencentLeadsPushDto.setLeadsFollowTag(pullLeads.getLeadsFollowTag());
        tencentLeadsPushDto.setLeadsName(pullLeads.getLeadsName());
        tencentLeadsPushDto.setLeadsTel(pullLeads.getLeadsTelephone());
        tencentLeadsPushDto.setTelLocation(pullLeads.getTelephoneLocation());
        tencentLeadsPushDto.setLeadsArea(pullLeads.getLeadsArea());
        tencentLeadsPushDto.setLeadsEmail(pullLeads.getLeadsEmail());
        tencentLeadsPushDto.setLeadsQq(pullLeads.getLeadsQq());
        tencentLeadsPushDto.setLeadsWechat(pullLeads.getLeadsWechat());
        tencentLeadsPushDto.setLeadsGender(String.valueOf(pullLeads.getLeadsGender()));
        tencentLeadsPushDto.setNationality(pullLeads.getNationality());
        tencentLeadsPushDto.setWorkingYears(pullLeads.getWorkingYears());
        if (!StringUtils.isEmpty(pullLeads.getAge())){
            tencentLeadsPushDto.setAge(Integer.valueOf(pullLeads.getAge()));
        }
        tencentLeadsPushDto.setProfession(pullLeads.getProfession());
        tencentLeadsPushDto.setIdNumber(pullLeads.getIdNumber());
        tencentLeadsPushDto.setAddress(pullLeads.getAddress());
        tencentLeadsPushDto.setBundle(pullLeads.getBundle());
        tencentLeadsPushDto.setLeadsCreateTime(pullLeads.getLeadsCreateTime());
        tencentLeadsPushDto.setLeadsActionTime(pullLeads.getLeadsActionTime());
        tencentLeadsPushDto.setShopName(pullLeads.getShopName());
        tencentLeadsPushDto.setShopAddress(pullLeads.getShopAddress());
        tencentLeadsPushDto.setAccess(1);
        return tencentLeadsPushDto;

    }
}

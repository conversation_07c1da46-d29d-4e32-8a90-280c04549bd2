package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.leads.mq.message.order.OrderChangeMessageDto;
import com.inngke.bp.leads.mq.process.OrderProcess;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2021/9/17 14:33
 **/
@Service
@MqConsumer(value = "order_change", consumerName = "leads_bp_yk_order_change", messageType = OrderChangeMessageDto.class)
public class LeadsOrderChangeListener implements InngkeMqListener<OrderChangeMessageDto> {
    private static final Logger logger = LoggerFactory.getLogger(LeadsOrderChangeListener.class);

    @Autowired
    private List<OrderProcess> orderProcessList;
    private Map<Integer, OrderProcess> orderProcessMap;

    @Autowired
    protected JsonService jsonService;

    @PostConstruct
    public void init() {
        orderProcessMap = orderProcessList.stream().collect(Collectors.toMap(OrderProcess::getProcessOrderType, process -> process));
    }

    @Override
    public void process(OrderChangeMessageDto orderChangeMessage) {
        String msg = jsonService.toJson(orderChangeMessage);
        logger.info("订单变更事件监听开始，事件信息体:{}",msg);
        Integer event = orderChangeMessage.getEvent();
        Integer bid = orderChangeMessage.getBid();
        if (event == null) {
            return;
        }
        if(bid == null ){
            return;
        }

        OrderProcess process = orderProcessMap.get(orderChangeMessage.getOrderType());
        if (process == null) {
            logger.warn("未处理的订单类型：orderType={}", orderChangeMessage.getOrderType());
            return;
        }

        if (process.match(orderChangeMessage)) {
            process.process(orderChangeMessage);
        }
    }
}
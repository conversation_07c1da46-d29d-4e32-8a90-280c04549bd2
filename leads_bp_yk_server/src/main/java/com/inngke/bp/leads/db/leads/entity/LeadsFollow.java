/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 线索跟进记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadsFollow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 线索ID，即leads.id
     */
    private Long leadsId;

    /**
     * 跟进人员工ID，即staffId
     */
    private Long staffId;

    /**
     * 后台管理员编号 即t_user.id
     */
    private Long userId;

    /**
     * 跟进类型
     */
    private Integer followType;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 跟进图片URL列表，多个使用半角逗号分隔
     */
    private String followImages;

    /**
     * 跟进时设置的线索状态，详见leads.status
     */
    private Integer leadsStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 客户流失原因
     */
    private Integer clientLostReasonType;

    /**
     * 客服跟进时设置的线索状态，详见leads.status
     */
    private Integer preFollowStatus;

    /**
     * 无效线索/线索流失时填写
     *
     * @demo 1
     * @required
     */
    private Long reasonId;

    /**
     * 无效线索/线索流失时填写
     *
     * @demo 客户明确表示无需求
     * @required
     */
    private String reason;

    /**
     * 操作者身份 1=导购 2=客服 3=管理员 0=系统操作
     */
    private Integer operatorRole;

    /**
     * 添加跟进前的线索状态，详见leads.status
     */
    private Integer beforeLeadsStatus;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String LEADS_ID = "leads_id";

    public static final String STAFF_ID = "staff_id";

    public static final String FOLLOW_TYPE = "follow_type";

    public static final String FOLLOW_CONTENT = "follow_content";

    public static final String FOLLOW_IMAGES = "follow_images";

    public static final String LEADS_STATUS = "leads_status";

    public static final String CREATE_TIME = "create_time";

    public static final String CLIENT_LOST_REASON_TYPE = "client_lost_reason_type";

    public static final String PRE_FOLLOW_STATUS = "pre_follow_status";

    public static final String OPERATOR_ROLE = "operator_role";

    public static final String BEFORE_LEADS_STATUS = "before_leads_status";

    public static final String REASON_ID = "reason_id";

}

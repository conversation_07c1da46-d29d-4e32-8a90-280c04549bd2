package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.dto.platform.CustomerServiceLeadsDto;
import com.inngke.bp.leads.dto.request.tp.FeiYuLeadsPushDto;
import com.inngke.bp.leads.service.LeadsTpConserveService;
import com.inngke.bp.leads.service.LeadsAICustomerService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.BidUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2022/4/24 14:53
 */
public class LeadsAICustomerServiceImpl implements LeadsAICustomerService {
    @Autowired
    private LeadsTpConserveService leadsTpConserveService;

    @Override
    public BaseResponse<String> handle(CustomerServiceLeadsDto request) {
        BaseResponse<Long> conserve = leadsTpConserveService.conserve(BidUtils.getBid(), request);
        if (BaseResponse.responseSuccessWithNonNullData(conserve)) {
            return BaseResponse.success("success");
        }

        return BaseResponse.error("同步失败");
    }
}

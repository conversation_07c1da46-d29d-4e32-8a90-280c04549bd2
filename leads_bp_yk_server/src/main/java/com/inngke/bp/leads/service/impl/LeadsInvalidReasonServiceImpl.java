package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.db.leads.entity.LeadsChannel;
import com.inngke.bp.leads.db.leads.entity.LeadsInvalidReason;
import com.inngke.bp.leads.db.leads.manager.LeadsInvalidReasonManager;
import com.inngke.bp.leads.dto.request.LeadsInvalidReasonMoveSortRequest;
import com.inngke.bp.leads.dto.request.LeadsInvalidReasonSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsInvalidReasonDto;
import com.inngke.bp.leads.service.LeadsInvalidReasonService;
import com.inngke.bp.leads.service.leadsInvalidReasonInitService;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.consts.LeadsServiceConsts.LOCK_PREFIX;

@Service
@DubboService(version = "1.0.0")
@Slf4j
public class LeadsInvalidReasonServiceImpl implements LeadsInvalidReasonService {

    private static final String LOCK = LOCK_PREFIX + "LeadsInvalidReason_%s";

    @Autowired
    private LeadsInvalidReasonManager leadsInvalidReasonManager;

    @Autowired
    private leadsInvalidReasonInitService leadsInvalidReasonInitService;

    @Autowired
    private LockService lockService;

    @Override
    public BaseResponse<List<LeadsInvalidReasonDto>> getList(BaseBidRequest request) {
        List<LeadsInvalidReason> leadsInvalidReasonList = leadsInvalidReasonManager.list(Wrappers.<LeadsInvalidReason>query()
                .eq(LeadsInvalidReason.BID, request.getBid())
                .eq(LeadsInvalidReason.ENABLE, 1)
                .orderByAsc(LeadsInvalidReason.SORT));

        if (ObjectUtils.isEmpty(leadsInvalidReasonList)) {
            leadsInvalidReasonInitService.init();
            leadsInvalidReasonList = getLeadsInvalidReasonList(request.getBid());
        }

        return BaseResponse.success(leadsInvalidReasonList.stream().map(i -> toLeadsInvalidReasonDto(i)).collect(Collectors.toList()));
    }

    private List<LeadsInvalidReason> getLeadsInvalidReasonList(Integer bid) {
        return leadsInvalidReasonManager.list(Wrappers.<LeadsInvalidReason>query()
                .eq(LeadsInvalidReason.BID, bid)
                .eq(LeadsInvalidReason.ENABLE, 1));
    }

    private LeadsInvalidReasonDto toLeadsInvalidReasonDto(LeadsInvalidReason leadsInvalidReason) {
        LeadsInvalidReasonDto leadsInvalidReasonDto = new LeadsInvalidReasonDto();
        leadsInvalidReasonDto.setReason(leadsInvalidReason.getReason());
        leadsInvalidReasonDto.setId(leadsInvalidReason.getId());
        return leadsInvalidReasonDto;
    }

    @Override
    public BaseResponse save(LeadsInvalidReasonSaveRequest request) {
        Integer bid = request.getBid();
        Long id = request.getId();
        Lock lock = lockService.getLock(String.format(LOCK, bid), 60);
        if (lock == null) {
            return BaseResponse.error("同时操作中，请稍后刷新下再操作！");
        }

        try {
            checkName(request);
            if (Objects.isNull(id)) {
                LeadsInvalidReason leadsInvalidReason = toLeadsInvalidReason(request);
                //保存
                save(bid, leadsInvalidReason);
                return BaseResponse.success();
            }else {
                LeadsInvalidReason getLeadsInvalidReason = leadsInvalidReasonManager.getOne(getQueryWrapper(bid, id));
                if (Objects.isNull(getLeadsInvalidReason)) {
                    return BaseResponse.error("数据不存在！");
                }
                //更新
                update(request, bid);
            }
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        return BaseResponse.success();
    }

    private void update(LeadsInvalidReasonSaveRequest request, Integer bid) {
        leadsInvalidReasonManager.update(Wrappers.<LeadsInvalidReason>update()
                .eq(LeadsInvalidReason.BID, bid)
                .eq(LeadsInvalidReason.ID, request.getId())
                .set(!ObjectUtils.isEmpty(request.getReason()),LeadsInvalidReason.REASON, request.getReason())
                .set(!ObjectUtils.isEmpty(request.getEnable()),LeadsInvalidReason.ENABLE, request.getEnable()));
    }

    private void save(Integer bid, LeadsInvalidReason leadsInvalidReason) {
        LeadsInvalidReason latestLeadsInvalidReason = leadsInvalidReasonManager.getOne(Wrappers.<LeadsInvalidReason>query()
                .eq(LeadsInvalidReason.BID, bid)
                .orderByDesc(LeadsInvalidReason.CREATE_TIME,LeadsInvalidReason.ID).last("limit 1"));
        if (!ObjectUtils.isEmpty(latestLeadsInvalidReason)
                && !ObjectUtils.isEmpty(latestLeadsInvalidReason.getSort())) {
            leadsInvalidReason.setSort(latestLeadsInvalidReason.getSort() + 1);
        }
        leadsInvalidReason.setId(SnowflakeHelper.getId());
        leadsInvalidReasonManager.save(leadsInvalidReason);
    }

    private QueryWrapper<LeadsInvalidReason> getQueryWrapper(Integer bid, Long id) {
        QueryWrapper<LeadsInvalidReason> queryWrapper = Wrappers.<LeadsInvalidReason>query()
                .eq(LeadsInvalidReason.BID, bid)
                .eq(LeadsInvalidReason.ID, id)
                .ne(LeadsInvalidReason.ENABLE, -1);
        return queryWrapper;
    }

    private LeadsInvalidReason toLeadsInvalidReason(LeadsInvalidReasonSaveRequest request) {
        LeadsInvalidReason leadsInvalidReason = new LeadsInvalidReason();
        leadsInvalidReason.setBid(request.getBid());
        leadsInvalidReason.setId(request.getId());
        leadsInvalidReason.setReason(request.getReason());
        leadsInvalidReason.setEnable(request.getEnable());
        return leadsInvalidReason;
    }

    private void checkName(LeadsInvalidReasonSaveRequest request) {
        Integer bid = request.getBid();
        Long id = request.getId();
        String reason = request.getReason();
        if (StringUtils.isEmpty(reason)) {
            return;
        }
        QueryWrapper<LeadsInvalidReason> queryWrapper = Wrappers.<LeadsInvalidReason>query()
                .in(LeadsInvalidReason.BID, bid)
                .eq(LeadsInvalidReason.REASON, reason)
                .ne(LeadsInvalidReason.ENABLE, -1);
        if (!Objects.isNull(id)) {
            queryWrapper.ne(LeadsChannel.ID, id);
        }
        int count = leadsInvalidReasonManager.count(queryWrapper);
        if (count > 0) {
            throw new InngkeServiceException("原因名称不能重复");
        }
    }

    @Override
    public BaseResponse switchSort(LeadsInvalidReasonMoveSortRequest request) {
        Integer bid = request.getBid();
        Lock lock = lockService.getLock(String.format(LOCK, bid), 60);
        if (lock == null) {
            return BaseResponse.error("同时操作中，请稍后刷新下再操作！");
        }
        try {
            Long prevId = request.getPrevId();
            Long nextId = request.getNextId();

            LeadsInvalidReason prevLeadsInvalidReason = leadsInvalidReasonManager.getOne(getQueryWrapper(bid, prevId));
            LeadsInvalidReason nextLeadsInvalidReason = leadsInvalidReasonManager.getOne(getQueryWrapper(bid, nextId));
            if (Objects.isNull(prevLeadsInvalidReason) || Objects.isNull(nextLeadsInvalidReason)) {
                return BaseResponse.error("数据不存在！");
            }

            leadsInvalidReasonManager.moveSort(bid, prevLeadsInvalidReason, nextLeadsInvalidReason);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        return BaseResponse.success();
    }
}

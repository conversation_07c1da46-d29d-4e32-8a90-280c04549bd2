package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.core.converter.LeadsStatisticsConverter;
import com.inngke.bp.leads.dto.request.LeadsStatisticsQuery;
import com.inngke.bp.leads.dto.request.LeadsStatusRequest;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
import com.inngke.bp.leads.dto.response.LeadsStatisticsExcelDto;
import com.inngke.bp.leads.dto.response.LeadsStatusDto;
import com.inngke.bp.leads.service.LeadsStatisticsService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索-统计
 * @since 2021/9/8 10:46 PM
 */
@RestController
@RequestMapping("/api/leads-statistics")
public class LeadsStatisticsApiController {
    @Autowired
    private LeadsStatisticsService leadsStatisticsService;

    /**
     * 线索统计查询
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    筛选条件
     * @return 统计信息列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<LeadsStatisticsExcelDto>> getLeadsStatistics(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsStatisticsQuery request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);

        BaseResponse<BasePaginationResponse<LeadsStatisticsDto>> leadsStatistics = leadsStatisticsService.getLeadsStatistics(request);
        //需要进行一次封装
        BasePaginationResponse<LeadsStatisticsExcelDto> resp = new BasePaginationResponse<>();
        List<LeadsStatisticsExcelDto> leadsStatisticsExcelDtos = new ArrayList<>();
        if (null != leadsStatistics.getData() && !CollectionUtils.isEmpty(leadsStatistics.getData().getList())) {
            leadsStatisticsExcelDtos = LeadsStatisticsConverter.toLeadsStatisticsExcelDto(leadsStatistics.getData().getList());
            resp.setTotal(leadsStatistics.getData().getTotal());
        }
        resp.setList(leadsStatisticsExcelDtos);
        return BaseResponse.success(resp);
    }

    /**
     * 导出线索统计查询
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    筛选条件
     * @return 生成的统计文件链接
     */
    @GetMapping("/export")
    public BaseResponse<String> getLeadsStatisticsExport(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsStatisticsQuery request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsStatisticsService.getLeadsStatisticsExport(request);
    }

    /**
     * 获取某个员工的统计数据
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param staffId    员工ID
     * @return 员工的统计数据
     */
    @GetMapping("/{staffId:\\d+}")
    public BaseResponse<LeadsStatusDto> getStaffStatistics(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long staffId
    ) {
        LeadsStatusRequest request = new LeadsStatusRequest();
        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setStaffId(staffId);
        return leadsStatisticsService.getLeadsStatusStatistics(request);
    }

    /**
     * 获取员工的待联系线索数
     * @param customerId 员工id
     * @param bid
     * @param operatorId
     * @return
     */
    @GetMapping("/{staffId:\\d+}/not-contact")
    public BaseResponse<Integer> getNotContactLeads(
            @PathVariable Long staffId,
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        LeadsStatusRequest request = new LeadsStatusRequest();
        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setStaffId(staffId);
        return leadsStatisticsService.getNotContactLeads(request);
    }
}

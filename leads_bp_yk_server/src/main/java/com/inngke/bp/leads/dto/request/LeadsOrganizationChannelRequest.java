package com.inngke.bp.leads.dto.request;

import com.inngke.bp.leads.service.enums.LeadsReportListDetailTypeEnum;
import com.inngke.common.dto.request.BaseBidOptPageRequest;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/24 15:16
 */
public class LeadsOrganizationChannelRequest extends BaseBidOptPageRequest {


    /**
     * 渠道
     */
    @NotNull(message = "channel不能为空")
    private Integer channel;

    /**
     * @see LeadsReportListDetailTypeEnum
     * 1：总客户数、15：下发数、2：有效客户数、3：无效客户数、8：量尺数、9：到店数、11：定金数、12：成交数、10：流失数
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 开始时间
     *
     * @demo 2022-10-25 00:00:00
     */
    @NotEmpty(message = "startTime不能为空")
    private String startTime;

    /**
     * 结束时间
     *
     * @demo 2022-11-24 24:00:00
     */
    @NotEmpty(message = "endTime不能为空")
    private String endTime;

    private Long sid;


    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}

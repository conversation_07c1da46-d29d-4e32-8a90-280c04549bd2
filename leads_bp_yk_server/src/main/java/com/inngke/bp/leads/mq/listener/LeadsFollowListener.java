package com.inngke.bp.leads.mq.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.QyGroupNotifyServiceForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.message.leads.LeadsFollowMessage;
import com.inngke.bp.leads.service.LeadsWxPubMessageService;
import com.inngke.bp.leads.service.message.MessageManagerService;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.RemindersToFollowedContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.BidUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/9/18 8:18 PM
 */
@Service
@MqConsumer(value = LeadsFollowListener.LEADS_TO_FOLLOW_TOPIC_NAME, consumerName = "leads_bp_yk_leads_follow", messageType = LeadsFollowMessage.class)
public class LeadsFollowListener implements InngkeMqListener<LeadsFollowMessage> {
    private static final Logger logger = LoggerFactory.getLogger(LeadsFollowListener.class);

    public static final String LEADS_TO_FOLLOW_TOPIC_NAME = "leads_follow";
    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsWxPubMessageService leadsWxPubMessageService;

    @Autowired
    private MessageManagerService messageManagerService;

    @Autowired
    private QyGroupNotifyServiceForLeads qyGroupNotifyServiceForLeads;

    private static final Map<Integer,String> TIME_OUT_STR_MAP = Maps.newHashMap();

    static {
        TIME_OUT_STR_MAP.put(1,"15分钟");
        TIME_OUT_STR_MAP.put(2,"24小时");
        TIME_OUT_STR_MAP.put(3,"1小时");
    }


    @Override
    public void process(LeadsFollowMessage pojo) {
        logger.info("线索跟进提醒mq进入，参数为：{},BidUtils={}", jsonService.toJson(pojo), BidUtils.getBid());
        if (Objects.isNull(pojo) || Objects.isNull(pojo.getId())) {
            return;
        }
        qyGroupNotifyServiceForLeads.sendLeadsGroupMsg(pojo.getBid(), pojo.getId());
    }

    /**
     * 消费消息
     *
     * @param pojo    消息实例
     */
    /*@Override
    public void process(LeadsFollowMessage pojo) {
        long timeStart = System.currentTimeMillis();
        logger.info("线索跟进提醒mq进入，参数为：{},BidUtils={}", jsonService.toJson(pojo), BidUtils.getBid());
        BidUtils.setBid(pojo.getBid());

        List<Leads> leadsList = getStaffNotFollowLeadsList(pojo);

        if (!CollectionUtils.isEmpty(leadsList)) {
            try {
                RemindersToFollowedContext ctx = new RemindersToFollowedContext(
                        pojo.getBid(),
                        MessageTypeEnum.REMINDERS_TO_FOLLOWED,
                        TIME_OUT_STR_MAP.getOrDefault(pojo.getType(), InngkeAppConst.EMPTY_STR),
                        leadsList,
                        pojo.getStaffId()
                );
                messageManagerService.send(ctx);
            } catch (Exception e) {
                logger.error("发送线索跟进消息失败", e);
            }
        }

        logger.info("耗时：{}ms", System.currentTimeMillis() - timeStart);
    }
*/
    private List<Leads> getStaffNotFollowLeadsList(LeadsFollowMessage pojo){
        return leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.DISTRIBUTE_STAFF_ID, pojo.getStaffId())
                        .eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                        .eq(Leads.BID, pojo.getBid())
                        .in(Leads.ID, pojo.getLeadsIds())
                        .select(Leads.ID, Leads.NAME, Leads.MOBILE, Leads.DISTRIBUTE_TIME)
        );
    }
}

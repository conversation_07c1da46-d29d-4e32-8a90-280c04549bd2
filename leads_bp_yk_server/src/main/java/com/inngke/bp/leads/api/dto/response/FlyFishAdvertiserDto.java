package com.inngke.bp.leads.api.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlyFishAdvertiserDto implements Serializable {
    /**
     * 广告主id
     */
    @JsonProperty("advertiser_id")
    private String advertiserId;

    /**
     * 广告主名称
     */
    @JsonProperty("advertiser_name")
    private String advertiserName;

    private String parentAdvertiserId;
}

package com.inngke.bp.leads.service.impl.tp.oauth;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.api.LeadsTpFlyFishApiService;
import com.inngke.bp.leads.api.dto.request.GetFlyFishTokenByCodeRequest;
import com.inngke.bp.leads.api.dto.request.RefreshFlyFishTokenRequest;
import com.inngke.bp.leads.api.dto.response.BaseFlyFishListResponse;
import com.inngke.bp.leads.api.dto.response.BaseFlyFishResponse;
import com.inngke.bp.leads.api.dto.response.FlyFishAccessTokenDto;
import com.inngke.bp.leads.api.dto.response.FlyFishAdvertiserDto;
import com.inngke.bp.leads.core.config.LeadsTpFlyFishConfig;
import com.inngke.bp.leads.core.utils.FlyFishApiUtils;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.db.leads.manager.LeadsTpOauthManager;
import com.inngke.bp.leads.dto.request.tp.GetTpAccessTokenRequest;
import com.inngke.bp.leads.dto.request.tp.GetTpLaunchOauthDataRequest;
import com.inngke.bp.leads.dto.request.tp.OauthCallbackRequest;
import com.inngke.bp.leads.dto.response.tp.TpLaunchOauthDataDto;
import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:33
 */
@Service
public class LeadsTpOauthFlyFishServiceImpl extends LeadsTpOauthAbstract {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpOauthFlyFishServiceImpl.class);

    @Autowired
    private LeadsTpFlyFishConfig leadsTpFlyFishConfig;

    @Autowired
    private LeadsTpFlyFishApiService leadsTpFlyFishApiService;

    @Autowired
    private LeadsTpOauthManager leadsTpOauthManager;

    @Override
    public LeadsTpTypeEnum getType() {
        return LeadsTpTypeEnum.FEI_YU;
    }

    @Override
    public BaseResponse<TpLaunchOauthDataDto> getLaunchOauthData(GetTpLaunchOauthDataRequest request) {
        String url = leadsTpFlyFishConfig.getOauthApi() +
                "?" +
                "app_id=" +
                leadsTpFlyFishConfig.getClientId() +
                "&redirect_uri=" +
                leadsTpFlyFishConfig.getRedirectUri() +
                "&state=" +
                request.getBid()+
                "&material_auth=1";

        TpLaunchOauthDataDto tpLaunchOauthDataDto = new TpLaunchOauthDataDto();
        tpLaunchOauthDataDto.setUrl(url);
        return BaseResponse.success(tpLaunchOauthDataDto);
    }

    @Override
    public BaseResponse<Boolean> oauthCallback(OauthCallbackRequest request) {
        FlyFishAccessTokenDto flyFishAccessToken = getAccessTokenByCode(request.getAuthorizationCode());

        LeadsTpOauth oauthData = createOauthData(request.getBid(), flyFishAccessToken);

        List<LeadsTpAccountInfo> leadsTpAccountList = createOauthAccountInfoList(request.getBid(), flyFishAccessToken);

        return BaseResponse.success(leadsTpOauthManager.saveAccount(request.getBid(), oauthData, leadsTpAccountList));
    }

    @Override
    public BaseResponse<String> getAccessToken(GetTpAccessTokenRequest request) {
        String accountId = request.getAccountId();
        Integer bid = request.getBid();

        String accessToken = getTokenFromCache(bid, accountId);
        if (StringUtils.isEmpty(accessToken)) {
            accessToken = getTokenFromDb(bid, accountId);
            if (StringUtils.isEmpty(accessToken)) {
                return BaseResponse.success(accessToken);
            }
            cacheToken(bid, accountId, accessToken);
        }

        return BaseResponse.success(accessToken);
    }

    @Override
    protected String getAccessTokenCacheKey(Integer bid, String accountId) {
        return TP_ACCESS_TOKEN_CACHE_KEY + bid + "type:" + LeadsTpTypeEnum.FEI_YU.getCode() + ":accountId:" + accountId;
    }


    /**
     * 创建授权数据
     *
     * @param bid
     * @param oauthData
     * @return
     */
    private LeadsTpOauth createOauthData(Integer bid, FlyFishAccessTokenDto oauthData) {
        LeadsTpOauth leadsTpOauth = new LeadsTpOauth();
        leadsTpOauth.setAccountId(Optional.ofNullable(oauthData.getAdvertiserIds()).map(List::stream).flatMap(Stream::findFirst).map(Object::toString).orElse(InngkeAppConst.EMPTY_STR));
        leadsTpOauth.setAccessToken(oauthData.getAccessToken());
        leadsTpOauth.setRefreshToken(oauthData.getRefreshToken());
        leadsTpOauth.setAccountName(oauthData.getAdvertiserName());
        leadsTpOauth.setType(LeadsTpTypeEnum.FEI_YU.getCode());
        leadsTpOauth.setBid(bid);

        return leadsTpOauth;
    }


    /**
     * 根据广告主账号获取同主体广告主信息
     *
     * @param bid
     * @return
     */
    private List<LeadsTpAccountInfo> createOauthAccountInfoList(Integer bid, FlyFishAccessTokenDto oauthData) {
        List<FlyFishAdvertiserDto> advertiserList = getAdvertiserList(oauthData.getAccessToken(), oauthData.getAdvertiserIds());
        if (CollectionUtils.isEmpty(advertiserList)) {
            return Lists.newArrayList();
        }

        return advertiserList.stream().map(advertiser -> {
            LeadsTpAccountInfo leadsTpAccountInfo = new LeadsTpAccountInfo();
            leadsTpAccountInfo.setAccountId(advertiser.getAdvertiserId());
            leadsTpAccountInfo.setAccountName(advertiser.getAdvertiserName());
            leadsTpAccountInfo.setType(LeadsTpTypeEnum.FEI_YU.getCode());
            leadsTpAccountInfo.setBid(bid);
            leadsTpAccountInfo.setParentAccountId(advertiser.getParentAdvertiserId());
            return leadsTpAccountInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 通过code 获取token
     *
     * @param authorizationCode
     * @return
     */
    private FlyFishAccessTokenDto getAccessTokenByCode(String authorizationCode) {

        GetFlyFishTokenByCodeRequest getTokenRequest = new GetFlyFishTokenByCodeRequest();
        getTokenRequest.setAuthCode(authorizationCode);
        getTokenRequest.setAppId(leadsTpFlyFishConfig.getClientId());
        getTokenRequest.setSecret(leadsTpFlyFishConfig.getClientSecret());
        getTokenRequest.setGrantType("auth_code");

        return FlyFishApiUtils.checkResponse(leadsTpFlyFishApiService.getAccessToken(getTokenRequest));
    }

    /**
     * 获取账户信息
     *
     * @param accessToken
     * @return
     */
    private List<FlyFishAdvertiserDto> getAdvertiserList(String accessToken, List<Long> accountIds) {
        List<FlyFishAdvertiserDto> flyFishAdvertiserList = Lists.newArrayList();
        accountIds.forEach((accountId -> {
            BaseFlyFishResponse<BaseFlyFishListResponse<FlyFishAdvertiserDto>> response = leadsTpFlyFishApiService.getAdvertiser(accessToken, accountId.toString());
            if (ObjectUtils.isEmpty(response) || !response.getCode().equals(0) || ObjectUtils.isEmpty(response.getData())) {
                return;
            }

            flyFishAdvertiserList.addAll(response.getData().getList().stream().peek(advertiser ->
                    advertiser.setParentAdvertiserId(accountId.toString())).collect(Collectors.toList()));
        }));

        return flyFishAdvertiserList;

    }

    /**
     * 刷新token
     *
     * @param request
     */
    public void refreshToken(BaseBidOptRequest request) {
        List<LeadsTpOauth> list = leadsTpOauthManager.list(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.TYPE,LeadsTpTypeEnum.FEI_YU.getCode()));
        if (CollectionUtils.isEmpty(list)){
            return;
        }

        AsyncUtils.runAsync(() ->
                list.forEach(leadsTpOauth -> {
                    RefreshFlyFishTokenRequest refreshRequest = new RefreshFlyFishTokenRequest();
                    refreshRequest.setRefreshToken(leadsTpOauth.getRefreshToken());
                    refreshRequest.setAppId(leadsTpFlyFishConfig.getClientId());
                    refreshRequest.setSecret(leadsTpFlyFishConfig.getClientSecret());
                    refreshRequest.setGrantType("refresh_token");

                    BaseFlyFishResponse<FlyFishAccessTokenDto> response =
                            leadsTpFlyFishApiService.refreshAccessToken(refreshRequest);
                    FlyFishAccessTokenDto flyFishAccessTokenDto;
                    try {
                        flyFishAccessTokenDto = FlyFishApiUtils.checkResponse(response);
                    }catch (Exception e){
                        logger.info("refresh error",e);
                        return;
                    }

                    LeadsTpOauth oauthData = createOauthData(leadsTpOauth.getBid(), flyFishAccessTokenDto);
                    oauthData.setAccountId(leadsTpOauth.getAccountId());

                    leadsTpOauthManager.saveToken(leadsTpOauth.getBid(), oauthData);
                    cacheToken(leadsTpOauth.getBid(), oauthData.getAccountId(), oauthData.getAccessToken());
                })
        );

        BaseResponse.success(true);
    }
}

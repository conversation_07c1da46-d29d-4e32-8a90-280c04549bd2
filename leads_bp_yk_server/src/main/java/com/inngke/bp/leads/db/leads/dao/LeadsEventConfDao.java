/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsEventConf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 线索事件配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsEventConfDao extends BaseMapper<LeadsEventConf> {

}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.service.LeadsBackReasonInitService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@RestController
@RequestMapping("/leadsBackReason")
public class LeadsBackReasonController {

    @Autowired
    private LeadsBackReasonInitService leadsBackReasonInitService;

    /**
     * 系统默认的初始化退回原因：
     ○ 48小时5呼无法呼通
     ○ 小广告、商务合作-要代理或招商
     ○ 项目采集
     ○ 售后投诉
     ○ 非代理产品
     ○ 其他
     *
     * @return
     */
    @PostMapping("/init")
    public BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> init(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        return leadsBackReasonInitService.init();
    }
}


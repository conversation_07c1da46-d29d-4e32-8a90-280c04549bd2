/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsLog;
import com.inngke.bp.leads.db.leads.dao.LeadsLogDao;
import com.inngke.bp.leads.db.leads.manager.LeadsLogManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 线索变更日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsLogManagerImpl extends ServiceImpl<LeadsLogDao, LeadsLog> implements LeadsLogManager {

    @Override
    public void batchSaveLeadsLog(List<LeadsLog> logs) {
        this.saveBatch(logs);
    }
}

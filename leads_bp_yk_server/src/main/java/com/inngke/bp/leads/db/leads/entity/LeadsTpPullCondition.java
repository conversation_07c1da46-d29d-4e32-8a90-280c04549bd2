/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 拉取第三方平台数据条件
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsTpPullCondition implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户Id
     */
    private Integer bid;

    /**
     * 1:腾讯广告 2:飞鱼
     */
    private Integer type;

    /**
     * 字段
     */
    private String fields;

    /**
     * 1:等于 0:不等于
     */
    private Boolean equal;

    /**
     * 值
     */
    private String valueList;

    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String TYPE = "type";

    public static final String FIELDS = "fields";

    public static final String EQUAL = "equal";

    public static final String VALUE_LIST = "value_list";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

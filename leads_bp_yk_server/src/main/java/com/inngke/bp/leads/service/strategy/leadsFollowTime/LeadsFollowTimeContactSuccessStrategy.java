package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.common.utils.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeContactSuccessStrategy")
public class LeadsFollowTimeContactSuccessStrategy implements LeadsFollowTimeStrategy {
    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateContactSuccess(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateContactSuccess",
                        QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("stateContactSuccess").gt(0)))
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateContactSuccess = bucketAggregations.get("stateContactSuccess");
        long count = stateContactSuccess.getDocCount();
        dto.setStateContactSuccess((int)count);
        if(dto.getLeadsCount()==0){
            return;
        }
        String rate = BigDecimal.valueOf(count).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getLeadsCount()),2, BigDecimal.ROUND_HALF_UP).toString();
        dto.setContactSuccessRate(rate+"%");
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateContactSuccess).reduce(Integer::sum).get();
        dto.setStateContactSuccess(sum);
        if(dto.getLeadsCount()==0){
            return;
        }
        String rate = BigDecimal.valueOf(sum).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getLeadsCount()),2, RoundingMode.HALF_UP).toString();
        dto.setContactSuccessRate(rate+"%");
    }
}

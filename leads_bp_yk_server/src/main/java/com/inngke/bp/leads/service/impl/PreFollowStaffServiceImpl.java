package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.LoginClientForLeads;
import com.inngke.bp.leads.client.RbacClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPreFollowConfigManager;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.service.PreFollowStaffService;
import com.inngke.bp.leads.utils.DecimalUtils;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.organize.service.DepartmentService;
import com.inngke.bp.organize.utils.DepartmentUtil;
import com.inngke.bp.store.dto.request.QueryStoreOrderCountMapRequest;
import com.inngke.bp.store.dto.response.StoreOrderAggDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.bp.store.dto.request.QueryStoreOrderCountMapRequest;
import com.inngke.bp.store.dto.response.StoreOrderAggDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeErrorException;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.request.RegionListRequest;
import com.inngke.ip.common.dto.response.SimpleRegionDto;
import com.inngke.ip.common.service.RegionService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/8 8:33 PM
 */
@Service
@DubboService(version = "1.0.0")
public class PreFollowStaffServiceImpl implements PreFollowStaffService {


    @Autowired
    private LeadsPreFollowConfigManager leadsPreFollowConfigManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private RegionService regionService;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 客服分配版本号
     */
    private String PRE_FOLLOW_VERSION = LeadsServiceConsts.APP_ID + ":preFollow:version:";

    /**
     * 客服分配数据前缀
     */
    private String PRE_FOLLOW_DATA = LeadsServiceConsts.APP_ID + ":preFollow:data:";

    private String PRE_FOLLOW_LOCK = LeadsServiceConsts.APP_ID + "lock:preFollow:";

    @Autowired
    private LockService lockService;

    /**
     * 12小时
     */
    private Integer PRE_FOLLOW_EXPIRE_TIME = 12;


    @Autowired
    protected LeadsManager leadsManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private DepartmentService departmentService;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store_bp_yk:}")
    private StoreOrderService storeOrderService;

    @Autowired
    private LoginClientForLeads loginClientForLeads;



    private String ERROR_MSG = "网络异常，请稍后重试";

    @Override
    public BaseResponse<List<SimplePreFollowStaffDto>> list(PreFollowStaffListRequest request) {
        //查询员工管理权限
        List<Long> mangerDeptIds = loginClientForLeads.getMangerDeptIds(request.getBid(), request.getOperatorId());
        List<UserStaffDto> userStaffDtoList = staffClientForLeads.getStaffByDeptIds(request.getBid(), Sets.newHashSet(mangerDeptIds));
        List<Long> staffIds = userStaffDtoList.stream().map(UserStaffDto::getId).collect(Collectors.toList());
        List<SimplePreFollowStaffDto> simplePreFollowStaffDtoList = Lists.newArrayList();
        if (StringUtils.isEmpty(request.getKeyword())) {
            simplePreFollowStaffDtoList= listAll(request);
        } else {
            simplePreFollowStaffDtoList = listByName(request);
        }

        return BaseResponse.success(filter(staffIds, simplePreFollowStaffDtoList));
    }

    private List<SimplePreFollowStaffDto> filter(List<Long> staffIds, List<SimplePreFollowStaffDto> simplePreFollowStaffDtoList) {
        return simplePreFollowStaffDtoList.stream().filter(simplePreFollowStaffDto -> staffIds.contains(simplePreFollowStaffDto.getStaffId())).collect(Collectors.toList());
    }

    private List<SimplePreFollowStaffDto> listAll(PreFollowStaffListRequest request) {

        Set<Long> staffs = rbacClientForLeads.listCustomerRoleUserIds(request.getBid());

        // 查询员工名称
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(request.getBid(), staffs);
        if (CollectionUtils.isEmpty(staffByIds)) {
            return Lists.newArrayList();
        }

        List<SimplePreFollowStaffDto> result = new ArrayList<>(staffs.size());

        staffByIds.forEach((staffId, staffDto) -> result.add(simplePreFollowStaffDto(staffDto)));
        return Lists.reverse(result);
    }

    private SimplePreFollowStaffDto simplePreFollowStaffDto(StaffDto staffDto) {
        SimplePreFollowStaffDto result = new SimplePreFollowStaffDto();
        result.setStaffId(staffDto.getId());
        result.setName(staffDto.getName());
        return result;

    }

    private List<SimplePreFollowStaffDto> listByName(PreFollowStaffListRequest request) {
        // 查询员工
        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(request.getBid());
        staffListRequest.setName(request.getKeyword());
        List<StaffDto> qyWxStaffSimpleInfos = staffClientForLeads.getStaffList(staffListRequest);
        if (CollectionUtils.isEmpty(qyWxStaffSimpleInfos)) {
            return Lists.newArrayList();
        }

        // 查询客服
        Set<Long> staffs = rbacClientForLeads.listCustomerRoleUserIds(request.getBid());

        List<SimplePreFollowStaffDto> result = new ArrayList<>(qyWxStaffSimpleInfos.size());

        qyWxStaffSimpleInfos.forEach(item -> {
            if (staffs.contains(item.getId())) {
                result.add(simplePreFollowStaffDto(item));
            }
        });

        return Lists.reverse(result);
    }

    @Override
    public BaseResponse<Boolean> addRule(PreFollowStaffAddRuleRequest request) {
        if (request.getName().length() > 5) {
            return BaseResponse.error("规则名称字符数量最多5个");
        }
        checkChannelAndRegion(request.getChannelIds(), request.getBid(), request.getStaffId(), null);

        Map<Integer, Integer> regionParenMap = regionParenMap();

        Set<String> regionsId = removeRegionChildren(request.getRegionIds(), regionParenMap);

        LeadsPreFollowConfig leadsPreFollowStaff = new LeadsPreFollowConfig();
        leadsPreFollowStaff.setId(snowflakeIdService.getId());
        leadsPreFollowStaff.setName(request.getName());
        leadsPreFollowStaff.setBid(request.getBid());
        leadsPreFollowStaff.setStaffId(request.getStaffId());
        leadsPreFollowStaff.setChannelIds(setToStr(request.getChannelIds()));
        leadsPreFollowStaff.setRegionIds(setToStr(regionsId));
        leadsPreFollowStaff.setCreateTime(LocalDateTime.now());


        boolean save = leadsPreFollowConfigManager.save(leadsPreFollowStaff);

        if (save) {
            incPreFollowVersion(request.getBid(), regionParenMap);
        }

        return BaseResponse.success(save);
    }

    /**
     * 判断渠道和区域是否已经添加
     */
    private void checkChannelAndRegion(Set<String> channels, int bid, Long staffId, Long excludeIds) {
        GetStaffAllChannelAndRegionDto staffAllChannelAndRegion = getStaffAllChannelAndRegion(bid, staffId, excludeIds);

        Set<String> channelIds = staffAllChannelAndRegion.getChannelIds();

        int channelSize = channelIds.size();

        channelIds.removeAll(channels);
        if (channelSize != channelIds.size()) {
            throw new InngkeErrorException("已有规则内已选的渠道不可再选择");
        }
    }


    /**
     * 查询员工所有的渠道和区域
     *
     * @param bid        bid
     * @param staffId    staffId
     * @param excludeIds 要排除的主键
     */
    private GetStaffAllChannelAndRegionDto getStaffAllChannelAndRegion(int bid, Long staffId, Long excludeIds) {

        List<LeadsPreFollowConfig> list = leadsPreFollowConfigManager.list(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, bid)
                .eq(LeadsPreFollowConfig.STAFF_ID, staffId)
                .ne(excludeIds != null, LeadsPreFollowConfig.ID, excludeIds));

        HashSet<String> channels = new HashSet<>();

        GetStaffAllChannelAndRegionDto result = new GetStaffAllChannelAndRegionDto();
        result.setChannelIds(channels);

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        list.forEach(item -> channels.addAll(strToSet(item.getChannelIds())));

        return result;
    }


    private Set<String> strToSet(String str) {
        if (StringUtils.isEmpty(str)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(str.split(InngkeAppConst.COMMA_STR));
    }

    private String setToStr(Set<String> set) {
        if (CollectionUtils.isEmpty(set)) {
            return InngkeAppConst.EMPTY_STR;
        }
        return String.join(InngkeAppConst.COMMA_STR, set);
    }


    @Override
    public BaseResponse<Boolean> updateRule(PreFollowStaffUpdateRuleRequest request) {

        LeadsPreFollowConfig one = leadsPreFollowConfigManager.getOne(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, request.getBid())
                .eq(LeadsPreFollowConfig.ID, request.getId()));
        if (one == null) {
            return BaseResponse.error("规则不存在");
        }
        Map<Integer, Integer> regionParenMap = regionParenMap();

        Set<String> regionsId = removeRegionChildren(request.getRegionIds(), regionParenMap);
        request.setRegionIds(regionsId);

        if (checkUpdateNoChange(request, one)) {
            return BaseResponse.success(true);
        }

        checkChannelAndRegion(request.getChannelIds(), request.getBid(), one.getStaffId(), request.getId());


        LeadsPreFollowConfig leadsPreFollowConfig = new LeadsPreFollowConfig();
        leadsPreFollowConfig.setId(request.getId());
        leadsPreFollowConfig.setRegionIds(setToStr(regionsId));
        leadsPreFollowConfig.setChannelIds(setToStr(request.getChannelIds()));
        leadsPreFollowConfig.setName(request.getName());


        boolean updateById = leadsPreFollowConfigManager.updateById(leadsPreFollowConfig);
        if (updateById) {
            incPreFollowVersion(request.getBid(), regionParenMap);
        }

        return BaseResponse.success(updateById);
    }

    private boolean checkUpdateNoChange(PreFollowStaffUpdateRuleRequest request, LeadsPreFollowConfig one) {
        return one.getName().equals(request.getName()) &&
                strToSet(one.getRegionIds()).equals(request.getRegionIds()) &&
                strToSet(one.getChannelIds()).equals(request.getChannelIds());
    }


    @Override
    public BaseResponse<List<SimpleChannelDto>> getChooseChannelsByStaffId(int bid, Long id) {
        return null;
    }

    @Override
    public BaseResponse<List<SimplePreFollowStaffRuleDto>> getRulesByStaffId(int bid, Long id) {

        List<LeadsPreFollowConfig> list = leadsPreFollowConfigManager.list(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, bid)
                .eq(LeadsPreFollowConfig.STAFF_ID, id)
                .orderByDesc(LeadsPreFollowConfig.ID));

        List<SimplePreFollowStaffRuleDto> result = new ArrayList<>();

        list.forEach(item -> {
            SimplePreFollowStaffRuleDto dto = new SimplePreFollowStaffRuleDto();
            dto.setId(item.getId());
            dto.setName(item.getName());
            result.add(dto);
        });


        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<FollowStaffRuleDto> getRuleDetail(int bid, Long id) {

        LeadsPreFollowConfig byId = leadsPreFollowConfigManager.getById(id);
        if (byId == null) {
            return BaseResponse.error("规则不存在");
        }

        Map<Integer, Set<String>> parentChildrenRegion = regionChildren();

        FollowStaffRuleDto result = regionsCompensate(byId, parentChildrenRegion);

        return BaseResponse.success(result);
    }

    private FollowStaffRuleDto regionsCompensate(LeadsPreFollowConfig leadsPreFollowConfig, Map<Integer, Set<String>> parentChildrenRegion) {
        FollowStaffRuleDto result = new FollowStaffRuleDto();

        result.setId(leadsPreFollowConfig.getId());
        result.setName(leadsPreFollowConfig.getName());

        // 处理添加时去除了父类的子类
        Set<String> regions = strToSet(leadsPreFollowConfig.getRegionIds());
        Set<String> regionsResult = new HashSet<>(regions);
        regionsCompensate(regionsResult, regions, parentChildrenRegion);
        result.setRegionIds(regionsResult);

        result.setChannelIds(strToSet(leadsPreFollowConfig.getChannelIds()));

        return result;
    }

    @Override
    public BaseResponse<SimplePreFollowStaffDto> getPreFollowStaff(GetPreFollowStaffRequest request) {
        Set<Long> staffs = rbacClientForLeads.listCustomerRoleUserIds(request.getBid());
        SimplePreFollowStaffDto preFollowStaffDto = new SimplePreFollowStaffDto();
        if (staffs.contains(request.getStaffId())) {
            preFollowStaffDto.setStaffId(request.getStaffId());
        }
        return BaseResponse.success(preFollowStaffDto);
    }


    @Override
    public BaseResponse<List<PreFollowStaffReportDto>> getPreFollowStaffReport(GetPreFollowStaffReportRequest request) {
        if (StringUtils.isEmpty(request.getStartTime()) || StringUtils.isEmpty(request.getEndTime())) {
            throw new InngkeServiceException("成交时间为空");
        }
        appendTime(request);
        List<Long> staffIds = getPreFollowStaffIds(request.getBid(), request.getId());
        if (CollectionUtils.isEmpty(staffIds)) {
            BaseResponse.success();
        }

        Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(request.getBid(), Sets.newHashSet(staffIds));
        List<PreFollowStaffReportDto> preFollowStaffReportDtoList = leadsManager.listByPreFollowStaffIds(request, staffIds);
        //查询成交数和总金额
        buildReportReponse(request.getBid(), staffMap, preFollowStaffReportDtoList);
        DepartmentUtil.translateName(departmentService, request.getBid(), preFollowStaffReportDtoList);
        //合并线索相同的员工
        List<PreFollowStaffReportDto> preFollowStaffReportAggDtoList = combine(preFollowStaffReportDtoList);
        formatDecimal(preFollowStaffReportAggDtoList);
        return BaseResponse.success(preFollowStaffReportAggDtoList);
    }

    private void formatDecimal(List<PreFollowStaffReportDto> preFollowStaffReportAggDtoList) {
        preFollowStaffReportAggDtoList.forEach(preFollowStaffReportDto -> {
            preFollowStaffReportDto.setDealTotalAmount(DecimalUtils.decimalFormat(new BigDecimal(Objects.isNull(preFollowStaffReportDto.getDealTotalAmount()) ? "0" : preFollowStaffReportDto.getDealTotalAmount())));
        });
    }

    private void buildReportReponse(Integer bid, Map<Long, StaffDto> staffMap, List<PreFollowStaffReportDto> preFollowStaffReportDtoList) {
        Map<String, StoreOrderAggDto> storeAggMap = getStoreAggMap(bid, preFollowStaffReportDtoList);
        preFollowStaffReportDtoList.forEach(dto -> {
            if (Objects.nonNull(storeAggMap.get(dto.getMobile()))) {
                dto.setDealCount(storeAggMap.get(dto.getMobile()).getOrderCount());
                dto.setDealTotalAmount(storeAggMap.get(dto.getMobile()).getTotalAmount().toString());
            }
            if (staffMap.get(dto.getStaffId()) != null) {
                dto.setMobile(staffMap.get(dto.getStaffId()).getMobile());
                dto.setName(staffMap.get(dto.getStaffId()).getName());
                dto.setDeptId(staffMap.get(dto.getStaffId()).getDepartmentId());
            }
        });
    }

    private List<PreFollowStaffReportDto> combine(List<PreFollowStaffReportDto> preFollowStaffReportDtoList) {
        Map<Long, List<PreFollowStaffReportDto>> PreFollowStaffReportMap = preFollowStaffReportDtoList.stream()
                .collect(Collectors.groupingBy(PreFollowStaffReportDto::getStaffId, Collectors.mapping(Function.identity(), Collectors.toList())));

        List<PreFollowStaffReportDto> preFollowStaffReportList = Lists.newArrayList();
        for (Map.Entry<Long, List<PreFollowStaffReportDto>> entry : PreFollowStaffReportMap.entrySet()) {
            Long staffId = entry.getKey();
            PreFollowStaffReportDto preFollowStaffReportDtoAgg = new PreFollowStaffReportDto();

            preFollowStaffReportDtoAgg.setStaffId(staffId);
            preFollowStaffReportDtoAgg.setName(entry.getValue().get(0).getName());
            preFollowStaffReportDtoAgg.setMobile(entry.getValue().get(0).getMobile());
            preFollowStaffReportDtoAgg.setDeptId(entry.getValue().get(0).getDeptId());
            preFollowStaffReportDtoAgg.setDeptName(entry.getValue().get(0).getDeptName());
            entry.getValue().forEach(preFollowStaffReportDto -> {
                preFollowStaffReportDtoAgg.setDealCount(cumsumInt(preFollowStaffReportDtoAgg.getDealCount(), preFollowStaffReportDto.getDealCount()));
                preFollowStaffReportDtoAgg.setDealTotalAmount(cumsumBigdemical(preFollowStaffReportDtoAgg.getDealTotalAmount(), preFollowStaffReportDto.getDealTotalAmount()));
            });
            preFollowStaffReportList.add(preFollowStaffReportDtoAgg);
        }
        return preFollowStaffReportList;
    }

    private String cumsumBigdemical(String initDealTotalAmount, String dealTotalAmount) {
        if (StringUtils.isEmpty(initDealTotalAmount)) {
            return dealTotalAmount;
        } else {
            dealTotalAmount = StringUtils.isEmpty(dealTotalAmount) ? "0" : dealTotalAmount;
            return new BigDecimal(initDealTotalAmount).add(new BigDecimal(dealTotalAmount)).toString();
        }
    }

    private int cumsumInt(Integer initCount, Integer count) {
        if (Objects.isNull(initCount)) {
            return count;
        } else {
            return initCount + count;
        }

    }

    private Map<String, StoreOrderAggDto> getStoreAggMap(Integer bid, List<PreFollowStaffReportDto> preFollowStaffReportDtoList) {
        List<String> mobileList = preFollowStaffReportDtoList.stream().map(PreFollowStaffReportDto::getMobile).collect(Collectors.toList());
        QueryStoreOrderCountMapRequest queryStoreOrderCountMapRequest = new QueryStoreOrderCountMapRequest();
        queryStoreOrderCountMapRequest.setBid(bid);
        queryStoreOrderCountMapRequest.setMobileList(mobileList);
        queryStoreOrderCountMapRequest.setType(2);
        BaseResponse<Map<String, StoreOrderAggDto>> response = storeOrderService.getStoreOrderAggMap(queryStoreOrderCountMapRequest);
        if (BaseResponse.responseErrorOrNullData(response)) {
            return Maps.newHashMap();
        }
        return response.getData();
    }

    private void appendTime(GetPreFollowStaffReportRequest request) {
        request.setStartTime(request.getStartTime() + " 00:00:00");
        request.setEndTime(request.getEndTime() + " 23:59:59");
    }

    private List<Long> getPreFollowStaffIds(Integer bid, Long staffId) {
        if (staffId != null) {
            LeadsPreFollowConfig leadsPreFollowConfig = leadsPreFollowConfigManager.getByStaffId(bid, staffId);
            if (leadsPreFollowConfig == null) {
                throw new InngkeServiceException("客服不存在");
            }
            return Lists.newArrayList(leadsPreFollowConfig.getStaffId());
        }
        return leadsPreFollowConfigManager.list(Wrappers.<LeadsPreFollowConfig>query()
                .select("distinct(staff_id)")
        ).stream().map(LeadsPreFollowConfig::getStaffId).collect(Collectors.toList());
    }

    @Override
    public BaseResponse<GetStaffAllChannelAndRegionDto> staffChannelRegion(int bid, Long id) {
        return BaseResponse.success(getStaffAllChannelAndRegion(bid, id, null));
    }


    /**
     * 获取客服版本号
     */
    @Override
    public String getPreFollowVersion(int bid) {
        String key = PRE_FOLLOW_VERSION + bid;
        String version = ObjectUtils.isEmpty(redisTemplate.opsForValue().get(key)) ? null : String.valueOf(redisTemplate.opsForValue().get(key));
        if (StringUtils.isEmpty(version)) {
            return incPreFollowVersionLock(bid);
        }
        Long expire = redisTemplate.getExpire(key);
        // 时间快到的时候直接刷新
        if (expire.compareTo(60L) <= 0) {
            return incPreFollowVersionLock(bid);
        }
        return version;
    }

    private String incPreFollowVersionLock(int bid) {
        Lock lock = lockService.getLock(PRE_FOLLOW_LOCK + bid, 15);
        if (lock == null) {
            throw new InngkeErrorException("正在更新数据，请稍后再试");
        }
        try {
            return incPreFollowVersion(bid);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 去除区域的子Id
     *
     * @param regionIds 区域集合
     * @return 区域子Id的集合
     */
    private Set<String> removeRegionChildren(Set<String> regionIds, Map<Integer, Integer> regionParenMap) {
        if (CollectionUtils.isEmpty(regionIds) || regionIds.size() == 1) {
            return regionIds;
        }


        Set<String> children = new HashSet<>(regionIds.size());

        regionIds.forEach(regionId -> {
            Integer regionParentId = regionParenMap.get(Integer.valueOf(regionId));
            if (regionParentId != null && !regionParentId.equals(0) && regionIds.contains(String.valueOf(regionParentId))) {
                children.add(regionId);
            }
        });

        regionIds.removeAll(children);

        return regionIds;
    }

    /**
     * 设置客服版本号
     */
    @Deprecated
    private String incPreFollowVersion1(int bid) {
        String key = PRE_FOLLOW_VERSION + bid;
        String version = String.valueOf(redisTemplate.opsForValue().increment(key));
        redisTemplate.expire(key, PRE_FOLLOW_EXPIRE_TIME, TimeUnit.HOURS);
        refreshPreFollow(bid, version);
        return version;
    }

    @Override
    public String incPreFollowVersion(int bid) {
        Map<Integer, Integer> regionParenMap = regionParenMap();
        return incPreFollowVersion(bid, regionParenMap);
    }

    @Override
    public BaseResponse<List<PreFollowStaffAndSetDto>> preFollowAndSet(PreFollowStaffListRequest request) {
        BaseResponse<List<SimplePreFollowStaffDto>> list = list(request);
        if (!BaseResponse.responseSuccess(list)) {
            return BaseResponse.error(list.getMsg());
        }

        List<SimplePreFollowStaffDto> data = list.getData();
        if (CollectionUtils.isEmpty(data)) {
            return BaseResponse.success(new ArrayList<>());
        }

        List<Long> staffIds = data.stream().map(SimplePreFollowStaffDto::getStaffId).collect(Collectors.toList());
        List<PreFollowStaffAndSetDto> result = data.stream().map(PreFollowStaffAndSetDto::simplePreFollowStaffDto).collect(Collectors.toList());

        List<LeadsPreFollowConfig> byStaffIds = leadsPreFollowConfigManager.getByStaffIds(request.getBid(), new HashSet<>(staffIds));

        Map<Long, List<LeadsPreFollowConfig>> staffPreFollowConfig = byStaffIds.stream().collect(Collectors.groupingBy(LeadsPreFollowConfig::getStaffId));

        Map<Integer, Set<String>> parentChildrenRegion = regionChildren();

        result.forEach(item -> {
            List<LeadsPreFollowConfig> leadsPreFollowConfigs = staffPreFollowConfig.getOrDefault(item.getStaffId(), new ArrayList<>(0));
            List<FollowStaffRuleDto> configs = new ArrayList<>();
            leadsPreFollowConfigs.forEach(leadsPreFollowConfig -> {
                FollowStaffRuleDto followStaffRuleDto = regionsCompensate(leadsPreFollowConfig, parentChildrenRegion);
                configs.add(followStaffRuleDto);
            });
            item.setRuleDtoList(configs);
        });

        return BaseResponse.success(result);
    }

    private String incPreFollowVersion(int bid, Map<Integer, Integer> regionParenMap) {
        String key = PRE_FOLLOW_VERSION + bid;
        String version = String.valueOf(redisTemplate.opsForValue().increment(key));
        redisTemplate.expire(key, PRE_FOLLOW_EXPIRE_TIME, TimeUnit.HOURS);

        List<LeadsPreFollowConfig> list = getAllLeadsPreFollowConfig(bid);

        Map<String, Map<String, Set<Long>>> channelRegionStaffsCache = channelRegionStaffsCache(list);

        channelRegionStaffsCache.forEach((channel, regionStaffs) -> {
            regionStaffs.forEach((region, staffs) -> {
                // 获取父类Id
                Integer parent1 = regionParenMap.get(Integer.valueOf(region));
                Integer parent2 = parent1 != null ? regionParenMap.get(parent1) : null;
                if (parent1 != null && !parent1.equals(0) && regionStaffs.get(String.valueOf(parent1)) != null) {
                    staffs.addAll(regionStaffs.get(String.valueOf(parent1)));
                }
                if (parent2 != null && !parent2.equals(0) && regionStaffs.get(String.valueOf(parent2)) != null) {
                    staffs.addAll(regionStaffs.get(String.valueOf(parent2)));
                }
            });
        });

        refreshPreFollow(bid, version, channelRegionStaffsCache);

        return version;
    }

    private List<LeadsPreFollowConfig> getAllLeadsPreFollowConfig(int bid) {
        Set<Long> staffs = rbacClientForLeads.listCustomerRoleUserIds(bid);
        // 账号状态为已开通的才会启用接受规则
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffs);

        Set<Long> staffIds = new HashSet<>(staffByIds.size());

        staffByIds.forEach((staffId, staffDto) -> {
            if (staffDto.getStatus().equals(StaffStatusEnum.OPENED.getCode())) {
                staffIds.add(staffId);
            }
        });

        if (CollectionUtils.isEmpty(staffIds)) {
            return Lists.newArrayList();
        }
        return leadsPreFollowConfigManager.list(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, bid)
                .in(LeadsPreFollowConfig.STAFF_ID, staffIds));
    }


    /**
     * 获取区域对应的父类Id
     *
     * @return Map<regionId, regionParentId>
     */
    private Map<Integer, Integer> regionParenMap() {
        RegionListRequest regionListRequest = new RegionListRequest();
        regionListRequest.setBid(1);
        BaseResponse<List<SimpleRegionDto>> threeLevelRegion = regionService.getThreeLevelRegion(regionListRequest);
        if (!BaseResponse.responseSuccess(threeLevelRegion)) {
            throw new InngkeErrorException("获取区域异常");
        }
        List<SimpleRegionDto> data = threeLevelRegion.getData();
        Map<Integer, Integer> regionParenMap = new HashMap<>();

        getParent(regionParenMap, data, 0);

        return regionParenMap;
    }

    /**
     * 查询出父类的子类并全部添加进去
     */
    private void regionsCompensate(Set<String> result, Set<String> regions, Map<Integer, Set<String>> parentChildrenRegion) {
        if (CollectionUtils.isEmpty(regions)) {
            return;
        }
        for (String region : regions) {
            Set<String> integers = parentChildrenRegion.get(Integer.valueOf(region));
            regionsCompensate(result, integers, parentChildrenRegion);
            if (!CollectionUtils.isEmpty(integers)) {
                result.addAll(integers);
            }
        }
    }

    /**
     * 获取区域父类对于的子类
     *
     * @return
     */
    private Map<Integer, Set<String>> regionChildren() {
        RegionListRequest regionListRequest = new RegionListRequest();
        regionListRequest.setBid(1);
        BaseResponse<List<SimpleRegionDto>> threeLevelRegion = regionService.getThreeLevelRegion(regionListRequest);
        if (!BaseResponse.responseSuccess(threeLevelRegion)) {
            throw new InngkeErrorException("获取区域异常");
        }
        List<SimpleRegionDto> data = threeLevelRegion.getData();
        Map<Integer, Set<String>> result = new HashMap<>();
        regionChildren(result, data, new HashSet<>(),true);
        return result;
    }

    private void regionChildren(Map<Integer, Set<String>> result, List<SimpleRegionDto> data, Set<String> children, boolean isRoot) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        for (SimpleRegionDto simpleRegionDto : data) {
            if (!isRoot) {
                children.add(String.valueOf(simpleRegionDto.getValue()));
            }
            Set<String> integers = result.computeIfAbsent(simpleRegionDto.getValue(), item -> new HashSet<>());
            regionChildren(result, simpleRegionDto.getChildren(), integers, false);
        }
    }

    private void getParent(Map<Integer, Integer> parentMap, List<SimpleRegionDto> data, Integer parentId) {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(data)) {
            return;
        }
        for (SimpleRegionDto datum : data) {
            parentMap.put(datum.getValue(), parentId);
            getParent(parentMap, datum.getChildren(), datum.getValue());
        }
    }


    private Map<String, Map<String, Set<Long>>> channelRegionStaffsCache(List<LeadsPreFollowConfig> list) {

        Map<String, Map<String, Set<Long>>> result = new HashMap<>(list.size());

        list.forEach(leadsPreFollowConfig -> {
            Set<String> channels = strToSet(leadsPreFollowConfig.getChannelIds());
            channels.forEach(channel -> {
                Set<String> regions = strToSet(leadsPreFollowConfig.getRegionIds());
                regions.forEach(region -> {
                    Map<String, Set<Long>> regionStaffMap = result.computeIfAbsent(channel, k -> new HashMap<>());
                    Set<Long> staffs = regionStaffMap.computeIfAbsent(region, k -> new HashSet<>());
                    staffs.add(leadsPreFollowConfig.getStaffId());
                });
            });
        });

        return result;
    }


    @Override
    public Long getPreFollow(int bid, String version, Integer channelId, Integer regionId) {
        String key = getPreFollowKey(bid, version, channelId, regionId);
        Set<String> range = redisTemplate.opsForZSet().range(key, 0, 0);
        if (CollectionUtils.isEmpty(range)) {
            return null;
        }
        String next = String.valueOf(range.iterator().next());
        redisTemplate.opsForZSet().incrementScore(key, next, 1);
        return Long.valueOf(next);
    }

    @Override
    public BaseResponse<Boolean> removePreFollowConfig(BaseIdsRequest request) {
        boolean remove = leadsPreFollowConfigManager.remove(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, request.getBid())
                .in(LeadsPreFollowConfig.STAFF_ID, request.getIds()));

        return BaseResponse.success(remove);
    }

    /**
     * 获取客服数据的Key
     */
    private String getPreFollowKey(int bid, String version, Integer channelId, Integer regionId) {
        return PRE_FOLLOW_DATA + bid + ":" + version + ":" + channelId + ":" + regionId;
    }

    private void refreshPreFollow(int bid, String version, Map<String, Map<String, Set<Long>>> channelRegionStaffsCache) {

        ZSetOperations<String, String> zSet = redisTemplate.opsForZSet();

        channelRegionStaffsCache.forEach((channel, regionStaffs) -> {

            regionStaffs.forEach((region, staffIds) -> staffIds.forEach(staffId -> {

                String key = getPreFollowKey(bid, version, Integer.valueOf(channel), Integer.valueOf(region));

                zSet.add(key, String.valueOf(staffId), 1);
                redisTemplate.expire(key, PRE_FOLLOW_EXPIRE_TIME, TimeUnit.HOURS);

            }));
        });

    }

    /**
     * 刷新客服数据
     */
    private void refreshPreFollow(int bid, String version) {
        List<LeadsPreFollowConfig> list = leadsPreFollowConfigManager.list(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, bid));

        ZSetOperations<String, String> zSet = redisTemplate.opsForZSet();

        list.forEach(item -> {
            Set<String> regions = strToSet(item.getRegionIds());
            Set<String> channels = strToSet(item.getChannelIds());
            regions.forEach(region -> channels.forEach(channel -> {

                String key = getPreFollowKey(bid, version, Integer.valueOf(channel), Integer.valueOf(region));

                zSet.add(key, String.valueOf(item.getStaffId()), 1);
                redisTemplate.expire(key, PRE_FOLLOW_EXPIRE_TIME, TimeUnit.HOURS);

            }));
        });
    }


}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.db.leads.entity.AllocationStaffRule;
import com.inngke.bp.leads.db.leads.dao.AllocationStaffRuleDao;
import com.inngke.bp.leads.db.leads.manager.AllocationStaffRuleManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 分配客服规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
@Service
public class AllocationStaffRuleManagerImpl extends ServiceImpl<AllocationStaffRuleDao, AllocationStaffRule> implements AllocationStaffRuleManager {

}

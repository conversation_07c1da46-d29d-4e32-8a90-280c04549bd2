package com.inngke.bp.leads.mq.process.order;

import com.inngke.bp.leads.mq.message.order.OrderChangeMessageDto;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/9/17 15:38 PM
 */
@Service
public class LeadsShopOrderProcess extends BaseOrderProcess {

    /**
     * 需要处理的订单类型
     *
     * @return 订单类型
     */
    @Override
    public Integer getProcessOrderType() {
        return 1;
    }

    /**
     * 是否需要处理
     *
     * @param message 消息体
     * @return 是否需要继续处理
     */
    @Override
    public boolean match(OrderChangeMessageDto message) {
        return message.getEvent() == 1;
    }

    /**
     * 处理逻辑
     *
     * @param message 消息体
     */
    @Override
    public void process(OrderChangeMessageDto message) {
        this.changeLeadsStatus(message);
    }
}

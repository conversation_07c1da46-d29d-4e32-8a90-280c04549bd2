package com.inngke.bp.leads.core.converter;

import com.inngke.bp.content.dto.response.ContentDto;
import com.inngke.bp.leads.dto.response.EntityIdNameDto;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class LeadsGoodsConverter {
    public static EntityIdNameDto toEntityIdNameDto(ContentDto shopGoods) {
        EntityIdNameDto entityIdNameDto = new EntityIdNameDto();
        entityIdNameDto.setId(shopGoods.getId());
        entityIdNameDto.setName(shopGoods.getTitle());
        Optional.ofNullable(shopGoods.getCategoryNames()).ifPresent(categoryNames->{
            if (!CollectionUtils.isEmpty(categoryNames)){
                entityIdNameDto.setClassifyNames((List<String>) categoryNames.stream().collect(Collectors.toList()));
            }
        });
        return entityIdNameDto;
    }
}

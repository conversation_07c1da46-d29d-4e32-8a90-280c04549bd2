package com.inngke.bp.leads.mq.process.client;

import com.inngke.bp.client.enums.ClientBusinessEnum;
import com.inngke.bp.leads.client.MqServiceForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.LeadsTransferResultDto;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.enums.LeadsTransferEnum;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ClientChangeTransferProcess implements ClientChangeProcess{

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    @Override
    public void process(ClientTransferMessage clientTransferMessage) {
        log.info("message content: {}", jsonService.toJson(clientTransferMessage));
        String business = clientTransferMessage.getBusiness();
        if (!String.valueOf(ClientBusinessEnum.CLIENT_TRANSFER_OF_CLIENT.getBusinessCode()).equals(business)) {
            log.info("丢弃");
            return;
        }

        Long targetStaffId = clientTransferMessage.getTargetStaffId();
        StaffDto targetStaff = staffClientForLeads.getStaffById(clientTransferMessage.getBid(), targetStaffId);
        if (Objects.isNull(targetStaff)) {
            log.info("未找到目标员工");
            return;
        }

        List<Leads> leadsListOfClient = leadsManager.findLeadsByClient(clientTransferMessage.getBid(), clientTransferMessage.getId());
        LeadsTransferResultDto leadsTransferResultDto = leadsManager.transferLeads(leadsListOfClient, targetStaff, LeadsTransferEnum.CLIENT_TRANSFER);
        if (Objects.isNull(leadsTransferResultDto)) {
            log.info("没有待转让线索");
            return;
        }
        log.info("待转让的线索：{}", jsonService.toJson(leadsTransferResultDto));
        Boolean transferFlag = leadsManager.doTransferLeads(leadsTransferResultDto);

        List<LeadsFollow> transferLeadsFollowList = leadsTransferResultDto.getTransferLeadsFollowList();
        if (Boolean.TRUE.equals(transferFlag) && !CollectionUtils.isEmpty(transferLeadsFollowList)) {
            transferLeadsFollowList.forEach(item -> mqServiceForLeads.sendLeadsFollowMq(item));
        }

        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setIds(leadsListOfClient.stream().map(Leads::getId).collect(Collectors.toList()));
        leadsUpdateRequest.setBid(clientTransferMessage.getBid());
        leadsEsService.updateDocs(leadsUpdateRequest);

        log.info("MessageId：{}, 转让结果：{}", clientTransferMessage.getInngkeMessageId(), transferFlag);
    }
}

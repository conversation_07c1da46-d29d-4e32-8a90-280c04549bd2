package com.inngke.bp.leads.service.template.detail;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffManageDepartmentClientForLeads;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.service.LeadsSearchService;
import com.inngke.bp.leads.service.enums.LeadsReportListDetailTypeEnum;
import com.inngke.bp.leads.service.template.leadsAchievement.AbstractLeadsAchievement;
import com.inngke.bp.leads.service.template.leadsAchievement.LeadsAchievementReportContext;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateUtils;
import com.inngke.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.mapper.ParsedDocument;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.*;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedMin;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 15:14
 */
@Slf4j
public abstract class LeadsAchievementDetail extends AbstractLeadsAchievement {

    private static final Logger logger = LoggerFactory.getLogger(LeadsAchievementDetail.class);

    @Autowired
    private RestHighLevelClient esClient;

    @Autowired
    private LeadsSearchService leadsSearchService;

    @Autowired
    private JsonService jsonService;


    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private StaffManageDepartmentClientForLeads staffManageDepartmentClientForLeads;

    @Override
    protected String getGroupByFiled(LeadsAchievementReportContext context) {
        context.setGroupByFiled("leadsId");
        return context.getGroupByFiled();
    }

    protected abstract void checkPara(LeadsAchievementReportContext context);


    protected void buildPageCountHandle(LeadsAchievementReportContext context,Integer type, Integer total){

    };


    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> getReportListDetail(LeadsAchievementReportContext context, Integer type, Integer total) {
        this.getGroupByFiled(context);

        checkPara(context);

        SearchSourceBuilder queryBuilder = this.initSearchSource(context);

        BoolQueryBuilder qb = this.initQueryBuilder(context);
        setQueryType(qb, type);

        this.afterQueryBuildPlugin(context);

        int from = (context.getPageNo() - 1) * context.getPageSize();
        Integer pageSize = context.getPageSize();

        String aggregationName = "leadsIdsAgg";

        this.buildPageCount(context);

        if (context.getResultCount() == 0) {
            BasePaginationResponse<LeadsBillingIndicatorsDto> response = new BasePaginationResponse<>();
            response.setTotal(context.getResultCount());
            response.setList(new ArrayList<>());
            return BaseResponse.success(response);
        }
        // 防止Es去重不准确
        buildPageCountHandle(context, type, total);

        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("updateTime.value").order(SortOrder.DESC);
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(aggregationName).field(context.getGroupByFiled()).size(context.getResultCount())
                .subAggregation(AggregationBuilders.max("updateTime").field("createTime"))
                .subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort",
                        Lists.newArrayList(sortBuilder))
                        .from(from).size(pageSize));


        queryBuilder.aggregation(aggregationBuilder).query(qb);

        SearchRequest searchRequest = new SearchRequest().indices("leads-event-log").source(queryBuilder);

        try {
            SearchResponse search = esClient.search(searchRequest, RequestOptions.DEFAULT);

            Aggregations aggregations = search.getAggregations();

            ParsedTerms leadsIdsAgg = aggregations.get(aggregationName);

            List<? extends Terms.Bucket> buckets = leadsIdsAgg.getBuckets();

            List<Long> leadsIds = new ArrayList<>(buckets.size());

            for (Terms.Bucket bucket : buckets) {
                leadsIds.add(Long.valueOf(bucket.getKeyAsString()));
            }


            BasePaginationResponse<LeadsBillingIndicatorsDto> leads = getLeads(context.getBid(), leadsIds, context.getResultCount());

            if(LeadsReportListDetailTypeEnum.orderType(type)) {
                // 转交过的线索：开单数，定金金额，成单金额，定金时间，成单时间只显示只属于自己的数据
                setTimeAndAmount(context, leadsIds, leads.getList());
            }

            return BaseResponse.success(leads);
        } catch (IOException e) {
            logger.warn("业绩报表员工维度查询失败：", e);
            return BaseResponse.error("查询失败失败");
        }
    }

    protected void setTimeAndAmountBoolQueryBuilder(BoolQueryBuilder qb, LeadsAchievementReportContext context) {

    }

    ;

    private void setTimeAndAmount(LeadsAchievementReportContext context, List<Long> leadsIds, List<LeadsBillingIndicatorsDto> list) throws IOException {
        this.getGroupByFiled(context);

        checkPara(context);

        SearchSourceBuilder queryBuilder = this.initSearchSource(context);

        BoolQueryBuilder qb = this.initQueryBuilder(context);

        this.setTimeAndAmountBoolQueryBuilder(qb, context);

        qb.must(QueryBuilders.termsQuery("leadsId", leadsIds));

        String aggregationName = "leadsIdsAgg";

        Map<Long, List<LeadsBillingIndicatorsDto>> collect = list.stream().collect(Collectors.groupingBy(LeadsBillingIndicatorsDto::getId));

        // 清空Es上的时间
        list.forEach(item->{
            item.setFullPayOrderCount(0);
            item.setDepositOrderCount(0);
            item.setStateDepositTimeStr("");
            item.setStateOrderSuccessTimeStr("");
            item.setOrderAmount(BigDecimal.ZERO);
            item.setDepositAmount(BigDecimal.ZERO);
        });


        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms(aggregationName).field(context.getGroupByFiled());
        aggregationBuilder.size(leadsIds.size());
        // 多条定金单或者多条成交单时定金时间和成交时间拿最早的
        // 员工 转交过的线索：开单数，定金金额，成单金额，定金时间，成单时间只显示只属于自己的数据
        setTimeAndAmountAgg(aggregationBuilder);

        queryBuilder.aggregation(aggregationBuilder).query(qb);

        SearchRequest searchRequest = new SearchRequest().indices("leads-event-log").source(queryBuilder);

        SearchResponse search = esClient.search(searchRequest, RequestOptions.DEFAULT);

        Aggregations aggregation = search.getAggregations();
        ParsedLongTerms agg = aggregation.get(aggregationName);
        for (Terms.Bucket bucket : agg.getBuckets()) {
            String leadsIdStr = bucket.getKeyAsString();
            List<LeadsBillingIndicatorsDto> leadsBillingIndicatorsDto = collect.get(Long.valueOf(leadsIdStr));
            setTimeAndAmountResult(bucket, leadsBillingIndicatorsDto);
        }
    }

    private void setTimeAndAmountAgg(TermsAggregationBuilder aggregationBuilder) {
        // 开单数
        FilterAggregationBuilder filter = AggregationBuilders.filter("fullPayOrderCount",
                QueryBuilders.termQuery("eventId", 20));

        FilterAggregationBuilder fullPayOrderCountAmount = AggregationBuilders.filter("fullPayOrderCountAmount",
                QueryBuilders.boolQuery().must(QueryBuilders.termQuery("eventId", 20))
                        .must(QueryBuilders.rangeQuery("deposit").gt(0)));
        // 定金时间
        fullPayOrderCountAmount.subAggregation(AggregationBuilders.min("stateDeposit").field("createTime"));

        aggregationBuilder.subAggregation(filter);
        aggregationBuilder.subAggregation(fullPayOrderCountAmount);


        // 成交时间
        FilterAggregationBuilder eventIdOrderAmount = AggregationBuilders.filter("eventIdOrderAmount",
                QueryBuilders.boolQuery().must(QueryBuilders.termQuery("eventId", 20))
                        .must(QueryBuilders.rangeQuery("payAmount").gt(0)));

        aggregationBuilder.subAggregation(eventIdOrderAmount);

        eventIdOrderAmount.subAggregation(AggregationBuilders.min("stateOrderSuccess").field("createTime"));

        // 定金金额
        aggregationBuilder.subAggregation(AggregationBuilders.sum("depositAmount").field("deposit"));
        // 成单金额
        aggregationBuilder.subAggregation(AggregationBuilders.sum("payAmount").field("payAmount"));
    }

    private void setTimeAndAmountResult(Terms.Bucket bucket, List<LeadsBillingIndicatorsDto> leadsBillingIndicatorsDto) {
        Aggregations aggregations = bucket.getAggregations();

        // 定金金额
        ParsedSum depositAmount = aggregations.get("depositAmount");
        String depositAmountStr = depositAmount.getValueAsString();
        // 成单金额
        ParsedSum payAmount = aggregations.get("payAmount");
        String payAmountStr = payAmount.getValueAsString();
        // 开单数
        ParsedFilter fullPayOrderCount = aggregations.get("fullPayOrderCount");
        long docCount = fullPayOrderCount.getDocCount();
        // 定金时间
        ParsedFilter fullPayOrderCountAmount = aggregations.get("fullPayOrderCountAmount");
        ParsedMin stateDeposit = fullPayOrderCountAmount.getAggregations().get("stateDeposit");
        Double stateDepositStr = stateDeposit.getValue();
        // 成交时间
        ParsedFilter eventIdOrderAmount = aggregations.get("eventIdOrderAmount");
        ParsedMin stateOrderSuccess = eventIdOrderAmount.getAggregations().get("stateOrderSuccess");
        Double stateOrderSuccessStr = stateOrderSuccess.getValue();

        for (LeadsBillingIndicatorsDto billingIndicatorsDto : leadsBillingIndicatorsDto) {
            billingIndicatorsDto.setDepositAmount(new BigDecimal(depositAmountStr));
            billingIndicatorsDto.setOrderAmount(new BigDecimal(payAmountStr));
            billingIndicatorsDto.setFullPayOrderCount((int) docCount);
            billingIndicatorsDto.setDepositOrderCount((int) fullPayOrderCountAmount.getDocCount());
            if (stateDepositStr.compareTo(0.0) > 0 && !"Infinity".equals(stateDeposit.getValueAsString())) {
                String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(stateDepositStr.longValue()));
                billingIndicatorsDto.setStateDepositTimeStr(s);
            }

            if (stateOrderSuccessStr.compareTo(0.0) > 0 && !"Infinity".equals(stateOrderSuccess.getValueAsString())) {
                String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(stateOrderSuccessStr.longValue()));
                billingIndicatorsDto.setStateOrderSuccessTimeStr(s);
            }
        }

    }

    private void setQueryType(BoolQueryBuilder qb, Integer type) {
        Map<Integer, Integer> transTypeMap = new HashMap<>();
        transTypeMap.put(LeadsReportListDetailTypeEnum.DISTRIBUTE.getCode(), 8);
        transTypeMap.put(LeadsReportListDetailTypeEnum.MEASURED.getCode(), 12);
        transTypeMap.put(LeadsReportListDetailTypeEnum.ORDERED.getCode(), 15);
        transTypeMap.put(LeadsReportListDetailTypeEnum.STORED.getCode(), 13);
        transTypeMap.put(LeadsReportListDetailTypeEnum.TRADED.getCode(), 18);
        transTypeMap.put(LeadsReportListDetailTypeEnum.LOST.getCode(), 1);

        Integer integer = transTypeMap.get(type);

        if (Objects.isNull(integer)) {
            throw new InngkeServiceException("type不正确");
        }

        qb.must(QueryBuilders.termQuery("eventId", integer));
    }


    private LeadsBillingIndicatorsDto hitToLeadsBillingIndicatorsDto(SearchHit hit) {
        LeadsBillingIndicatorsDto leadsBillingIndicatorsDto = jsonService.toObject(hit.getSourceAsString(), LeadsBillingIndicatorsDto.class);
        Long zero = 0L;
        if (zero.compareTo(leadsBillingIndicatorsDto.getStateDeposit()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getStateDeposit()));
            leadsBillingIndicatorsDto.setStateDepositTimeStr(s);
        }
        if (zero.compareTo(leadsBillingIndicatorsDto.getStateOrderSuccess()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getStateOrderSuccess()));
            leadsBillingIndicatorsDto.setStateOrderSuccessTimeStr(s);
        }
        if (zero.compareTo(leadsBillingIndicatorsDto.getDistributeTime()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getDistributeTime()));
            leadsBillingIndicatorsDto.setDistributeTimeStr(s);
        }
        if (zero.compareTo(leadsBillingIndicatorsDto.getCreateTime()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getCreateTime()));
            leadsBillingIndicatorsDto.setCreateTimeStr(s);
        }

        return leadsBillingIndicatorsDto;
    }

    private BasePaginationResponse<LeadsBillingIndicatorsDto> getLeads(Integer bid, List<Long> leadsIds, Integer total) throws IOException {


        if (CollectionUtils.isEmpty(leadsIds)) {
            BasePaginationResponse<LeadsBillingIndicatorsDto> response = new BasePaginationResponse<>();
            response.setTotal(0);
            response.setList(new ArrayList<>());
            return response;
        }

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery(LeadsEsDto.ID, leadsIds))
                .must(QueryBuilders.termQuery("bid", bid));

        BasePaginationResponse<LeadsBillingIndicatorsDto> result = leadsSearchService.getLeadsByEs(bid, queryBuilder, 0, leadsIds.size());

        result.setTotal(total);

        return result;
    }

    protected Set<Long> getManageDepartment(Integer bid, Long deptId, Long staffId) {
        Set<Long> managerDepartmentIds = staffManageDepartmentClientForLeads.getStaffManageDepartment(bid, staffId);

        // 获取传入部门的子部门
        if (Objects.nonNull(deptId) && !deptId.equals(0L)) {
            // 获取子部门
            Set<Long> childrenDepartment = departmentClientForLeads.getChildrenDepartment(bid, deptId);
            childrenDepartment.add(deptId);

            managerDepartmentIds = Sets.newHashSet(CollUtil.intersection(childrenDepartment, managerDepartmentIds));
        }
        return managerDepartmentIds;
    }

}

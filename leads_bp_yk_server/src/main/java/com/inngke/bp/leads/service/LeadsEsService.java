package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.response.EsDocsResponse;

import java.util.List;
import java.util.Map;

/**
 * 构建ES索引服务类
 *
 * <AUTHOR>
 */
public interface LeadsEsService {

    /**
     * 批量获取ES文档
     * @param request
     * @return
     */
    BaseResponse<EsDocsResponse<LeadsEsDto>> getBatchDoc(LeadsEsBatchRequest request);

    /**
     * 更新单个LeadsDoc
     * @param request
     * @return
     */
    BaseResponse<Boolean> updateDocs(LeadsUpdateRequest request);

    void sendLeadsChangeMq(Integer bid, List<Long> leadsIds, Boolean notify<PERSON><PERSON><PERSON>, String leadsName, Integer event);

    /**
     * 新增文档
     * @param request
     * @return
     */
    BaseResponse<Boolean> createLeadsDocs(LeadsAddRequest request);

    BaseResponse<List<LeadsEsDto>> searchLeads(LeadsSearchRequest request);


    BaseResponse updateByStaffId(LeadsUpdateByStaffIdRequest request);
}

package com.inngke.bp.leads.mq.listener;

import com.google.common.collect.Lists;
import com.inngke.bp.client.dto.response.demand.DemandItemDto;
import com.inngke.bp.client.enums.DemandStatusEnum;
import com.inngke.bp.client.service.DemandService;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 客户流失，线索同步流失
 */
@Component
@MqConsumer(value = "demand_change", consumerName = "leads_bp_yk_demand_change", messageType = ClientTransferMessage.class)
@Slf4j
public class LeadsClientChangeListener implements InngkeMqListener<ClientTransferMessage> {

    @Override
    public void process(ClientTransferMessage clientTransferMessage) {

    }
}

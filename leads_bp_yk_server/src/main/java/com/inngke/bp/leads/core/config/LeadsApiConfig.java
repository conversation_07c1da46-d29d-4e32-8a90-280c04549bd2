package com.inngke.bp.leads.core.config;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitScan;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitInvocationHandler;
import com.inngke.bp.leads.api.AiCustomerService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Proxy;

/**
 * <AUTHOR>
 * @date 2022/3/3 14:54
 */
@Configuration
@RetrofitScan("com.inngke.bp.leads.api")
public class LeadsApiConfig {
}

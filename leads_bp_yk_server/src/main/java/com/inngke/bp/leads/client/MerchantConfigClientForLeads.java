package com.inngke.bp.leads.client;

import com.inngke.bp.organize.dto.request.merchant.GetMerchantConfigRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.merchant.MerchantConfigDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.organize.service.MerchantConfigService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * MerchantConfigClient
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/19 15:27
 */
@Component
@Slf4j
public class MerchantConfigClientForLeads {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.merchant_bp_yk:}")
    private MerchantConfigService merchantConfigService;

    @Resource
    private StaffClientForLeads staffClientForLeads;

    public Long getFlyFishLeadsOperatorStaffId(int bid) {
        return getThirdPartyLeadsOperatorStaffId(bid, "flyFish.leads.createStaff");
    }

    public Long getTencentLeadsOperatorStaffId(int bid) {
        return getThirdPartyLeadsOperatorStaffId(bid, "tencent.leads.createStaff");
    }

    public Long getTianMaoLeadsOperatorStaffId(int bid) {
        return getThirdPartyLeadsOperatorStaffId(bid, "tianMao.leads.createStaff");
    }

    public Long getAiLeadsOperatorStaffId(int bid) {
        return getThirdPartyLeadsOperatorStaffId(bid,"ai.leads.createStaff");
    }

    public Long getThirdPartyLeadsOperatorStaffId(int bid, String code) {
        GetMerchantConfigRequest request = new GetMerchantConfigRequest();
        request.setBid(bid);
        request.setCode(code);
        BaseResponse<MerchantConfigDto> response = merchantConfigService.get(request);
        if (BaseResponse.responseSuccessWithNonNullData(response)) {
            return 0L;
        }
        MerchantConfigDto merchantConfig = response.getData();
        String value = merchantConfig.getValue();
        if (StringUtils.isBlank(value)) {
            return 0L;
        }

        StaffDto staff = staffClientForLeads.getStaffById(bid, Long.valueOf(value));
        if (Objects.isNull(staff) || StaffStatusEnum.DELETE.getCode() == staff.getStatus()) {
            return 0L;
        }

        return staff.getId();
    }

    public Boolean isOpenForwardRollback(Integer bid) {
        Integer open =1;
        GetMerchantConfigRequest request = new GetMerchantConfigRequest();
        request.setCode("leads_forward_rollback");
        request.setBid(bid);

        return Optional.ofNullable(merchantConfigService.get(request)).map(BaseResponse::getData)
                .map(MerchantConfigDto::getValue).map(Integer::valueOf).map(open::equals).orElse(false);
    }

}

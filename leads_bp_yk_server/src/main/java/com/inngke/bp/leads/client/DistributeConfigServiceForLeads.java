package com.inngke.bp.leads.client;

import com.inngke.bp.distribute.dto.response.DistributeConfigDto;
import com.inngke.bp.distribute.service.DistributeConfigService;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DistributeConfigServiceForLeads {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributeConfigService  distributeConfigService;

    @Autowired
    private JsonService jsonService;

    public DistributeConfigDto get(Integer bid){
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setBid(bid);
        request.setOperatorId(0L);
        BaseResponse<DistributeConfigDto> distributeConfigDtoBaseResponse = distributeConfigService.get(request);
        if (!BaseResponse.responseSuccessWithNonNullData(distributeConfigDtoBaseResponse)){
            log.warn("获取配置失败！bid={}",bid);
            throw new InngkeServiceException("获取合伙人配置失败");
        }
        return distributeConfigDtoBaseResponse.getData();
    }
}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.MqServiceForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.utils.LeadsFollowContentUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsCallLog;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsCallLogManager;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.notify.context.OthersHelpContactLeadsNotifyContext;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsEventService;
import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.enums.ErrorCode;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import com.inngke.ip.reach.dto.response.PrivatePhoneConfDTO;
import com.inngke.ip.reach.service.PrivatePhoneConfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@DubboService(version = "1.0.0", timeout = 3000)
@Service
@Slf4j
public class LeadsEventServiceImpl implements LeadsEventService {

    private static final String LEADS_CONTACT_FAIL_KEY = LeadsServiceConsts.APP_ID + InngkeAppConst.CLN_STR + "contact_fail" + InngkeAppConst.CLN_STR;

    @Autowired
    LeadsManager leadsManager;

    @Autowired
    LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsCallLogManager leadsCallLogManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    private PrivatePhoneConfService privatePhoneConfService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    @Autowired
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    /**
     * 拨打线索电话
     *
     * @param request
     * @return
     */
    @Override
    public BaseResponse<Boolean> dial(LeadsEventDialRequest request) {
        request.getOperatorId();
        Leads leads = getLeads(request.getBid(), request.getId());

        if(leads == null || LeadsStatusEnum.delStatus().contains(leads.getStatus())){
            return BaseResponse.error(ErrorCode.NOT_FOUNT.getCode(), "线索已不存在");
        }

        //修改状态
        LeadsStatusEnum leadsStatus = getAndEditStatus(leads);
        //添加跟进
        Boolean added = addDialFollow(leads, leadsStatus,request.getOperatorId());
        if (!added) {
            return BaseResponse.error("添加失败");
        }
        AsyncUtils.runAsync(() -> {
            if (Objects.nonNull(request.getOperatorStaffId()) && !leads.getDistributeStaffId().equals(request.getOperatorStaffId()) && leads.getDistributeStaffId() > 0L) {
                OthersHelpContactLeadsNotifyContext context = OthersHelpContactLeadsNotifyContext.init(request.getBid(), leads.getId(), request.getOperatorStaffId());
                TemplateMessageContentBuilder<OthersHelpContactLeadsNotifyContext> builder = templateMessageBuilderFactory.getBuilder(context);
                builder.sendMessage(context);
            }
        });


        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<Boolean> getPhone(LeadsPhoneGetRequest request) {
        Integer bid = request.getBid();
        Long leadsId = request.getLeadsId();

        Integer privatePhoneConf = getPrivatePhoneConf(bid);

        StaffDto staffDto = staffClientForLeads.getStaffByCid(request.getBid(), request.getOperatorId());

        // 如果开启隐号服务
        if (privatePhoneConf == 1) {
            // 获取拨打电话记录
            List<LeadsCallLog> callLog = leadsCallLogManager.list(
                    Wrappers.<LeadsCallLog>query()
                            .eq(LeadsCallLog.BID, bid)
                            .eq(LeadsCallLog.LEADS_ID, leadsId)
                            .eq(LeadsCallLog.TYPE, 1)
            );
            if (CollectionUtils.isEmpty(callLog)) {
                return BaseResponse.success();
            }

            Leads leads = leadsManager.getOne(
                    Wrappers.<Leads>query()
                            .eq(Leads.BID, bid)
                            .eq(Leads.ID, leadsId)
            );
            if (null == leads) {
                return BaseResponse.error("线索信息错误");
            }
            //修改为完全显示手机号
            leads.setShowPhone(1);

            // 根据线索的状态，添加跟进记录,设置线索状态
            //添加跟进记录
            LeadsStatusEnum nowStatus = LeadsStatusEnum.parse(leads.getStatus());
            if (LeadsStatusEnum.INTENT != nowStatus) {
//                boolean statusChangeFlag = leads.getStatus() > 0 && leads.getStatus() < LeadsStatusEnum.INTENT.getStatus();
//                String changeStatusStr = "，系统将线索状态由【" + LeadsStatusEnum.parse(leads.getStatus()).getName() + "】修改为【" + LeadsStatusEnum.INTENT.getName() + "】";
//                if (statusChangeFlag) {
//                    leads.setStatus(LeadsStatusEnum.INTENT.getStatus());
//                }

                LeadsFollow leadsFollow = new LeadsFollow();
                leadsFollow.setBid(bid);
                leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
                leadsFollow.setLeadsId(request.getLeadsId());
                leadsFollow.setLeadsStatus(leads.getStatus());
                leadsFollow.setUserId(request.getOperatorId());
                if (Objects.nonNull(staffDto)) {
                    leadsFollow.setStaffId(staffDto.getId());
                }
                leadsFollow.setBeforeLeadsStatus(leads.getStatus());
                leadsFollow.setCreateTime(LocalDateTime.now());
                leadsFollow.setFollowContent("获取客户完整手机号码");
                boolean save = leadsFollowManager.save(leadsFollow);
                if (save) {
                    mqServiceForLeads.sendLeadsFollowMq(leadsFollow);
                }

                leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
            }
            leadsManager.updateById(leads);

            return BaseResponse.success();
        }
        //未开启隐号服务
        List<LeadsCallLog> callLog = leadsCallLogManager.list(
                Wrappers.<LeadsCallLog>query()
                        .eq(LeadsCallLog.BID, bid)
                        .eq(LeadsCallLog.LEADS_ID, leadsId)
                        .eq(LeadsCallLog.TYPE, 2)
        );
        Boolean isFirst = CollectionUtils.isEmpty(callLog);
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, request.getLeadsId())
        );

        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(bid);
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setLeadsId(request.getLeadsId());
        leadsFollow.setUserId(request.getOperatorId());
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setStaffId(staffDto.getId());
        }
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        leadsFollow.setCreateTime(LocalDateTime.now());
        if (isFirst && leads.getStatus() <= LeadsStatusEnum.DISTRIBUTED.getStatus() && leads.getStatus() != LeadsStatusEnum.UNSURE_INTENT.getStatus()) {
            LeadsStatusEnum newStatus = LeadsFollowContentUtil.getNewStatus(leads);
            leadsFollow.setLeadsStatus(newStatus.getStatus());
            String followContent = "拨打客户电话并获取完整手机号码";
            if (newStatus.getStatus() != leads.getStatus()){
                followContent = "拨打客户电话并获取完整手机号码，系统将线索状态由【"+ LeadsStatusEnum.parse(leads.getStatus()).getName() +"】修改为【" + newStatus.getName() + "】";
            }
            leadsFollow.setFollowContent(followContent);
            LeadsCallLog leadsCallLog = new LeadsCallLog();
            leadsCallLog.setLeadsId(leads.getId());
            leadsCallLog.setVoiceId(0L);
            leadsCallLog.setType(2);
            leadsCallLog.setBid(bid);
            leadsCallLog.setCreateTime(LocalDateTime.now());
            leadsCallLogManager.save(leadsCallLog);
            leads.setStatus(newStatus.getStatus());
            if (Objects.isNull(leads.getFirstContactTime())){
                leads.setFirstContactTime(LocalDateTime.now());
            }
        } else {
            leadsFollow.setFollowContent("获取客户的完整手机号码");
        }
        leads.setShowPhone(1);
        if (leadsFollow.getLeadsStatus() != null && (leadsFollow.getLeadsStatus().equals(LeadsStatusEnum.CONTACTED.getStatus()))) {
            LocalDateTime distributeTime = leads.getDistributeTime();
            LocalDateTime now = LocalDateTime.now();
            long hours = Duration.between(distributeTime, now).toHours();
            if (hours < 24) {
                leads.setContactIn24(1);
            }
        }
        leadsManager.updateById(leads);
        Boolean contactFollow = leadsFollowManager.createContactFollow(leadsFollow);
        if (Boolean.TRUE.equals(contactFollow)) {
            mqServiceForLeads.sendLeadsFollowMq(leadsFollow);
        }

        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(Lists.newArrayList(leads.getId()));
//        leadsUpdateRequest.setRefreshEs(true);
        leadsEsService.updateDocs(leadsUpdateRequest);

        AsyncUtils.runAsync(() -> {
            if (Objects.nonNull(request.getOperatorStaffId()) && !leads.getDistributeStaffId().equals(request.getOperatorStaffId()) && leads.getDistributeStaffId() > 0L) {
                OthersHelpContactLeadsNotifyContext context = OthersHelpContactLeadsNotifyContext.init(request.getBid(), leads.getId(), request.getOperatorStaffId());
                TemplateMessageContentBuilder<OthersHelpContactLeadsNotifyContext> builder = templateMessageBuilderFactory.getBuilder(context);
                builder.sendMessage(context);
            }
        });

        return BaseResponse.success(true);
    }


    @Override
    public BaseResponse<Boolean> createCallLog(LeadsPrivatePhoneCallLogRequest request) {
        Long leadsId = request.getLeadsId();
        Integer bid = request.getBid();

        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, leadsId)
        );
        if (null == leads) {
            return BaseResponse.error("无效的线索id" + leads);
        }
        // 添加通话记录
        LeadsCallLog leadsCallLog = new LeadsCallLog();
        leadsCallLog.setBid(bid);
        leadsCallLog.setLeadsId(leadsId);
        leadsCallLog.setVoiceId(request.getPrivateVoiceRecordId());
        leadsCallLog.setCreateTime(Objects.isNull(request.getCallTime()) ? LocalDateTime.now() : request.getCallTime());
        boolean save = leadsCallLogManager.save(leadsCallLog);
        // 联系成功删除key
        if (save) {
            redisTemplate.delete(LEADS_CONTACT_FAIL_KEY + leads.getId());
        }

        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Boolean> contactFail(BaseIdRequest request) {
        redisTemplate.opsForValue().increment(LEADS_CONTACT_FAIL_KEY + request.getBid() + InngkeAppConst.CLN_STR + request.getId(), 1);
        redisTemplate.expire(LEADS_CONTACT_FAIL_KEY + request.getBid() + InngkeAppConst.CLN_STR + request.getId(), 3, TimeUnit.DAYS);
        return BaseResponse.success(true);
    }

    /**
     * 添加跟进
     *
     * @param leads
     * @param leadsStatus
     * @param operatorId
     * @return
     */
    private Boolean addDialFollow(Leads leads, LeadsStatusEnum leadsStatus, Long operatorId) {
        LeadsFollow leadsFollow = new LeadsFollow();

        leadsFollow.setBid(leads.getBid());
        leadsFollow.setLeadsId(leads.getId());
        StaffDto staffDto = staffClientForLeads.getStaffByCid(leads.getBid(), operatorId);
        leadsFollow.setUserId(operatorId);
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setStaffId(staffDto.getId());
        }
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setLeadsStatus(leadsStatus.getStatus());
        leadsFollow.setCreateTime(LocalDateTime.now());

        String content = "拨打客户电话";
        if (leads.getStatus().equals(LeadsStatusEnum.DISTRIBUTED.getStatus())) {
            content = "第一次拨打客户电话，系统将线索状态由【未联系】修改为【" + leadsStatus.getName() + "】";
        }

        leadsFollow.setFollowContent(content);

        boolean save = leadsFollowManager.save(leadsFollow);
        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
        //添加通话记录
        // 添加通话记录
        LocalDateTime now = LocalDateTime.now();
        LeadsCallLog leadsCallLog = new LeadsCallLog();
        leadsCallLog.setBid(leads.getBid());
        leadsCallLog.setLeadsId(leads.getId());
        leadsCallLog.setVoiceId(0L);
        leadsCallLog.setType(2);
        leadsCallLog.setCreateTime(now);
        leadsCallLogManager.save(leadsCallLog);

        if (save) {
            mqServiceForLeads.sendLeadsFollowMq(leadsFollow);
            leadsManager.update(
                    Wrappers.<Leads>update()
                            .eq(Leads.BID, leads.getBid())
                            .eq(Leads.ID, leads.getId())
                            .set(Leads.LAST_FOLLOW_ID, leadsFollow.getId())
                            .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
                            .set(Leads.LAST_CONTACT_TIME, now)
                            .set(Objects.isNull(leads.getFirstContactTime()), Leads.FIRST_CONTACT_TIME, now)
            );
        }

        //更新线索状态记录
        UpdateLeadsStatusRecordRequest updateLeadsStatusRecordRequest = new UpdateLeadsStatusRecordRequest();
        updateLeadsStatusRecordRequest.setStatus(leadsStatus.getStatus());
        updateLeadsStatusRecordRequest.setLeadsId(leads.getId());
        updateLeadsStatusRecordRequest.setBid(leads.getBid());
        leadsServiceV2.updateLeadsStatusRecord(updateLeadsStatusRecordRequest);

        return save;
    }

    /**
     * 获取线索
     *
     * @param bid
     * @param id
     * @return
     */
    private Leads getLeads(Integer bid, Long id) {
        return leadsManager.getOne(new QueryWrapper<Leads>()
                .eq(Leads.BID, bid)
                .eq(Leads.ID, id).last("LIMIT 1"));
    }

    /**
     * 修改并获取状态
     *
     * @param leads
     * @return
     */
    private LeadsStatusEnum getAndEditStatus(Leads leads) {
        if (!leads.getStatus().equals(LeadsStatusEnum.DISTRIBUTED.getStatus())) {
            return LeadsStatusEnum.parse(leads.getStatus());
        }
        LocalDateTime now = LocalDateTime.now();
        LeadsStatusEnum newStatus = getNewStatus(leads);
        int status = newStatus.getStatus();
        Leads updateLeads = new Leads();
        updateLeads.setId(leads.getId());
        updateLeads.setBid(leads.getBid());
        updateLeads.setStatus(status);
        updateLeads.setLastContactTime(now);
        //如果为24小时内联系则赋值到24小时内联系的字段中
        if (status == LeadsStatusEnum.CONTACTED.getStatus()) {
            updateLeads.setContactIn24(1);
        }
        leadsManager.updateLeads(updateLeads, 0L);

        //异步更新ES
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
            esRequest.setBid(leads.getBid());
            esRequest.setIds(Lists.newArrayList(leads.getId()));
            leadsEsService.updateDocs(esRequest);
        });
        return newStatus;
    }

    /**
     * 获取对应状态
     *
     * @param leads
     * @return
     */
    private LeadsStatusEnum getNewStatus(Leads leads) {
        return LeadsStatusEnum.CONTACTED;
    }

    private Integer getPrivatePhoneConf(Integer bid) {
        // 获取商户配置，判断当前商户是否开启隐号
        BaseBidRequest bidRequest = new BaseBidRequest();
        bidRequest.setBid(bid);
        BaseResponse<PrivatePhoneConfDTO> confResp = privatePhoneConfService.get(bidRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(confResp)) {
            throw new InngkeServiceException("获取隐号配置失败");
        }
        return confResp.getData().getEnable();
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsDraft;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.dto.request.LeadsBatchImportRequest;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface LeadsDraftManager extends IService<LeadsDraft> {

    void importLeadsDraft(Long batchId, List<LeadsDraft> drafts, int errorCount, String errorFileUrl);

    void batchSaveLeadsDraft(List<LeadsDraft> list);
}

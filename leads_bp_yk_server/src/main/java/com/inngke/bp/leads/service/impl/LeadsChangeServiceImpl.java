package com.inngke.bp.leads.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.client.dto.response.client.ClientDto;
import com.inngke.bp.client.request.ClientBindLeadsRequest;
import com.inngke.bp.client.service.ClientBindLeadsService;
import com.inngke.bp.distribute.dto.request.DistributeCustomerTransferRequest;
import com.inngke.bp.distribute.dto.response.DistributeConfigDto;
import com.inngke.bp.distribute.service.DistributorLeadsService;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.core.converter.StaffAndAgentConverter;
import com.inngke.bp.leads.core.utils.StaffToAgentUtil;
import com.inngke.bp.leads.db.leads.entity.*;
import com.inngke.bp.leads.db.leads.manager.*;
import com.inngke.bp.leads.dto.DistributeStaffState;
import com.inngke.bp.leads.dto.LeadsExtDataDto;
import com.inngke.bp.leads.dto.StaffLeadsCountDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.*;
import com.inngke.bp.leads.notify.context.LeadsOrderRefundMessageContext;
import com.inngke.bp.leads.notify.context.OthersHelpContactLeadsNotifyContext;
import com.inngke.bp.leads.service.*;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.leads.service.impl.contact.LeadsContactFollowFactoryService;
import com.inngke.bp.leads.service.schedule.LeadsRegionCache;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.bp.organize.dto.response.channel.ChannelDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.bp.user.dto.request.customer.CustomerMobilesQuery;
import com.inngke.bp.user.dto.response.AgentIdAndNameDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.ds.annotation.DS;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.response.RegionDto;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/9/8 6:20 PM
 */
@Service
@DubboService(version = "1.0.0")
public class LeadsChangeServiceImpl implements LeadsChangeService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsChangeServiceImpl.class);
    public static final int A_HOUR = 3600;
    public static final int A_DAY = A_HOUR * 24;
    public static final Integer AUTOMATIC_ALLOCATION = 1;
    public static final String CUSTOMER_ROLE = "customer";

    /**
     * 线索分配缓存
     */
    private final static String DISTRIBUTE_LEADS_CACHE = LeadsServiceConsts.APP_ID + ":distributeCache:";

    /**
     * 5分钟
     */
    private final static Integer DISTRIBUTE_LEADS_CACHE_EXPIRE = 5;

    /**
     * 线索分配锁
     */
    private final static String DISTRIBUTE_LOCK_KEY = LeadsServiceConsts.APP_ID + ":lock:" + "distribute:";


    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsConfManager leadsConfManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerService customerService;

    @Autowired
    private LeadsWxPubMessageService leadsWxPubMessageService;

    @Autowired
    private LeadsRegionCache leadsRegionCache;

    @Autowired
    private LeadsDistributeConfManager leadsDistributeConfManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsContactFollowFactoryService leadsContactFollowFactoryService;

    @Autowired
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private LockService lockService;

    @Autowired
    private LeadsDistributeChannelConfManager leadsDistributeChannelConfManager;

    @Autowired
    private LeadsDistributeForwardConfManager leadsDistributeForwardConfManager;

    /**
     * 用于分配预览 轮询分配但不影响下次的分配顺序
     */
    private final static ThreadLocal<Map<String, LeadsDistributeConf>> LOCAL_LEADS_DISTRIBUTE_CONF = new ThreadLocal<>();

    private LeadsLogManager leadsLogManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store_bp_yk:}")
    private StoreOrderService storeOrderService;


    @Autowired
    StaffClientForLeads staffClientForLeads;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    ChannelClientForLeads channelClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributorLeadsService distributorService;

    @DubboReference(version = "1.0.0", timeout = 5000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientBindLeadsService clientBindLeadsService;

    @Autowired
    private DistributeConfigServiceForLeads distributeConfigServiceForLeads;

    @Autowired
    private StaffToAgentUtil staffToAgentUtil;

    @Autowired
    private PreFollowStaffService preFollowStaffService;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @Autowired
    private LeadsRepeatService leadsRepeatService;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    @Autowired
    private ClientGetClientForLeads clientGetClientForLeads;

    @Autowired
    private ClientCommonClientForLeads clientCommonClientForLeads;




    /**
     * 添加线索
     *
     * @param request 线索信息
     * @return 保存成功后的线索信息
     */
    @Override
    public BaseResponse<LeadsDto> add(LeadsAddRequest request) {
        Integer bid = request.getBid();
        Leads leads = LeadsConverter.toAddLeads(request);
        if (Objects.isNull(leads)) {
            logger.error("线索添加：请求体为空");
            return BaseResponse.error("请求体为空");
        }
        //检查线索重复
        checkRepeatLeads(leads);
        //组装是否退款信息
        installExtData(leads);
        //线索信息校验
        validateLeadsInfo(leads);
        //组装客户uid和id
        installCustomerInfo(leads, request.getOperatorId());

        //获取线索配置，用于判断是否需要自动分配
        LeadsConf leadsConf = leadsConfManager.getOne(Wrappers.<LeadsConf>query().select(LeadsConf.DISTRIBUTE_TYPE).eq(LeadsConf.ID, leads.getBid()));

        //根据线索中的区域编号组装线索中的区域名称，同时当为自动分配的状态下对区域接收人进行匹配
        fillAreaInformation(leads, findLeadsAllRegion(leads));

        //线索分配
        DistributeStaffState distributeStaffState = null;
        if (AUTOMATIC_ALLOCATION.equals(leadsConf.getDistributeType())) {
            distributeStaffState = distributeStaff(leads, bid);
        }

        Map<Long, Long> staffAndAgent = Maps.newHashMap();
        if (!Objects.isNull(leads.getDistributeStaffId())) {
            // 设置分发索引的经销商 要迁移到Es里面
            staffAndAgent = staffToAgentUtil.getStaffAndAgent(Lists.newArrayList(leads), bid);
        }

        Long leadsId = leadsManager.addLeads(leads, request.getOperatorId());

        // 添加跟进记录
        boolean isPreFollow = leads.getPreFollowStaffId() != null && !Long.valueOf(0).equals(leads.getPreFollowStaffId());
        //保存自动转交跟进记录
        List<LeadsFollow> autoForwardFollowList = Optional.ofNullable(distributeStaffState).map(DistributeStaffState::getAutoForwardLeadsFollowList).map(autoForwardFollow -> {
            autoForwardFollow.forEach(f -> f.setLeadsId(leadsId));
            return autoForwardFollow;
        }).orElse(Lists.newArrayList());

        //分配后将线索分配人设置为自动 转交前的员工id用于添加跟进记录
        if (!CollectionUtils.isEmpty(autoForwardFollowList)){
            Leads newLeads = jsonService.toObject(jsonService.toJson(leads), Leads.class);
            autoForwardFollowList.stream().findFirst().map(LeadsFollow::getStaffId).ifPresent(newLeads::setDistributeStaffId);

            distributeLeadsFollow(request.getBid(), request.getCreateStaffId(), Lists.newArrayList(newLeads), isPreFollow);
        }else {
            distributeLeadsFollow(request.getBid(), request.getCreateStaffId(), Lists.newArrayList(leads), isPreFollow);
        }

        leadsFollowManager.saveBatch(autoForwardFollowList);


        LeadsDto leadsDto = LeadsConverter.toLeadsListItem(leads, staffAndAgent);
        Long distributeStaffId = leads.getDistributeStaffId();
        //获取是否开齐自动分配配置，用于组装给前端展示在自动分配时是否自动分配成功/失败  当为手动分配时则为正常新增流程
        if (AUTOMATIC_ALLOCATION.equals(leadsConf.getDistributeType())) {
            Long staffId = isPreFollow ? leads.getPreFollowStaffId() : leads.getDistributeStaffId();
            if (staffId == null || staffId.equals(0L)) {
                leadsDto.setErrorMsg(leads.getErrorMsg());
                leadsDto.setDistributeStatus(-1);
                leadsDto.setDistributeTime(LocalDateTimeUtil.toEpochMilli(LocalDateTime.now()));
            } else if (!isPreFollow && leads.getDistributeStaffId() != null) {
                //如果开启了自动分配且自动分配数据已经成功入库则异步发送公众号消息
                AsyncUtils.runAsync(() -> sendWxPubMessage(bid, request.getOperatorId(), Lists.newArrayList(leads), null, 0));
                sendDelayMessage(request.getOperatorId(), leads.getBid(), Lists.newArrayList(leads));
            }
        } else {
            leadsDto.setDistributeStatus(0);
        }

        // lambada要求引用不可变
        Map<Long, Long> finalStaffAndAgent = staffAndAgent;
        AsyncUtils.runAsync(() -> {

        });
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsAddRequest leadsAddRequest = new LeadsAddRequest();
            leadsAddRequest.setBid(bid);
            leadsAddRequest.setIds(Lists.newArrayList(leadsId));
            leadsAddRequest.setStaffToAgent(finalStaffAndAgent);
            leadsEsService.createLeadsDocs(leadsAddRequest);
        });
        return BaseResponse.success(leadsDto);
    }

    private void checkRepeatLeads(Leads leads) {
        List<Leads> leadsList = leadsRepeatService.checkRepeat(leads.getBid(), leads.getMobile(), leads.getWeChat(), null);
        if (!CollectionUtils.isEmpty(leadsList)) {
            throw new InngkeServiceException("重复线索");
        }
    }

    private void installExtData(Leads leads) {
        Integer channel = leads.getChannel();
        if (channel != null && (channel == 1 || channel == 2) && StringUtils.isEmpty(leads.getExtData())) {
            LeadsExtDataDto leadsExtDataDto = new LeadsExtDataDto();
            //若为天猫和京东渠道的线索传递过来的数据为空时，默认其是否退款为0，并将json存储进入数据库
            leadsExtDataDto.setIsRefund(0);
            String extDataJson = jsonService.toJson(leadsExtDataDto);
            leads.setExtData(extDataJson);
        }

    }

    @Override
    public BaseResponse<LeadsDto> addIncludeStaff(LeadsAddIncludeStaffRequest request) {
        Long distributeAgentId = Optional.ofNullable(request.getDistributeAgentId()).orElse(0L);
        Long distributeStaffId = Optional.ofNullable(request.getDistributeStaffId()).orElse(0L);
        Long customerId = Optional.ofNullable(request.getCustomerId()).orElse(0L);
        Long customerUid = Optional.ofNullable(request.getCustomerUid()).orElse(0L);

        Leads leads = LeadsConverter.toAddLeads(request);
        if (Objects.isNull(leads)) {
            logger.error("addIncludeStaff：请求体为空");
            return BaseResponse.error("请求体为空");
        }
        if (LeadsChannelEnum.REPORT.getChannel().equals(request.getChannel())) {
            leads.setType(LeadsTypeEnum.INFORMATION.getCode());
        }

        // leads.setDistributeAgentId(distributeAgentId);
        leads.setDistributeStaffId(distributeStaffId);
        leads.setCustomerId(customerId);
        leads.setCustomerUid(customerUid);

        leads.setExtData(request.getExtData());
        leads.setStatus(1);
        leads.setDistributeTime(LocalDateTime.now());
        installRegionIdByName(leads);
        Long leadsId = leadsManager.addLeads(leads, request.getOperatorId());

        Map<Long, Long> staffToAgentMap = new HashMap<>();
        staffToAgentMap.put(distributeStaffId, distributeAgentId);
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsAddRequest leadsAddRequest = new LeadsAddRequest();
            leadsAddRequest.setBid(request.getBid());
            leadsAddRequest.setIds(Lists.newArrayList(leadsId));
            leadsAddRequest.setStaffToAgent(staffToAgentMap);
            leadsEsService.createLeadsDocs(leadsAddRequest);
        });
        //如果开启了自动分配且自动分配数据已经成功入库则异步发送公众号消息
        if (request.getNotifyGuide()) {
            String timeInfo = "48小时内";
            AsyncUtils.runAsync(() -> sendWxPubMessage(request.getBid(), request.getOperatorId(), Lists.newArrayList(leads), timeInfo, 1));
        }
        // 添加跟进记录
        addIncludeStaffDistributeFollow(request, leads);

        LeadsDto leadsDto = LeadsConverter.toLeadsListItem(leads, staffToAgentMap);
        return BaseResponse.success(leadsDto);
    }

    /**
     * 合伙人报备添加跟进记录
     */
    private void addIncludeStaffDistributeFollow(LeadsAddIncludeStaffRequest request, Leads leads) {
        // 类型 1=主动报备（默认） 2=代报备
        if (request.getReportType() == null) {
            return;
        }
        String content = "";
        if (request.getReportType().equals(1)) {
            DistributeConfigDto distributeConfigDto = distributeConfigServiceForLeads.get(request.getBid());
            String distributorName = distributeConfigDto.getDistributorName();
            content = "新建转介绍线索，来自" + distributorName + "主动报备";
        }
        if (request.getReportType().equals(2)) {
            content = "新建转介绍线索，来自导购代报备";
        }
        if (request.getReportType().equals(3)) {
            content = "新建转介绍线索，来自导购代报备";
        }
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(request.getBid());
        leadsFollow.setLeadsId(leads.getId());
        leadsFollow.setUserId(request.getOperatorId());
        leadsFollow.setStaffId(request.getSid());
        if (request.getSid() == null || request.getSid().equals(0L)) {
            leadsFollow.setStaffId(request.getDistributeStaffId());
        }
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setFollowContent(content);
        if (!CollectionUtils.isEmpty(request.getImages())) {
            leadsFollow.setFollowImages(String.join(",", request.getImages()));
        }
        leadsFollow.setLeadsStatus(leads.getStatus());
        leadsFollow.setPreFollowStatus(leads.getPreFollowStatus());
        leadsFollow.setCreateTime(LocalDateTime.now());

        leadsFollowManager.saveFollow(leadsFollow);
    }

    public void installRegionIdByName(Leads leads) {
        String cityName = leads.getCityName();
        String provinceName = leads.getProvinceName();
        String areaName = leads.getAreaName();

        Map<String, RegionDto> cityNameMap = leadsRegionCache.getCityNameMap();
        Map<String, RegionDto> provinceNameMap = leadsRegionCache.getProvinceNameMap();
        Map<String, RegionDto> areaNameMap = leadsRegionCache.getAreaNameMap();
        if (!StringUtils.isEmpty(cityName) && leads.getCityId() == null) {
            RegionDto regionDto = cityNameMap.get(cityName);
            if (regionDto != null) {
                leads.setCityId(regionDto.getId());
            } else {
                throw new InngkeServiceException("通过市级名称转换id失败");
            }
        }

        if (!StringUtils.isEmpty(provinceName) && leads.getProvinceId() == null) {
            RegionDto regionDto = provinceNameMap.get(provinceName);
            if (regionDto != null) {
                leads.setProvinceId(regionDto.getId());
            } else {
                throw new InngkeServiceException("通过省级名称转换id失败");
            }
        }

        if (!StringUtils.isEmpty(areaName) && leads.getAreaId() == null) {
            RegionDto regionDto = areaNameMap.get(areaName);
            if (regionDto != null) {
                leads.setAreaId(regionDto.getId());
            } else {
                throw new InngkeServiceException("通过地区名称转换id失败");
            }
        }

    }


    /**
     * 填充线索区域名称
     *
     * @param leads      线索详情
     * @param regionList 区域列表
     */
    private void fillAreaInformation(Leads leads, List<RegionDto> regionList) {
        Map<Integer, RegionDto> regionMap = regionList.stream().filter(region -> !ObjectUtils.isEmpty(region))
                .collect(Collectors.toMap(RegionDto::getId, (item) -> item));

        leads.setProvinceName(ObjectUtils.isEmpty(regionMap.get(leads.getProvinceId())) ? "" :
                regionMap.get(leads.getProvinceId()).getFullName());

        leads.setCityName(ObjectUtils.isEmpty(regionMap.get(leads.getCityId())) ? "" :
                regionMap.get(leads.getCityId()).getFullName());

        leads.setAreaName(ObjectUtils.isEmpty(regionMap.get(leads.getAreaId())) ? "" :
                regionMap.get(leads.getAreaId()).getFullName());
    }

    /**
     * 获取下一个分配的员工
     *
     * @param leadsDistributeConf
     * @param distributeStaffId   为不0说明指定了分配导购
     * @return
     */
    private Long getNextAssignedStaff(LeadsDistributeConf leadsDistributeConf, Boolean indexNeedsToUpdated, Long distributeStaffId) {
        Long nextStaffId = null;

        String staffIds = leadsDistributeConf.getStaffIds();

        TreeSet<Long> staffSet = Sets.newTreeSet(Sets.newHashSet(staffIds.split(",")).stream().map(Long::parseLong).collect(Collectors.toSet()));

        Long indexes = leadsDistributeConf.getIndexes();
        if (indexes == 0L || !staffSet.contains(indexes)) {
            nextStaffId = staffSet.first();
        }

        Iterator<Long> iterator = staffSet.stream().iterator();
        while (iterator.hasNext() && nextStaffId == null) {
            if (iterator.next().equals(indexes)) {
                nextStaffId = iterator.hasNext() ? iterator.next() : staffSet.first();
            }
        }

        boolean updateIndex = distributeStaffId == null || distributeStaffId.equals(nextStaffId) || distributeStaffId.equals(0L);
        //不需要更新员工分配索引 如:分配预览
        if (indexNeedsToUpdated && updateIndex) {
            leadsDistributeConfManager.update(Wrappers.<LeadsDistributeConf>update()
                    .eq(LeadsDistributeConf.ID, leadsDistributeConf.getId())
                    .set(LeadsDistributeConf.INDEXES, nextStaffId)
            );
        } else {
            leadsDistributeConf.setIndexes(nextStaffId);
        }

        return nextStaffId;
    }

    /**
     * 设置自动分配的员工
     */
    private void setUpAutomaticallyAssignedStaff(
            Map<String, LeadsDistributeConf> distributeMap, Leads leads, Map<Integer, String> channelIdNameMap,
            DistributeStaffState distributeStaffState, Boolean indexNeedsToUpdated) {
        Long staffId;
        if (Objects.isNull(leads.getDistributeStaffId()) || leads.getDistributeStaffId() <= 0L){
            logger.info("未指定接收人->setUpAutomaticallyAssignedStaff distributeMap :{}", distributeMap);
            //找到最佳配置 省>市>区
            LeadsDistributeConf automaticallyAssignConfig = findAutomaticallyAssignConfig(leads, distributeStaffState.getChannelConfIdsMap(),distributeMap);
            //未找到匹配的接收人并且未指定接收人
            if (ObjectUtils.isEmpty(automaticallyAssignConfig) ) {
                setErrorMsg(leads, channelIdNameMap);
                return;
            }

            //未指定分配员工 执行轮流分配 获取下一个分配的员工
            staffId = getNextAssignedStaff(automaticallyAssignConfig, indexNeedsToUpdated, null);
        }else {
            logger.info("指定接收人:{}",leads.getDistributeStaffId());
            staffId = leads.getDistributeStaffId();
        }

        //查看员工是否开启了自动转交配置若开启则将线索分配到配置的员工处
        //判断是否分配预览，分配预览不做自动转交
        if (distributeStaffState.isFinalSaveIndex() || indexNeedsToUpdated){
            staffId = autoForwardStaff(distributeStaffState, leads, staffId);
        }
        logger.info("setUpAutomaticallyAssignedStaff staffId :{}", staffId);

        //当线索配置为自动分配时且地区线索接收人配置中有绑定当前线索的区域接收人时则将接受人绑定进当前线索数据中
        if (Objects.nonNull(staffId)) {
            leads.setDistributeStaffId(staffId);
            //将线索数据填充为已分配未联系
            leads.setStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
            leads.setDistributeTime(LocalDateTime.now());
        }
    }

    private Long autoForwardStaff(DistributeStaffState distributeStaffState, Leads leads, Long staffId) {
        StaffDto staff = staffClientForLeads.getStaffById(leads.getBid(), staffId);
        Long forwardStaffId = leadsDistributeForwardConfManager.getByStaffId(leads.getBid(), staffId);
        if (Objects.isNull(forwardStaffId)){
            return staffId;
        }

        StaffDto forwardStaff = staffClientForLeads.getStaffById(leads.getBid(), forwardStaffId);

        //判断被转交的员工状态 不为已开通 不转交
        if (!StaffStatusEnum.OPENED.equals(StaffStatusEnum.parse(forwardStaff.getStatus()))){
            return staffId;
        }

        distributeStaffState.setAutoForwardLeadsFollowList(Optional.ofNullable(distributeStaffState.getAutoForwardLeadsFollowList()).orElse(Lists.newArrayList()));

        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setId(SnowflakeHelper.getId());
        leadsFollow.setBid(leads.getBid());
        leadsFollow.setLeadsId(leads.getId());
        leadsFollow.setStaffId(staffId);
        leadsFollow.setFollowType(1);
        leadsFollow.setFollowContent("【" + staff.getName() + "】将线索分配给【" + forwardStaff.getName() + "】");
        leadsFollow.setLeadsStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
        leadsFollow.setOperatorRole(0);
        leadsFollow.setCreateTime(LocalDateTime.now());

        distributeStaffState.getAutoForwardLeadsFollowList().add(leadsFollow);

        return forwardStaffId;
    }

    private void setErrorMsg(Leads leads, Map<Integer, String> channelIdNameMap) {
        //当系统为自动分配但没有找到当前线索的区域接收人时则设置当前线索的错误信息，且线索的状态为分配失败
        leads.setStatus(LeadsStatusEnum.DISTRIBUTE_ERR.getStatus());


        String msg = Optional.ofNullable(leads.getProvinceName()).orElse("") +
                Optional.ofNullable(leads.getCityName()).orElse("") +
                Optional.ofNullable(leads.getAreaName()).orElse("") + "未设置接收人员";

        String channelName = channelIdNameMap.get(Optional.ofNullable(leads.getChannelId()).orElse(0L).intValue());

        msg = StringUtils.isEmpty(channelName) ? msg : channelName + InngkeAppConst.MIDDLE_LINE_STR + msg;

        leads.setErrorMsg(msg);
        leads.setDistributeTime(LocalDateTime.now());
    }

    /**
     * 找到最佳分配配置
     *
     * @param leads             线索的所有区域 排序必须为 省,市,区
     * @param channelConfIdsMap
     * @param distributeMap
     * @return
     */
    private LeadsDistributeConf findAutomaticallyAssignConfig(Leads leads, Map<Integer, List<Long>> channelConfIdsMap, Map<String, LeadsDistributeConf> distributeMap) {
        Long channelId = Optional.ofNullable(leads.getChannelId()).orElse(0L);

        //优先使用线索渠道配置的接收人员
        if (Objects.nonNull(leads.getChannel())){
            List<Long> confIds = channelConfIdsMap.get(leads.getChannel());
            Long channelConfId = Optional.ofNullable(confIds).map(Collection::stream).flatMap(Stream::findFirst).orElse(null);
            if (Objects.nonNull(channelConfId)){
                LeadsDistributeConf leadsDistributeConf = Optional.ofNullable(Optional.ofNullable(distributeMap.get(getLeadsDistributeMapKey(channelConfId, leads.getAreaId())))
                                .orElse(distributeMap.get(getLeadsDistributeMapKey(channelConfId, leads.getCityId()))))
                        .orElse(distributeMap.get(getLeadsDistributeMapKey(channelConfId, leads.getProvinceId())));
                if (Objects.nonNull(leadsDistributeConf)){
                    return leadsDistributeConf;
                }
            }
        }

        //未找到再使用线索产品渠道的接收人
        return Optional.ofNullable(Optional.ofNullable(distributeMap.get(getLeadsDistributeMapKey(channelId, leads.getAreaId())))
                        .orElse(distributeMap.get(getLeadsDistributeMapKey(channelId, leads.getCityId()))))
                .orElse(distributeMap.get(getLeadsDistributeMapKey(channelId, leads.getProvinceId())));
    }

    private String getLeadsDistributeMapKey(Long channelId, Integer areaId) {
        return channelId + InngkeAppConst.CLN_STR + areaId;
    }

    /**
     * 获取线索所有的地区 [省,市,区]
     *
     * @param leads 线索详情
     * @return
     */
    private List<RegionDto> findLeadsAllRegion(Leads leads) {
        return Lists.newArrayList(
                leadsRegionCache.getProvinceNumMap().get(leads.getProvinceId()),
                leadsRegionCache.getCityNumMap().get(leads.getCityId()),
                leadsRegionCache.getAreaNumMap().get(leads.getAreaId())
        );
    }

    /**
     * 修改线索信息
     *
     * @param request 修改请求
     * @return 修改成功后的线索信息
     */
    @Override
    public BaseResponse<LeadsDto> save(LeadsUpdateRequest request) {
        Leads leads = LeadsConverter.toUpdateLeads(request);
        Integer bid = request.getBid();
        if (Objects.isNull(leads)) {
            logger.error("线索修改：请求体为空");
            return BaseResponse.error("请求体为空");
        }
        //线索信息校验
        validateLeadsInfo(leads);
        //组装客户uid和id
        installCustomerInfo(leads, request.getOperatorId());
        LeadsConf leadsConf = leadsConfManager.getOne(Wrappers.<LeadsConf>query().select(LeadsConf.DISTRIBUTE_TYPE).eq(LeadsConf.ID, leads.getBid()));
        Integer distributeType = leadsConf.getDistributeType();

        //填充地址
        fillAreaInformation(leads, findLeadsAllRegion(leads));

        //线索分配
        if (distributeType == 1) {
            distributeStaff(leads, bid);
        }
        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(Lists.newArrayList(leads), bid);
        leadsManager.updateLeads(leads, request.getOperatorId());
        LeadsDto leadsVo = LeadsConverter.toLeadsListItem(leads, staffAndAgent);
        Long distributeStaffId = leads.getDistributeStaffId();
        if (distributeType == 1) {
            if (leads.getDistributeStaffId() == null || distributeStaffId == 0) {
                leadsVo.setErrorMsg(leads.getErrorMsg());
                leadsVo.setDistributeStatus(-1);
            } else {
                //如果开启了自动分配且自动分配数据已经成功入库则异步发送公众号消息
                AsyncUtils.runAsync(() -> sendWxPubMessage(bid, request.getOperatorId(), Lists.newArrayList(leads), null, 0));
                //异步发送延时公众号消息发送
                sendDelayMessage(request.getOperatorId(), leads.getBid(), Lists.newArrayList(leads));
            }
        } else {
            leadsVo.setDistributeStatus(0);
        }

        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(bid);
            leadsUpdateRequest.setIds(Lists.newArrayList(request.getId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return BaseResponse.success(leadsVo);
    }

    public BaseResponse<Boolean> updateStatus(LeadsStatusUpdateRequest request) {

        Integer status = request.getStatus();
        Integer bid = request.getBid();
        Long customerId = request.getOperatorId();

        // 通过客户编号查询员工信息
        List<StaffDto> staffList = staffClientForLeads.getStaffListByCustomerId(bid, customerId);
        // 找出员工相关的经销商信息
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setIds(staffList.stream().map(StaffDto::getId).collect(Collectors.toList()));
        baseIdsRequest.setBid(bid);
        List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtoList = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        if (Objects.isNull(staffIdAndAgentIdDtoList) || staffIdAndAgentIdDtoList.isEmpty()) {
            logger.error("员工的经销商信息查询失败 {}", baseIdsRequest);
            return BaseResponse.error("员工的经销商信息查询失败" + baseIdsRequest);
        }

        // 适配原来的数据结构
        List<AgentIdAndNameDto> agentIdAndNameList = staffIdAndAgentIdDtoList.stream().map(StaffAndAgentConverter::toAgentIdAndNameDto).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentIdAndNameList)) {
            return BaseResponse.error("操作员信息缺失,无法修改线索状态");
        }

        AgentIdAndNameDto agentIdAndNameDto = agentIdAndNameList.get(0);
        String name = agentIdAndNameDto.getName();
        //查询
        Leads leads = leadsManager.getOne(Wrappers.<Leads>query()
                .select(Leads.ID, Leads.BID, Leads.DISTRIBUTE_STAFF_ID, Leads.STATUS)
                .eq(Leads.BID, bid).eq(Leads.ID, request.getId()).last("LIMIT 1"));

        String oldStatus = LeadsStatusEnum.parse(leads.getStatus()).getName();
        String nowStatus = LeadsStatusEnum.parse(status).getName();
        String followInfo = "将线索状态由【" + oldStatus + "】修改为【" + nowStatus + "】";
        try {
            leadsManager.updateStatus(leads, customerId, followInfo, status);
        } catch (Exception e) {
            logger.error("修改线索状态失败，错误信息", e);
            return BaseResponse.error("修改线索状态失败");
        }
        return BaseResponse.success(true);
    }

    /**
     * 批量分配线索
     *
     * @param request 分配请求
     * @return 是否分配成功
     */
    @Override
    public BaseResponse<LeadsDistributeResultDto> distribute(LeadsDistributeRequest request) {
        logger.info("com.inngke.bp.leads.service.impl.LeadsChangeServiceImpl.distribute");
        Integer bid = request.getBid();
        //线索IDs
        Map<Long, Long> guidesMap = distributeGetLeadsId(request.getGuides());
        Set<Long> idList = Optional.ofNullable(request.getIds()).orElse(new HashSet<>());
        idList.addAll(guidesMap.keySet());
        if (CollectionUtils.isEmpty(idList)){
            logger.warn("分配线索id集合为空！");
            return BaseResponse.error("批量分配线索失败!");
        }
        //根据线索Id集合查询所有相应的线索，查询出来整个实体信息是为了在线索日志的log_content字段中存储json,只有未分配以下的状态的线索才可以进行分配
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, idList)
                        .in(Leads.STATUS,
                                LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                                LeadsStatusEnum.PUSH_BACK.getStatus(),
                                LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                                LeadsStatusEnum.PRE_FOLLOW.getStatus())
        );

        Long staffId = request.getStaffId();
        //当请求参数中存在staffId时代表为重新分配时的接口请求，若不存在则代表为一键分配时的接口请求
        List<LeadsFollow> autoForwardLeadsFollowList = Lists.newArrayList();
        if (staffId == null) {
            //根据配置自动分配
            List<Leads> autoDistribute = leadsList.stream().filter(item -> needAutoDistribute(guidesMap, item.getId())).collect(Collectors.toList());
            List<Leads> notAutoDistribute = leadsList.stream().filter(item -> !needAutoDistribute(guidesMap, item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(autoDistribute)) {
                DistributeStaffState distributeStaffState = distributeStaffFinalSaveIndex(autoDistribute, bid);
                Optional.ofNullable(distributeStaffState.getAutoForwardLeadsFollowList()).ifPresent(autoForwardLeadsFollowList::addAll);
            }
            if (!CollectionUtils.isEmpty(notAutoDistribute)) {
                notAutoDistribute.forEach(item -> item.setDistributeStaffId(guidesMap.get(item.getId())));
                DistributeStaffState distributeStaffState = distributeStaffFinalSaveIndex(notAutoDistribute, bid);
                Optional.ofNullable(distributeStaffState.getAutoForwardLeadsFollowList()).ifPresent(autoForwardLeadsFollowList::addAll);
            }
        } else {
            //分配给指定员工
            StaffDto staff = staffClientForLeads.getStaffById(bid, staffId);
            if (Objects.isNull(staff)){
                throw new InngkeServiceException("获取员工信息失败");
            }
            leadsList.forEach(item -> item.setDistributeStaffId(staffId));
        }

        //Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(leadsList, bid);
        // 数据维护到Es中 不需要维护到MySQL中
        // setAgentsId(leadsList);

        //批量分配
        leadsManager.saveDistributeLeads(leadsList, request.getOperatorId());

        //保存自动转交的跟进记录
        autoForwardLeadsFollowList = autoForwardLeadsFollowList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(autoForwardLeadsFollowList)){
            // 添加跟进记录，【操作者员工姓名】将线索分配给【接收线索员工姓名】
            Map<Long, List<LeadsFollow>> leadsAutoForwardFollowGroup = autoForwardLeadsFollowList.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));
            List<Leads> newLeadsList = jsonService.toObjectList(jsonService.toJson((Serializable) leadsList), Leads.class);
            newLeadsList.forEach(leads -> Optional.ofNullable(
                    leadsAutoForwardFollowGroup.get(leads.getId())
            ).flatMap(leadsAutoForwardFollowList ->
                    leadsAutoForwardFollowList.stream().findFirst().map(LeadsFollow::getStaffId)
            ).ifPresent(leads::setDistributeStaffId));

            distributeLeadsFollow(bid, request.getSid(), newLeadsList, false);

            leadsFollowManager.saveBatch(autoForwardLeadsFollowList);
        }else {
            // 添加跟进记录，【操作者员工姓名】将线索分配给【接收线索员工姓名】
            distributeLeadsFollow(bid, request.getSid(), leadsList, false);
        }

        //发送公众号消息
        AsyncUtils.runAsync(() -> sendWxPubMessage(bid, request.getOperatorId(), leadsList, null, 0));

        if (Boolean.TRUE.equals(request.getCanRelationClient())) {
            leadsList.stream().filter(leads -> org.apache.commons.lang3.StringUtils.isNotBlank(leads.getMobile()))
                    .filter(leads -> Objects.nonNull(leads.getDistributeStaffId()) && leads.getDistributeStaffId() > 0L)
                    .forEach(leads -> {
                        ClientDto clientDto = clientGetClientForLeads.findClientByMobileAndStaff(bid, leads.getMobile(), leads.getDistributeStaffId());
                        ClientBindLeadsRequest clientBindLeadsRequest = new ClientBindLeadsRequest();
                        clientBindLeadsRequest.setBid(leads.getBid());
                        clientBindLeadsRequest.setLeadsBasicDto(LeadsConverter.leadsBasicDtoToClientLeadsBasicDto(LeadsConverter.toLeadsBasicDto(leads)));
                        clientBindLeadsRequest.setStaffId(leads.getDistributeStaffId());
                        if (Objects.nonNull(clientDto)) {
                            clientBindLeadsService.autoClientBindLeads(clientBindLeadsRequest);
                        } else {
                            clientBindLeadsService.autoLeadsTransformClient(clientBindLeadsRequest);
                        }
                    });
        }

        //异步发送各分配员分组的线索延时通知
        LeadsDistributeResultDto leadsDistributeResultDto = toLeadsDistributeResultDto(leadsList);
        //更新es索引
        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(new ArrayList<>(idList));
        leadsUpdateRequest.setRefreshEs(true);
        leadsEsService.updateDocs(leadsUpdateRequest);

        return BaseResponse.success(leadsDistributeResultDto);
    }

    private void distributeLeadsFollow(Integer bid, Long sid, List<Leads> leadsList, boolean isPreFollow) {
        if (CollectionUtils.isEmpty(leadsList)) {
            return;
        }
        List<LeadsFollow> follows = new ArrayList<>(leadsList.size());

        Set<Long> staffIds = new HashSet<>(leadsList.size() + 1);
        staffIds.add(sid);
        Map<Long, Long> leadsIdToStaffId = new HashMap<>(leadsList.size());

        LocalDateTime now = LocalDateTime.now();
        for (Leads leads : leadsList) {
            Long staffId = isPreFollow ? leads.getPreFollowStaffId() : leads.getDistributeStaffId();
            Long leadsId = leads.getId();
            if (staffId != null && !staffId.equals(0L)) {
                LeadsFollow follow = new LeadsFollow();
                follow.setBid(bid);
                follow.setLeadsId(leadsId);
                follow.setStaffId(sid);
                StaffDto staffDto = staffClientForLeads.getStaffById(bid, sid);
                if (Objects.nonNull(staffDto)) {
                    follow.setUserId(staffDto.getCustomerId());
                }
                follow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
                follow.setLeadsStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
                follow.setCreateTime(now);
                follow.setBeforeLeadsStatus(leads.getStatus());
                leadsIdToStaffId.put(leadsId, staffId);

                follows.add(follow);

                staffIds.add(staffId);
            }
        }
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffIds);

        String sName = staffByIds.get(sid) == null ? "系统" : staffByIds.get(sid).getName();

        follows.forEach(item -> {
            Long staffId = leadsIdToStaffId.get(item.getLeadsId());
            String staffName = staffByIds.get(staffId) == null ? "" : staffByIds.get(staffId).getName();
            String content = "【" + sName + "】" + "将线索分配给" + "【" + staffName + "】";
            item.setFollowContent(content);
        });
        if (CollectionUtils.isEmpty(follows)) {
            return;
        }
        leadsFollowManager.saveBatch(follows);

        follows.forEach(item -> leadsFollowCacheService.add(bid, item.getId()));

        leadsManager.updateBatchById(
                follows.stream().map(follow -> {
                    Leads leads = new Leads();
                    leads.setBid(bid);
                    leads.setId(follow.getLeadsId());
                    leads.setLastFollowId(follow.getId());
                    leads.setLastFollowTime(follow.getCreateTime());
                    return leads;
                }).collect(Collectors.toList())
        );
    }

    /**
     * 判断是否需要自动分配
     */
    private boolean needAutoDistribute(Map<Long, Long> staffIdMap, Long leadsId) {
        Long staffId = staffIdMap.get(leadsId);
        return staffId == null || staffId.equals(0L);
    }

    @Override
    public BaseResponse<LeadsDistributeResultDto> distributeWithFollowStaff(LeadsDistributeRequest request) {
        // 锁两秒，限制用户操作频率
        Lock lock = lockService.getLock(DISTRIBUTE_LOCK_KEY + request.getOperatorId(), 2);
        if (lock == null) {
            return BaseResponse.error("操作速度过快");
        }
        // 获取配置
        LeadsConf leadsConf = leadsConfManager.getById(request.getBid());

        // 走原来的分配逻辑
        if (leadsConf == null || !Boolean.TRUE.equals(leadsConf.getPreFollowEnable())) {
            return distribute(request);
        }
        Set<Long> leadsIds = distributeGetAllLeadsId(request);
        int leadsAllCount = leadsIds.size();

        Map<Long, Long> preFollowMap = distributeGetLeadsId(request.getPreFollows());
        // 指定分配给客服
        List<LeadsDistributeToStaff> notAutoDistribute = Optional.ofNullable(request.getPreFollows()).orElse(new ArrayList<>()).stream()
                .filter(item -> !needAutoDistribute(preFollowMap, item.getLeadsId())).collect(Collectors.toList());

        // 去除分配给指定客服和导购的线索
        leadsIds.removeAll(distributeGetLeadsId(notAutoDistribute).keySet());
        Set<Long> guides = distributeGetLeadsId(request.getGuides()).keySet();
        leadsIds.removeAll(guides);

        List<Leads> leadsList = CollectionUtils.isEmpty(leadsIds) ? new ArrayList<>() : leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .in(Leads.ID, leadsIds)
                        .between(Leads.STATUS, LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(), LeadsStatusEnum.TO_DISTRIBUTE.getStatus()));

        // 已经客服分配的集合
        Set<Long> hasDistributeId = new HashSet<>(leadsList.size());

        List<Leads> hasPreFollowLeads = distributeFollow(request.getBid(), leadsList, leadsConf);
        // 添加分配给指定客服
        hasPreFollowLeads.addAll(createUpdatePreFollowStaffIdLeads(notAutoDistribute));
        hasPreFollowLeads.forEach(item -> hasDistributeId.add(item.getId()));

        // 去除已经分配给客服的线索
        leadsIds.removeAll(hasDistributeId);

        LeadsDistributeResultDto result = new LeadsDistributeResultDto();

        if (!CollectionUtils.isEmpty(leadsIds) || !CollectionUtils.isEmpty(guides)) {
            LeadsDistributeRequest distributeRequest = new LeadsDistributeRequest();
            distributeRequest.setBid(request.getBid());
            distributeRequest.setStaffId(request.getStaffId());
            distributeRequest.setIds(leadsIds);
            distributeRequest.setGuides(request.getGuides());
            distributeRequest.setOperatorId(request.getOperatorId());
            BaseResponse<LeadsDistributeResultDto> distribute = distribute(request);
            if (!BaseResponse.responseSuccessWithNonNullData(distribute)) {
                return distribute;
            }
            result = distribute.getData();
        }

        if (!CollectionUtils.isEmpty(hasPreFollowLeads)) {
            List<Leads> updates = new ArrayList<>(hasPreFollowLeads.size());
            hasPreFollowLeads.forEach(item -> updates.add(createUpdatePreFollowStaffIdLeads(item.getId(), item.getPreFollowStaffId())));
            leadsManager.updateBatchById(updates);

            // 添加跟进记录
            distributeLeadsFollow(request.getBid(), request.getSid(), updates, true);

            //更新es索引
            //AsyncUtils.runAsync(() -> {
            List<Long> ids = new ArrayList<>(hasPreFollowLeads.size());
            hasPreFollowLeads.forEach(item -> ids.add(item.getId()));

            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setIds(ids);
            leadsUpdateRequest.setRefreshEs(true);
            leadsEsService.updateDocs(leadsUpdateRequest);
            //});
            AsyncUtils.runAsync(() -> AsyncUtils.runAsync(() -> leadsEsService.sendLeadsChangeMq(request.getBid(), ids, null , null, 4)));
        }

        result.setLeadsAllCount(leadsAllCount);
        result.setDistributedCount(hasDistributeId.size() + Optional.ofNullable(result.getDistributedCount()).orElse(0));
        result.setDistributeErrorCount((Optional.ofNullable(result.getDistributeErrorCount()).orElse(0)));

        return BaseResponse.success(result);
    }


    private Set<Long> distributeGetAllLeadsId(LeadsDistributeRequest request) {
        Set<Long> result = new HashSet<>();
        if (!CollectionUtils.isEmpty(request.getIds())) {
            result.addAll(request.getIds());
        }
        result.addAll(distributeGetLeadsId(request.getGuides()).keySet());
        result.addAll(distributeGetLeadsId(request.getPreFollows()).keySet());
        return result;
    }

    private Map<Long, Long> distributeGetLeadsId(List<LeadsDistributeToStaff> leadStaffs) {
        if (CollectionUtils.isEmpty(leadStaffs)) {
            return new HashMap<>(0);
        }
        Map<Long, Long> result = new HashMap<>(leadStaffs.size());
        leadStaffs.forEach(item -> result.put(item.getLeadsId(), item.getStaffId()));
        return result;
    }


    public boolean distributeWithFollowStaffSingle(int bid, List<Leads> leadsList, LeadsConf leadsConf) {
        if (CollectionUtils.isEmpty(leadsList)) {
            return false;
        }
        Leads leads = leadsList.get(0);
        List<Leads> updates = distributeFollow(bid, leadsList, leadsConf);
        if (!updates.isEmpty()) {
            Leads updateLead = updates.get(0);

            leads.setPreFollowStaffId(updateLead.getPreFollowStaffId());
            leads.setPreFollowStatus(updateLead.getPreFollowStatus());
            leads.setStatus(updateLead.getStatus());
            leads.setDistributeTime(updateLead.getDistributeTime());
            leads.setDistributeFollowTime(updateLead.getDistributeFollowTime());

            return true;
        }
        return false;
    }

    /**
     * 进行分配线索，如果分配成功会设置status、preFollowStatus、preFollowStaffId、distributeTime、distributeFollowTime
     *
     * @param bid       bid
     * @param leadsList 线索列表
     * @param leadsConf 线索配置
     * @return
     */
    private List<Leads> distributeFollow(int bid, List<Leads> leadsList, LeadsConf leadsConf) {
        logger.info("com.inngke.bp.leads.service.impl.LeadsChangeServiceImpl.distributeFollow");
        if (CollectionUtils.isEmpty(leadsList)) {
            return new ArrayList<>();
        }
        // 获取版本号
        String version = preFollowStaffService.getPreFollowVersion(bid);
        // 分配给正在跟进的客服
        List<Leads> updates = repetitionFollowLeadsDistribute(bid, leadsConf, leadsList);

        // 进行分配
        leadsList.forEach(leads -> {
            // 根据区、市、省顺序获取客服Id
            Long followStaffId = distributeFollowStaffId(bid, version, leads);
            if (followStaffId != null) {
                Leads updatePreFollowStaffIdLeads = createUpdatePreFollowStaffIdLeads(leads, followStaffId);
                updates.add(updatePreFollowStaffIdLeads);
            }
        });
        return updates;
    }


    private List<Leads> createUpdatePreFollowStaffIdLeads(List<LeadsDistributeToStaff> preFollows) {
        if (CollectionUtils.isEmpty(preFollows)) {
            return Lists.newArrayList();
        }
        List<Leads> result = new ArrayList<>();
        preFollows.forEach(item -> result.add(createUpdatePreFollowStaffIdLeads(item.getLeadsId(), item.getStaffId())));
        return result;
    }

    private Leads createUpdatePreFollowStaffIdLeads(Leads leads, Long followStaffId) {
        return createUpdatePreFollowStaffIdLeads(leads, leads.getId(), followStaffId);
    }

    private Leads createUpdatePreFollowStaffIdLeads(Long leadId, Long followStaffId) {
        Leads update = new Leads();
        return createUpdatePreFollowStaffIdLeads(update, leadId, followStaffId);
    }

    private Leads createUpdatePreFollowStaffIdLeads(Leads update, Long leadId, Long followStaffId) {
        update.setId(leadId);
        update.setStatus(LeadsStatusEnum.PRE_FOLLOW.getStatus());
        update.setPreFollowStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
        update.setPreFollowStaffId(followStaffId);
//        update.setDistributeTime(LocalDateTime.now());
        update.setDistributeFollowTime(LocalDateTime.now());
        return update;
    }

    /**
     * 如果分配成功会设置status、preFollowStatus、preFollowStaffId、distributeTime、distributeFollowTime
     *
     * @param bid       bid
     * @param leadsConf 线索配置
     * @param leadsList 线索列表
     * @return 成功的线索
     */
    private List<Leads> repetitionFollowLeadsDistribute(int bid, LeadsConf leadsConf, List<Leads> leadsList) {
        List<Leads> updates = new ArrayList<>(leadsList.size());
        if (leadsConf != null && !leadsConf.getRepeatDistributionWay().equals(0)) {
            return updates;
        }

        // 查询是否有相同的手机号码
        Set<String> mobiles = new HashSet<>(leadsList.size());

        leadsList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getMobile())) {
                mobiles.add(item.getMobile());
            }
        });

        // 客服列表
        Set<Long> staffs = rbacClientForLeads.listCustomerRoleUserIds(bid);
        if (CollectionUtils.isEmpty(staffs)) {
            return updates;
        }

        if (CollectionUtils.isEmpty(mobiles)) {
            return updates;
        }

        List<Leads> repetitionLeadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.MOBILE, mobiles)
                        .ge(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                        .ne(Leads.PRE_FOLLOW_STAFF_ID, 0)
                        .orderByDesc(Leads.CREATE_TIME)
                        .groupBy(Leads.MOBILE)
                        .select(Leads.MOBILE, Leads.DISTRIBUTE_STAFF_ID, Leads.PRE_FOLLOW_STAFF_ID)
        );
        if (CollectionUtils.isEmpty(repetitionLeadsList)) {
            return updates;
        }

        Map<String, Long> mobileAndStaffMap = repetitionLeadsList.stream().collect(Collectors.toMap(Leads::getMobile, Leads::getPreFollowStaffId));

        List<Leads> repetition = new ArrayList<>(mobileAndStaffMap.size());

        leadsList.forEach(item -> {
            Long preFollowStaffId = mobileAndStaffMap.get(item.getMobile());
            if (preFollowStaffId != null && staffs.contains(preFollowStaffId)) {
                Leads updatePreFollowStaffIdLeads = createUpdatePreFollowStaffIdLeads(item, preFollowStaffId);
                updates.add(updatePreFollowStaffIdLeads);
                repetition.add(item);
            }
        });

        leadsList.removeAll(repetition);

        return updates;
    }


    /**
     * 获取客服Id
     */
    private Long distributeFollowStaffId(int bid, String version, Leads leads) {
        List<Integer> regionIds = regionIds(leads);

        String staffIdCache = getDistributeCache(leads.getId(), regionIds, version);
        if (staffIdCache != null) {
            return Long.valueOf(staffIdCache);
        }

        Integer channel = leads.getChannel();

        Integer region = null;
        Long staffId = null;
        for (Integer regionId : regionIds) {
            region = regionId;
            staffId = preFollowStaffService.getPreFollow(bid, version, channel, region);
            if (staffId != null) {
                setDistributeCache(leads.getId(), region, staffId, version);
                return staffId;
            }
        }

        return staffId;
    }

    private List<Integer> regionIds(Leads leads) {

        List<Integer> list = new ArrayList<>(3);

        if (leads.getAreaId() != null && !leads.getAreaId().equals(0)) {
            list.add(leads.getAreaId());
        }

        if (leads.getCityId() != null && !leads.getCityId().equals(0)) {
            list.add(leads.getCityId());
        }

        if (leads.getProvinceId() != null && !leads.getProvinceId().equals(0)) {
            list.add(leads.getProvinceId());
        }

        return list;
    }

    private String getDistributeCache(Long leadId, List<Integer> regionIds, String version) {
        if (leadId == null) {
            return null;
        }
        for (Integer regionId : regionIds) {
            String key = DISTRIBUTE_LEADS_CACHE + leadId + InngkeAppConst.CLN_STR + regionId + InngkeAppConst.CLN_STR + version;
            String staffId = ObjectUtils.isEmpty(redisTemplate.opsForValue().get(key)) ? null : String.valueOf(redisTemplate.opsForValue().get(key));
            if (staffId != null) {
                return staffId;
            }
        }
        return null;
    }

    private void setDistributeCache(Long leadId, Integer regionId, Long staffId, String version) {
        if (leadId == null) {
            return;
        }
        String key = DISTRIBUTE_LEADS_CACHE + leadId + InngkeAppConst.CLN_STR + regionId + InngkeAppConst.CLN_STR + version;
        redisTemplate.opsForValue().set(key, String.valueOf(staffId), DISTRIBUTE_LEADS_CACHE_EXPIRE, TimeUnit.MINUTES);
    }


    /**
     * 异步分组发送延时线索消息通知
     *
     * @param operatorId 操作员编号
     * @param bid        商户编号
     * @param leadsList  线索集合
     */
    private void sendDelayMessage(Long operatorId, Integer bid, List<Leads> leadsList) {
        //如果为空集合则直接返回
        if (CollectionUtils.isEmpty(leadsList)) {
            return;
        }
        // 需求需要支持自定义通知时间，改为定时任务实现LeadsSchedule.customFollowNotify()
    /*    Map<Long, List<Leads>> staffGroup = leadsList.stream().collect(Collectors.groupingBy(Leads::getDistributeStaffId));
        //异步发送各分配员分组的线索延时通知
        staffGroup.forEach((id, list) ->
                AsyncUtils.runAsync(() -> {
                    List<Long> leadsIds = list.stream().map(Leads::getId).collect(Collectors.toList());
                    //发延时消息，一小时后触发跟进提醒
                    LeadsFollowMessage message = new LeadsFollowMessage();
                    message.setStaffId(id);
                    message.setLeadsIds(leadsIds);
                    message.setOperatorId(operatorId);
                    message.setBid(bid);

                    MqDelaySendRequest sendRequest = new MqDelaySendRequest();
                    sendRequest.setTopic(LeadsFollowListener.LEADS_TO_FOLLOW_TOPIC_NAME);
                    sendRequest.setOperatorId(operatorId);
                    sendRequest.setBid(bid);

                    //15分钟类型
                    message.setType(1);
                    sendRequest.setDeliverAfter(A_HOUR / 4);
                    sendRequest.setPayload(jsonService.toJson(message));
                    mqService.sendDelay(sendRequest);

                    //发送1小时类型
                    message.setType(3);
                    sendRequest.setDeliverAfter(A_HOUR);
                    sendRequest.setPayload(jsonService.toJson(message));
                    mqService.sendDelay(sendRequest);

                    //发送24小时延时，24小时类型
                    message.setType(2);
                    sendRequest.setDeliverAfter(A_DAY);
                    sendRequest.setPayload(jsonService.toJson(message));
                    mqService.sendDelay(sendRequest);
                })
        );*/
    }

    /**
     * 客服分配
     */
    private List<LeadsDistributePreviewDto> distributePreFollowPreview(LeadsDistributeRequest request, LeadsConf leadsConf) {
        Integer bid = request.getBid();
        Set<Long> idList = request.getIds();

        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, idList)
                        .in(Leads.STATUS,
                                LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                                LeadsStatusEnum.PUSH_BACK.getStatus(),
                                LeadsStatusEnum.TO_DISTRIBUTE.getStatus()
                        )
        );

        List<Leads> result = distributeFollow(request.getBid(), leadsList, leadsConf);

        //组装预览结果集合
        return installPreviewResultList(result, true);
    }

    @Override
    public BaseResponse<List<LeadsDistributePreviewDto>> distributePreview(LeadsDistributeRequest request) {
        logger.info("distributePreview 分发预览");
        Integer bid = request.getBid();
        //线索IDs
        Set<Long> ids = request.getIds();

        Set<Long> preFollowIds = new HashSet<>();

        List<LeadsDistributePreviewDto> result = new ArrayList<>(ids.size());

        // 获取配置
        LeadsConf leadsConf = leadsConfManager.getById(request.getBid());

        if (leadsConf != null && Boolean.TRUE.equals(leadsConf.getPreFollowEnable()) && !Boolean.TRUE.equals(request.getPreFollowDistribute())) {
            List<LeadsDistributePreviewDto> leadsDistributePreviewDto = distributePreFollowPreview(request, leadsConf);
            leadsDistributePreviewDto.forEach(item -> {
                preFollowIds.add(item.getId());
                item.setDistributeStaffName(item.getDistributeStaffName() + "（客服）");
            });
            result.addAll(leadsDistributePreviewDto);
        }

        ids.removeAll(preFollowIds);

        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.success(result);
        }

        //根据线索Id集合查询所有相应的线索，查询出来整个实体信息是为了在线索日志的log_content字段中存储json,只有未分配以下的状态的线索才可以进行分配
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, ids)
                        .in(Leads.STATUS,
                                LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                                LeadsStatusEnum.PUSH_BACK.getStatus(),
                                LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                                LeadsStatusEnum.PRE_FOLLOW.getStatus()
                        ));

        Long staffId = request.getStaffId();
        //当请求参数中存在staffId时代表为重新分配时的接口请求，若不存在则代表为一键分配时的接口请求
        if (staffId == null) {
            //根据配置自动分配
            logger.info("distributePreview 根据配置自动分配");
            distributeStaff(leadsList, bid);
        } else {
            //分配给指定员工
            logger.info("distributePreview 分配给指定员工 staffId {}", staffId);
            leadsList.forEach(item -> item.setDistributeStaffId(staffId));
        }
        //组装预览结果集合
        List<LeadsDistributePreviewDto> leadsDistributePreviews = installPreviewResultList(leadsList, false);

        result.addAll(leadsDistributePreviews);

        return BaseResponse.success(result);
    }

    public void sendWxPubMessage(int bid, Long operatorId, List<Leads> leadsList, String timeInfo, int leadsType) {
        if (CollectionUtils.isEmpty(leadsList)) {
            return;
        }
        List<StaffLeadsCountDto> staffCounter = Lists.newArrayList();
        leadsList.stream()
                //过滤掉没有线索接收人的线索
                .filter(item -> {
                    Long distributeStaffId = item.getDistributeStaffId();
                    return distributeStaffId != null && distributeStaffId > 0;
                })
                .collect(Collectors.groupingBy(Leads::getDistributeStaffId, Collectors.toList()))
                .forEach((staffId, list) -> {
                    if (!CollectionUtils.isEmpty(list)) {
                        int count = list.size();
                        Leads leads = list.get(0);
                        String name = leads.getName();
                        String mobile = "";
                        if (StringUtils.isNotBlank(leads.getMobile())) {
                            mobile = leads.getMobile().replaceFirst(leads.getMobile().substring(3, 7), "****");
                        }

                        if (count > 1) {
                            Leads secLead = list.get(1);
                            String secName = secLead.getName();
                            //String secMobile = secLead.getMobile().replaceFirst(secLead.getMobile().substring(3, 7), "****");
                            String secMobile = secLead.getMobile();
                            if (StringUtils.isNotEmpty(secMobile) && secMobile.length() == 11) {
                                secMobile = secLead.getMobile().replaceFirst(secLead.getMobile().substring(3, 7), "****");
                            }
                            name = name + "," + secName + "...";
                            mobile = leads.getMobile().replaceFirst(leads.getMobile().substring(3, 7), "****") + "," + secMobile + "..";
                        }
                        StaffLeadsCountDto dto = new StaffLeadsCountDto();
                        dto.setStaffId(staffId);
                        dto.setCount(Math.toIntExact(count));
                        dto.setMessageName(name);
                        dto.setMobile(mobile);
                        dto.setLeadsChannelType(Objects.nonNull(leads.getChannelSource())&&leads.getChannelSource().equals(2) ? 1 : 0);
                        dto.setLeadsId(leads.getId());
                        Long id = leads.getId();
                        if (count == 1) {
                            dto.setLeadsId(id);
                        }
                        staffCounter.add(dto);
                    }
                });
        leadsWxPubMessageService.batchSendDistributeMessage(bid, operatorId, staffCounter, timeInfo, leadsType);
    }

    private LeadsDistributeResultDto toLeadsDistributeResultDto(List<Leads> leadsList) {
        int allListSize = leadsList.size();
        long distributeErrorCount = leadsList.stream().filter(item -> item.getStatus().equals(LeadsStatusEnum.DISTRIBUTE_ERR.getStatus())).count();
        int distributedCount = allListSize - (int) distributeErrorCount;
        LeadsDistributeResultDto leadsDistributeResultDto = new LeadsDistributeResultDto();
        leadsDistributeResultDto.setLeadsAllCount(allListSize);
        leadsDistributeResultDto.setDistributedCount(distributedCount);
        leadsDistributeResultDto.setDistributeErrorCount((int) distributeErrorCount);
        return leadsDistributeResultDto;
    }

    /**
     * 获取线索的配置
     *
     * @param bid        bid
     * @param regionIds  地址ID
     * @param channelIds 渠道ID
     * @return
     */
    private Map<String, LeadsDistributeConf> getDistributeMap(Integer bid, Set<Integer> regionIds
            , Set<Long> channelIds, boolean indexNeedsToUpdated) {
        if (!indexNeedsToUpdated) {
            Map<String, LeadsDistributeConf> leadsDistributeConfMap = LOCAL_LEADS_DISTRIBUTE_CONF.get();
            if (leadsDistributeConfMap != null) {
                return leadsDistributeConfMap;
            }
        }
        if (regionIds.isEmpty()) {
            return Maps.newHashMap();
        }
        List<LeadsDistributeConf> list = leadsDistributeConfManager.list(
                Wrappers.<LeadsDistributeConf>query()
                        .eq(LeadsDistributeConf.BID, bid)
                        .in(LeadsDistributeConf.REGION_ID, regionIds)
                        .in(LeadsDistributeConf.CHANNEL_ID, channelIds)
                        .select(LeadsDistributeConf.ID, LeadsDistributeConf.REGION_ID, LeadsDistributeConf.STAFF_IDS
                                , LeadsDistributeConf.INDEXES, LeadsDistributeConf.CHANNEL_ID, LeadsDistributeConf.BID)
        );

        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }

        Map<String, LeadsDistributeConf> leadsDistributeConfMap = list.stream().collect(Collectors.toMap((conf ->
                getLeadsDistributeMapKey(conf.getChannelId(), conf.getRegionId())), (item) -> item));
        if (!indexNeedsToUpdated) {
            LOCAL_LEADS_DISTRIBUTE_CONF.set(leadsDistributeConfMap);
        }

        return leadsDistributeConfMap;
    }

    /**
     * 分配单条线索 分配一次保存一次分配索引(会影响下次的分配顺序)
     *
     * @param leads 线索
     * @param bid   bid
     * @return
     */
    private DistributeStaffState distributeStaff(Leads leads, Integer bid) {
        // 获取配置
        LeadsConf leadsConf = leadsConfManager.getById(bid);

        boolean enablePreFollow = leadsConf != null && leadsConf.getPreFollowEnable();

        // 分配客服不成功，调原来的接口
        if (!enablePreFollow || !distributeWithFollowStaffSingle(bid, Lists.newArrayList(leads), leadsConf)) {
            return distributeStaff(Lists.newArrayList(leads), bid, true, false);
        }
        return null;
    }

    /**
     * 批量分配线索 不保存分配索引(不影响下次的分配顺序)
     *
     * @param leadsList 线索列表
     * @param bid       bid
     */
    private DistributeStaffState distributeStaff(List<Leads> leadsList, Integer bid) {
        return distributeStaff(leadsList, bid, false, false);
    }

    /**
     * 批量分配线索 中途不保存分配索引,分配完成后保存分配索引(会影响下次的分配顺序)
     *
     * @param leadsList 线索列表
     * @param bid       bid
     * @return
     */
    private DistributeStaffState distributeStaffFinalSaveIndex(List<Leads> leadsList, Integer bid) {
        return distributeStaff(leadsList, bid, false, true);
    }

    /**
     * 线索分配主要逻辑
     *
     * @param leadsList           线索列表
     * @param bid                 bid
     * @param indexNeedsToUpdated 是否需要更新员工轮询分配索引
     * @param finalSaveIndex      最终是否需要更新员工轮询分配索引
     * @return
     */
    private DistributeStaffState distributeStaff(List<Leads> leadsList, Integer bid, boolean indexNeedsToUpdated,
                                                 boolean finalSaveIndex) {
        logger.info("distributeStaff 分发员工 leadList size {}", leadsList.size());
        try {
            // 这里执行重复线索分配逻辑，返回重复的线索（函数里已被分配了员工）
            List<Leads> repetitionLeads = doRepetitionLeadsDistribute(bid, leadsList);

            // 将重复线索剔除，不走地区分配
            if (!CollectionUtils.isEmpty(repetitionLeads)) {
                Set<Long> repetitionIds = repetitionLeads.stream().map(Leads::getId).collect(Collectors.toSet());
                leadsList.removeIf(filter -> repetitionIds.contains(filter.getId()));
            }
            logger.info("distributeStaff leadsList {}", leadsList.size());

            Map<Integer, List<Long>> channelConfIdsMap = leadsDistributeChannelConfManager.getChannelConfIdsMap(bid);

            //获取所有的地址ID 和 渠道ID
            Set<Integer> regionIds = Sets.newHashSet();
            Set<Long> channelIds = Sets.newHashSet();
            leadsList.forEach(item -> {
                regionIds.add(item.getProvinceId());
                regionIds.add(item.getCityId());
                regionIds.add(item.getAreaId());
                channelIds.add(Optional.ofNullable(item.getChannelId()).orElse(0L));
            });

            channelIds.addAll(
                    channelConfIdsMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet())
            );

            logger.info("distributeStaff regionIds {}", regionIds);

            DistributeStaffState distributeStaffState = new DistributeStaffState();
            distributeStaffState.setLeadsList(leadsList);
            distributeStaffState.setChannelConfIdsMap(channelConfIdsMap);
            distributeStaffState.setFinalSaveIndex(finalSaveIndex);

            //分配员工
            if (!CollectionUtils.isEmpty(regionIds)) {
                Map<String, LeadsDistributeConf> distributeMap =
                        getDistributeMap(bid, regionIds, channelIds, indexNeedsToUpdated);

                // key-channelId value-channelName
                Map<Integer, String> channelIdNameMap =
                        channelClientForLeads.getAllChannel(bid).stream().collect(
                                Collectors.toMap(ChannelDto::getId, ChannelDto::getName));

                logger.info("distributeStaff channelIdNameMap {}", channelIdNameMap.size());

                leadsList.forEach((leads ->
                        setUpAutomaticallyAssignedStaff(distributeMap, leads, channelIdNameMap, distributeStaffState, indexNeedsToUpdated)));
            }

            // 剩余的元素分配完成后，将重复分配的元素add回原有List
            leadsList.addAll(repetitionLeads);

            AsyncUtils.runAsync(() -> AsyncUtils.runAsync(() -> leadsEsService.sendLeadsChangeMq(bid, leadsList.stream().map(Leads::getId).collect(Collectors.toList()), false, null, 4)));

            //保存分配配置里的index
            if (indexNeedsToUpdated) {
                saveDistributeConfIndex();
            }

            return distributeStaffState;
        } finally {
            if (finalSaveIndex) saveDistributeConfIndex();
            if (!indexNeedsToUpdated) LOCAL_LEADS_DISTRIBUTE_CONF.remove();
        }
    }

    private void saveDistributeConfIndex() {
        Map<String, LeadsDistributeConf> leadsDistributeConfMap = LOCAL_LEADS_DISTRIBUTE_CONF.get();
        if (!CollectionUtils.isEmpty(leadsDistributeConfMap)) {
            List<LeadsDistributeConf> leadsDistributeConfList = leadsDistributeConfMap.keySet()
                    .stream().map((leadsDistributeConfMap::get)).collect(Collectors.toList());
            leadsDistributeConfManager.updateBatchById(leadsDistributeConfList);
        }
    }

    private List<Leads> doRepetitionLeadsDistribute(Integer bid, List<Leads> leadsList) {
        if (CollectionUtils.isEmpty(leadsList)) {
            return Lists.newArrayList();
        }
        BidUtils.setBid(bid);
        LeadsConf conf = leadsConfManager.getOne(
                Wrappers.<LeadsConf>query()
                        .eq(LeadsConf.ID, bid)
                        .eq(LeadsConf.ENABLE, 1)
        );
        if (Objects.isNull(conf) || Objects.isNull(conf.getRepeatDistributionWay())) {
            return Lists.newArrayList();
        }
        // 如果开启重复线索分配导购，直接将重复线索分配给对应导购
        if (conf.getRepeatDistributionWay() == 0) {
            Set<Long> staffIds = new HashSet<>(leadsList.size());
            //先找到重复的线索，将重复的线索从leadsList中剔除，不走之前的配置逻辑，走重复线索分配的逻辑
            List<Leads> repetitionLeads = Lists.newArrayList();
            Map<String, Long> mobileAndStaffMap = getRepetitionLeads(bid, leadsList);
            leadsList.forEach(item -> {
                Long staffId = mobileAndStaffMap.get(item.getMobile());
                if (!StringUtils.isEmpty(item.getMobile()) && staffId != null && staffId != 0) {
                    staffIds.add(staffId);
                    item.setDistributeStaffId(staffId);
                    item.setStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
                    item.setDistributeTime(LocalDateTime.now());
                    repetitionLeads.add(item);
                }
            });
            // 去除员工状态不为已开通的
            doRemoveNoOpenStaff(bid, repetitionLeads, staffIds);
            return repetitionLeads;
        }
        return Lists.newArrayList();
    }

    private void doRemoveNoOpenStaff(int bid, List<Leads> repetitionLeads, Set<Long> staffIds) {
        if (CollectionUtils.isEmpty(repetitionLeads) || CollectionUtils.isEmpty(staffIds)) {
            return;
        }

        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffIds);

        Set<Leads> needRemoveStaff = new HashSet<>(repetitionLeads.size());

        repetitionLeads.forEach(item -> {
            StaffDto staffDto = staffByIds.get(item.getDistributeStaffId());
            if (staffDto == null || !staffDto.getStatus().equals(StaffStatusEnum.OPENED.getCode())) {
                item.setDistributeStaffId(0L);
                needRemoveStaff.add(item);
            }
        });
        repetitionLeads.removeAll(needRemoveStaff);
    }


    /**
     * 获取重复线索所属的员工id
     *
     * @return
     */
    private Map<String, Long> getRepetitionLeads(Integer bid, List<Leads> leadsList) {
        Set<String> mobiles = leadsList.stream().map(Leads::getMobile).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mobiles)) {
            return Maps.newHashMap();
        }
        List<Leads> repetitionLeadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.MOBILE, mobiles)
                        .ge(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                        .ne(Leads.DISTRIBUTE_STAFF_ID, 0)
                        .orderByDesc(Leads.CREATE_TIME)
                        .groupBy(Leads.MOBILE)
                        .select(Leads.MOBILE, Leads.DISTRIBUTE_STAFF_ID)
        );
        if (CollectionUtils.isEmpty(repetitionLeadsList)) {
            return Maps.newHashMap();
        }
        Map<String, Long> mobileAndStaffMap = repetitionLeadsList.stream().collect(Collectors.toMap(Leads::getMobile, Leads::getDistributeStaffId));
        return mobileAndStaffMap;

    }


    /**
     * 迁移到StaffToAgentUtil里了
     */
    @Deprecated
    private Map<Long, Long> getStaffAndAgent(List<Leads> leadsList, Integer bid) {
        List<Long> staffIds = leadsList.stream()
                .map(Leads::getDistributeStaffId)
                .filter(distributeStaffId -> distributeStaffId != 0L)
                .collect(Collectors.toList());

        //获取id对应的经销商id键值对map,这个dubbo返回的数据中key和val都是不为null的
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(staffIds);
        List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtoList = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        if (Objects.isNull(staffIdAndAgentIdDtoList) || staffIdAndAgentIdDtoList.isEmpty()) {
            return Maps.newHashMapWithExpectedSize(1);
        }


        return staffIdAndAgentIdDtoList
                .stream()
                .collect(Collectors.groupingBy(StaffIdAndAgentIdDto::getId, Collectors.collectingAndThen(Collectors.toList(), t -> t.get(0).getAgentId())));
    }

    private List<LeadsDistributePreviewDto> installPreviewResultList(List<Leads> leadsList, boolean isPreFollow) {
        if (CollectionUtils.isEmpty(leadsList)) {
            return Lists.newArrayList();
        }
        Integer bid = leadsList.get(0).getBid();
        //获取员工集合对应的经销商id映射Map
        Map<Long, LeadsStaffAndDepartmentName> staffAndAgent = getStaffAndDepartment(bid, leadsList, isPreFollow);

        return leadsList.stream().map(leads -> {
            Long staffId = isPreFollow ? leads.getPreFollowStaffId() : leads.getDistributeStaffId();
            Integer roleType = isPreFollow ? LeadsDistributeRoleType.CUSTOMER_SERVICE.getCode() : LeadsDistributeRoleType.GUIDE.getCode();
            LeadsStaffAndDepartmentName staffAndAgentDto = staffAndAgent.get(staffId);
            LeadsDistributePreviewDto distributePreviewDto = LeadsConverter.toLeadsDistributePreviewDto(leads);
            distributePreviewDto.setStaffId(staffId);
            distributePreviewDto.setRoleType(roleType);
            //无对应的分配人员的线索组装异常信息，有对应分配人员的线索组装经销商信息
            if (staffId != null && staffId != 0 && staffAndAgentDto != null) {
                distributePreviewDto.setDepartmentName(Optional.ofNullable(staffAndAgentDto.getDepartmentName()).orElse(""));
                distributePreviewDto.setDistributeStaffName(Optional.ofNullable(staffAndAgentDto.getStaffName()).orElse(""));
            }
            return distributePreviewDto;
        }).collect(Collectors.toList());

    }

    private Map<Long, LeadsStaffAndDepartmentName> getStaffAndDepartment(Integer bid, List<Leads> leadsList, boolean isPreFollow) {
        Set<Long> staffIds = new HashSet<>(leadsList.size());
        if (isPreFollow) {
            leadsList.forEach(item -> staffIds.add(item.getPreFollowStaffId()));
        } else {
            leadsList.forEach(item -> staffIds.add(item.getDistributeStaffId()));
        }
        if (CollectionUtils.isEmpty(staffIds)) {
            return new HashMap<>(0);
        }
        // 查询员工
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffIds);

        Map<Long, LeadsStaffAndDepartmentName> result = new HashMap<>(staffByIds.size());
        Map<Long, List<LeadsStaffAndDepartmentName>> departmentIdsStaff = new HashMap<>(staffByIds.size());

        Set<Long> departmentIds = new HashSet<>(staffIds.size());

        staffByIds.forEach((staffId, staff) -> {
            departmentIds.add(staff.getDepartmentId());

            LeadsStaffAndDepartmentName leadsStaffAndDepartmentName = new LeadsStaffAndDepartmentName();
            leadsStaffAndDepartmentName.setStaffName(staff.getName());

            result.put(staffId, leadsStaffAndDepartmentName);

            List<LeadsStaffAndDepartmentName> leadsStaffAndDepartmentNames =
                    departmentIdsStaff.computeIfAbsent(staff.getDepartmentId(), t -> new ArrayList<>());
            leadsStaffAndDepartmentNames.add(leadsStaffAndDepartmentName);
        });

        // 查询部门
        List<DepartmentDto> departmentByIds = departmentClientForLeads.getDepartmentByIds(bid, new ArrayList<>(departmentIds));

        departmentByIds.forEach(item -> {
            List<LeadsStaffAndDepartmentName> leadsStaffAndDepartmentNames = departmentIdsStaff.get(item.getId());
            if (leadsStaffAndDepartmentNames == null) {
                return;
            }
            leadsStaffAndDepartmentNames.forEach(departmentStaffName -> departmentStaffName.setDepartmentName(item.getName()));
        });
        return result;

    }

    private Map<Long, StaffIdAndAgentIdDto> getStaffAndAgents(List<Leads> leadsList, Integer bid) {
        List<Long> staffIds = leadsList.stream()
                .map(Leads::getDistributeStaffId)
                .filter(distributeStaffId -> distributeStaffId != 0L)
                .collect(Collectors.toList());
        //获取id对应的经销商id键值对map,这个dubbo返回的数据中key和val都是不为null的
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(staffIds);
        List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtoList = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        if (Objects.isNull(staffIdAndAgentIdDtoList) || staffIdAndAgentIdDtoList.isEmpty()) {
            return Maps.newHashMapWithExpectedSize(1);
        }
        return staffIdAndAgentIdDtoList.stream().collect(Collectors.toMap(StaffIdAndAgentIdDto::getId, t -> t));
    }

    /**
     * 退回线索
     *
     * @param request 退回请求
     * @return 是否退回成功
     */
    @Override
    public BaseResponse<Boolean> pushBack(LeadsPushBackRequest request) {
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.ID, request.getId())
        );

        if (leads == null) {
            return BaseResponse.error("退回的线索不存在");
        }
        LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(leads.getStatus());
        if (statusEnum == null) {
            return BaseResponse.error("当前线索状态异常！");
        }

        if ((statusEnum.getStatus() < LeadsStatusEnum.DISTRIBUTED.getStatus() && !statusEnum.equals(LeadsStatusEnum.UNSURE_INTENT)) || statusEnum.getStatus() > LeadsStatusEnum.INSTALLED.getStatus()) {
            throw new InngkeServiceException("线索当前状态不允许退回");
        }
        //更新线索状态记录
        ClearLeadsStatusRecordRequest clearLeadsStatusRecordRequest = new ClearLeadsStatusRecordRequest();
        clearLeadsStatusRecordRequest.setBid(request.getBid());
        clearLeadsStatusRecordRequest.setLeadsIds(Sets.newHashSet(request.getId()));
        clearLeadsStatusRecordRequest.setStatus(LeadsStatusEnum.PUSH_BACK.getStatus());
        clearLeadsStatusRecordRequest.setHasKf(
                Objects.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus(), leads.getStatus()) ||
                        (leads.getPreFollowStaffId() != null && !leads.getPreFollowStaffId().equals(0L))
        );
        leadsServiceV2.clearLeadsStatusRecord(clearLeadsStatusRecordRequest);

        leads.setBid(request.getBid());
        leads.setErrorMsg(request.getContent());
        LeadsFollow leadsFollow = leadsManager.pushBack(leads, request.getOperatorId(), request.getOperatorUserId(), request.getReason(), request.getReasonId(), request.getPushbackImages());

        if (Objects.nonNull(leadsFollow)) {
            mqServiceForLeads.sendLeadsFollowMq(leadsFollow);
        }

        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setIds(Lists.newArrayList(leads.getId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return BaseResponse.success(true);
    }

    /**
     * 删除线索
     * 仅允许删除分配失败和退回状态的线索
     *
     * @param request 删除请求
     * @return 是否删除成功
     */
    @Override
    public BaseResponse<Boolean> delete(LeadsBatchRequest request) {
        Set<Long> ids = request.getIds();
        List<Long> idList = Lists.newArrayList(ids.toArray(new Long[0]));
        int size = leadsManager.list(Wrappers.<Leads>query().eq(Leads.BID, request.getBid()).in(Leads.ID, idList).lt(Leads.STATUS, 2)).size();
        if (size != ids.size()) {
            return BaseResponse.error("线索中有非未联系以下状态不允许删除");
        }
        //状态1为未联系，状态2为24小时联系
        leadsManager.batchDelete(request.getBid(), idList, request.getOperatorId());
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
            esRequest.setBid(request.getBid());
            esRequest.setIds(idList);
            leadsEsService.updateDocs(esRequest);
        });
        return BaseResponse.success(true);
    }

    /**
     * 转移线索
     *
     * @param request 转移请求
     * @return 是否转移成功
     */
    @Override
    public BaseResponse<Boolean> forward(LeadsForwardRequest request) {
        Integer bid = request.getBid();
        LeadsConf one = leadsConfManager.getOne(Wrappers.<LeadsConf>query().select(LeadsConf.FORWARD_ENABLE).eq(LeadsConf.ID, bid));
        boolean forwardEnable = false;
        if (one != null) {
            forwardEnable = one.getForwardEnable();
        }
        if (!forwardEnable) {
            return BaseResponse.error("系统暂不允许线索转发");
        }

        Leads leads = leadsManager.getOne(Wrappers.<Leads>query().eq(Leads.BID, bid).eq(Leads.ID, request.getId()));

        Long forwardStaffId = request.getOperatorStaffId();
        Long acceptStaffId = request.getStaffId();
        List<Long> staffIds = Lists.newArrayList();
        staffIds.add(forwardStaffId);
        staffIds.add(acceptStaffId);

        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(bid);
        staffListRequest.setIds(staffIds);
        List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);
        if (Objects.isNull(staffList) || staffList.isEmpty()) {
            throw new InngkeServiceException("转移员工不存在");
        }

        Map<Long, StaffDto> staffIdMap;
        StaffDto forwardStaff;
        StaffDto acceptStaff;
        if (!CollectionUtils.isEmpty(staffList)) {
            staffIdMap = staffList.stream().collect(Collectors.toMap(StaffDto::getId, t -> t));
            forwardStaff = staffIdMap.get(forwardStaffId);
            acceptStaff = staffIdMap.get(acceptStaffId);
            if (forwardStaff == null) {
                throw new InngkeServiceException("转移员工信息不存在");
            }
            if (acceptStaff == null) {
                throw new InngkeServiceException("转移接收员工信息不存在");
            }
        } else {
            throw new InngkeServiceException("转移员工不存在");
        }
        leads.setDistributeStaffId(acceptStaffId);
        LeadsFollow forward = leadsManager.forward(leads, request, forwardStaffId, forwardStaff.getName(), acceptStaff.getName());
        if (Objects.nonNull(forward)) {
            mqServiceForLeads.sendLeadsFollowMq(forward, 2);
        }
        AsyncUtils.runAsync(() -> {
            StaffLeadsCountDto counter = new StaffLeadsCountDto();
            counter.setStaffId(acceptStaffId);
            counter.setCount(1);
            counter.setMessageName(leads.getName());
            counter.setMobile(leads.getMobile());
            counter.setLeadsId(leads.getId());
            List<StaffLeadsCountDto> staffCounter = Lists.newArrayList(counter);
            leadsWxPubMessageService.batchSendDistributeMessage(bid, request.getOperatorId(), staffCounter, null, 0);

            //异步更新es数据
            LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
            esRequest.setBid(bid);
            esRequest.setIds(Lists.newArrayList(leads.getId()));
            leadsEsService.updateDocs(esRequest);
        });

        //修改distributorCustomer
        transfer(bid, leads, acceptStaffId);
        return BaseResponse.success(true);
    }

    private void transfer(Integer bid, Leads leads, Long acceptStaffId) {
        StaffDto staffDto = staffClientForLeads.getStaffById(bid, acceptStaffId);
        Long leadId = leads.getId();
        DistributeCustomerTransferRequest distributeCustomerTransferRequest = new DistributeCustomerTransferRequest();
        distributeCustomerTransferRequest.setLeadId(leadId);
        distributeCustomerTransferRequest.setBid(bid);
        distributeCustomerTransferRequest.setTargetGuideId(staffDto.getCustomerId());
        distributorService.transfer(distributeCustomerTransferRequest);
    }

    @Override
    public BaseResponse<Boolean> updateLeads(LeadsUpdateRequest request) {
        Long leadsId = request.getId();
        Leads existsLeads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.ID, leadsId)
                        .select(Leads.STATUS, Leads.EXT_DATA, Leads.PAY_TIME, Leads.DISTRIBUTE_TIME,
                                Leads.PRE_FOLLOW_STAFF_ID, Leads.PRE_FOLLOW_STATUS, Leads.DISTRIBUTE_STAFF_ID
                                , Leads.FOLLOW_STATUSES, Leads.KF_FOLLOW_STATUSES)
        );
        checkRequest(request, existsLeads);

        boolean requestIsRefund = false;
        if (StringUtils.isNotEmpty(request.getExtData())) {
            LeadsExtDataDto leadsExtDataDto = jsonService.toObject(request.getExtData(), LeadsExtDataDto.class);
            if (leadsExtDataDto != null && leadsExtDataDto.getIsRefund() != null && leadsExtDataDto.getIsRefund().equals(1)) {
                requestIsRefund = true;
            }
        }
        //如果线索状态修改，则记录leadsFollow
        LeadsFollow leadsFollow = buildLeadsFollow(request, existsLeads);

        //leads主体
        Leads leads = LeadsConverter.toLeads(request);
        // 添加校验
        validateLeadsInfo(leads);

        //添加线索状态记录
        checkPreFollowStatus(existsLeads.getStatus());


        if (!existsLeads.getPreFollowStaffId().equals(request.getPreFollowStaffId())) {
            leads.setPreFollowStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
        }
        // 判断是否24小时内联系
        leads.setContactIn24(checkCallInOneDay(request.getStatus(), existsLeads.getDistributeTime()));
        //组装LeadsLog
        LeadsLog leadsLog = buildLeadsLog(request, existsLeads.getStatus(), leads);
        //组装ExtData数据
        installExtData(leads);

        //更新leads
        Boolean updateFlag = leadsManager.updateLeads(leads, leadsFollow, leadsLog);
        if (!Boolean.TRUE.equals(updateFlag)) {
            return BaseResponse.success(false);
        }
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setIds(Lists.newArrayList(leadsId));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        //发送退款消息
        if (requestIsRefund) {
            String extData = existsLeads.getExtData();
            LeadsExtDataDto leadsExtDataDto = null;
            if (StringUtils.isNotEmpty(extData)) {
                leadsExtDataDto = jsonService.toObject(extData, LeadsExtDataDto.class);
            }
            if (leadsExtDataDto == null || Integer.valueOf(0).equals(leadsExtDataDto.getIsRefund())) {
//                AsyncUtils.runAsync(() -> {
//                    if (!BaseResponse.responseSuccess(leadsQyWxTemplateMessageUtils.sendLeadsRefundMessage(request.getBid(), request.getId()))) {
//                        leadsWxPubTemplateMessageUtils.sendLeadsRefundMessage(request.getBid(), request.getId());
//                    }
//                });
                LeadsOrderRefundMessageContext notifyContext = LeadsOrderRefundMessageContext.init(request.getBid(), leadsId);
                TemplateMessageContentBuilder<LeadsOrderRefundMessageContext> builder = templateMessageBuilderFactory.getBuilder(notifyContext);
                builder.sendMessage(notifyContext);
            }
        }
        //更新线索状态记录
        if (Objects.nonNull(request.getStatus())) {
            //判断是否客服接待线索
            UpdateLeadsStatusRecordRequest updateLeadsStatusRecordRequest = new UpdateLeadsStatusRecordRequest();
            updateLeadsStatusRecordRequest.setLeadsId(leadsId);
            updateLeadsStatusRecordRequest.setBid(request.getBid());
            updateLeadsStatusRecordRequest.setStatus(Objects.nonNull(request.getPreFollowStatus()) ? request.getPreFollowStatus() : request.getStatus());
            leadsServiceV2.updateLeadsStatusRecord(updateLeadsStatusRecordRequest);
        }
        return BaseResponse.success(true);
    }

    private void setFollowList(LeadsUpdateRequest request, Leads leads, String followList) {
        if (isChangeStatusForGuide(request)) {
            leads.setFollowStatuses(followList);
        }

        if (isChangeStatusForClient(request)) {
            leads.setKfFollowStatuses(followList);
        }
    }

    private String addFollowList(LeadsUpdateRequest request, Leads existsLeads) {
        //导购接待
        if (isChangeStatusForGuide(request)) {
            return getNewFollowList(request.getStatus(), existsLeads.getFollowStatuses());
        }
        //客服接待
        if (isChangeStatusForClient(request)) {
            return getNewFollowList(request.getPreFollowStatus(), existsLeads.getKfFollowStatuses());
        }
        return InngkeAppConst.EMPTY_STR;
    }

    private boolean isChangeStatusForClient(LeadsUpdateRequest request) {
        if (Objects.isNull(request.getStatus())) {
            return false;
        }
        return LeadsStatusEnum.PRE_FOLLOW.getStatus() == request.getStatus()
                && LeadsStatusEnum.DISTRIBUTED.getStatus() != request.getPreFollowStatus();
    }

    private boolean isChangeStatusForGuide(LeadsUpdateRequest request) {
        if (Objects.isNull(request.getStatus())) {
            return false;
        }
        return LeadsStatusEnum.DISTRIBUTED.getStatus() != request.getStatus()
                && LeadsStatusEnum.PRE_FOLLOW.getStatus() != request.getStatus();
    }

    private String getNewFollowList(Integer status, String followStatuses) {
        List<LeadsFollowStatusDto> leadsFollowStatusDtoList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(followStatuses)) {
            leadsFollowStatusDtoList = jsonService.toObjectList(followStatuses, LeadsFollowStatusDto.class);
        }
        boolean isContain = leadsFollowStatusDtoList.stream().anyMatch(i -> status.equals(i.getId()));
        if (!isContain) {
            LeadsFollowStatusDto leadsFollowStatusDto = new LeadsFollowStatusDto();
            leadsFollowStatusDto.setId(status);
            leadsFollowStatusDto.setName(LeadsStatusEnum.parse(status).getName());
            leadsFollowStatusDtoList.add(leadsFollowStatusDto);
        }
        return jsonService.toJson(new ArrayList<>(leadsFollowStatusDtoList));
    }

    private Integer checkCallInOneDay(Integer status, LocalDateTime distributeTime) {
        if (status != null && (status.equals(LeadsStatusEnum.CONTACTED.getStatus()))) {
            if (Objects.isNull(distributeTime)) {
                throw new InngkeServiceException("分配时间为空");
            }
            LocalDateTime now = LocalDateTime.now();
            long hours = Duration.between(distributeTime, now).toHours();
            if (hours < 24) {
                return 1;
            }
        }
        return null;
    }

    private LeadsLog buildLeadsLog(LeadsUpdateRequest request, Integer status, Leads leads) {
        LeadsLog leadsLog = new LeadsLog();
        leadsLog.setCreateTime(LocalDateTime.now());
        //状态修改，为0时代表管理人员
        leadsLog.setDistributeStaffId(leads.getDistributeStaffId());
        //0为待分配
        leadsLog.setStatusChange(leads.getStatus() == null ? status : leads.getStatus());
        leadsLog.setLogContent(jsonService.toJson(leads));
        leadsLog.setOperatorId(request.getOperatorId());
        leadsLog.setBid(request.getBid());
        leadsLog.setLeadsId(leads.getId());
        leadsLog.setDistributeStaffId(leads.getDistributeStaffId());
        return leadsLog;
    }

    private LeadsFollow buildLeadsFollow(LeadsUpdateRequest request, Leads existsLeads) {
        LeadsFollow leadsFollow = null;
        if (!checkPreFollowStatus(existsLeads.getStatus())
                && statusChange(request.getStatus(), existsLeads.getStatus())) {
            String followInfo = buildFollowContent(request, existsLeads.getStatus(), request.getStatus(), false);
            //创建leadsFollow对象
            leadsFollow = buildFollow(request, followInfo);
            leadsFollow.setLeadsStatus(request.getStatus());
            leadsFollow.setPreFollowStatus(existsLeads.getPreFollowStatus());
        }
        if (checkPreFollowStatus(existsLeads.getStatus())
                && statusChange(request.getPreFollowStatus(), existsLeads.getPreFollowStatus())) {
            String followInfo = buildFollowContent(request, existsLeads.getPreFollowStatus(), request.getPreFollowStatus(), true);
            //创建leadsFollow对象
            leadsFollow = buildFollow(request, followInfo);
            leadsFollow.setLeadsStatus(existsLeads.getStatus());
            leadsFollow.setPreFollowStatus(request.getPreFollowStatus());
        }
        if (request.getPreFollowStaffId() != null
                && !request.getPreFollowStaffId().equals(existsLeads.getPreFollowStaffId())) {
            // 将跟进客服由【XX】改为【XX】
            String followInfo = buildFollowContent(request.getBid(), request.getOperatorStaffId(), existsLeads.getPreFollowStaffId(), request.getPreFollowStaffId());
            leadsFollow = buildFollow(request, followInfo);
            leadsFollow.setLeadsStatus(existsLeads.getStatus());
            leadsFollow.setPreFollowStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
        }
        if (Objects.nonNull(leadsFollow)) {
            leadsFollow.setUserId(request.getOperatorId());
        }
        return leadsFollow;
    }

    /**
     * 文案：将跟进客服由【XX】改为【XX】
     */
    private String buildFollowContent(int bid, Long operatorStaffId, Long beforeStaffId, Long afterStaffId) {

        Map<Long, StaffDto> byStaffIds = staffClientForLeads.getStaffByIds(bid, Sets.newHashSet(operatorStaffId, beforeStaffId, afterStaffId));

        StaffDto beforeStaff = byStaffIds.get(beforeStaffId);
        StaffDto afterStaff = byStaffIds.get(afterStaffId);
        StaffDto operatorStaff = byStaffIds.get(operatorStaffId);

        String beforeStaffName = beforeStaff != null ? beforeStaff.getName() : "";
        String afterStaffName = afterStaff != null ? afterStaff.getName() : "";
        String operatorStaffName = operatorStaff != null ? operatorStaff.getName() : "";

        return "【" + operatorStaffName + "】将线索跟进客服由【" + beforeStaffName + "】修改为【" + afterStaffName + "】";
    }


    /**
     * 检查是否为客服线索
     *
     * @param status
     * @return
     */
    private boolean checkPreFollowStatus(Integer status, Integer preStatus) {
        return (Objects.nonNull(status) && status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus())) || Objects.nonNull(preStatus);
    }

    /**
     * 检查是否客服接待状态
     *
     * @param status
     * @return
     */
    private boolean checkPreFollowStatus(Integer status) {
        return status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus());
    }

    private boolean statusChange(Integer status, Integer existStatus) {
        return null != status && !existStatus.equals(status);
    }

    private LeadsFollow buildFollow(LeadsUpdateRequest request, String followInfo) {
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(request.getBid());
        leadsFollow.setFollowType(LeadsFollowTypeEnum.MANUAL.getCode());
        leadsFollow.setFollowContent(followInfo);
        leadsFollow.setLeadsId(request.getId());
        leadsFollow.setUserId(request.getOperatorId());
        leadsFollow.setStaffId(request.getOperatorStaffId());
        leadsFollow.setCreateTime(LocalDateTime.now());
        Leads leads = getOne(request.getBid(), request.getId());
        if (Objects.nonNull(leads)) {
            leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        }
        return leadsFollow;
    }

    private String buildFollowContent(LeadsUpdateRequest request, Integer fromStatus, Integer toStatus, Boolean isPreFollow) {
        //获取管理员名称
        StaffDto staffDto = staffClientForLeads.getStaffById(request.getBid(), request.getOperatorStaffId());
        String name = "";
        if (Objects.nonNull(staffDto)) {
            name = staffDto.getName();
        }
        if (Boolean.TRUE.equals(isPreFollow)) {
            String oldStatus = LeadsPreFollowStatusEnum.parse(fromStatus).getName();
            String nowStatus = LeadsPreFollowStatusEnum.parse(toStatus).getName();
            //组装followInfo
            return "将线索状态由【" + oldStatus + "】修改为【" + nowStatus + "】";
        }
        //获取新老线索状态
        String oldStatus = LeadsStatusEnum.parse(fromStatus).getName();
        String nowStatus = LeadsStatusEnum.parse(toStatus).getName();
        //组装followInfo
        String followInfo = "将线索状态由【" + oldStatus + "】修改为【" + nowStatus + "】";
        return followInfo;
    }

    private void checkRequest(LeadsUpdateRequest request, Leads existsLeads) {
        if (existsLeads == null) {
            throw new InngkeServiceException("没有找到需要修改的线索记录");
        }
        if (Objects.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus(), existsLeads.getStatus())
                && !Objects.equals(existsLeads.getPreFollowStaffId(), 0)
                && Objects.isNull(request.getPreFollowStaffId())) {
            throw new InngkeServiceException("请选择新的客服后再保存");
        }
        checkLeadsStatusUpdated(request, existsLeads);
    }

    /**
     * 校验线索状态，当newStatus>2时,线索历史记录必须修改过为已联系
     *
     * @param request
     */
    private void checkLeadsStatusUpdated(LeadsUpdateRequest request, Leads existsLeads) {
        LeadsStatusEnum newStatus = LeadsStatusEnum.parse(request.getStatus());
        if (Objects.isNull(newStatus)) {
            return;
        }
        LeadsStatusEnum oldStatus = LeadsStatusEnum.parse(existsLeads.getStatus());
        if (Objects.isNull(oldStatus)) {
            return;
        }
        if (newStatus == oldStatus) {
            return;
        }
        if (oldStatus.getStatus() >= LeadsStatusEnum.CONTACTED.getStatus()) {
            return;
        }
        if (newStatus.getStatus() == LeadsStatusEnum.DISTRIBUTED.getStatus() && existsLeads.getStatus() > newStatus.getStatus()) {
            throw new InngkeServiceException("线索状态【" + oldStatus.getName() + "】不允许修改为【" + newStatus.getName() + "】");
        }
        if (newStatus.getStatus() > LeadsStatusEnum.CONTACTED.getStatus()) {
            int count = leadsFollowManager.count(
                    Wrappers.<LeadsFollow>query()
                            .eq(LeadsFollow.LEADS_ID, existsLeads.getId())
                            .eq(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.CONTACTED.getStatus())
            );
            if (count == 0) {
                throw new InngkeServiceException("线索需要为【" + LeadsStatusEnum.CONTACTED.getName() + "】才能修改为【" + newStatus.getName() + "】");
            }
        }
    }


    @Override
    @DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
    public BaseResponse<Boolean> recoveryLeads(LeadsRecoveryRequest request) {
        List<Long> leadsIds = request.getLeadsList();
        Integer bid = request.getBid();
        Long operatorId = request.getOperatorId();
        //检查是否有回收权限
        checkRecoveryPerssiom(request);


            List<Long> ids = leadsManager.recoveryLeads(leadsIds, operatorId, bid, request.getStaffId());
            //异步更新es
            if (!CollectionUtils.isEmpty(ids)) {
                LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
                esRequest.setBid(request.getBid());
                esRequest.setIds(leadsIds);
                esRequest.setRefreshEs(true);
                leadsEsService.updateDocs(esRequest);

                LeadsAddRequest leadsAddRequest = new LeadsAddRequest();
                leadsAddRequest.setBid(bid);
                leadsAddRequest.setIds(ids);
                leadsEsService.createLeadsDocs(leadsAddRequest);
            }
            logger.info("线索回收处理完成，请求处理{}条，实际处理{}!", leadsIds.size(), ids.size());
            return BaseResponse.success(true);
    }


    @Override
    public BaseResponse<Boolean> contactLeads(PrivateVoiceRecordDTO privateVoiceRecordDTO) {
        Long leadsId = privateVoiceRecordDTO.getLeadsId();
        if (null == leadsId || leadsId <= 0) {
            logger.info("无效的线索id:{}", leadsId);
            return BaseResponse.error("无效的线索id" + leadsId);
        }
        Leads leads = getOne(privateVoiceRecordDTO.getBid(),privateVoiceRecordDTO.getLeadsId());
        if (null == leads) {
            logger.info("无效的线索id:{}", leadsId);
            return BaseResponse.error("无效的线索id" + leadsId);
        }

        LeadsContactFollowService leadsContactFollowService = leadsContactFollowFactoryService
                .getInstance(leads, privateVoiceRecordDTO);
        logger.info("获取到处理service{}", leadsContactFollowService.getClass());

        //添加线索跟进
        leadsContactFollowService.createContactFollow(leads, privateVoiceRecordDTO);
        // 发送帮助联系通知
        AsyncUtils.runAsync(() -> {
            StaffDto operatorStaff = staffClientForLeads.getStaffByCid(privateVoiceRecordDTO.getBid(), privateVoiceRecordDTO.getGuideId());
            if (Objects.nonNull(operatorStaff) && !leads.getDistributeStaffId().equals(operatorStaff.getId()) && leads.getDistributeStaffId() > 0L) {
                OthersHelpContactLeadsNotifyContext context = OthersHelpContactLeadsNotifyContext.init(privateVoiceRecordDTO.getBid(), leadsId, operatorStaff.getId());
                TemplateMessageContentBuilder<OthersHelpContactLeadsNotifyContext> builder = templateMessageBuilderFactory.getBuilder(context);
                builder.sendMessage(context);
            }
        });


        return BaseResponse.success(true);
    }

    private Leads getOne(Integer bid,Long leadsId) {
        return leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, leadsId)
        );
    }

    @Override
    public BaseResponse<Boolean> transfer(LeadTransferRequest request) {
        Integer bid = request.getBid();
        Long staffId = request.getStaffId();
        Long targetStaffId = request.getTargetStaffId();

        List<Leads> leads = leadsManager.list(Wrappers.<Leads>query()
                .eq(Leads.BID, bid)
                .eq(Leads.DISTRIBUTE_STAFF_ID, staffId)
                .select(Leads.ID)
        );

        if (CollectionUtils.isEmpty(leads)) {
            return BaseResponse.success(false);
        }
        leads.forEach(lead -> {
            LeadsForwardRequest leadsForwardRequest = new LeadsForwardRequest();
            leadsForwardRequest.setBid(bid);
            leadsForwardRequest.setId(lead.getId());
            leadsForwardRequest.setStaffId(targetStaffId);
            leadsForwardRequest.setOperatorId(request.getOperatorId());
            forward(leadsForwardRequest);
        });
        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<Boolean> updateLeadsClientStatus(LeadsClientStatusUpdateRequest request) {
        logger.info("更新线索客户状态，请求参数：{}", jsonService.toJson(request));
        boolean saved = leadsManager.updateClientStatus(request.getBid(), request.getId(), request.getStatus());
        if (!saved){
            logger.info("更新线索客户状态失败，线索id：{}", request.getId());
            return BaseResponse.success(false);
        }

        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(Lists.newArrayList(request.getId()));
        leadsEsService.updateDocs(leadsUpdateRequest);
        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<Boolean> batchUpdateLeadsClientStatus(BatchLeadsClientStatusUpdateRequest request) {
        List<Leads> leadsList = Lists.newArrayList();
        request.getLeadsStatusMap().forEach((leadsId,status)->{
            Leads leads = new Leads();
            leads.setId(leadsId);
            leads.setClientStatus(status);
            leadsList.add(leads);
        });
        leadsManager.updateBatchById(leadsList);

        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(leadsList.stream().map(Leads::getId).collect(Collectors.toList()));
        leadsEsService.updateDocs(leadsUpdateRequest);
        return BaseResponse.success(true);
    }

    /**
     * 为新增和修改的线索信息组装客户信息
     *
     * @param leads      新增/修改的线索信息
     * @param operatorId 操作员
     */
    private void installCustomerInfo(Leads leads, Long operatorId) {
        if (StringUtils.isEmpty(leads.getMobile())) {
            return;
        }

        CustomerMobilesQuery userRequest = new CustomerMobilesQuery();
        userRequest.setBid(leads.getBid());
        userRequest.setOperatorId(operatorId);
        Set<String> mobiles = Sets.newHashSet(leads.getMobile());
        userRequest.setMobiles(mobiles);
        //调用用户服务，返回带customerId，mobile的用户列表
        BaseResponse<List<CustomerDto>> resp = customerService.getCustomerByMobile(userRequest);
        if (resp.getCode() != 0) {
            throw new InngkeServiceException("获取用户信息失败");
        }
        List<CustomerDto> data = resp.getData();
        if (!CollectionUtils.isEmpty(data)) {
            CustomerDto customerDto = data.get(0);
            leads.setCustomerId(customerDto.getId());
        }
    }

    /**
     * 校验线索新增和修改时的信息
     *
     * @param leads 线索信息
     */
    private void validateLeadsInfo(Leads leads) {
        String name = leads.getName();
        if (StringUtils.isEmpty(name)) {
            throw new InngkeServiceException("客户姓名不能为空");
        }

        String mobile = leads.getMobile();
        if (StringUtils.isEmpty(mobile) && StringUtils.isEmpty(leads.getWeChat())) {
            throw new InngkeServiceException("手机号和微信不能同时为空");
        }

        if (!StringUtils.isEmpty(leads.getMobile())) {
            if (!Validator.isMobile(leads.getMobile())) {
                throw new InngkeServiceException("手机号码不合法");
            }
        }

    }

    /**
     * ● 客资清洗客服角色：
     * ○ 线索有创建人：
     * ■ 线索有跟进客服：只有当前跟进客服才有权限回收
     * ■ 线索无跟进客服：只有线索创建人才有权限回收
     * ○ 线索无创建人（即系统对接）
     * ■ 都有回收权限
     * ● 其他角色：都有回收权限
     *
     * @param request
     */
    private void checkRecoveryPerssiom(LeadsRecoveryRequest request) {
        List<Long> leadsIds = request.getLeadsList();
        List<Leads> list = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .in(Leads.ID, leadsIds)
                        .select(Leads.CREATED_STAFF_ID, Leads.PRE_FOLLOW_STAFF_ID,Leads.STATUS)
        );
        //获取操作者角色
        Set<String> userRoleCode = rbacClientForLeads.getUserRoleCode(request.getBid(), request.getStaffId());
        boolean hasRole = userRoleCode.contains(CUSTOMER_ROLE);
        List<Integer> nonAllocatedLeadsStatus = LeadsStatusEnum.getNonAllocatedLeadsStatus();
        //校验权限
        list.forEach(
                leads -> {
                    Long preFollowStaffId = leads.getPreFollowStaffId();
                    Long createStaffId = leads.getCreateStaffId();
                    Integer status = leads.getStatus();

                    if (!hasRole || createStaffId <= 0) {
                        return;
                    }

                    if (Objects.nonNull(preFollowStaffId) && preFollowStaffId > 0
                            && nonAllocatedLeadsStatus.contains(status)){
                        if (Objects.equals(createStaffId,request.getStaffId())){
                            return;
                        }
                        throw new InngkeServiceException("回收失败，自己创建的线索才有权限回收");
                    }


                    if ((Objects.nonNull(preFollowStaffId) && preFollowStaffId > 0 && !preFollowStaffId.equals(request.getStaffId()))
                            || ((Objects.isNull(preFollowStaffId) || preFollowStaffId <= 0) && !createStaffId.equals(request.getStaffId()))) {
                        throw new InngkeServiceException("回收失败，线索跟进客服是自己的才有权限回收");
                    }
                }
        );
    }

    public BaseResponse<Boolean> updateStaffId(LeadsCreateUpdateRequest request) {
        logger.info("批量修改线索创建人入参bid:{},ids:{},staffId:{}", request.getBid(),request.getIds(),request.getStaffId());
        Integer count = staffClientForLeads.isExitByStaffId(request);
        if (StringUtils.isBlank(request.getStaffId())||count<1){
            logger.info("批量修改线索创建人不存在");
           return BaseResponse.error("创建人不存在");
        }
        List<Long> ids = Arrays.asList(request.getIds().split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
        //批量修改
        boolean update = false;
        if (StringUtils.isNotBlank(request.getIds())) {
            update = leadsManager.update(
                    Wrappers.<Leads>update()
                            .eq(Leads.BID, request.getBid())
                            .in(Leads.ID, ids)
                            .set(Leads.CREATED_STAFF_ID, Long.parseLong(request.getStaffId()))
                            .set(Leads.UPDATE_TIME, LocalDateTime.now())
            );
        }
        if (!update) {
            throw new InngkeServiceException("修改失败");
        }
        logger.info("批量修改线索创建人成功");
        return BaseResponse.success(update);
    }

}

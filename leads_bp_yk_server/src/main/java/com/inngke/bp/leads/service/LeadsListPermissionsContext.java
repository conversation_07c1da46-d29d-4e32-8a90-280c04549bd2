package com.inngke.bp.leads.service;

import com.google.common.collect.Sets;
import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.service.strategy.permissions.list.ListPermissionsStrategy;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

@Component
public class LeadsListPermissionsContext {

    @Autowired
    private LeadsListPermissionsFactory leadsListPermissionsFactory;

    public void handle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder, Set<String> userRoleCodes) {
        ListPermissionsStrategy listPermissionsStrategy = leadsListPermissionsFactory.analysisGetListPermissionsStrategy(
                Optional.ofNullable(request.getStatusSet()).orElse(Sets.newHashSet()), request.getStatusGroup());

        listPermissionsStrategy.handle(request, queryBuilder, userRoleCodes);
    }
}

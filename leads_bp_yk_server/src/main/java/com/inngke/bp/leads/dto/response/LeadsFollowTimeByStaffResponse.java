package com.inngke.bp.leads.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/8 10:35
 */
@Data
public class LeadsFollowTimeByStaffResponse implements Serializable {
    /**
     * ID
     */
    private Long id;
    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 经销商ID
     */
    private Long agentId;

    /**
     * 经销商名称
     */
    private String agentName;

    /**
     * 跟进人员工ID，即staffId
     */
    private Long staffId;

    /**
     * 跟进人员工名称
     */
    private String staffName;

    /**
     * 跟进人员工名称
     */
    private String mobile;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 各级部门
     *
     * @demo {1, 2, 3}
     */
    private List<Long> deptIds;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门名称，按/分割
     * @demo 营客/研发部
     *
     */
    private String departmentNameAll;

    /**
     * 线索分配的时间
     */
    private Long distributeTime;

    /**
     * 线索总数
     */
    private Integer leadsCount = 0;

    /**
     * 有效客户数
     */
    private int stateAvail = 0;

    /**
     * 无效数
     */
    private int stateNoAvail = 0;

    /**
     * 待联系数
     */
    private int notContactCount = 0;


    /**
     * 24小时联系数
     */
    private int contactIn24Count = 0;

    /**
     * 联系数
     */
    private int stateContact = 0;

    /**
     * 成功联系数
     */
    private int stateContactSuccess = 0;

    /**
     * 量尺数
     */
    private int stateMeasuring = 0;

    /**
     * 到店数
     */
    private int stateArrivalStore = 0;

    /**
     * 定金记录数
     */
    private int stateDeposit = 0;

    /**
     * 累计定金金额
     */
    private BigDecimal depositAmount = BigDecimal.ZERO;

    /**
     * 累计成交金额
     */
    private BigDecimal orderAmount = BigDecimal.ZERO;

    /**
     * 成交数
     */
    private int stateOrderSuccess;

    /**
     * 首次联系时间间隔（秒）
     */
    private Long firstCallIntervalTime = 0L;

    /**
     * 首次联系时间间隔
     */
    private String firstCallIntervalTimeStr = "0小时";

    /**
     * 客单价
     */
    private String customerUnitPrice;

    /**
     * 流失数
     */
    private Integer lostNum = 0;


    /**
     * 联系率
     */
    private String contactRate="0.00%";

    /**
     * 24小时联系率
     */
    private String contactIn24Rate = "0.00%";

    /**
     * 成功联系率
     */
    private String contactSuccessRate="0.00%";

    /**
     * 量尺率
     */
    private String measuringRate="0.00%";

    /**
     * 到店率
     */
    private String arrivalStoreRate="0.00%";

    /**
     * 定金转化数
     */
    private String depositRate="0.00%";

    /**
     * 成交率
     */
    private String orderSuccessRate="0.00%";

    /**
     * 流失率
     */
    private String lostRate="0.00%";




}

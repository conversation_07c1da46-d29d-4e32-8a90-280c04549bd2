/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索下发配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsDistributeConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 区域ID
     */
    private Integer regionId;

    /**
     * 接收参与线索下发的员工id，多个使用半角逗号分隔
     */
    private String staffIds;

    /**
     * 分配索引
     *
     */
    private Long indexes;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String REGION_ID = "region_id";

    public static final String CHANNEL_ID = "channel_id";

    public static final String STAFF_IDS = "staff_ids";

    public static final String INDEXES = "indexes";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

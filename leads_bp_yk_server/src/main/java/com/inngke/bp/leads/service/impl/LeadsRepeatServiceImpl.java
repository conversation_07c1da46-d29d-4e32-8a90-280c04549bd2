package com.inngke.bp.leads.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.ListByMobileOrWeChatRequest;
import com.inngke.bp.leads.dto.response.LeadsConfDto;
import com.inngke.bp.leads.service.LeadsConfService;
import com.inngke.bp.leads.service.LeadsRepeatService;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Component
public class LeadsRepeatServiceImpl implements LeadsRepeatService {

    @Autowired
    private LeadsConfService leadsConfService;

    @Autowired
    private LeadsManager leadsManager;

    private final Integer OPEN = 1;


    public List<Leads> checkRepeat(Integer bid, String mobile, String weChat, Set<Long> excludeIds) {
        ListByMobileOrWeChatRequest listByMobileOrWeChatRequest = new ListByMobileOrWeChatRequest();
        listByMobileOrWeChatRequest.setBid(bid);
        if (!StringUtils.isEmpty(mobile)) {
            listByMobileOrWeChatRequest.setMobile(mobile);
        }
        if (!StringUtils.isEmpty(weChat)) {
            listByMobileOrWeChatRequest.setWeChat(weChat);
        }

        if (!ObjectUtils.isEmpty(excludeIds)) {
            listByMobileOrWeChatRequest.setExcludeIds(excludeIds);
        }

        return check(listByMobileOrWeChatRequest);
    }

    /**
     * @param
     * @param
     * @return
     */
    private List<Leads> check(ListByMobileOrWeChatRequest request) {
        if(ObjectUtils.isEmpty(request.getMobile()) && ObjectUtils.isEmpty(request.getWeChat())){
            return Lists.newArrayList();
        }
        BaseBidOptRequest baseBidOptRequest = new BaseBidOptRequest();
        baseBidOptRequest.setBid(request.getBid());

        BaseResponse<LeadsConfDto> response = leadsConfService.getLeadsConf(baseBidOptRequest);
        LeadsConfDto leadsConfDto = response.getData();
        if (Objects.isNull(leadsConfDto) || !OPEN.equals(leadsConfDto.getOpenRepeatRemove())) {
            return Lists.newArrayList();
        }
        List<Leads> leads = leadsManager.listByKeyword(request);
        if (CollectionUtils.isEmpty(leads)) {
            return Lists.newArrayList();
        }
        List<Leads> filterLeads = filter(leads, leadsConfDto.getRepeatRemoveDay());
        if (CollectionUtils.isEmpty(filterLeads)) {
            return Lists.newArrayList();
        }

        return filterLeads;
    }

    private List<Leads> filter(List<Leads> leads, Integer repeatRemoveDay) {
        List<Leads> leadsList = leads.stream().filter(lead -> {
            long expirationDate = LocalDateTimeUtil.toEpochMilli(lead.getCreateTime().plus(repeatRemoveDay, ChronoUnit.DAYS).toLocalDate());
            long now = LocalDateTimeUtil.toEpochMilli(LocalDateTime.now().toLocalDate());
            if (now >= expirationDate) {
                //已超过现在重复时间
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leadsList)) {
            return Lists.newArrayList();
        }

        return leadsList;
    }

}

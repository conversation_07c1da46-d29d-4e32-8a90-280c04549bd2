package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * Created by emiya on 2022/6/15 18:43
 *
 * <AUTHOR>
 * @date 2022/6/15 18:43
 */
@Component(value = "LeadsOrderAmountStrategy")
public class LeadsOrderAmountStrategy implements LeadsFollowTimeStrategy {

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.sum("orderAmount").field("orderAmount")
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedSum orderAmount = bucketAggregations.get("orderAmount");
        double amountValue = orderAmount.getValue();
        dto.setOrderAmount(Optional.ofNullable(BigDecimal.valueOf(amountValue).setScale(0, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO));
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        BigDecimal orderAmount = dtos.stream().filter(t -> t.getOrderAmount() != null).map(LeadsFollowTimeByStaffResponse::getOrderAmount).reduce(BigDecimal::add).get();
        dto.setOrderAmount(orderAmount.setScale(0, RoundingMode.HALF_UP));
    }
}

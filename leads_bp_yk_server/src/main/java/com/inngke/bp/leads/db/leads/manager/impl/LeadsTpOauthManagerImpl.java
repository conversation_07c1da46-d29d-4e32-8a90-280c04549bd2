/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.db.leads.dao.LeadsTpOauthDao;
import com.inngke.bp.leads.db.leads.manager.LeadsTpAccountInfoManager;
import com.inngke.bp.leads.db.leads.manager.LeadsTpOauthManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 广告平台授权信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsTpOauthManagerImpl extends ServiceImpl<LeadsTpOauthDao, LeadsTpOauth> implements LeadsTpOauthManager {

    @Autowired
    private LeadsTpAccountInfoManager leadsTpAccountInfoManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delSaveBatch(Integer bid, List<LeadsTpOauth> leadsTpOauthList) {
        boolean remove = this.remove(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.BID, bid)
        );

        if (!remove) {
            return false;
        }

        return this.saveBatch(leadsTpOauthList);
    }

    @Override
    public boolean delSaveBatchByAccountId(Integer bid, String accountId, List<LeadsTpOauth> leadsTpOauthList) {
        boolean remove = this.remove(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.BID, bid)
                .eq(LeadsTpOauth.ACCOUNT_ID, accountId)
        );

        if (!remove) {
            return false;
        }

        return this.saveBatch(leadsTpOauthList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAccount(Integer bid, LeadsTpOauth leadsTpOauth, List<LeadsTpAccountInfo> leadsTpAccountInfoList) {
        LeadsTpOauth exist = this.getOne(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.BID, bid)
                .eq(LeadsTpOauth.ACCOUNT_ID, leadsTpOauth.getAccountId())
                .select(LeadsTpOauth.ID)
        );

        //新增
        if (ObjectUtils.isEmpty(exist)){
            this.save(leadsTpOauth);
            if (!CollectionUtils.isEmpty(leadsTpAccountInfoList)){
                return leadsTpAccountInfoManager.saveBatch(leadsTpAccountInfoList);
            }
            return true;
        }

        //更新
        leadsTpOauth.setId(exist.getId());
        this.saveOrUpdate(leadsTpOauth);
        if (!CollectionUtils.isEmpty(leadsTpAccountInfoList)){
            Set<String> accountIds = leadsTpAccountInfoList.stream().map(LeadsTpAccountInfo::getAccountId).collect(Collectors.toSet());
            Map<String, Long> existMap = leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                    .eq(LeadsTpAccountInfo.BID, bid)
                    .eq(LeadsTpAccountInfo.ENABLE,1)
                    .in(LeadsTpAccountInfo.ACCOUNT_ID, accountIds)
                    .select(LeadsTpAccountInfo.ID, LeadsTpAccountInfo.ACCOUNT_ID)
            ).stream().collect(Collectors.toMap(LeadsTpAccountInfo::getAccountId, LeadsTpAccountInfo::getId));

            leadsTpAccountInfoList.forEach(leadsTpAccountInfo ->
                    leadsTpAccountInfo.setId(existMap.get(leadsTpAccountInfo.getAccountId())));

            leadsTpAccountInfoManager.saveOrUpdateBatch(leadsTpAccountInfoList);
        }

        return true;
    }

    @Override
    public boolean saveToken(Integer bid, LeadsTpOauth leadsTpOauth){
        LeadsTpOauth exist = this.getOne(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.BID, bid)
                .eq(LeadsTpOauth.ACCOUNT_ID, leadsTpOauth.getAccountId())
                .orderByDesc(LeadsTpOauth.CREATE_TIME)
                .last("limit 1")
                .select(LeadsTpOauth.ID)
        );

        //新增
        if (!ObjectUtils.isEmpty(exist)){
            leadsTpOauth.setId(exist.getId());
        }

        //更新
        return this.saveOrUpdate(leadsTpOauth);
    }

}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.dao.LeadsFollowDao;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsLog;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsLogManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.LeadsFollowBatchCountDTO;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.common.ds.annotation.DS;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 线索跟进记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsFollowManagerImpl extends ServiceImpl<LeadsFollowDao, LeadsFollow> implements LeadsFollowManager {

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsFollowDao leadsFollowDao;

    @Autowired
    private LeadsLogManager leadsLogManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createContactFollow(LeadsFollow leadsFollow) {
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, leadsFollow.getBid())
                        .eq(Leads.ID, leadsFollow.getLeadsId())
        );
        Integer oldStatus = leads.getStatus();
        leads.setStatus(leadsFollow.getLeadsStatus());

        boolean update = leadsManager.updateById(leads);
        if (update) {
            if (!StringUtils.isEmpty(leadsFollow.getFollowContent())) {
                //拼装跟进记录
                this.save(leadsFollow);
                leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
                leadsManager.update(
                        Wrappers.<Leads>update()
                                .eq(Leads.BID, leads.getBid())
                                .eq(Leads.ID, leads.getId())
                                .set(Leads.LAST_FOLLOW_ID, leadsFollow.getId())
                                .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
                );
            }
            //拼装日志
            if (!oldStatus.equals(leadsFollow.getLeadsStatus())) {
                LeadsLog leadsLog = new LeadsLog();
                leadsLog.setLeadsId(leadsFollow.getLeadsId());
                leadsLog.setBid(leadsFollow.getBid());
                leadsLog.setOperatorId(0L);
                leadsLog.setDistributeStaffId(0L);
                leadsLog.setStatusChange(leadsFollow.getLeadsStatus());
                leadsLog.setCreateTime(LocalDateTime.now());
                leadsLog.setLogContent(jsonService.toJson(leadsFollow));
            }
        }

        return update;
    }

    @Override
    public Boolean saveFollow(LeadsFollow leadsFollow) {
        return createContactFollow(leadsFollow);
    }

    @Override
    public List<LeadsFollowBatchCountDTO> countBatch(Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return Lists.newArrayList();
        }
        return leadsFollowDao.countBatch(leadsIds);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFollow(Leads leads, LeadsFollow leadsFollow, Integer leadsStatus) {
        //保存线索跟进
        boolean followUpdate = this.saveOrUpdate(leadsFollow);
        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
        leads.setLastFollowId(leadsFollow.getId());
        leads.setLastFollowTime(LocalDateTime.now());
        // 判断是否24小时内联系
        if (leadsStatus != null && (leadsStatus.equals(LeadsStatusEnum.CONTACTED.getStatus()))) {
            LocalDateTime distributeTime = leads.getDistributeTime();
            LocalDateTime now = LocalDateTime.now();
            long hours = Duration.between(distributeTime, now).toHours();
            if (hours < 24) {
                leads.setContactIn24(1);
            }
            if (Objects.isNull(leads.getFirstContactTime())){
                leads.setFirstContactTime(now);
            }
        }
        //更新线索的最新跟进记录&&线索是否为24小时内联系
        boolean leadsUpdate = leadsManager.updateById(leads);
        return followUpdate && leadsUpdate;
    }

    @Override
    public List<LeadsFollow> getListByLastId(Long lastId, Integer size) {
        return this.list(Wrappers.<LeadsFollow>query()
                .gt(LeadsFollow.ID, Optional.ofNullable(lastId).orElse(0L))
                .ne(LeadsFollow.LEADS_STATUS, 1)
                .last("LIMIT " + size)
        );
    }

    /**
     * 获取线索最新跟进记录
     *
     * @param leadsIds
     * @param isPC
     * @return
     */
    @Override
    public List<LeadsFollow> getLastFollowRecordList(List<Long> leadsIds, boolean isPC) {
        return leadsFollowDao.getLastFollowRecordList(leadsIds, isPC);
    }

    @Override
    public Integer getFollowCountByLeadsIds(Integer bid, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return 0;
        }
        return this.count(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, bid)
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .apply("follow_content  NOT REGEXP '处回收|转交'")

        );
    }

    @Override
    public Map<Long,Integer> getFollowCountMapByLeadsIds(Integer bid, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return Maps.newHashMap();
        }

        return this.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, bid)
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .apply("follow_content  NOT REGEXP '处回收|转交'")
                        .groupBy(LeadsFollow.LEADS_ID)
                        .select("count(*) bid,"+LeadsFollow.LEADS_ID)
        ).stream().collect(Collectors.toMap(LeadsFollow::getLeadsId,LeadsFollow::getBid));
    }

    @Override
    public List<LeadsFollow> findFollowByLeadsIds(Integer bid, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return Lists.newArrayList();
        }
        return this.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, bid)
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .apply("follow_content  NOT REGEXP '处回收|转交'")
        );
    }

    @Override
    public Map<Long, LocalDateTime> getLeadsFirstCallTime(Integer bid, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return Maps.newHashMap();
        }

        Map<Long, List<LeadsFollow>> leadsFollowGroup = list(Wrappers.<LeadsFollow>query().eq(LeadsFollow.BID, bid)
                .in(LeadsFollow.LEADS_ID, leadsIds)
                .in(LeadsFollow.LEADS_STATUS, Lists.newArrayList(
                        LeadsStatusEnum.CONTACTED.getStatus(),
                        LeadsStatusEnum.DISTRIBUTED.getStatus()
                ))
                .orderByAsc(LeadsFollow.CREATE_TIME)
        ).stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));

        Map<Long,LocalDateTime> leadsFirstCallTimeMap = Maps.newHashMap();

        leadsFollowGroup.forEach((leadsId, leadsFollow) -> {
            Map<Integer, List<LeadsFollow>> leadsStatusFollowGroup = leadsFollow.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsStatus));
            List<LeadsFollow> distributedLeadsFollows = leadsStatusFollowGroup.get(LeadsStatusEnum.DISTRIBUTED.getStatus());
            List<LeadsFollow> contactedLeadsFollows = leadsStatusFollowGroup.get(LeadsStatusEnum.CONTACTED.getStatus());
            if (CollectionUtils.isEmpty(contactedLeadsFollows)){
                return;
            }
            LocalDateTime lastDistributedTime;
            //拿到最后退回的时间
            if (!CollectionUtils.isEmpty(distributedLeadsFollows)){
                distributedLeadsFollows.sort(Comparator.comparing(LeadsFollow::getCreateTime).reversed());
                lastDistributedTime = distributedLeadsFollows.stream().findFirst()
                        .map(LeadsFollow::getCreateTime).orElse(null);
            } else {
                lastDistributedTime = null;
            }

            contactedLeadsFollows.sort(Comparator.comparing(LeadsFollow::getCreateTime));
            contactedLeadsFollows.stream()
                    .filter(f->Objects.isNull(lastDistributedTime) || f.getCreateTime().isAfter(lastDistributedTime))
                    .findFirst().ifPresent(
                            leadsFirstCallTime -> leadsFirstCallTimeMap.put(leadsId, leadsFirstCallTime.getCreateTime())
                    );
        });

        return leadsFirstCallTimeMap;
    }

    @Override
    public LocalDateTime getLeadsFirstCallTime(Integer bid, Long leadsId) {
        return getLeadsFirstCallTime(bid, Sets.newHashSet(leadsId)).get(leadsId);
    }

    @Override
    public List<LeadsFollow> getLeadsLatestFollowList(Integer bid, List<Long> leadsIds) {
        return this.baseMapper.getLeadsLatestFollowList(bid, leadsIds);
    }

    @Override
    public List<LeadsFollow> getByLeadsIds(Integer bid, List<Long> ids) {
        ids = ids.stream().filter(Objects::nonNull).filter(id -> id > 0L).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<LeadsFollow>query()
                .eq(LeadsFollow.BID, bid)
                .in(LeadsFollow.LEADS_ID, ids)
                .orderByAsc(LeadsFollow.ID)
        );
    }

    @Override
    public List<LeadsFollow> getLeadsFollowList(Integer bid, Long leadsId, LocalDateTime createTime, Integer limit) {
        if (Objects.isNull(limit)) limit = 10;
        if (limit > 1000) limit = 1000;
        return this.list(Wrappers.<LeadsFollow>query()
                .eq(LeadsFollow.BID, bid)
                .eq(Objects.nonNull(leadsId), LeadsFollow.LEADS_ID, leadsId)
                .gt(Objects.nonNull(createTime),LeadsFollow.CREATE_TIME, createTime)
                .orderByAsc(LeadsFollow.CREATE_TIME)
                .last("LIMIT " + limit)
        );
    }

    @Override
    public boolean save(LeadsFollow entity) {
        return super.save(entity);
    }

    @Override
    public boolean saveOrUpdate(LeadsFollow entity) {
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean saveBatch(Collection<LeadsFollow> entityList, int batchSize) {
        return super.saveBatch(entityList, batchSize);
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<LeadsFollow> entityList, int batchSize) {
        return super.saveOrUpdateBatch(entityList, batchSize);
    }
}

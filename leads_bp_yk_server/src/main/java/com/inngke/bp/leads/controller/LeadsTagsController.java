package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.LeadsTagAddRequest;
import com.inngke.bp.leads.dto.request.LeadsTagsDetailRequest;
import com.inngke.bp.leads.dto.response.LeadsTagsDetailDto;
import com.inngke.bp.leads.mq.listener.ClientChangeListenerForLeads;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.service.LeadsTagsService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 线索打标签
 *
 * <AUTHOR>
 * @chapter 线索
 * @section 线索-标签
 * @since 2022/6/7
 **/
@RestController
@RequestMapping("/api/leads-tags")
public class LeadsTagsController {

    @Autowired
    private LeadsTagsService leadsTagsService;

    /**
     * 查询线索标签详情
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID，0表示系统操作
     * @param request    请求实体
     * @return 返回实体
     */
    @GetMapping()
    public BaseResponse<LeadsTagsDetailDto> tagsDetail(@RequestHeader int bid,
                                                       @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
                                                       @Validated LeadsTagsDetailRequest request) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsTagsService.tagsDetail(request);
    }

    /**
     * 线索添加标签
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID，0表示系统操作
     * @param request    请求实体
     * @return 返回实体
     */
    @PutMapping()
    public BaseResponse<Boolean> tagsAdd(@RequestHeader int bid,
                                         @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
                                         @Validated @RequestBody LeadsTagAddRequest request,
                                         @RequestAttribute JwtPayload jwtPayload) {
        request.setBid(bid);
        request.setOperatorId(jwtPayload.getCid());
        return leadsTagsService.tagsAdd(request);
    }


    @Autowired
    private JsonService jsonService;

    @Autowired
    private ClientChangeListenerForLeads clientTransferListenerOfLeads;

    @GetMapping("/test123")
    public void test123() {
        String str = "{\"bid\":1213,\"sourceStaffId\":158300442852327443,\"targetStaffId\":161307598564360287,\"id\":161668650695131267,\"event\":2,\"business\":\"10\"}";
        ClientTransferMessage clientTransferMessage = jsonService.toObject(str, ClientTransferMessage.class);
        clientTransferListenerOfLeads.process(null, clientTransferMessage);
    }

}

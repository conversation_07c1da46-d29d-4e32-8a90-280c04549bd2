package com.inngke.bp.leads.core.converter;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
import com.inngke.bp.leads.dto.response.LeadsStatisticsExcelDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/17 4:21 PM
 */
public class LeadsStatisticsConverter {
    private LeadsStatisticsConverter() {
    }

    public static LeadsStatisticsExcelDto toLeadsStatisticsExcelDto(LeadsStatisticsDto dto) {
        LeadsStatisticsExcelDto excelDto = new LeadsStatisticsExcelDto();
        excelDto.setAgentId(dto.getAgentId());
        excelDto.setStaffId(dto.getStaffId());
        excelDto.setLeadsCount(dto.getLeadsCount());
        excelDto.setOrderCount(dto.getOrderCount());
        excelDto.setOrderTotalAmount(dto.getOrderTotalAmount());
        //总线索数: 分配给到该员工的总线索数（当前线索负责人是该员工，不包括已转交、退回、管理员回收的）
        BigDecimal leadsCount = BigDecimal.valueOf(dto.getLeadsCount());
        //线索有效率: 线索状态不为“待联系”且未被标记为“无效线索”的线索数/该员工的总线索数(不为-3和1的状态)
        BigDecimal availableLeadsRate = BigDecimal.ZERO;
        BigDecimal availableLeads = leadsCount.subtract(BigDecimal.valueOf(dto.getStatusInvalid() + dto.getStatus1()));
        if (availableLeads.compareTo(BigDecimal.ZERO) != 0) {
            availableLeadsRate = availableLeads.divide(leadsCount, 2, RoundingMode.UP).scaleByPowerOfTen(2);
        }
        excelDto.setAvailableLeadsRate(availableLeadsRate);
        //24h联系率: 线索状态被标记过为"24h内联系“的数量/该员工的总线索数(为2的状态)
        BigDecimal contact24hLeadsRate = BigDecimal.ZERO;
        BigDecimal contact24hLeads = BigDecimal.valueOf(dto.getStatus2());
        if (contact24hLeads.compareTo(BigDecimal.valueOf(0)) != 0) {
            contact24hLeadsRate = contact24hLeads.divide(leadsCount, 2, RoundingMode.UP).scaleByPowerOfTen(2);
        }
        excelDto.setContact24hLeadsRate(contact24hLeadsRate);
        //总联系率: 线索状态被标记过为”24h内联系“及”24h后联系“的数量/该员工的总线索数(为2和3状态)
        BigDecimal contactTotalLeadsRate = BigDecimal.ZERO;
        BigDecimal contactTotalLeads = BigDecimal.valueOf(dto.getStatus3()).add(contact24hLeads);
        if (contact24hLeads.compareTo(BigDecimal.ZERO) != 0) {
            contactTotalLeadsRate = contactTotalLeads.divide(leadsCount, 2, RoundingMode.UP).scaleByPowerOfTen(2);
        }
        excelDto.setContactTotalLeadsRate(contactTotalLeadsRate);
        //到店率: 线索状态被标记过为“ 到店”的数量/该员工的总线索数(为6的状态)
        BigDecimal storedLeadsRate = BigDecimal.ZERO;
        BigDecimal storedLeads = BigDecimal.valueOf(dto.getStatus6());
        if (storedLeads.compareTo(BigDecimal.ZERO) != 0) {
            storedLeadsRate = storedLeads.divide(leadsCount, 2, RoundingMode.UP).scaleByPowerOfTen(2);
        }
        excelDto.setStoredLeadsRate(storedLeadsRate);
        //转化率: 线索状态为“ 已成交”的数量/该员工的总线索数(为11的状态)
        BigDecimal tradedLeadsRate = BigDecimal.ZERO;
        BigDecimal tradedLeads = BigDecimal.valueOf(dto.getStatus11());
        if (tradedLeads.compareTo(BigDecimal.ZERO) != 0) {
            tradedLeadsRate = tradedLeads.divide(leadsCount, 2, RoundingMode.UP).scaleByPowerOfTen(2);
        }
        excelDto.setTradedLeadsRate(tradedLeadsRate);

        return excelDto;
    }

    public static List<LeadsStatisticsExcelDto> toLeadsStatisticsExcelDto(List<LeadsStatisticsDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(LeadsStatisticsConverter::toLeadsStatisticsExcelDto).collect(Collectors.toList());
    }

    public static void getLeadsAllStatusStatistics(List<Leads> leadsList, LeadsStatisticsDto statisticsDto) {
        Map<Integer, Long> statusCounts = leadsList.stream().collect(Collectors.groupingBy(Leads::getStatus, Collectors.counting()));
        statisticsDto.setStatusInvalid(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.INVALID.getStatus(), 0L)));
        statisticsDto.setStatusDistributeError(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(), 0L)));
        statisticsDto.setStatusPushBack(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.PUSH_BACK.getStatus(), 0L)));
        statisticsDto.setStatus0(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.TO_DISTRIBUTE.getStatus(), 0L)));
        statisticsDto.setStatus1(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.DISTRIBUTED.getStatus(), 0L)));
        statisticsDto.setStatus2(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.CONTACTED.getStatus(), 0L)));
        statisticsDto.setStatus3(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.SUCCESS_CONTACT.getStatus(), 0L)));
        statisticsDto.setStatus4(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.INTENT.getStatus(), 0L)));
        statisticsDto.setStatus5(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.MEASURED.getStatus(), 0L)));
        statisticsDto.setStatus6(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.STORED.getStatus(), 0L)));
        statisticsDto.setStatus7(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.QUOTED_PRICE.getStatus(), 0L)));
        statisticsDto.setStatus8(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.ORDERED.getStatus(), 0L)));
        statisticsDto.setStatus9(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.TO_INSTALL.getStatus(), 0L)));
        statisticsDto.setStatus10(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.INSTALLED.getStatus(), 0L)));
        statisticsDto.setStatus11(Math.toIntExact(statusCounts.getOrDefault(LeadsStatusEnum.TRADED.getStatus(), 0L)));
    }
}

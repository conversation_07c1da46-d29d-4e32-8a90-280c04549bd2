/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpPullCondition;
import com.inngke.bp.leads.db.leads.dao.LeadsTpPullConditionDao;
import com.inngke.bp.leads.db.leads.manager.LeadsTpPullConditionManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 拉取第三方平台数据条件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsTpPullConditionManagerImpl extends ServiceImpl<LeadsTpPullConditionDao, LeadsTpPullCondition> implements LeadsTpPullConditionManager {

    @Override
    public List<LeadsTpPullCondition> saveOrUpdateBatch(Integer bid, List<LeadsTpPullCondition> leadsTpPullConditionList) {
        List<String> fieldList = leadsTpPullConditionList.stream()
                .map(LeadsTpPullCondition::getFields).collect(Collectors.toList());
        List<LeadsTpPullCondition> existList = this.list(Wrappers.<LeadsTpPullCondition>query()
                .eq(LeadsTpPullCondition.BID, bid)
                .in(LeadsTpPullCondition.FIELDS, fieldList)
                .select(LeadsTpPullCondition.ID, LeadsTpPullCondition.FIELDS,LeadsTpPullCondition.TYPE)
        );
        if (!CollectionUtils.isEmpty(existList)) {
            Map<String, Long> existMap = existList.stream()
                    .collect(Collectors.toMap((leadsTpPullCondition) ->
                                    leadsTpPullCondition.getFields() + leadsTpPullCondition.getType(),
                            LeadsTpPullCondition::getId));

            leadsTpPullConditionList.forEach(leadsTpPullCondition -> {
                String fields = leadsTpPullCondition.getFields();
                Integer type = leadsTpPullCondition.getType();
                leadsTpPullCondition.setId(existMap.get(fields + type));
            });
        }

        this.saveOrUpdateBatch(leadsTpPullConditionList);

        return this.list(Wrappers.<LeadsTpPullCondition>query()
                .eq(LeadsTpPullCondition.BID, bid).in(LeadsTpPullCondition.FIELDS, fieldList)
        );
    }
}

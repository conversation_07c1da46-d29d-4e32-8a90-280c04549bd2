package com.inngke.bp.leads.service;

import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/4/24 15:47
 */
public interface LeadsPullService {

    LeadsTpTypeEnum getType();

    void pull(BaseBidOptRequest request);

    void pull(String startTime,BaseBidOptRequest request);
}

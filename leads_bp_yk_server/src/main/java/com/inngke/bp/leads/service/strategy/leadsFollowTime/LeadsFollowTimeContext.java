package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.common.utils.DateTimeUtils;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/8 13:38
 */
@Component
public class LeadsFollowTimeContext {
    private static final Logger log = LoggerFactory.getLogger(LeadsFollowTimeContext.class);

    private LeadsFollowTimeFactory leadsFollowTimeFactory;

    public LeadsFollowTimeContext() {

    }

    @Autowired
    public LeadsFollowTimeContext(LeadsFollowTimeFactory leadsFollowTimeFactory) {
        this.leadsFollowTimeFactory = leadsFollowTimeFactory;
    }

    public void process(LeadsFollow leadsFollow, LeadsFollowTime leadsFollowTime) {
        LeadsFollowTimeStrategy leadsFollowTimeStrategy = leadsFollowTimeFactory.getLeadsFollowTimeStrategy(leadsFollow.getLeadsStatus());
        if (Objects.isNull(leadsFollowTimeStrategy)) {
            //无需统计的，不需要抛出错误
//            log.error("leadsFollowTimeStrategy is null,status={}", leadsFollow.getLeadsStatus());
            return;
        }
        leadsFollowTimeStrategy.process(leadsFollowTime, DateTimeUtils.getMilli(leadsFollow.getCreateTime()));
    }

    public void process(TermsAggregationBuilder aggregation) {
        List<LeadsFollowTimeStrategy> allStrategy = leadsFollowTimeFactory.getAllStrategy();
        if (CollectionUtils.isEmpty(allStrategy)) {
            return;
        }

        allStrategy.forEach(strategy -> strategy.process(aggregation));
    }

    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        List<LeadsFollowTimeStrategy> allStrategy = leadsFollowTimeFactory.getAllStrategy();
        if (CollectionUtils.isEmpty(allStrategy)) {
            return;
        }

        allStrategy.forEach(strategy -> strategy.process(bucketAggregations, dto));
    }

    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        List<LeadsFollowTimeStrategy> allStrategy = leadsFollowTimeFactory.getAllStrategy();
        if (CollectionUtils.isEmpty(allStrategy)) {
            return;
        }

        allStrategy.forEach(strategy -> {
            strategy.process(dtos, dto);
        });
    }
}

package com.inngke.bp.leads.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:49
 */
@Configuration
@ConfigurationProperties(prefix = "leads.tencent-oauth-data")
public class LeadsTpTencentConfig {
    /**
     * 应用ID
     */
    private Long clientId;

    /**
     * 应用Secret
     */
    private String clientSecret;

    /**
     * 回调链接
     */
    private String redirectUri;

    /**
     * 授权接口
     */
    private String oauthApi;

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getOauthApi() {
        return oauthApi;
    }

    public void setOauthApi(String oauthApi) {
        this.oauthApi = oauthApi;
    }
}

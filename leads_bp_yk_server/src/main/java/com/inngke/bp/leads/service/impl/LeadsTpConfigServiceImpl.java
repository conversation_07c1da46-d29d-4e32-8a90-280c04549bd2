package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.dto.request.MultiSetLeadsTpConfRequest;
import com.inngke.bp.leads.dto.request.SetLeadsTpConfRequest;
import com.inngke.bp.leads.dto.response.tp.*;
import com.inngke.bp.leads.service.AiCustomerServiceLeadsService;
import com.inngke.bp.leads.service.tp.LeadsTpConfigService;
import com.inngke.bp.organize.dto.request.merchant.MultiGetMerchantConfigRequest;
import com.inngke.bp.organize.dto.request.merchant.MultiSetMerchantConfigRequest;
import com.inngke.bp.organize.dto.request.merchant.SetMerchantConfigRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.merchant.MerchantConfigDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.organize.service.MerchantConfigService;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 线索第三方平台设置
 *
 * <AUTHOR>
 * @date 2022/3/3 10:30
 */
@Service
public class LeadsTpConfigServiceImpl implements LeadsTpConfigService {

    @Value("#{${leads.func-default-conf}}")
    private Map<String, String> defaultConf;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.merchant_bp_yk:}")
    private MerchantConfigService merchantConfigService;

    @Autowired
    private AiCustomerServiceLeadsService aiCustomerServiceLeadsService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Resource
    private DepartmentClientForLeads departmentClientForLeads;

    @Override
    public BaseResponse<TpAiCustomerServiceConfigDto> getAiCustomerServiceConfig(BaseBidRequest request) {
        BaseResponse<TpConfigDto> tpConfig = getTpConfig(request);
        TpAiCustomerServiceConfigDto aiCustomerServiceConfig = tpConfig.getData().getAiCustomerServiceConfig();

        return BaseResponse.success(aiCustomerServiceConfig);
    }

    @Override
    public BaseResponse<TpFlyFishConfigDto> getFlyFishConfig(BaseBidRequest request) {
        return null;
    }

    @Override
    public BaseResponse<TpTencentAdConfigDto> getTencentAdConfig(BaseBidRequest request) {
        return null;
    }

    @Override
    public BaseResponse<TpConfigDto> getTpConfig(BaseBidRequest request) {
        MultiGetMerchantConfigRequest getConfigRequest = new MultiGetMerchantConfigRequest();
        getConfigRequest.setCodeList(Lists.newArrayList(defaultConf.keySet()));
        getConfigRequest.setBid(request.getBid());

        BaseResponse<BasePaginationResponse<MerchantConfigDto>> configResponse =
                merchantConfigService.multiGet(getConfigRequest);
        if (!BaseResponse.responseSuccess(configResponse)) {
            return BaseResponse.error(configResponse.getMsg());
        }

        return BaseResponse.success(createTpConfigDto(request.getBid(),configResponse.getData().getList()));
    }

    private TpConfigDto createTpConfigDto(Integer bid,List<MerchantConfigDto> configList) {
        Map<String, String> configMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(configList)) {
            configMap = configList.stream().collect(
                    Collectors.toMap(MerchantConfigDto::getCode, MerchantConfigDto::getValue));
        }

        TpTencentAdConfigDto tpTencentAdConfigDto = new TpTencentAdConfigDto();
        tpTencentAdConfigDto.setUseSwitch(Boolean.valueOf(Optional.ofNullable(configMap.get("tencent.useSwitch"))
                .orElse(defaultConf.get("tencent.useSwitch"))));
        tpTencentAdConfigDto.setUrl(Optional.ofNullable(configMap.get("tencent.url"))
                .orElse(defaultConf.get("tencent.url")) + bid);
        tpTencentAdConfigDto.setSecret(Optional.ofNullable(configMap.get("tencent.secret"))
                .orElse(defaultConf.get("tencent.secret")));
        tpTencentAdConfigDto.setToken(Optional.ofNullable(configMap.get("tencent.token"))
                .orElse(defaultConf.get("tencent.token")));
        Long tencentCreateStaffId = Long.valueOf(Optional.ofNullable(configMap.get("tencent.leads.createStaff"))
                .orElse(defaultConf.get("tencent.leads.createStaff")));

        TpFlyFishConfigDto tpFlyFishConfigDto = new TpFlyFishConfigDto();
        tpFlyFishConfigDto.setUseSwitch(Boolean.valueOf(Optional.ofNullable(configMap.get("flyFish.useSwitch"))
                .orElse(defaultConf.get("flyFish.useSwitch"))));
        tpFlyFishConfigDto.setUrl(Optional.ofNullable(configMap.get("flyFish.url"))
                .orElse(defaultConf.get("flyFish.url")) + bid);
        tpFlyFishConfigDto.setToken(Optional.ofNullable(configMap.get("flyFish.token"))
                .orElse(defaultConf.get("flyFish.token")));
        tpFlyFishConfigDto.setSecret(Optional.ofNullable(configMap.get("flyFish.secret"))
                .orElse(defaultConf.get("flyFish.secret")));
        Long flyFishCreateStaffId = Long.valueOf(Optional.ofNullable(configMap.get("flyFish.leads.createStaff"))
                .orElse(defaultConf.get("flyFish.leads.createStaff")));

        TpAiCustomerServiceConfigDto tpAiCustomerServiceConfigDto = new TpAiCustomerServiceConfigDto();
        tpAiCustomerServiceConfigDto.setUseSwitch(Boolean.valueOf(Optional.ofNullable(configMap.get("aiCustomerService.useSwitch"))
                .orElse(defaultConf.get("aiCustomerService.useSwitch"))));
        tpAiCustomerServiceConfigDto.setAppId(Optional.ofNullable(configMap.get("aiCustomerService.appId"))
                .orElse(defaultConf.get("aiCustomerService.appId")));
        tpAiCustomerServiceConfigDto.setSecret(Optional.ofNullable(configMap.get("aiCustomerService.secret"))
                .orElse(defaultConf.get("aiCustomerService.secret")));
        Long aiCreateStaffId = Long.valueOf(Optional.ofNullable(configMap.get("ai.leads.createStaff"))
                .orElse(defaultConf.get("ai.leads.createStaff")));

        Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(bid, Sets.newHashSet(tencentCreateStaffId, flyFishCreateStaffId, aiCreateStaffId));
        Map<Long, DepartmentDto> deparmentMap = departmentClientForLeads.getDepartmentByIds(bid, Lists.newArrayList(staffMap.values().stream().map(StaffDto::getDepartmentId).collect(Collectors.toSet()))).stream().collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
        setOperatorCreateStaffId(tencentCreateStaffId, staffMap, deparmentMap, tpTencentAdConfigDto);
        setOperatorCreateStaffId(flyFishCreateStaffId, staffMap, deparmentMap, tpFlyFishConfigDto);
        setOperatorCreateStaffId(aiCreateStaffId, staffMap, deparmentMap, tpAiCustomerServiceConfigDto);



        TpConfigDto tpConfigDto = new TpConfigDto();
        tpConfigDto.setAiCustomerServiceConfig(tpAiCustomerServiceConfigDto);
        tpConfigDto.setTencentConfig(tpTencentAdConfigDto);
        tpConfigDto.setFlyFishConfig(tpFlyFishConfigDto);

        return tpConfigDto;
    }

    private void setOperatorCreateStaffId(Long createStaffId, Map<Long, StaffDto> staffMap, Map<Long, DepartmentDto> departmentDtoMap, TpBaseConfigDto baseConfigDto) {
        if (0L != createStaffId) {
            StaffDto staffDto = staffMap.get(createStaffId);
            if (Objects.nonNull(staffDto)) {
                String staffName = staffDto.getStatus() == StaffStatusEnum.DELETE.getCode() ? staffDto.getName() + "(已删除)" : staffDto.getName();
                baseConfigDto.setOperatorCreateStaffId(staffDto.getId());
                baseConfigDto.setOperatorCreateStaffName(staffName);
                DepartmentDto departmentDto = departmentDtoMap.get(staffDto.getDepartmentId());
                if (Objects.nonNull(departmentDto)) {
                    baseConfigDto.setOperatorCreateStaffDepartmentId(departmentDto.getId());
                    baseConfigDto.setOperatorCrateStaffDepartmentName(departmentDto.getName());
                }
            }
        }
    }

    @Override
    public BaseResponse<TpConfigDto> setTpConfig(SetLeadsTpConfRequest request) {

        BaseResponse<Boolean> checkResponse = checkConfigKey(Lists.newArrayList(request.getCode()));
        if (!BaseResponse.responseSuccess(checkResponse)){
            return BaseResponse.error(checkResponse.getMsg());
        }

        SetMerchantConfigRequest setConfRequest = createSetFunctionConfigRequest(request);

        BaseResponse<MerchantConfigDto> setResponse = merchantConfigService.set(setConfRequest);
        if (!BaseResponse.responseSuccess(setResponse)){
            return BaseResponse.error(setResponse.getMsg());
        }

        return getTpConfig(request);
    }

    private SetMerchantConfigRequest createSetFunctionConfigRequest(SetLeadsTpConfRequest request){
        SetMerchantConfigRequest setFunctionConfigRequest = new SetMerchantConfigRequest();
        setFunctionConfigRequest.setCode(request.getCode());
        setFunctionConfigRequest.setValue(request.getValue());
        setFunctionConfigRequest.setBid(request.getBid());
        return setFunctionConfigRequest;
    }

    @Override
    public BaseResponse<TpConfigDto> multiSetTpConfig(MultiSetLeadsTpConfRequest request) {
        List<SetLeadsTpConfRequest> confList = request.getConfList();
        BaseResponse<Boolean> checkResponse = checkConfigKey(confList.stream().
                map(SetLeadsTpConfRequest::getCode).collect(Collectors.toList()));

        if (!BaseResponse.responseSuccess(checkResponse)){
            return BaseResponse.error(checkResponse.getMsg());
        }

        BaseResponse<Boolean> checkEffectiveResponse = checkEffective(request);
        if (!BaseResponse.responseSuccess(checkEffectiveResponse)){
            return BaseResponse.error("请填写正确的应用ID或密钥");
        }

        MultiSetMerchantConfigRequest setRequest = new MultiSetMerchantConfigRequest();
        setRequest.setBid(request.getBid());
        setRequest.setConfigList(confList.stream()
                .map(this::createSetFunctionConfigRequest).collect(Collectors.toList()));

        BaseResponse<BasePaginationResponse<MerchantConfigDto>> setResponse =
                merchantConfigService.multiSet(setRequest);

        if (!BaseResponse.responseSuccess(setResponse)){
            return BaseResponse.error("保存失败"+setResponse.getMsg());
        }

        return getTpConfig(request);
    }

    private BaseResponse<Boolean> checkConfigKey(List<String> keyList){

        ArrayList<String> configKeys = Lists.newArrayList(defaultConf.keySet());
        for (String key : keyList) {
            if (!configKeys.contains(key)){
                return BaseResponse.error(key+":配置项不存在");
            }
        }

        return BaseResponse.success(true);
    }

    private BaseResponse<Boolean> checkEffective(MultiSetLeadsTpConfRequest request) {
        Map<String, String> configMap = request.getConfList().stream().collect(Collectors.toMap(SetLeadsTpConfRequest::getCode, SetLeadsTpConfRequest::getValue));
        Set<String> keyList = configMap.keySet();
        if (keyList.contains("aiCustomerService.appId") || keyList.contains("aiCustomerService.secret")) {
            TpAiCustomerServiceConfigDto tpAiCustomerServiceConfigDto = new TpAiCustomerServiceConfigDto();
            tpAiCustomerServiceConfigDto.setAppId(configMap.get("aiCustomerService.appId"));
            tpAiCustomerServiceConfigDto.setAppId(configMap.get("aiCustomerService.appId"));
            tpAiCustomerServiceConfigDto.setSecret(configMap.get("aiCustomerService.secret"));

            BaseResponse<Boolean> response = aiCustomerServiceLeadsService.checkConfig(tpAiCustomerServiceConfigDto);

            if (response.getData() == null || !response.getData().equals(true)){
                return BaseResponse.error(response.getMsg());
            }
        }

        return BaseResponse.success(true);
    }

    public void setDefaultConf(Map<String, String> defaultConf) {
        this.defaultConf = defaultConf;
    }
}

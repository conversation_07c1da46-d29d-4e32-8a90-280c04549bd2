package com.inngke.bp.leads.mq.process.client;

import com.inngke.bp.client.enums.EntityChangeMqEventEnum;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClientRemoveProcess
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/10/7 15:45
 */
@Slf4j
@Service
public class ClientRemoveProcess implements ClientChangeProcess {
    @Resource
    private JsonService jsonService;

    @Resource
    private LeadsManager leadsManager;

    @Resource
    private LeadsEsService leadsEsService;


    @Override
    public void process(ClientTransferMessage entityChangeMessageDto) {
        if (Objects.isNull(entityChangeMessageDto) || !entityChangeMessageDto.getEvent().equals(EntityChangeMqEventEnum.DELETE.getCode())) {
            log.info("ClientRemoveListenerOfLeads.process: entityChangeMessageDto is null or event is not 3; messageInfo:{}", JsonUtil.toJsonString(entityChangeMessageDto));
            return;
        }

        Long clientId = entityChangeMessageDto.getId();

        List<Leads> leadsList = leadsManager.findLeadsByClient(entityChangeMessageDto.getBid(), clientId);
        if (CollectionUtils.isEmpty(leadsList)) {
            log.info("clientId:{} not has leads", clientId);
            return;
        }
        leadsList.forEach(leads -> leads.setStatus(LeadsStatusEnum.DELETED.getStatus()));
        boolean saveBatch = leadsManager.saveOrUpdateBatch(leadsList);
        if (saveBatch) {
            //更新es索引
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(entityChangeMessageDto.getBid());
            leadsUpdateRequest.setIds(leadsList.stream().map(Leads::getId).collect(Collectors.toList()));
            leadsUpdateRequest.setRefreshEs(true);
            leadsEsService.updateDocs(leadsUpdateRequest);
        }
    }
}

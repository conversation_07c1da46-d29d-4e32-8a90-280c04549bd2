package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.utils.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeArrivalStoreStrategy")
public class LeadsFollowTimeArrivalStoreStrategy implements LeadsFollowTimeStrategy {
    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateArrivalStore(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateArrivalStore",
                        QueryBuilders.boolQuery()
                                .should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.STORED.getStatus()))
                                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.STORED.getStatus()))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateArrivalStore = bucketAggregations.get("stateArrivalStore");
        long count = stateArrivalStore.getDocCount();
        dto.setStateArrivalStore((int)count);
        // 获取有效客户数
        ParsedFilter contactFilter = bucketAggregations.get("validCount");
        long validCount = contactFilter.getDocCount();
        if (validCount > 0L) {
            String rate = BigDecimal.valueOf(count).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(validCount),2, RoundingMode.HALF_DOWN).toString();
            dto.setArrivalStoreRate(rate+"%");
        }
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos,LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateArrivalStore).reduce(Integer::sum).get();
        dto.setStateArrivalStore(sum);
        if(dto.getStateAvail()==0){
            return;
        }
        String rate = BigDecimal.valueOf(sum).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getStateAvail()),2, RoundingMode.HALF_DOWN).toString();
        dto.setArrivalStoreRate(rate+"%");
    }
}

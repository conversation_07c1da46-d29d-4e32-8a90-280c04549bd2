package com.inngke.bp.leads.core.utils;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.ip.reach.enums.ReleaseStatusEnum;

import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 初始化跟进记录内容工具
 * <AUTHOR>
 */
public class LeadsFollowContentUtil {
    private static final String content = "{0}{1}{2}";

    private static final String statusChangeContent = "，系统将线索状态由【{0}】修改为【{1}】";

    public static String initLeadsFollowContent(Leads leads, Boolean isFirst, PrivateVoiceRecordDTO dto) {
        Integer releaseCause = dto.getReleaseCause();
        String start = "";
        String mind = "";
        String end = "";
        start = isFirst ? "第一次拨打客户虚拟号，" : "拨打客户虚拟号，";
        // 判断分配时间 24h内/24h后
        LeadsStatusEnum statusEnum = getNewStatus(leads);
        ReleaseStatusEnum releaseStatusEnum = ReleaseStatusEnum.parse(releaseCause);
        if ((ReleaseStatusEnum.R31 ==  releaseStatusEnum && dto.getVoiceTime() > 0) || (ReleaseStatusEnum.R16 == releaseStatusEnum && dto.getVoiceTime() > 0)) {
            Integer voiceTime = dto.getVoiceTime();
            Integer minutes = voiceTime / 60;
            Integer seconds = voiceTime % 60;
            mind = minutes > 0 ? "通话时长" + minutes + "分" + seconds + "秒" : "通话时长" + seconds + "秒";
        } else if (ReleaseStatusEnum.R21 == ReleaseStatusEnum.parse(releaseCause)) {
            mind = "客户拒接";
        } else {
            mind = "未接通";
        }
        end = isFirst ? MessageFormat.format(statusChangeContent, LeadsStatusEnum.parse(leads.getStatus()).getName(), statusEnum.getName()) : "";

        return MessageFormat.format(content, start, mind, end);
    }

    /**
     * 获取线索状态 若是已联系后面的状态则不变 若是待联系 返回已联系
     *
     * @param leads
     * @return
     */
    public static LeadsStatusEnum getNewStatus(Leads leads) {
        return leads.getStatus() > LeadsStatusEnum.CONTACTED.getStatus() &&
                !leads.getStatus().equals(LeadsStatusEnum.PRE_FOLLOW.getStatus())?
                LeadsStatusEnum.parse(leads.getStatus()) : LeadsStatusEnum.CONTACTED;
    }
}

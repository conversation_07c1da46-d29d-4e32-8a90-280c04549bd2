package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.StaffLeadsCountDto;
import com.inngke.bp.leads.dto.request.ToFollowLeadsMessageDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/17 7:43 PM
 */
public interface LeadsWxPubMessageService {
    /**
     * 发送线索新增微信公众号消息
     *
     * @param bid                 商户ID
     * @param operatorId          操作者ID
     * @param staffLeadsCountList 员工新分配线索数量
     */
    void batchSendDistributeMessage(int bid, long operatorId, List<StaffLeadsCountDto> staffLeadsCountList,String timeInfo,int leadsType);

    /**
     * 您有XX条线索超时未跟进！
     *
     * @param request 待跟进消息请求
     */
    void sendToFollowLeadsMessage(ToFollowLeadsMessageDto request);
}

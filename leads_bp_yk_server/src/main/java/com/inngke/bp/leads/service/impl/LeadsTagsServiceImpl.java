package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.MqServiceForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsTagAddRequest;
import com.inngke.bp.leads.dto.request.LeadsTagsDetailRequest;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.response.LeadsTagsDetailDto;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.bp.leads.service.LeadsTagsService;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.request.CustomerTagsTypeJudgeRequest;
import com.inngke.bp.user.dto.request.tags.CustomerTagsJudgeRequest;
import com.inngke.bp.user.dto.response.CustomerTagsDetailDto;
import com.inngke.bp.user.dto.response.CustomerTagsDetailTagsDto;
import com.inngke.bp.user.service.CustomerTagsService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/6/9
 **/
@Service
public class LeadsTagsServiceImpl implements LeadsTagsService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTagsServiceImpl.class);

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerTagsService customerTagsService;

    @Autowired
    private LeadsManager leadsManager;


    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    @Override
    public BaseResponse<Boolean> tagsAdd(LeadsTagAddRequest request) {
        Leads leads = leadsManager.getById(request.getLeadsId());
        if (leads == null) {
            return BaseResponse.error("数据不存在", false);
        }
        if (!leads.getBid().equals(request.getBid())) {
            return BaseResponse.error("数据不允许更改", false);
        }

        String content = tagsUpdateContent(
                StringUtils.isEmpty(leads.getTags()) ? new ArrayList<>() : Arrays.asList(leads.getTags().split(",")),
                request.getTags() == null ? new ArrayList<>() : request.getTags());

        if (StringUtils.isEmpty(content)) {
            return BaseResponse.success(true);
        }
        if (content.length() > 3000) {
            content = content.substring(0, 3000);
        }
        Leads update = new Leads();
        update.setId(leads.getId());
        if (Objects.nonNull(request.getTags())){
            update.setTags(list2TagsName(request.getTags()));
        }
        if (Objects.nonNull(request.getEnterpriseTags())){
            update.setEnterpriseTags(list2TagsName(request.getEnterpriseTags()));
        }

        if (!leadsManager.updateById(update)) {
            return BaseResponse.error("更新失败", false);
        }
        if (request.getNeedLeadsFollow() == null || request.getNeedLeadsFollow()) {
            // 添加跟进记录
            LeadsFollow leadsFollow = new LeadsFollow();
            leadsFollow.setBid(leads.getBid());
            leadsFollow.setLeadsId(leads.getId());
            StaffDto staffDto = staffClientForLeads.getStaffByCid(request.getBid(), request.getOperatorId());
            leadsFollow.setUserId(request.getOperatorId());
            if (Objects.nonNull(staffDto)) {
                leadsFollow.setStaffId(staffDto.getId());
            }
            leadsFollow.setBeforeLeadsStatus(leads.getStatus());
            leadsFollow.setFollowType(LeadsFollowTypeEnum.MANUAL.getCode());
            leadsFollow.setFollowContent(content);
            leadsFollow.setLeadsStatus(leads.getStatus());
            leadsFollow.setCreateTime(LocalDateTime.now());

            boolean save = leadsFollowManager.save(leadsFollow);
            if (save) {
                // 发送添加跟进记录Mq
                mqServiceForLeads.sendLeadsFollowMq(leadsFollow);
            }
            leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
            leadsManager.update(Wrappers.<Leads>update().eq(Leads.BID, request.getBid())
                    .eq(Leads.ID, request.getLeadsId())
                    .set(Leads.LAST_FOLLOW_ID, leadsFollow.getId())
                    .set(Leads.LAST_FOLLOW_TIME, leadsFollow.getCreateTime())
            );
        }
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setOperatorId(request.getOperatorId());
            leadsUpdateRequest.setIds(Lists.newArrayList(request.getLeadsId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return BaseResponse.success(true);
    }


    /**
     * 返回更改信息
     * 操作	                      跟进人	          跟进时间	    跟进记录描述
     * 添加新的标签	              操作人的员工姓名	  成功保存的时间	给客户添加标签【XXX】、【XXX】
     * 删除已添加的标签	          操作人的员工姓名	  成功保存的时间	删除客户标签【XXX】、【XXX】
     * 删除已有标签并添加新的标签	  操作人的员工姓名   成功保存的时间	删除客户标签【XXX】、【XXX】，添加新的客户标签【XXX】、【XXX】
     *
     * @param oldTags 旧的标签
     * @param newTags 新的标签
     * @return 标签更改描述
     */
    private String tagsUpdateContent(List<String> oldTags, List<String> newTags) {
        if (oldTags == null) {
            oldTags = new ArrayList<>();
        }
        if (newTags == null) {
            newTags = new ArrayList<>();
        }
        Set<String> oldSet = new HashSet<>(oldTags);
        Set<String> newSet = new HashSet<>(newTags);
        StringBuilder stringBuilder = new StringBuilder();
        // 删除的客户标签
        int sizeOld = 0;
        int sizeNew = 0;
        for (String s : oldTags) {
            if (!newSet.contains(s) && sizeOld != 0) {
                stringBuilder.append("、").append("【").append(s).append("】");
                continue;
            }
            if (!newSet.contains(s) && sizeOld == 0) {
                stringBuilder.append("删除客户标签 ").append("【").append(s).append("】");
                sizeOld++;
            }
        }
        for (String s : newTags) {
            if (oldSet.contains(s)) {
                continue;
            }
            if (sizeNew != 0) {
                stringBuilder.append("、").append("【").append(s).append("】");
                continue;
            }
            if (sizeOld != 0) {
                stringBuilder.append("，");
            }
            if (sizeOld != 0 && org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                stringBuilder.append("添加客户标签 ").append("【").append(s).append("】");
                sizeNew++;
            }
            if (sizeOld == 0 && org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                stringBuilder.append("给客户添加标签 ").append("【").append(s).append("】");
                sizeNew++;
            }
        }
        return stringBuilder.toString();
    }


    @Override
    public BaseResponse<LeadsTagsDetailDto> tagsDetail(LeadsTagsDetailRequest request) {
        Long def = 0L;
        if (def.equals(request.getCustomerId())) {
            return BaseResponse.error("customerId参数不正确");
        }
        try {
            List<String> tag = new ArrayList<>(0);
            if (request.getLeadsId() != null) {
                Leads byId = leadsManager.getById(request.getLeadsId());
                if (byId != null && StringUtils.isNotEmpty(byId.getTags())) {
                    tag = Arrays.asList(byId.getTags().split(","));
                }
            }
            CustomerTagsJudgeRequest judgeRequest = new CustomerTagsJudgeRequest();
            judgeRequest.setTags(tag);
            judgeRequest.setBid(request.getBid());
            judgeRequest.setCustomerId(request.getCustomerId());
            BaseResponse<CustomerTagsDetailDto> customerTagsDetailDtoBaseResponse = customerTagsService.tagsJudge(judgeRequest);
            if (!InngkeApiConst.API_SUCCESS_CODE.equals(customerTagsDetailDtoBaseResponse.getCode())) {
                return BaseResponse.error(customerTagsDetailDtoBaseResponse.getMsg());
            }
            return BaseResponse.success(new LeadsTagsDetailDto(customerTagsDetailDtoBaseResponse.getData()));
        } catch (Exception e) {
            logger.error("线索查询标签详情异常：", e);
        }
        return BaseResponse.error("查询请稍后重试");
    }


    @Override
    public List<CustomerTagsDetailTagsDto> customerTagsDetailTagsDto(CustomerTagsTypeJudgeRequest request) {
        return customerTagsService.tagsJudge(request);
    }


    /**
     * 子标签名称列表转字符串
     *
     * @param tagsName 子标签名称列表
     * @return 子标签名字符串，逗号分割
     */
    private String list2TagsName(List<String> tagsName) {
        if (tagsName == null || tagsName.isEmpty()) {
            return "";
        }
        return String.join(",", tagsName);
    }


}

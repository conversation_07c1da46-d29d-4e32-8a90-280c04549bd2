package com.inngke.bp.leads.service;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/4/1 16:41
 */
public interface LeadsContactFollowService {


    /**
     * 创建拨打电话的跟进记录
     *
     * @return
     */
    BaseResponse<Boolean> createContactFollow(Leads leads, PrivateVoiceRecordDTO dto);


    LeadsContactStatusEnum getStatusEnum();
}

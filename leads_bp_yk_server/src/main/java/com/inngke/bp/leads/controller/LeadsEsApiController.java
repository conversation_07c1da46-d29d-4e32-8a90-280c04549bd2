package com.inngke.bp.leads.controller;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfPermissionsManager;
import com.inngke.bp.leads.dto.request.LeadsAddRequest;
import com.inngke.bp.leads.dto.request.LeadsEsBatchRequest;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.service.LeadsDistributeRegionPermissionsService;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.schedule.LeadsRegionCache;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.response.EsDocsResponse;
import com.inngke.ip.common.dto.response.RegionDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 线索管理es处理器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/leads")
public class LeadsEsApiController {

    @Autowired
    private LeadsEsService leadsEsService;

    @GetMapping("/docs")
    public BaseResponse<EsDocsResponse<LeadsEsDto>> getBatchLeadsDocs(
            @RequestHeader int bid,
            LeadsEsBatchRequest request
    ) {
        request.setBid(bid);
        return leadsEsService.getBatchDoc(request);
    }

    /**
     * es更新某个客户的数据
     *
     * @param id  客户id即为customer.id
     * @param bid 商户id
     * @return
     */
    @PutMapping("/{id:\\d+}/doc")
    public BaseResponse<Boolean> updateLeadsDoc(
            @PathVariable long id,
            @RequestHeader int bid,
            @RequestBody LeadsUpdateRequest request
    ) {
        request.setBid(bid);
        request.setId(id);
        return leadsEsService.updateDocs(request);
    }

    @PostMapping("/{id:\\d+}")
    public BaseResponse<Boolean> createLeadsDoc(
            @PathVariable long id,
            @RequestHeader int bid
    ) {
        LeadsAddRequest request = new LeadsAddRequest();
        request.setId(id);
        request.setBid(bid);
        return leadsEsService.createLeadsDocs(request);
    }

    @Autowired
    private LeadsDistributeConfPermissionsManager leadsDistributeConfPermissionsManager;
    @GetMapping("/test")
    public void updateES() {
//        LeadsUpdateRequest request = new LeadsUpdateRequest();
//        request.setBid(1);
//        request.setIds(Lists.newArrayList(34L, 35L));
        leadsDistributeConfPermissionsManager.removeDistributeConfPermissions(7, 629L);
//        leadsEsService.updateDocs(request);
    }

    @GetMapping("/leads")
    public void test() {
        LeadsEsBatchRequest request = new LeadsEsBatchRequest();
        request.setBid(7);
        request.setLastLeadsId(4193L);
        request.setPageSize(2);
        BaseResponse<EsDocsResponse<LeadsEsDto>> batchDoc = leadsEsService.getBatchDoc(request);
        System.out.println(batchDoc.getData().getList());
    }


    @Autowired
    private LeadsRegionCache leadsRegionCache;

    @GetMapping("/test-region")
    public RegionDto testRegion(@RequestParam String region){
        RegionDto regionDto = leadsRegionCache.analysisRegion(region);

        return regionDto;
    }
}

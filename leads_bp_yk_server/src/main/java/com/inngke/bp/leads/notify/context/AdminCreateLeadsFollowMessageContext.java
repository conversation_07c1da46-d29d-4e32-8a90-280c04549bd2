package com.inngke.bp.leads.notify.context;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.ApplicationContextGetBeanHelper;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.service.enums.LeadsNotifyMessageEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.context.NotifyMessageContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * AdminCreateLeadsFollowMessageContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/21 17:40
 */
public class AdminCreateLeadsFollowMessageContext implements NotifyMessageContext {

    private int bid;

    private static final LeadsNotifyMessageEnum notifyEnum = LeadsNotifyMessageEnum.ADMIN_ADD_LEADS_FOLLOW_NOTIFY;


    private long targetStaffId;

    private String qyWxUserId;

    private String mobile;

    private String name;

    private String wxPubOpenId;

    private Map<String, String> pathParams;


    public String content;

    public String getMobile() {
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    public String getName() {
        return name;
    }

    public String getContent() {
        return content;
    }


    @Override
    public int getBid() {
        return bid;
    }

    @Override
    public String getMsgTypeCode() {
        return notifyEnum.getTemplateCode();
    }

    @Override
    public String getPath() {
        return notifyEnum.getPath();
    }

    @Override
    public Long getTargetStaffId() {
        return Long.valueOf(this.targetStaffId);
    }

    @Override
    public Map<String, String> getPathParams() {

        return pathParams;
    }

    @Override
    public String getQyUserId() {
        return qyWxUserId;
    }

    @Override
    public String getWxPubOpenId() {
        return wxPubOpenId;
    }

    @Override
    public TemplateMessageContentBuilder<? extends NotifyMessageContext> getMessageBuilder() {
        return null;
    }

    public static AdminCreateLeadsFollowMessageContext init(int bid, long leadsId, String followContent) {
        LeadsManager leadsManager = ApplicationContextGetBeanHelper.getBean(LeadsManager.class);
        Leads leads = leadsManager.getById(bid, leadsId);

        AdminCreateLeadsFollowMessageContext context = new AdminCreateLeadsFollowMessageContext();
        context.bid = bid;
        context.targetStaffId = leads.getDistributeStaffId();
        context.content = followContent;
        context.name = leads.getName();
        context.mobile = leads.getMobile();
        StaffClientForLeads staffClient = ApplicationContextGetBeanHelper.getBean(StaffClientForLeads.class);
        StaffDto staff = staffClient.getStaffById(bid, context.targetStaffId);
        if (Objects.nonNull(staff)) {
            if (StringUtils.isNotBlank(staff.getQyUserId())) {
                context.qyWxUserId = staff.getQyUserId();
            }

            CustomerGetServiceClientForLeads customerClient = ApplicationContextGetBeanHelper.getBean(CustomerGetServiceClientForLeads.class);
            CustomerDto customer = customerClient.getCustomerById(bid, staff.getCustomerId());
            if (Objects.nonNull(customer)) {
                context.wxPubOpenId = customer.getWxPubOpenId();
            }
        }
        Map<String, String> params = Maps.newHashMap();
        params.put("activeTab", "follow");
        params.put("staffId", context.getTargetStaffId().toString());
        params.put("id", String.valueOf(leadsId));
        context.pathParams = params;

        return context;
    }
}

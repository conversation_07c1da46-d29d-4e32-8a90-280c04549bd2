package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeNoAvailStrategy")
public class LeadsFollowTimeNoAvailStrategy implements LeadsFollowTimeStrategy {

    @Autowired
    private LeadsManager leadsManager;

    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime,long time) {
        leadsFollowTime.setStateNoAvail(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateNoAvail",
                        QueryBuilders.boolQuery()
                                .should(QueryBuilders.termsQuery("status", Arrays.asList(LeadsStatusEnum.LOST.getStatus(), LeadsStatusEnum.INVALID.getStatus())))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateNoAvail = bucketAggregations.get("stateNoAvail");
        long count = stateNoAvail.getDocCount();
        dto.setStateNoAvail((int)count);
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateNoAvail).reduce(Integer::sum).get();
        dto.setStateNoAvail(sum);
    }
}

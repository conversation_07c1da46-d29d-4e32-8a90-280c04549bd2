/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索变更日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 操作者ID，一般是staffId，0表示系统操作
     */
    private Long operatorId;

    /**
     * 此线索分配给哪个员工ID，仅在分配时有数据
     */
    private Long distributeStaffId;

    /**
     * 线索状态变更为：-2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交 12=无效线索
     */
    private Integer statusChange;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String LEADS_ID = "leads_id";

    public static final String OPERATOR_ID = "operator_id";

    public static final String DISTRIBUTE_STAFF_ID = "distribute_staff_id";

    public static final String STATUS_CHANGE = "status_change";

    public static final String LOG_CONTENT = "log_content";

    public static final String CREATE_TIME = "create_time";

}

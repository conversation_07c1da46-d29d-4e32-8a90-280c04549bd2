package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.inngke.bp.organize.dto.request.GetLoginDtoRequest;
import com.inngke.bp.organize.dto.response.LoginDto;
import com.inngke.bp.organize.service.LoginService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/9/26 9:33
 **/
@Slf4j
@Component
public class LoginClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private LoginService loginService;

    public LoginDto getLoginDtoByCustomerId(Integer bid, Long customerId) {
        log.info("getLoginDtoByCustomerId bid :{} customerId: {}", bid, customerId);
        GetLoginDtoRequest getLoginDtoRequest = new GetLoginDtoRequest();
        getLoginDtoRequest.setBid(bid);
        getLoginDtoRequest.setId(customerId);
        BaseResponse<LoginDto> response = loginService.getByCustomerId(getLoginDtoRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public LoginDto getLoginDtoByStaffId(Integer bid, Long staffId) {
        log.info("getLoginDtoByCustomerId bid :{} staffId: {}", bid, staffId);
        GetLoginDtoRequest getLoginDtoRequest = new GetLoginDtoRequest();
        getLoginDtoRequest.setBid(bid);
        getLoginDtoRequest.setId(staffId);
        BaseResponse<LoginDto> response = loginService.getByStaffId(getLoginDtoRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public LoginDto getManagerDeptIdByCid(Integer bid, Long operatorId) {
        GetLoginDtoRequest getLoginDtoRequest = new GetLoginDtoRequest();
        getLoginDtoRequest.setBid(bid);
        getLoginDtoRequest.setId(operatorId);
        BaseResponse<LoginDto> response = loginService.getByCustomerId(getLoginDtoRequest);
        if (BaseResponse.responseErrorOrNullData(response)) {
            return null;
        }
        return response.getData();
    }

    public List<Long> getMangerDeptIds(Integer bid, Long cid) {
        LoginDto loginDto = getLoginDtoByCustomerId(bid, cid);
        if (Objects.nonNull(loginDto)
                && !CollectionUtils.isEmpty(loginDto.getManageDepartmentIds())) {
            return Lists.newArrayList(loginDto.getManageDepartmentIds());
        }
        throw new InngkeServiceException("获取管理权限失败");
    }

}

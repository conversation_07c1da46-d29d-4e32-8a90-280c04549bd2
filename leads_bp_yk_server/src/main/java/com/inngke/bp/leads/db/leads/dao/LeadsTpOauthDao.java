/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 广告平台授权信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsTpOauthDao extends BaseMapper<LeadsTpOauth> {

}

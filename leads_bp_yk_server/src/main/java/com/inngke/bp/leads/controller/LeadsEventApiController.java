package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.annotation.LeadsIdValidator;
import com.inngke.bp.leads.dto.request.LeadsEventDialRequest;
import com.inngke.bp.leads.dto.request.LeadsPhoneGetRequest;
import com.inngke.bp.leads.service.LeadsChangeService;
import com.inngke.bp.leads.service.LeadsEventService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 *
 * @chapter 线索
 * @section 事件
 */
@RestController
@RequestMapping("/api/leads/event")
@Validated
public class LeadsEventApiController {

    @Autowired
    LeadsEventService leadsEventService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsChangeService leadsChangeService;

    /**
     * 线索事件-拨打电话
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param id         线索ID
     * @return
     */
    @PostMapping("/dial/{id:\\d+}")
    public BaseResponse<Boolean> dial(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) Long operatorId,
            @PathVariable @LeadsIdValidator Long id,
            @RequestAttribute JwtPayload jwtPayload) {
        LeadsEventDialRequest leadsEventDialRequest = new LeadsEventDialRequest();

        leadsEventDialRequest.setBid(bid);
        leadsEventDialRequest.setOperatorId(jwtPayload.getCid());
        leadsEventDialRequest.setId(id);
        leadsEventDialRequest.setOperatorStaffId(jwtPayload.getSid());
        return leadsEventService.dial(leadsEventDialRequest);
    }

    /**
     * 获取完整手机号
     * @return
     */
    @PostMapping("/phone")
    public BaseResponse<Boolean> getPhone(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) Long operatorId,
            @RequestBody @Validated LeadsPhoneGetRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setBid(bid);
        request.setOperatorId(jwtPayload.getCid());
        request.setOperatorStaffId(jwtPayload.getSid());
        return leadsEventService.getPhone(request);
    }

    /**
     * 上报联系失败事件
     *
     * @param bid 商户id
     * @param operatorId 操作者id
     * @param leadsId 线索id
     * @return 操作结果
     */
    @GetMapping("/contact/fail/{leadsId:\\d+}")
    public BaseResponse<Boolean> contactFailEvent(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) Long operatorId,
            @PathVariable Long leadsId) {
        BaseIdRequest request = new BaseIdRequest();
        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setId(leadsId);

        return leadsEventService.contactFail(request);
    }
}

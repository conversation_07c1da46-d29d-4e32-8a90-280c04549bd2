package com.inngke.bp.leads.service.strategy.permissions.list;

import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;
import java.util.Set;

/**
 * 客服清洗
 */
@Component
public class ListCustomerPermissionsStrategy extends ListPermissionsStrategyAbstract {


    /**
     * 自己创建的和自己所管理部门的员工创建的
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    @Override
    protected void otherPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultOtherPermissionsHandle(request, queryBuilder);
    }

    @Override
    protected void merchantManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultMerchantManagePermissionsHandle(request, queryBuilder);
    }

    @Override
    protected BoolQueryBuilder getDefaultPermissionsBuilder(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        Set<Long> staffManageDepartmentIds = getStaffManageDepartmentIds(request.getBid(), request.getSid());

        //自己创建的
        BoolQueryBuilder sonQueryBuilder = QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getSid()));

        //自己所管理部门的员工创建的,自己所管理部门员工[[跟进的]]
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            sonQueryBuilder
                    .should(QueryBuilders.termsQuery(LeadsEsDto.CREATE_DEPARTMENT_ID, staffManageDepartmentIds))
                    .should(QueryBuilders.termsQuery(LeadsEsDto.PRE_FOLLOW_DEPARTMENT_IDS, staffManageDepartmentIds));
        }

        return sonQueryBuilder;
    }


    @Override
    protected void customerManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        //查看所在部门+管理部门权限范围的数据（按归属员工所在部门）
        Set<Long> staffManageDepartmentIds = getStaffManageDepartmentIds(request.getBid(), request.getSid());
        StaffDto staff = staffClientForLeads.getStaffById(request.getBid(), request.getSid());
        if (Objects.nonNull(staff)) {
            staffManageDepartmentIds.add(staff.getDepartmentId());
        }
        BoolQueryBuilder sonQueryBuilder = QueryBuilders.boolQuery();
        //自己所管理部门的员工创建的,自己所管理部门员工[[跟进的]]
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            sonQueryBuilder
                    .should(QueryBuilders.termsQuery(LeadsEsDto.PRE_FOLLOW_DEPARTMENT_IDS, staffManageDepartmentIds));
        }

        queryBuilder.must(sonQueryBuilder);
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 线索变更日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface LeadsLogManager extends IService<LeadsLog> {

    void batchSaveLeadsLog(List<LeadsLog> logs);
}

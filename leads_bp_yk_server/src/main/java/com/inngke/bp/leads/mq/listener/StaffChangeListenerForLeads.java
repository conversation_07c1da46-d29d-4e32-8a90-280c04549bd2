package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.bp.leads.mq.process.organize.StaffChangeProcess;
import com.inngke.bp.organize.enums.EntityChangeTypeEnum;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@MqConsumer(value = StaffChangeListenerForLeads.TOPIC_STAFF_CHANGE, consumerName = "leads_bp_yk_staff_change", messageType = EntityChangeMessageDto.class)
@Log4j2
public class StaffChangeListenerForLeads implements InngkeMqListener<EntityChangeMessageDto> {

    public static final String TOPIC_STAFF_CHANGE = "staff_change";
    @Autowired
    private List<StaffChangeProcess> staffChangeProcessesList;

    @Override
    public void process(EntityChangeMessageDto entityChangeMessageDto) {
        if (entityChangeMessageDto == null) {
            log.error("处理失败消息体未空");
            return;
        }
        EntityChangeTypeEnum changeTypeEnum = EntityChangeTypeEnum.parse(entityChangeMessageDto.getEvent());
        if (changeTypeEnum == null) {
            log.error("消息事件错误 消息体:{}", entityChangeMessageDto);
            return;
        }

        StaffChangeProcess staffChangeProcess = null;
        for (StaffChangeProcess staffChange : staffChangeProcessesList) {
            if (staffChange.getHandleEntityChangeType().equals(changeTypeEnum)) {
                staffChangeProcess = staffChange;
                break;
            }
        }

        if (staffChangeProcess == null) {
            log.error("未找到消息处理器{}", entityChangeMessageDto);
            return;
        }

        staffChangeProcess.handle(entityChangeMessageDto);
    }
}

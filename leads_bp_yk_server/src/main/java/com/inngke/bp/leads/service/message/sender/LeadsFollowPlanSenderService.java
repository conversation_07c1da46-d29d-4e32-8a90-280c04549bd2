package com.inngke.bp.leads.service.message.sender;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.bp.leads.service.message.context.RemindersToFollowedContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class LeadsFollowPlanSenderService extends MessageSenderServiceAbs{
    private static final Logger logger = LoggerFactory.getLogger(LeadsFollowPlanSenderService.class);

    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.REMINDERS_TO_FOLLOWED;
    }

    @Override
    public void init(MessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(MessageContext context) {
        RemindersToFollowedContext ctx = context.transferContext();
        if (ctx.getTemplateMessageSendRequest() != null) {
            return ctx.getTemplateMessageSendRequest();
        }

        List<LeadsEsDto> leadsEsDtoList = ctx.getLeadsEsDtoList();
        List<Leads> leadsList = ctx.getLeadsList();

        if (CollectionUtils.isEmpty(leadsEsDtoList) && CollectionUtils.isEmpty(leadsList)) {
            logger.info("准备发送线索跟进提醒失败,线索数据为空!");
            return null;
        }
        if(StringUtils.isEmpty(ctx.getTargetWxPubOpenId()) && StringUtils.isEmpty(context.getTargetQyUserId())){
            logger.info("准备发送线索跟进提醒失败,无法通知!");
            return null;
        }

        int size = leadsEsDtoList != null ? leadsEsDtoList.size() : leadsList.size();
        String leadsId = leadsEsDtoList != null ? leadsEsDtoList.get(0).getId() : String.valueOf(leadsList.get(0).getId());

        String timeOut = ctx.getTimeOut();

        Map<String, String> params = Maps.newHashMap();
        params.put("umaEvent", "clueRemind");
        params.put("staffId", String.valueOf(ctx.getTargetSid()));

        if (size == 1) {
            params.put("leadsId", leadsId);
            params.put("id", leadsId);
        } else {
            params.put("secondStatus", "1");
        }

        List<String> nameList = null;
        List<String> mobileList = null;
        String createTime = null;
        String content;
        if (leadsEsDtoList != null && !CollectionUtils.isEmpty(leadsEsDtoList)) {
            nameList = leadsEsDtoList.stream().map(LeadsEsDto::getName).filter(StringUtils::isNotBlank)
                    .limit(2).collect(Collectors.toList());
            mobileList = leadsEsDtoList.stream().map(LeadsEsDto::getMobile).filter(StringUtils::isNotBlank)
                    .limit(2).map(this::cellPhoneNumberDesensitization).collect(Collectors.toList());
            if (size == 1) {
                LeadsEsDto leadsEsDto = leadsEsDtoList.get(0);
                createTime = DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(leadsEsDto.getDistributeTime()), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
            } else {
                createTime = DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(leadsEsDtoList.get(leadsEsDtoList.size() - 1).getDistributeTime()), DateTimeUtils.YYYY_MM_DD_HH_MM_SS)
                        + "~" +
                        DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(leadsEsDtoList.get(0).getDistributeTime()), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
            }
            content = "请及时跟进处理以免错过最佳跟进时间！";
        } else {
            nameList = leadsList.stream().map(Leads::getName).filter(StringUtils::isNotBlank)
                    .limit(2).collect(Collectors.toList());
            mobileList = leadsList.stream().map(Leads::getMobile).filter(StringUtils::isNotBlank)
                    .limit(2).map(this::cellPhoneNumberDesensitization).collect(Collectors.toList());
            createTime = leadsList.stream().filter(leads -> Objects.nonNull(leads.getDistributeTime()))
                    .min(Comparator.comparing(Leads::getDistributeTime))
                    .map(Leads::getDistributeTime)
                    .map(distributeTime -> DateTimeUtils.format(distributeTime, DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                    .orElse(InngkeAppConst.EMPTY_STR);
            content = "线索下发已超过" + timeOut + "，请及时跟进处理以免错过最佳跟进时间！";
        }

        String extName = size > 2 && nameList.size() > 1 ? "..." : "";
        String extMobile = size > 2 && mobileList.size() > 1 ? "..." : "";

        String names = nameList.size() > 1 ? Joiner.on("，").join(nameList) + extName :
                nameList.stream().findFirst().orElse(InngkeAppConst.EMPTY_STR);
        String mobiles = mobileList.size() > 1 ? Joiner.on("，").join(mobileList) + extMobile :
                mobileList.stream().findFirst().orElse("未知");

        return getTemplateRequestBuilder(ctx)
                .setTitle("您有" + size + "条线索超时未跟进！")
                .setVar("title", "您有" + size + "条线索超时未跟进！")
                .setVar("name", StringUtils.isNotBlank(ctx.getTargetQyUserId()) ? names : ("您有" + size + "条线索超时未跟进"))
                .setVar("mobile", StringUtils.isEmpty(ctx.getTargetQyUserId()) ? (size > 1 ? mobileList.get(0) + ".." : mobileList.get(0)) : mobiles)
                .setVar("followType", "跟进提醒")
                .setVar("followContent", "线索下发已超过" + timeOut)
                .setVar("createTime", createTime)
                .setVar("content", content)
                .setMpPagePath(getMessageType().getPath(params))
                .build();
    }

    /**
     * 手机号脱敏
     */
    private String cellPhoneNumberDesensitization(String mobile){
        if (StringUtils.isBlank(mobile)){
            return mobile;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 线索跟进数据报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsFollowTimeDao extends BaseMapper<LeadsFollowTime> {

}

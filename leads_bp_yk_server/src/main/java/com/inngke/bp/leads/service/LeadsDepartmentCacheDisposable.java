package com.inngke.bp.leads.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.service.DepartmentService;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class LeadsDepartmentCacheDisposable {

    private DepartmentService departmentService;

    private Map<Integer,DepartmentDto> bidDepartmentTreeMap = Maps.newTreeMap();

    private Map<Integer,Map<Long,DepartmentDto>> bidDepartmentMap = Maps.newTreeMap();


    public LeadsDepartmentCacheDisposable(Integer bid,DepartmentService departmentService){
        this.departmentService = departmentService;

        initBidDepartmentTree(bid);
    }

    public LeadsDepartmentCacheDisposable(DepartmentService departmentService){
        this.departmentService = departmentService;
    }

    private void  initBidDepartmentTree(Integer bid){
        if (bidDepartmentTreeMap.containsKey(bid)){
            return;
        }
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        BaseResponse<List<DepartmentDto>> response = departmentService.getRootDepartments(request);
        if (BaseResponse.responseSuccessWithNonNullData(response)){
            Long id = response.getData().stream().findFirst().orElse(new DepartmentDto()).getId();
            if (!ObjectUtils.isEmpty(id)){
                GetDepartmentRequest requestAll = new GetDepartmentRequest();
                requestAll.setDepartmentId(id);
                requestAll.setChildrenLevel(20);
                requestAll.setBid(bid);

                BaseResponse<DepartmentDto> allResponse = departmentService.getParentWithChildrenById(requestAll);
                if (BaseResponse.responseSuccessWithNonNullData(allResponse)){
                    bidDepartmentTreeMap.put(bid,allResponse.getData());
                    buildDepartmentMap(bid,null);
                }
            }
        }
    }

    public List<Long> getParentDepartmentIds(Integer bid,Long departmentId) {
        if (Objects.isNull(departmentId)) {
            return Lists.newArrayList();
        }
        Map<Long, DepartmentDto> departmentMap = getDepartmentMap(bid);
        List<Long> departmentIds = Lists.newArrayList();

        while (true){
            DepartmentDto departmentDto = departmentMap.get(departmentId);
            if (ObjectUtils.isEmpty(departmentDto)){
                return departmentIds;
            }
            departmentIds.add(departmentId);
            departmentId = departmentDto.getParentId();
        }
    }

    private void  buildDepartmentMap(Integer bid,DepartmentDto currentDepartment){
        if (ObjectUtils.isEmpty(currentDepartment)){
            currentDepartment = bidDepartmentTreeMap.get(bid);
        }

        Map<Long, DepartmentDto> departmentMap = bidDepartmentMap.computeIfAbsent(bid, BID -> Maps.newHashMap());
        departmentMap.put(currentDepartment.getId(),currentDepartment);
        if (!CollectionUtils.isEmpty(currentDepartment.getChildren())){
            for (DepartmentDto child : currentDepartment.getChildren()) {
                if (!ObjectUtils.isEmpty(child)){
                    buildDepartmentMap(bid,child);
                }
            }
        }
    }

    public Map<Long, DepartmentDto> getDepartmentMap(Integer bid){
        initBidDepartmentTree(bid);

        return bidDepartmentMap.get(bid);
    }

    public List<DepartmentDto> getParentDepartment(Integer bid, Long departmentId) {
        Map<Long, DepartmentDto> departmentMap = getDepartmentMap(bid);
        List<DepartmentDto> departments = Lists.newArrayList();

        while (true) {
            DepartmentDto departmentDto = departmentMap.get(departmentId);
            if (ObjectUtils.isEmpty(departmentDto)) {
                return departments;
            }
            departments.add(departmentDto);
            departmentId = departmentDto.getParentId();
        }
    }

    public DepartmentDto getDepartment(Integer bid, Long departmentId) {
        Map<Long, DepartmentDto> departmentMap = getDepartmentMap(bid);
        return departmentMap.get(departmentId);
    }
}

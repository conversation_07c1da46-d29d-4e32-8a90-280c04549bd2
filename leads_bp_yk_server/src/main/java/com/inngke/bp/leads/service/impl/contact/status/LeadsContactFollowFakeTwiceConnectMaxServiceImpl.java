package com.inngke.bp.leads.service.impl.contact.status;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.bp.leads.service.impl.contact.AbstractLeadsContactFollowService;
import org.springframework.stereotype.Service;

/**
 * 虚拟号 第二次拨打 接通 10s以上
 * 线索状态为已联系： 拨打客户电话，通话时长15秒，系统将线索状态由【已联系】修改为【已成功联系】
 * 线索处于“已成功联系”及之后的状态： 拨打客户电话，通话时长15秒
 *
 * <AUTHOR>
 * @date 2022/4/2 14:25
 */
@Service
public class LeadsContactFollowFakeTwiceConnectMaxServiceImpl extends AbstractLeadsContactFollowService {

    @Override
    protected String generateContactFollowContent(Leads leads, PrivateVoiceRecordDTO dto) {
        String content = "拨打客户电话，通话时长" + getTalkTimeText(dto.getVoiceTime());

        //若线索状态 需要变更则添加变更记录
        if (!LeadsStatusEnum.parse(leads.getStatus()).equals(getNewStatus(leads))) {
            return content + "，系统将线索状态由【已联系】修改为【已成功联系】";
        }
        return content;
    }

    @Override
    protected LeadsStatusEnum getNewStatus(Leads leads) {

        //若状态在 已成功联系下级 则返回已成功联系 否则不变更线索状态
        if (leads.getStatus() < LeadsStatusEnum.SUCCESS_CONTACT.getStatus()) {
            return LeadsStatusEnum.SUCCESS_CONTACT;
        }

        return LeadsStatusEnum.parse(leads.getStatus());
    }

    @Override
    public LeadsContactStatusEnum getStatusEnum() {
        return LeadsContactStatusEnum.FAKE_TWICE_CONNECT_MAX;
    }
}

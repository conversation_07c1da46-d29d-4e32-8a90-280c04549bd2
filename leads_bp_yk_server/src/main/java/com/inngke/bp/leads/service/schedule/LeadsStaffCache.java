package com.inngke.bp.leads.service.schedule;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.core.config.privatization.PrivatizationDb;
import com.inngke.common.service.LockService;
import com.inngke.common.service.impl.BaseBidDataCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/14 19:18
 */
@Component
public class LeadsStaffCache extends BaseBidDataCacheService<Map<Long, StaffDto>> {
    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LockService lockService;

    // @Scheduled(fixedRate = 13000)
    public void init() {
        loadData();
    }

    /**
     * 当前数据的名称，比如：部门
     *
     * @return 数据名称
     */
    @Override
    protected String getDataName() {
        return "员工";
    }

    /**
     * 加载数据
     *
     * @param privatizationDb 私有化数据库实例
     * @param lastUpdateTime  上次更新时间，首次进入为null
     * @return 更新了多少数据
     */
    @Override
    protected int loadData(PrivatizationDb privatizationDb, LocalDateTime lastUpdateTime) {
        // org staff 没有拿enable这个字段
        StaffListRequest exsitStaffRequest = new StaffListRequest();
        exsitStaffRequest.setEnable(1);
        List<StaffDto> exsitStaffList = staffClientForLeads.getStaffList(exsitStaffRequest);

        StaffListRequest notExsitStaffRequest = new StaffListRequest();
        notExsitStaffRequest.setEnable(0);
        List<StaffDto> noExsitStaffList = staffClientForLeads.getStaffList(notExsitStaffRequest);

        exsitStaffList.forEach(staff -> {
            Integer bid = staff.getBid();
            this.bidDataMap.computeIfAbsent(bid, beid -> Maps.newHashMap()).put(staff.getId(), staff);
        });

        noExsitStaffList.forEach(noExsitStaff -> {
            Integer bid = noExsitStaff.getBid();
            //无效
            getData(bid).remove(noExsitStaff.getId());
        });

        return exsitStaffList.size();
    }

    public String getStaffName(int bid, Long staffId) {
        if (staffId == null) {
            return null;
        }
        Map<Long, StaffDto> map = getData(bid);
        if (map == null) {
            return null;
        }
        StaffDto staffDto = map.get(staffId);
        if (staffDto == null) {
            return null;
        }
        return staffDto.getName();
    }
}

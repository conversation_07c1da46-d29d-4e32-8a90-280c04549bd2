package com.inngke.bp.leads.db.leads.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;
import com.inngke.common.ds.annotation.DS;

/**
 * <AUTHOR>
 * @since 2022/9/8
 **/
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsPreFollowConfigDao extends BaseMapper<LeadsPreFollowConfig> {
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 分配客服规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AllocationStaffRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 客服ID,即staffId
     */
    private Long preFollowStaffId;

    /**
     * 渠道ID列表
     */
    private String channelIds;

    /**
     * 区域ID列表
     */
    private String regionIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String BID = "bid";

    public static final String PRE_FOLLOW_STAFF_ID = "pre_follow_staff_id";

    public static final String CHANNEL_IDS = "channel_ids";

    public static final String REGION_IDS = "region_ids";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

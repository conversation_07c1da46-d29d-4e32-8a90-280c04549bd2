/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.mq.listener.ClientChangeListenerForLeads;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.service.AliCloudCallService;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 广告平台授权用户信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@RestController
@RequestMapping("/leadsTpInfo")
public class LeadsTpInfoController {

    @Autowired
    private JsonService jsonService;

    @Autowired
    private ClientChangeListenerForLeads clientTransferListenerOfLeads;

    @Autowired
    private AliCloudCallService aliCloudCallService;

    @GetMapping("/test")
    public void test() {
        String str = "{\"bid\":1213,\"sourceStaffId\":158300442852327443,\"targetStaffId\":156757368477057034,\"id\":161208075582177388,\"event\":2,\"business\":\"10\"}";
        ClientTransferMessage clientTransferMessage = jsonService.toObject(str, ClientTransferMessage.class);
        clientTransferListenerOfLeads.process(null, clientTransferMessage);

    }
}


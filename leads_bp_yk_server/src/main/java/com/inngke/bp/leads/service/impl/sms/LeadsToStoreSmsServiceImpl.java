package com.inngke.bp.leads.service.impl.sms;

import com.inngke.bp.leads.dto.request.LeadsSmsSendRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.enums.LeadsSmsTemplateEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.springframework.stereotype.Service;

/**
 * 到店短信
 *
 * <AUTHOR>
 * @date 2022/4/6 11:16
 */
@Service
public class LeadsToStoreSmsServiceImpl extends AbstractLeadsSmsService {
    @Override
    public LeadsSmsTemplateEnum getLeadsSmsTemplateEnum() {
        return LeadsSmsTemplateEnum.TO_STORE;
    }

    @Override
    protected String getSmsTemplateContent() {
        return "尊敬的{1}，我是{2}销售顾问{3}，您已预约{4}到店，门店地址{5}，如行程有变或有其他问题可随时与我联系{6}{7}";
    }

    @Override
    protected String getFollowContent(LeadsDto leads, LeadsSmsSendRequest request) {
        LeadsStatusEnum leadsStatusEnum = LeadsStatusEnum.parse(leads.getStatus());

        //如果线索状态大于到店
        if (leadsStatusEnum.getStatus() >= getNewStatusEnum().getStatus()) {
            return "给客户发送到店短信\n短信内容：" + getSmsContentInfo(request.getLeadsSmsVarList());
        }

        return "给客户发送到店短信，系统自动将线索状态由【" +
                leadsStatusEnum.getName() +
                "】变更为【"+LeadsStatusEnum.STORED.getName()+"】\n短信内容：" +
                getSmsContentInfo(request.getLeadsSmsVarList());
    }

    @Override
    protected LeadsStatusEnum getNewStatusEnum() {
        return LeadsStatusEnum.STORED;
    }

}

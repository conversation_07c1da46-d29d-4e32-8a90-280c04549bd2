package com.inngke.bp.leads.core.converter;

import com.inngke.bp.leads.db.leads.entity.LeadsConf;
import com.inngke.bp.leads.dto.response.LeadsConfDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.StringUtils;

import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/9 14:30 PM
 */
public class LeadsConfConverter {

    public static LeadsConfDto toLeadsConfDto(LeadsConf leadsConf) {
        LeadsConfDto leadsConfDto = new LeadsConfDto();

        leadsConfDto.setId(leadsConf.getId());
        leadsConfDto.setEnable(leadsConf.getEnable());
        leadsConfDto.setDistributeType(leadsConf.getDistributeType());
        leadsConfDto.setForwardEnable(leadsConf.getForwardEnable());
        leadsConfDto.setPushbackEnable(leadsConf.getPushbackEnable());
        leadsConfDto.setRepeatDistributionWay(leadsConf.getRepeatDistributionWay());
        leadsConfDto.setPreFollowEnable(leadsConf.getPreFollowEnable());
        leadsConfDto.setCreateTime(leadsConf.getCreateTime() != null ? leadsConf.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);
        leadsConfDto.setUpdateTime(leadsConf.getUpdateTime() != null ? leadsConf.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);
        leadsConfDto.setOpenRepeatRemove(leadsConf.getOpenRepeatRemove());
        leadsConfDto.setRepeatRemoveDay(leadsConf.getRepeatRemoveDay());
        leadsConfDto.setPushbackReason(leadsConf.getPushbackReason());
        leadsConfDto.setPushbackImage(leadsConf.getPushbackImage());
        leadsConfDto.setLeadsInvalidReason(leadsConf.getLeadsInvalidReason());
        leadsConfDto.setDisplayRepeat(leadsConf.getDisplayRepeat());
        leadsConfDto.setTextMessageNotify(leadsConf.getTextMessageNotify());
        List<Integer> leadsFollowNotifyList = new ArrayList<>();
        leadsConfDto.setLeadsFollowNotifyList(leadsFollowNotifyList);
        if (!StringUtils.isEmpty(leadsConf.getLeadsFollowNotify())) {
            String[] split = leadsConf.getLeadsFollowNotify().split(InngkeAppConst.COMMA_STR);
            Arrays.stream(split).forEach(item -> leadsFollowNotifyList.add(Integer.valueOf(item)));
        }
        return leadsConfDto;
    }
}

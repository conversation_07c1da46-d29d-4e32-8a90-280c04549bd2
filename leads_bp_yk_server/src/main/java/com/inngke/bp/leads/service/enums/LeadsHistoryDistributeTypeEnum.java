package com.inngke.bp.leads.service.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 17:20
 */
public enum LeadsHistoryDistributeTypeEnum {

    DISTRIBUTE_LEADS(1, "导购线索数据转交"),
    FOLLOW_LEADS(2, "客服线索数据转交")
    ;


    LeadsHistoryDistributeTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static LeadsHistoryDistributeTypeEnum parse(int code) {
        for (LeadsHistoryDistributeTypeEnum item : LeadsHistoryDistributeTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }


}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索转交撤回代办
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsTransferRollbackTodo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer bid;

    /**
     * 线索id
     */
    private Long leadsId;

    /**
     * 原始员工id
     */
    private Long sourceStaffId;

    /**
     * 目标员工id
     */
    private Long targetStaffId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public static final String ID = "id";

    public static final String LEADS_ID = "leads_id";

    public static final String SOURCE_STAFF_ID = "source_staff_id";

    public static final String CREATE_TIME = "create_time";

}

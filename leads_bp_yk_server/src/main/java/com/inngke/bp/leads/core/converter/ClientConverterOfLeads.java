package com.inngke.bp.leads.core.converter;

import com.inngke.bp.client.dto.request.client.AddClientFollowRequest;
import com.inngke.bp.client.request.ClientCreateRequest;
import com.inngke.bp.leads.dto.request.LeadsFollowCreateRequest;

import java.util.Objects;

/**
 * ClientConverterOfLeads
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/14 10:23
 */
public class ClientConverterOfLeads {

    private ClientConverterOfLeads() {

    }


    public static ClientCreateRequest leadsClientCreateRequestToClientCreateRequest(Integer bid, com.inngke.bp.leads.dto.request.ClientCreateRequest request) {
        ClientCreateRequest clientCreateRequest = new ClientCreateRequest();
        clientCreateRequest.setId(request.getId());
        clientCreateRequest.setBid(bid);
        clientCreateRequest.setClientName(request.getClientName());
        clientCreateRequest.setMobile(request.getMobile());
        clientCreateRequest.setWx(request.getWx());
        clientCreateRequest.setLevelId(request.getLevelId());
        clientCreateRequest.setStaffId(request.getStaffId());
        clientCreateRequest.setOperatorId(request.getOperatorId());
        clientCreateRequest.setProvinceId(request.getProvinceId());
        clientCreateRequest.setCityId(request.getCityId());
        clientCreateRequest.setAreaId(request.getAreaId());
        clientCreateRequest.setAddress(request.getAddress());
        clientCreateRequest.setRemark(request.getRemark());
        return clientCreateRequest;

    }

    public static AddClientFollowRequest toAddFollowRequest(LeadsFollowCreateRequest leadsFollow) {
        if (Objects.isNull(leadsFollow)){
            return null;
        }

        AddClientFollowRequest addClientFollowRequest = new AddClientFollowRequest();
        addClientFollowRequest.setContent(leadsFollow.getContent());
        addClientFollowRequest.setImageList(leadsFollow.getImages());
        addClientFollowRequest.setReminderTime(leadsFollow.getReminderTime());
        addClientFollowRequest.setPlanContent(leadsFollow.getPlanContent());
        return addClientFollowRequest;
    }
}

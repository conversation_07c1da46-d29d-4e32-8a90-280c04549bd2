package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.dto.request.LeadsCreateUpdateRequest;
import com.inngke.bp.organize.dto.request.staff.GetStaffByCustomerIdsRequest;
import com.inngke.bp.organize.dto.request.staff.GetStaffByLastIdQuery;
import com.inngke.bp.organize.dto.request.staff.GetStaffCountByDepIdsRequest;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDepartmentAgentSimpleDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.organize.service.StaffManageDepartmentService;
import com.inngke.bp.organize.service.StaffService;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.bp.user.dto.request.staff.StaffGetRequest;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.bp.user.service.UserStaffService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StaffClientForLeads {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private StaffService staffService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private StaffManageDepartmentService staffManageDepartmentService;

    @Autowired
    CustomerGetService customerGetService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private UserStaffService userStaffService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    public Map<Long,StaffDto> getByCustomerIds(Integer bid, List<Long> customerIds) {
        HashMap<Long, StaffDto> resultMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(customerIds)){
            return resultMap;
        }
        GetStaffByCustomerIdsRequest request=new GetStaffByCustomerIdsRequest();
        request.setBid(bid);
        request.setIds(customerIds);

        log.info("根据customerId获取员工请求：{}", jsonService.toJson(request));
        BaseResponse<List<StaffDto>> response = staffService.getStaffByCustomerIds(request);
        if (!BaseResponse.responseSuccess(response)) {
            log.warn("根据customerId获取员工异常：{}", jsonService.toJson(request));
            return Maps.newHashMap();
        }
        return response.getData().stream().collect(Collectors.toMap(StaffDto::getCustomerId, Function.identity(), (key1, key2) -> key1));
    }

    /**
     * 获取员工和经销商的关系数据
     *
     * @param request 请求类
     * @return 返回员工和经销商的关系数据
     */
    public List<StaffIdAndAgentIdDto> getStaffIdAndAgentIdDto(BaseIdsRequest request) {
        //获取到该员工属于哪个部门id,部门名称,经销商id,经销商名称
        BaseResponse<List<StaffIdAndAgentIdDto>> response = staffService.getStaffIdAndAgentIdDto(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 获取员工部门营销商数据
     *
     * @param request 通过员工编号请求 复数
     * @return 员工部门营销商数据
     */
    public List<StaffDepartmentAgentSimpleDto> getStaffDepartmentAgentSimple(BaseIdsRequest request) {
        BaseResponse<List<StaffDepartmentAgentSimpleDto>> response = staffService.getStaffDepartmentAgentSimpleDto(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 获取员工信息
     *
     * @param request 请求类
     * @return 返回员工信息
     */
    public List<StaffDto> getStaffList(StaffListRequest request) {
        BaseResponse<List<StaffDto>> response = staffService.getStaffList(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("查询员工数据失败:{}", response);
            return Lists.newArrayList();
        }
        return response.getData();
    }

    /**
     * 通过客户编号获取员工信息
     */
    public List<StaffDto> getStaffListByCustomerId(Integer bid, Long customerId) {
        // 通过staffId查询员工信息
        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(bid);
        staffListRequest.setCustomerId(customerId);
        return this.getStaffList(staffListRequest);
    }

    public Map<Long, Integer> getCountMapByDeptIds(BaseIdsRequest request) {
        GetStaffCountByDepIdsRequest getStaffCountByDepIdsRequest = new GetStaffCountByDepIdsRequest();
        getStaffCountByDepIdsRequest.setDepIds(Sets.newHashSet(request.getIds()));
        getStaffCountByDepIdsRequest.setBid(request.getBid());
        getStaffCountByDepIdsRequest.setStatus(StaffStatusEnum.OPENED.getCode());
        BaseResponse<Map<Long, Integer>> departmentStaffCount = staffService.getCountMapByDeptIds(getStaffCountByDepIdsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(departmentStaffCount) || CollectionUtils.isEmpty(departmentStaffCount.getData())) {
            return Maps.newHashMap();
        }
        return departmentStaffCount.getData();
    }

    public Map<Long, Integer> getCountMapByDeptIds(BaseIdsRequest request,List<Set<Long>> ids) {
        GetStaffCountByDepIdsRequest getStaffCountByDepIdsRequest = new GetStaffCountByDepIdsRequest();
        getStaffCountByDepIdsRequest.setDepIds(Sets.newHashSet(request.getIds()));
        getStaffCountByDepIdsRequest.setBid(request.getBid());
        getStaffCountByDepIdsRequest.setStatus(StaffStatusEnum.OPENED.getCode());
        getStaffCountByDepIdsRequest.setDepIdsCollection(ids);
        BaseResponse<Map<Long, Integer>> departmentStaffCount = staffService.getCountMapByDeptIds(getStaffCountByDepIdsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(departmentStaffCount) || CollectionUtils.isEmpty(departmentStaffCount.getData())) {
            return Maps.newHashMap();
        }
        return departmentStaffCount.getData();
    }

    public Map<Long, StaffDto> getStaffByIds(int bid, Set<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return new HashMap<>(0);
        }
        BaseIdsRequest request = new BaseIdsRequest();
        request.setIds(Lists.newArrayList(staffIds));
        request.setOperatorId(0L);
        request.setBid(bid);

        BaseResponse<List<StaffDto>> response = staffService.getStaffByIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return Maps.newHashMap();
        }

        return response.getData().stream().collect(Collectors.toMap(StaffDto::getId, Function.identity()));
    }

    public StaffDto getStaffById(Integer bid, Long id) {
        Map<Long, StaffDto> staffByIds = getStaffByIds(bid, Sets.newHashSet(id));
        return staffByIds.get(id);
    }

    public List<UserStaffDto> getStaffByMobileList(Integer bid, List<String> mobiles) {
        StaffGetRequest staffGetRequest = new StaffGetRequest();
        staffGetRequest.setBid(bid);
        staffGetRequest.setMobiles(mobiles);
        staffGetRequest.setPageSize(mobiles.size());
        BaseResponse<List<UserStaffDto>> response = userStaffService.getList(staffGetRequest);
        if (BaseResponse.responseErrorOrNullData(response)) {
            return Lists.newArrayList();
        }
        return response.getData();
    }

    public Set<Long> getStaffManageDepartmentIds(int bid,Long staffId) {
        BaseIdRequest request = new BaseIdRequest();
        request.setId(staffId);
        request.setOperatorId(0L);
        request.setBid(bid);

        BaseResponse<Set<Long>> response = staffManageDepartmentService.getManageDepartmentByStaffId(request);
        if (BaseResponse.responseErrorOrNullData(response)){
            return Sets.newHashSet();
        }

        return response.getData();
    }

    public List<UserStaffDto> getStaffByDeptIds(Integer bid, Set<Long> deptIds) {
        StaffGetRequest staffGetRequest = new StaffGetRequest();
        staffGetRequest.setBid(bid);
        staffGetRequest.setDeptIds(deptIds);
        staffGetRequest.setPageSize(9999);
        BaseResponse<List<UserStaffDto>> response = userStaffService.getList(staffGetRequest);
        if (BaseResponse.responseErrorOrNullData(response)) {
            return Lists.newArrayList();
        }
        return response.getData();
    }

    public List<StaffDto> getStaffByCustomerIds(GetStaffByCustomerIdsRequest request){
        BaseResponse<List<StaffDto>> staffByCustomerIdsResp = staffService.getStaffByCustomerIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(staffByCustomerIdsResp)){
            log.error("获取员工数据失败 {}", jsonService.toJson(request));
            return Lists.newArrayList();
        }
        return staffByCustomerIdsResp.getData();
    }

    public StaffDto getStaffByCid(Integer bid, Long id) {
        GetStaffByCustomerIdsRequest getStaffByCustomerIdsRequest = new GetStaffByCustomerIdsRequest();
        getStaffByCustomerIdsRequest.setBid(bid);
        getStaffByCustomerIdsRequest.setIds(Lists.newArrayList(id));
        List<StaffDto> staffDtoList = getStaffByCustomerIds(getStaffByCustomerIdsRequest);
        if (CollectionUtils.isEmpty(staffDtoList)) {
            return null;
        }
        staffDtoList = staffDtoList.stream().filter(staffDto -> staffDto.getStatus().equals(2)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(staffDtoList)) {
            return null;
        }
        return staffDtoList.get(0);
    }

    public List<StaffDto> getStaffByBidOfCache(Integer bid) {
        String str = stringRedisTemplate.opsForValue().get("organize_bp_yk" + InngkeAppConst.CLN_STR + "bid:" + bid);
        List<StaffDto> staffList = Optional.ofNullable(jsonService.toObjectList(str, StaffDto.class)).orElse(Lists.newArrayList());
        if (!CollectionUtils.isEmpty(staffList)) {
            return staffList;
        }

        GetStaffByLastIdQuery query = new GetStaffByLastIdQuery();
        query.setBid(bid);
        query.setLimit(1000);
        Long lastId = null;
        do {
            query.setLastId(lastId);
            BaseResponse<List<StaffDto>> response = staffService.getSimpleStaffByLastId(query);
            if (!BaseResponse.responseSuccessWithNonNullData(response)) {
                log.warn("获取员工数据失败！bid={}", bid);
                lastId = -1L;
                continue;
            }
            List<StaffDto> staffResponseData = response.getData();
            if (CollectionUtils.isEmpty(staffResponseData)) {
                lastId = -1L;
            } else {
                lastId = staffResponseData.get(staffResponseData.size() - 1).getId();
                staffList.addAll(staffResponseData);
            }
        } while (lastId >= 0L);
        stringRedisTemplate.opsForValue().set("organize_bp_yk" + InngkeAppConst.CLN_STR + "bid:" + bid, jsonService.toJson((Serializable) staffList), 1, TimeUnit.HOURS);

        return staffList;
    }
    public StaffDto getByCustomerId(Integer bid, Long cid) {
        return Optional.ofNullable(getByCustomerIds(bid, Lists.newArrayList(cid))).map(map -> map.get(cid)).orElse(null);
    }
    /**
     * 根据员工查询当前员工是否有效的存在
     * @param request
     * @return
     */
    public Integer isExitByStaffId(LeadsCreateUpdateRequest request) {
        if(StringUtils.isBlank(request.getStaffId())){
            return null;
        }
        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(request.getBid());
        staffListRequest.setId(Long.valueOf(request.getStaffId()));
        staffListRequest.setStatus(2);
        log.info("查询当前员工是否存在以及是否有效：request:{}", jsonService.toJson(request));
        BaseResponse<Integer> response = staffService.countStaffNum(staffListRequest);
        if (!BaseResponse.responseSuccess(response)) {
            log.warn("查询不到该员工：{}", jsonService.toJson(request));
            return null;
        }
        return response.getData() ;
    }

}

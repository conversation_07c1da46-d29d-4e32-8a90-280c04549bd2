package com.inngke.bp.leads.service.impl.tp;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.entity.LeadsTpPullCondition;
import com.inngke.bp.leads.db.leads.manager.LeadsTpPullConditionManager;
import com.inngke.bp.leads.dto.request.tp.SaveLeadsTpPullConditionRequest;
import com.inngke.bp.leads.dto.response.tp.LeadsTpPullConditionDto;
import com.inngke.bp.leads.service.tp.LeadsTpPullConditionService;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseListResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:05
 */
@Service
@DubboService(version = "1.0.0")
public class LeadsTpPullConditionServiceImpl implements LeadsTpPullConditionService {

    @Autowired
    private LeadsTpPullConditionManager leadsTpPullConditionManager;

    @Override
    public BaseResponse<List<LeadsTpPullConditionDto>> getLeadsTpPullConditionList(BaseBidOptRequest request) {
        List<LeadsTpPullCondition> list = leadsTpPullConditionManager.list(Wrappers.<LeadsTpPullCondition>query()
                .eq(LeadsTpPullCondition.BID, request.getBid())
        );

        return BaseResponse.success(list.stream().map(this::toDto).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<List<LeadsTpPullConditionDto>> saveLeadsTpPullConditionList(SaveLeadsTpPullConditionRequest request) {
        List<LeadsTpPullConditionDto> tpPullConditionDtoList = request.getTpPullConditionList();

        List<LeadsTpPullCondition> tpPullConditionList = tpPullConditionDtoList.stream().map(this::toEntity)
                .map(leadsTpPullCondition -> leadsTpPullCondition.setBid(request.getBid()))
                .collect(Collectors.toList());

        List<LeadsTpPullCondition> tpPullConditions = leadsTpPullConditionManager.saveOrUpdateBatch(request.getBid(), tpPullConditionList);

        return BaseResponse.success(tpPullConditions.stream().map(this::toDto).collect(Collectors.toList()));
    }

    private LeadsTpPullCondition toEntity(LeadsTpPullConditionDto leadsTpPullConditionDto){
        LeadsTpPullCondition leadsTpPullCondition = new LeadsTpPullCondition();
        leadsTpPullCondition.setType(leadsTpPullConditionDto.getType());
        leadsTpPullCondition.setFields(leadsTpPullConditionDto.getFields());
        leadsTpPullCondition.setEqual(leadsTpPullConditionDto.getEqual());
        leadsTpPullCondition.setValueList(StringUtils.join(leadsTpPullConditionDto.getValueList(),","));

        return leadsTpPullCondition;
    }

    private LeadsTpPullConditionDto toDto(LeadsTpPullCondition leadsTpPullCondition){
        LeadsTpPullConditionDto leadsTpPullConditionDto = new LeadsTpPullConditionDto();
        leadsTpPullConditionDto.setType(leadsTpPullCondition.getType());
        leadsTpPullConditionDto.setFields(leadsTpPullCondition.getFields());
        leadsTpPullConditionDto.setEqual(leadsTpPullCondition.getEqual());
        if (!StringUtils.isEmpty(leadsTpPullCondition.getValueList())){
            leadsTpPullConditionDto.setValueList(Lists.newArrayList(leadsTpPullCondition.getValueList().split(",")));
        }else {
            leadsTpPullConditionDto.setValueList(Lists.newArrayList());
        }
        return leadsTpPullConditionDto;

    }
}

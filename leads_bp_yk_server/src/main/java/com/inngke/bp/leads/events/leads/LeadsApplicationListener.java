package com.inngke.bp.leads.events.leads;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.core.utils.AsyncUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * LeadsApplicationListener
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/13 13:47
 */
@Component
@Slf4j
public class LeadsApplicationListener {

    @Autowired
    private LeadsEsService leadsEsService;

    @EventListener
    public void leadsCreateListener(LeadsCreateEvent leadsCreateEvent) {
        log.info("LeadsCreateEvent");
    }

    @EventListener
    public void leadsBindClientListener(LeadsBindClientEvent event) {
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(event.getBid());
            leadsUpdateRequest.setIds(Lists.newArrayList(event.getLeadsId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
    }
}

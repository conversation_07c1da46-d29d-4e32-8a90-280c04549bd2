package com.inngke.bp.leads.service.impl.contact;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsContactFollowService;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/4/1 17:22
 */
@Component
public class LeadsContactFollowFactoryService {

    @Autowired
    private List<LeadsContactFollowService> leadsContactFollowServiceList;

    /**
     * 获取各种状态的处理对象
     *
     * @param leadsContactStatusEnum 状态
     * @return
     */
    public LeadsContactFollowService getInstance(LeadsContactStatusEnum leadsContactStatusEnum) {

        for (LeadsContactFollowService leadsContactFollowService : leadsContactFollowServiceList) {
            if (leadsContactStatusEnum.equals(leadsContactFollowService.getStatusEnum())){
                return leadsContactFollowService;
            }
        }

        throw new InngkeServiceException("未实现回调类型{" + leadsContactStatusEnum.getCode() + "}");
    }

    public LeadsContactFollowService getInstance(Leads leads, PrivateVoiceRecordDTO dto) {
        //是否为首次通话
        boolean isFirstTime = leads.getStatus() < LeadsStatusEnum.CONTACTED.getStatus();
        //是否为虚拟通话
        boolean isFakeMobile = !ObjectUtils.isEmpty(dto);
        //是否已打通
        boolean isConnect = Optional.ofNullable(dto.getVoiceTime()).orElse(0) > 0;
        //通话时长是否>15秒
        boolean isGreater = Optional.ofNullable(dto.getVoiceTime()).orElse(0) > 15;

        LeadsContactStatusEnum leadsContactStatusEnum =
                LeadsContactStatusEnum.parse(isFakeMobile, isFirstTime, isConnect, isGreater);
        return getInstance(leadsContactStatusEnum);
    }
}

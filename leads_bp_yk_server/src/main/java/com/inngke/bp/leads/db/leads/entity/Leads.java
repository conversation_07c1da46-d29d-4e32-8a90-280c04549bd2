/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inngke.bp.leads.dto.LeadsSnapshotDto;
import com.inngke.bp.leads.dto.request.LeadsAttachment;
import com.inngke.bp.leads.dto.response.LeadsFollowStatusDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.utils.DateTimeUtils;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Leads implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id
     */
    @TableField()
    private Long customerId;

    /**
     * 关联用户的ID， 即customer.uid
     */
    private Long customerUid;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 客户ID
     */
    private Long clientId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 微信号
     */
    private String weChat;

    /**
     * 线索状态： -4=删除 -3=无效线索 -2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交
     *
     * @see LeadsStatusEnum
     */
    private Integer status;

    /**
     * 是否在24小时内联系 0否 1是
     */
    @TableField(value = "contact_in_24")
    private Integer contactIn24;

    /**
     * 省份ID，0表示未匹配
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    private Integer channel;

    /**
     * 线索来源类型
     */
    private Integer channelType;

    /**
     * 线索来源渠道
     */
    private Integer channelSource;

    /**
     * 下单账号
     */
    private String orderAccount;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品链接
     */
    private String goodsLink;

    /**
     * 订购商品数量
     */
    private Integer goodsNum;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款金额
     */
    private BigDecimal payAmount;

    /**
     * 订单留言
     */
    private String orderMessage;

    /**
     * 其它备注
     */
    private String remark;

    /**
     * 外部平台线索ID
     */
    private String tpLeadsId;

    /**
     * 广告活动名称
     */
    private String promotionName;

    /**
     * 报名时间
     */
    private LocalDateTime registryTime;

    /**
     * 需求时间：0=未指定 1=一个月内 2=三个月内 3=六个月内 4=一年内 5=一年以上
     */
    private Integer expectIn;

    /**
     * 装修风格，详见枚举
     */
    private Integer style;

    /**
     * 批次ID，0表示非批次导入
     */
    private Long batchId;

    /**
     * 分配的经销商ID，即shop_agent_id
     */
    // private Long distributeAgentId;

    /**
     * 分配给哪个员工，即staffId
     */
    private Long distributeStaffId;

    /**
     * 分配时间
     */
    private LocalDateTime distributeTime;

    /**
     * 分配线索给客服的时间
     */
    private LocalDateTime distributeFollowTime;

    /**
     * 退回时间
     */
    private LocalDateTime pushBackTime;

    /**
     * 异常原因
     */
    private String errorMsg;

    /**
     * 最近一条待跟进记录ID
     */
    private Long lastFollowId;

    /**
     * 额外数据
     */
    private String extData;

    /**
     * 标签
     */
    private String tags;

    /**
     * 企业标签
     */
    private String enterpriseTags;

    /**
     * 外部标签
     */
    private String externalTags;

    /**
     * 是否完整显示手机号 0：否 1：是
     */
    private Integer showPhone;

    /**
     * 被回收的线索ID
     */
    private Long recoveryFrom;

    /**
     * 首次联系时间
     */
    private LocalDateTime firstContactTime;

    /**
     * 最后联系时间
     */
    private LocalDateTime lastContactTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 关联客户时间
     */
    private LocalDateTime relationClientTime;


    /**
     * 更新时间
     */
    private LocalDateTime lastFollowTime;

    /**
     * 客服id
     *
     * @demo 3030
     */
    private Long preFollowStaffId;

    /**
     * 客服接待时线索状态
     *
     * @demo 1
     */
    private Integer preFollowStatus;

    /**
     * 线索类型 1:订单类 2:信息类
     *
     * @demo 1
     */
    private Integer type;

    /**
     * 创建人StaffID
     *
     * @demo 110
     */
    private Long createStaffId;

    /**
     * 上报员工id
     *
     * @demo 1412
     */
    private Long reportStaffId;

    /**
     * 等级 A B C D
     */
    @Deprecated
    private String level;

    /**
     * 取“客户管理-客户设置-客户等级设置”内设置的等级
     */
    private Integer levelId;

    /**
     * 退回员工Id
     */
    private Long pushBackStaffId;

    /**
     * 已经被回收的线索Id
     */
    private String recoveryFromIds;

    /**
     * @see LeadsFollowStatusDto
     * 跟进状态
     */
    private String followStatuses;

    /**
     * @see LeadsFollowStatusDto
     * 客服跟进状态
     */
    private String kfFollowStatuses;

    /**
     * 需求产品
     */
    private String demandProduct;

    /**
     * 产品ids
     *
     * @demo [1, 2, 3]
     */
    private String productIds;

    /**
     * 平台ID
     */
    private String tpId;

    /**
     * 附件列表
     * @see LeadsAttachment
     */
    private String attachmentList;

    /**
     * 客户状态
     */
    private Integer clientStatus;

    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String CUSTOMER_ID = "customer_id";

    public static final String CUSTOMER_UID = "customer_uid";

    public static final String CHANNEL_ID = "channel_id";

    public static final String NAME = "name";

    public static final String MOBILE = "mobile";

    public static final String WE_CHAT = "we_chat";

    public static final String STATUS = "status";

    public static final String CONTACT_IN_24 = "contact_in_24";

    public static final String PROVINCE_ID = "province_id";

    public static final String PROVINCE_NAME = "province_name";

    public static final String CITY_ID = "city_id";

    public static final String CITY_NAME = "city_name";

    public static final String AREA_ID = "area_id";

    public static final String AREA_NAME = "area_name";

    public static final String ADDRESS = "address";

    public static final String CHANNEL = "channel";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String CHANNEL_SOURCE = "channel_source";

    public static final String ORDER_ACCOUNT = "order_account";

    public static final String ORDER_SN = "order_sn";

    public static final String GOODS_NAME = "goods_name";

    public static final String GOODS_LINK = "goods_link";

    public static final String GOODS_NUM = "goods_num";

    public static final String PAY_TIME = "pay_time";

    public static final String PAY_AMOUNT = "pay_amount";

    public static final String ORDER_MESSAGE = "order_message";

    public static final String REMARK = "remark";

    public static final String TP_LEADS_ID = "tp_leads_id";

    public static final String PROMOTION_NAME = "promotion_name";

    public static final String REGISTRY_TIME = "registry_time";

    public static final String EXPECT_IN = "expect_in";

    public static final String STYLE = "style";

    public static final String BATCH_ID = "batch_id";

     public static final String DISTRIBUTE_AGENT_ID = "distribute_agent_id";

    public static final String DISTRIBUTE_STAFF_ID = "distribute_staff_id";

    public static final String DISTRIBUTE_TIME = "distribute_time";

    public static final String DISTRIBUTE_FOLLOW_TIME = "distribute_follow_time";

    public static final String PUSH_BACK_TIME = "push_back_time";

    public static final String ERROR_MSG = "error_msg";

    public static final String LAST_FOLLOW_ID = "last_follow_id";

    public static final String EXT_DATA = "ext_data";

    public static final String TAGS = "tags";

    public static final String ENTERPRISE_TAGS = "enterprise_tags";

    public static final String EXTERNAL_TAGS = "externalTags";

    public static final String SHOW_PHONE = "show_phone";

    public static final String RECOVERY_FROM = "recovery_from";

    public static final String FIRST_CONTACT_TIME = "first_contact_time";

    public static final String LAST_CONTACT_TIME = "last_contact_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String LAST_FOLLOW_TIME = "last_follow_time";

    public static final String PRE_FOLLOW_STAFF_ID = "pre_follow_staff_id";

    public static final String PRE_FOLLOW_STATUS = "pre_follow_status";

    public static final String TYPE = "type";

    public static final String CREATED_STAFF_ID = "create_staff_id";

    public static final String REPORT_STAFF_ID = "report_staff_id";
    public static final String LEVEL = "level";

    public static final String LEVEL_ID = "level_id";

    public static final String PUSH_BACK_STAFF_ID = "push_back_staff_id";

    public static final String RECOVERY_FROM_IDS = "recovery_from_ids";

    public static final String FOLLOW_STATUSES = "follow_statuses";

    public static final String KF_FOLLOW_STATUSES = "kf_follow_statuses";

    public static final String DEMAND_PRODUCT = "demand_product";

    public static final String CLIENT_ID = "client_id";

    public static final String RELATION_CLIENT_TIME = "relation_client_time";

    public static final String TP_ID = "tp_id";

    public static final String ATTACHMENT_LIST = "attachment_list";

    public static final String CLIENT_STATUS = "client_status";

    public static final String PRODUCT_IDS = "product_ids";


    public LeadsSnapshotDto converterLeadsSnapshot() {
        LeadsSnapshotDto leadsSnapshotDto = new LeadsSnapshotDto();
        leadsSnapshotDto.setId(this.getId());
        leadsSnapshotDto.setBid(this.getBid());
        leadsSnapshotDto.setLastFollowTime(DateTimeUtils.getMilli(this.getLastFollowTime()));
        leadsSnapshotDto.setLastFollowId(this.getLastFollowId());
        leadsSnapshotDto.setDistributeStaffId(this.getDistributeStaffId());
        leadsSnapshotDto.setDistributeTime(DateTimeUtils.getMilli(this.getDistributeTime()));
        leadsSnapshotDto.setDistributeFollowTime(DateTimeUtils.getMilli(this.getDistributeFollowTime()));
        leadsSnapshotDto.setPreFollowStaffId(this.getPreFollowStaffId());
        return leadsSnapshotDto;

    }
}

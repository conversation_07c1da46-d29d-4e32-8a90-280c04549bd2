/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.dto.request.LeadsFollowTimeByDepartmentRequest;
import com.inngke.bp.leads.dto.request.LeadsFollowTimeByStaffRequest;
import com.inngke.bp.leads.dto.request.LeadsOrganizationChannelRequest;
import com.inngke.bp.leads.dto.request.LeadsOrganizationPreFollowRequest;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.dto.response.LeadsOrganizationPreFollowDto;
import com.inngke.bp.leads.service.LeadsFollowTimeService;
import com.inngke.bp.leads.service.schedule.LeadsFollowTimeSchedule;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.BidUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 线索
 * @section 线索-报表
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@RestController
@RequestMapping("/api/leads-follow-time")
public class LeadsFollowTimeController {
    @Autowired
    private LeadsFollowTimeService leadsFollowTimeService;

    @Autowired
    private LeadsFollowTimeSchedule leadsFollowTimeSchedule;

    @PostMapping("/allSync")
    public BaseResponse<Void> allSync(@RequestHeader int bid){
        AsyncUtils.runAsync(() -> {
            BidUtils.setBid(bid);
            leadsFollowTimeService.allSync(bid);
        });
        return BaseResponse.success();
    }

    @PostMapping("/updateLeadsFollowTime")
    public BaseResponse<Void> updateLeadsFollowTime(@RequestHeader int bid){
        AsyncUtils.runAsync(() -> {
            BidUtils.setBid(bid);
            leadsFollowTimeSchedule.updateLeadsFollowTime();
        });
        return BaseResponse.success();
    }

    /**
     *根据导购查询列表
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query     跟进请求
     * @return 线索根据报表
    */
    @GetMapping("/listByStaff")
    public BaseResponse<BasePaginationResponse<LeadsFollowTimeByStaffResponse>> listByStaff(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsFollowTimeByStaffRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ){
        query.setOperatorId(jwtPayload.getCid());
        query.setBid(bid);
        query.setSId(jwtPayload.getSid());
        return leadsFollowTimeService.listByStaff(query);
    }

    /**
     * 根据导购查询统计指标明细
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query     跟进请求
     * @return 线索根据报表
     */
    @GetMapping("/listByStaffDetail")
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByStaffDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsFollowTimeByStaffRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ){
        query.setOperatorId(jwtPayload.getCid());
        query.setBid(bid);
        query.setSId(jwtPayload.getSid());
        return leadsFollowTimeService.listByStaffDetail(query);
    }

    /**
     *根据部门查询列表
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query     跟进请求
     * @return 线索根据报表
     */
    @GetMapping("/listByDepartment")
    public BaseResponse<List<LeadsFollowTimeByStaffResponse>> listByDepartment(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsFollowTimeByDepartmentRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ){
        query.setOperatorId(jwtPayload.getCid());
        query.setBid(bid);
        query.setsId(jwtPayload.getSid());
        return leadsFollowTimeService.listByDepartment(query);
    }

    /**
     * 根据部门查询统计指标明细
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      跟进请求
     * @return 线索根据报表
     */
    @GetMapping("/listByDepartmentDetail")
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByDepartmentDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsFollowTimeByDepartmentRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        query.setOperatorId(jwtPayload.getCid());
        query.setBid(bid);
        query.setsId(jwtPayload.getSid());
        return leadsFollowTimeService.listByDepartmentDetail(query);
    }

    /**
     * 客服报表
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      跟进请求
     * @param jwtPayload jwt
     * @return
     */
    @GetMapping("/listByPreFollow")
    public BaseResponse<List<LeadsOrganizationPreFollowDto>> listByPreFollow(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsOrganizationPreFollowRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        query.setOperatorId(operatorId);
        query.setBid(bid);
        query.setSid(jwtPayload.getSid());
        return leadsFollowTimeService.listByPreFollow(query);
    }

    /**
     * 客服报表统计指标明细
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      跟进请求
     * @param jwtPayload jwt
     * @return
     */
    @GetMapping("/listByPreFollowDetail")
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByPreFollowDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsOrganizationPreFollowRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        query.setOperatorId(operatorId);
        query.setBid(bid);
        query.setSid(jwtPayload.getSid());
        return leadsFollowTimeService.listByPreFollowDetail(query);
    }

    /**
     * 渠道报表统计指标明细
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      跟进请求
     * @param jwtPayload jwt
     * @return
     */
    @GetMapping("/listByChannelDetail")
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByChannelDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @Validated LeadsOrganizationChannelRequest query,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        query.setOperatorId(operatorId);
        query.setBid(bid);
        query.setSid(jwtPayload.getSid());
        return leadsFollowTimeService.listByChannelDetail(query);
    }


    /**
     * 部门导出数据
     */
    @GetMapping("/exportDepartment")
    public BaseResponse<List<LeadsFollowTimeByStaffResponse>> exportDepartment(@RequestHeader int bid,
                                                                               @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
                                                                               LeadsFollowTimeByDepartmentRequest query,
                                                                               @RequestAttribute JwtPayload jwtPayload){
        query.setOperatorId(operatorId);
        query.setBid(bid);
        query.setsId(jwtPayload.getSid());
        return leadsFollowTimeService.exportDepartmentV2(query);
    }

}


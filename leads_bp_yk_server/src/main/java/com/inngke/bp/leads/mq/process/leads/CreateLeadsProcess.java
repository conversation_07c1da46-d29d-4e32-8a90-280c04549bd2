package com.inngke.bp.leads.mq.process.leads;

import com.inngke.bp.leads.dto.request.LeadsAddRequest;
import com.inngke.bp.leads.mq.message.leads.LeadsChangeMessage;
import com.inngke.bp.leads.mq.process.LeadsProcess;
import com.inngke.bp.leads.service.LeadsEsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CreateLeadsProcess implements LeadsProcess {
    @Autowired
    private LeadsEsService leadsEsService;

    @Override
    public int type() {
        return 1;
    }

    @Override
    public void process(LeadsChangeMessage message) {
        List<Long> leadsIds = message.getLeadsId();
        LeadsAddRequest leadsAddRequest = new LeadsAddRequest();
        leadsAddRequest.setBid(message.getBid());
        leadsAddRequest.setIds(leadsIds);
        leadsEsService.createLeadsDocs(leadsAddRequest);
    }
}

package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsCallLog  implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer bid;

    /**
     * 通话类型：1=开启隐号后通话；2=未开启隐号通话
     */
    private Integer type;

    /**
     * 线索id
     */
    private Long leadsId;

    /**
     * 导购-客户隐号关系绑定唯一值，即reach_i[_yk.private_bind.inner_id
     */
    private Long voiceId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String TYPE = "type";

    public static final String LEADS_ID = "leads_id";

    public static final String VOICE_ID = "voice_id";

    public static final String CREATE_TIME = "create_time";
}

package com.inngke.bp.leads.core.converter;

import com.inngke.bp.organize.dto.response.AgentDto;
import com.inngke.bp.user.dto.response.AgentIdAndNameDto;

/**
 * <AUTHOR>
 * @since 2022/8/10 17:48
 **/
public class AgentDtoConverter {

    public static AgentIdAndNameDto toAgentIdAndNameDto(AgentDto agentDto) {
        AgentIdAndNameDto agentIdAndNameDto = new AgentIdAndNameDto();
        agentIdAndNameDto.setName(agentDto.getName());
        agentIdAndNameDto.setId(agentDto.getId());
        return agentIdAndNameDto;
    }

}

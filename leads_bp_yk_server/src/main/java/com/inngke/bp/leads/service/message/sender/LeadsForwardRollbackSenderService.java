package com.inngke.bp.leads.service.message.sender;

import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

@Component
public class LeadsForwardRollbackSenderService extends MessageSenderServiceAbs {

    @Resource
    private StaffClientForLeads staffClientForLeads;
    @Resource
    private CustomerGetServiceClientForLeads customerGetServiceClientForLeads;

    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.LEADS_FORWARD_ROLLBACK;
    }

    @Override
    public void init(MessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(MessageContext ctx) {
        StaffDto staff = staffClientForLeads.getStaffById(ctx.getBid(), ctx.getTargetSid());

        if (Objects.nonNull(staff) && StaffStatusEnum.OPENED.equals(StaffStatusEnum.parse(staff.getStatus()))){
            ctx.setTargetQyUserId(staff.getQyUserId());
        }

        CustomerDto customer = customerGetServiceClientForLeads.getCustomerById(ctx.getBid(), staff.getCustomerId());
        if (Objects.nonNull(customer) && Integer.valueOf(1).equals(customer.getWxPubSubscribe())){
            ctx.setTargetWxPubOpenId(customer.getWxPubOpenId());
        }

        Leads leads = ctx.getLeads();

        String mobile = StringUtils.isNotBlank(leads.getMobile()) ?
                leads.getMobile().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : InngkeAppConst.MIDDLE_LINE_STR;

        return getTemplateRequestBuilder(ctx)
                .setVar("clientName", leads.getName())
                .setVar("mobile", mobile)
                .setVar("followType", "线索回收")
                .setVar("followContent", "24h未跟进，线索已被回收")
                .setMpPagePath(getMessageType().getPath())
                .build();
    }
}

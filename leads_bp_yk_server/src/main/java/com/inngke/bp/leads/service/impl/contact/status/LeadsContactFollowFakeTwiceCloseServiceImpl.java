package com.inngke.bp.leads.service.impl.contact.status;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.bp.leads.service.impl.contact.AbstractLeadsContactFollowService;
import org.springframework.stereotype.Service;

/**
 * 虚拟号 第二次拨打 未接通
 * 拨打客户电话，未接通
 *
 * <AUTHOR>
 * @date 2022/4/2 16:43
 */
@Service
public class LeadsContactFollowFakeTwiceCloseServiceImpl extends AbstractLeadsContactFollowService {
    @Override
    public LeadsContactStatusEnum getStatusEnum() {
        return LeadsContactStatusEnum.FAKE_TWICE_CLOSE;
    }

    @Override
    protected String generateContactFollowContent(Leads leads, PrivateVoiceRecordDTO dto) {
        return "拨打客户电话，未接通";
    }
}

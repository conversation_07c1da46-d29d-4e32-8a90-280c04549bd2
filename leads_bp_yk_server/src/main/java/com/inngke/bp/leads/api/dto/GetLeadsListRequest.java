package com.inngke.bp.leads.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/3/3 9:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetLeadsListRequest extends AiCustomerServiceBaseRequest {

    /**
     * 否	int	分页 默认1
     */
    private Integer page;

    /**
     * 否	int	每页返回数 默认20
     */
    @JsonProperty("pagesize")
    private Integer pageSize;

    /**
     * 否	string	开始时间 比如2021-12-27 14:57:58
     */
    @JsonProperty("start_time")
    private String startTime;

    /**
     * 否	string	结束时间 比如2021-12-27 14:57:58
     */
    @JsonProperty("end_time")
    private String endTime;

}

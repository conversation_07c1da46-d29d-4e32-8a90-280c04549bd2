package com.inngke.bp.leads.notify.context;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.ApplicationContextGetBeanHelper;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.LeadsExtDataDto;
import com.inngke.bp.leads.service.enums.LeadsNotifyMessageEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.context.NotifyMessageContext;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * LeadsOrderRefundMesageContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/22 18:07
 */
public class LeadsOrderRefundMessageContext implements NotifyMessageContext {
    private int bid;

    private long leadsId;

    private Optional<Leads> leads;

    private long targetStaffId;

    private Map<String, String> pathParams = Maps.newHashMap();

    private String qyUserId;

    private String wxPubOpenId;

    private LeadsNotifyMessageEnum notifyEnum = LeadsNotifyMessageEnum.LEADS_ORDER_REFUND_NOTIFY;


    public String getName() {
        return leads.map(Leads::getName).orElse("");
    }

    public String getMobile() {
        return leads.map(Leads::getMobile).orElse("");
    }

    public String getOrderNo() {
        return leads.map(Leads::getOrderSn).orElse("");
    }

    public String getGoodsName() {
        return leads.map(Leads::getGoodsName).orElse("");
    }

    public BigDecimal getPrice() {
        return leads.map(Leads::getPayAmount).orElse(BigDecimal.ZERO);
    }

    public String getRefundReason() {
        return leads.map(Leads::getExtData).map(str -> JsonUtil.jsonToObject(str, LeadsExtDataDto.class)).map(LeadsExtDataDto::getRefundRemark).orElse("");
    }

    public String getPayTime() {
        return leads.map(Leads::getPayTime).map(payTime -> DateTimeUtils.format(payTime, DateTimeUtils.YYYY_MM_DD_HH_MM_SS)).orElse("暂无信息");
    }


    @Override
    public int getBid() {
        return bid;
    }


    @Override
    public String getMsgTypeCode() {
        return notifyEnum.getTemplateCode();
    }

    @Override
    public String getPath() {
        return notifyEnum.getPath();
    }

    @Override
    public Long getTargetStaffId() {
        return targetStaffId;
    }

    @Override
    public Map<String, String> getPathParams() {
        return pathParams;
    }

    @Override
    public String getQyUserId() {
        return qyUserId;
    }

    @Override
    public String getWxPubOpenId() {
        return wxPubOpenId;
    }

    @Override
    public TemplateMessageContentBuilder<? extends NotifyMessageContext> getMessageBuilder() {
        return null;
    }

    public static LeadsOrderRefundMessageContext init(int bid, long leadsId) {
        LeadsManager leadsManager = ApplicationContextGetBeanHelper.getBean(LeadsManager.class);
        Leads leads = leadsManager.getById(bid, leadsId);
        LeadsOrderRefundMessageContext context = new LeadsOrderRefundMessageContext();
        if (Objects.nonNull(leads)) {
            context.bid = leads.getBid();
            context.leadsId = leadsId;
            context.targetStaffId = leads.getDistributeStaffId();
            context.leads = Optional.ofNullable(leads);
            StaffClientForLeads staffClient = ApplicationContextGetBeanHelper.getBean(StaffClientForLeads.class);
            StaffDto staff = staffClient.getStaffById(bid, context.targetStaffId);
            if (Objects.nonNull(staff)) {
                if (StringUtils.isNotBlank(staff.getQyUserId())) {
                    context.qyUserId = staff.getQyUserId();
                }

                CustomerGetServiceClientForLeads customerClient = ApplicationContextGetBeanHelper.getBean(CustomerGetServiceClientForLeads.class);
                CustomerDto customer = customerClient.getCustomerById(bid, staff.getCustomerId());
                if (Objects.nonNull(customer)) {
                    context.wxPubOpenId = customer.getWxPubOpenId();
                }
            }
            context.pathParams.put("id", String.valueOf(leadsId));
        }
        return context;
    }
}

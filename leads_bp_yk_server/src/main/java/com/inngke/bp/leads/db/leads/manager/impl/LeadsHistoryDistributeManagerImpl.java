/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsHistoryDistribute;
import com.inngke.bp.leads.db.leads.dao.LeadsHistoryDistributeDao;
import com.inngke.bp.leads.db.leads.manager.LeadsHistoryDistributeManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsHistoryDistributeManagerImpl extends ServiceImpl<LeadsHistoryDistributeDao, LeadsHistoryDistribute> implements LeadsHistoryDistributeManager {

}

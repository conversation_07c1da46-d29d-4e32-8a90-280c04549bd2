package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptPageRequest;
import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class LeadsOrganizationPreFollowRequest extends BaseBidOptPageRequest {

    /**
     * 员工Id
     */
    private Long staffId;


    /**
     * 1：总客户数、13：已跟进数、14：待跟进数、15：下发数、16：待下发数
     */
    private Integer type;

    /**
     * 分配时间-起始，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-10 10:23:45
     */
    private String distributeTimeStart;

    /**
     * 分配时间-结束，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-11 10:23:45
     */
    private String distributeTimeEnd;


    /**
     * 创建时间-起始，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-10 10:23:45
     */
    private String createTimeStart;

    /**
     * 创建时间-结束，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-11 10:23:45
     */
    private String createTimeEnd;


    private Long sid;

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }


    public String getDistributeTimeStart() {
        return distributeTimeStart;
    }

    public void setDistributeTimeStart(String distributeTimeStart) {
        this.distributeTimeStart = distributeTimeStart;
    }

    public String getDistributeTimeEnd() {
        return distributeTimeEnd;
    }

    public void setDistributeTimeEnd(String distributeTimeEnd) {
        this.distributeTimeEnd = distributeTimeEnd;
    }

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }
}

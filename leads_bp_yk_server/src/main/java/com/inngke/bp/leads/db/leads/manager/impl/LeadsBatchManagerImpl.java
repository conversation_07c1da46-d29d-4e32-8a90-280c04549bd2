/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsBatch;
import com.inngke.bp.leads.db.leads.dao.LeadsBatchDao;
import com.inngke.bp.leads.db.leads.manager.LeadsBatchManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsBatchManagerImpl extends ServiceImpl<LeadsBatchDao, LeadsBatch> implements LeadsBatchManager {

}

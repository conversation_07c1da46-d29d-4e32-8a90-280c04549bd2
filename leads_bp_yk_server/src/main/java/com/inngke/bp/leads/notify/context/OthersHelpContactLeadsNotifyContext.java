package com.inngke.bp.leads.notify.context;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.ApplicationContextGetBeanHelper;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.notify.builder.OthersHelpContactLeadsNotifyBuilder;
import com.inngke.bp.leads.service.enums.LeadsNotifyMessageEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.context.NotifyMessageContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * OthersHelpContactLeadsNotifyContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/11 14:15
 */
public class OthersHelpContactLeadsNotifyContext implements NotifyMessageContext {

    private int bid;

    private final LeadsNotifyMessageEnum notifyEnum = LeadsNotifyMessageEnum.OTHERS_HELP_CONTACT_LEADS;

    private Long targetStaffId;

    private String qyWxUserId;

    private String wxPubOpenId;

    private Map<String, String> pathParams;

    private String name;

    private String mobile;

    private String othersName;


    @Override
    public int getBid() {
        return this.bid;
    }

    @Override
    public String getMsgTypeCode() {
        return notifyEnum.getTemplateCode();
    }

    @Override
    public String getPath() {
        return notifyEnum.getPath();
    }

    @Override
    public Long getTargetStaffId() {
        return this.targetStaffId;
    }

    @Override
    public Map<String, String> getPathParams() {
        return this.pathParams;
    }

    @Override
    public String getQyUserId() {
        return this.qyWxUserId;
    }

    @Override
    public String getWxPubOpenId() {
        return this.wxPubOpenId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return StringUtils.isNotBlank(this.mobile) ? this.mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : "-";
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOthersName() {
        return othersName;
    }

    public void setOthersName(String othersName) {
        this.othersName = othersName;
    }

    public static OthersHelpContactLeadsNotifyContext init(int bid, long leadsId, Long othersStaffId) {
        LeadsManager leadsManager = ApplicationContextGetBeanHelper.getBean(LeadsManager.class);
        Leads leads = leadsManager.getById(bid, leadsId);

        OthersHelpContactLeadsNotifyContext context = new OthersHelpContactLeadsNotifyContext();
        if (Objects.nonNull(leads)) {
            context.bid = bid;
            context.name = leads.getName();
            context.mobile = leads.getMobile();
            context.targetStaffId = leads.getDistributeStaffId();
        }
        StaffClientForLeads staffClient = ApplicationContextGetBeanHelper.getBean(StaffClientForLeads.class);
        StaffDto otherStaff = staffClient.getStaffById(bid, othersStaffId);
        StaffDto targetStaff = staffClient.getStaffById(bid, leads.getDistributeStaffId());
        if (Objects.nonNull(targetStaff)) {
            if (StringUtils.isNotBlank(targetStaff.getQyUserId())) {
                context.qyWxUserId = targetStaff.getQyUserId();
            }
            context.othersName = otherStaff.getName();

            CustomerGetServiceClientForLeads customerClient = ApplicationContextGetBeanHelper.getBean(CustomerGetServiceClientForLeads.class);
            CustomerDto customer = customerClient.getCustomerById(bid, targetStaff.getCustomerId());
            if (Objects.nonNull(customer)) {
                context.wxPubOpenId = customer.getWxPubOpenId();
            }
        }
        context.pathParams = buildPathParams(context, leads);
        return context;
    }

    @Override
    public TemplateMessageContentBuilder<? extends NotifyMessageContext> getMessageBuilder() {
        return ApplicationContextGetBeanHelper.getBean(OthersHelpContactLeadsNotifyBuilder.class);
    }


    private static Map<String, String> buildPathParams(OthersHelpContactLeadsNotifyContext context, Leads leads) {
        Map<String, String> params = Maps.newHashMap();
        if (Objects.isNull(leads)) {
            return params;
        }
        params.put("staffId", String.valueOf(leads.getDistributeStaffId()));
        params.put("mes_type", StringUtils.isNotBlank(context.getQyUserId()) ? "qy_wx" : "wx_pub");
        params.put("id", String.valueOf(leads.getId()));
        return params;
    }
}

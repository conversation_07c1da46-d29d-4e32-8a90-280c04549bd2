package com.inngke.bp.leads.events.leads;

import org.springframework.context.ApplicationEvent;

/**
 * LeadsEvent
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/13 10:40
 */
public class LeadsBindClientEvent extends ApplicationEvent {

    private Integer bid;

    private Long leadsId;

    private Long clientId;

    public LeadsBindClientEvent(Object source, Integer bid, Long leadsId, Long clientId) {
        super(source);
        this.bid = bid;
        this.leadsId = leadsId;
        this.clientId = clientId;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeForwardConf;
import com.inngke.bp.leads.db.leads.dao.LeadsDistributeForwardConfDao;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeForwardConfManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 员工线索自动转发配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsDistributeForwardConfManagerImpl extends ServiceImpl<LeadsDistributeForwardConfDao, LeadsDistributeForwardConf> implements LeadsDistributeForwardConfManager {

    @Override
    public boolean saveOrUpdateByStaffId(LeadsDistributeForwardConf entity) {
        LeadsDistributeForwardConf exist = getOne(Wrappers.<LeadsDistributeForwardConf>query().eq(LeadsDistributeForwardConf.BID, entity.getBid()).eq(LeadsDistributeForwardConf.STAFF_ID, entity.getStaffId()));
        if (Objects.nonNull(exist)){
            entity.setId(exist.getId());
            return updateById(entity);
        }

        entity.setCreateTime(LocalDateTime.now());
        return save(entity);
    }

    @Override
    public boolean existForward(Integer bid, Long forwardStaffId) {
        return count(Wrappers.<LeadsDistributeForwardConf>query().eq(LeadsDistributeForwardConf.STAFF_ID,forwardStaffId).eq(LeadsDistributeForwardConf.BID,bid)) > 0;
    }

    @Override
    public boolean isForwarder(Integer bid, Long staffId) {
        return count(Wrappers.<LeadsDistributeForwardConf>query().eq(LeadsDistributeForwardConf.FORWARD_STAFF_ID,staffId).eq(LeadsDistributeForwardConf.BID,bid)) > 0;
    }

    @Override
    public Long getByStaffId(Integer bid, Long staffId) {
        return Optional.ofNullable(getOne(Wrappers.<LeadsDistributeForwardConf>query()
                .eq(LeadsDistributeForwardConf.BID,bid)
                .eq(LeadsDistributeForwardConf.STAFF_ID,staffId)
        )).map(LeadsDistributeForwardConf::getForwardStaffId).orElse(null);
    }
}

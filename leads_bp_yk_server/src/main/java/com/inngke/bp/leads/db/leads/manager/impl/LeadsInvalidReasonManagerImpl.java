/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.dao.LeadsInvalidReasonDao;
import com.inngke.bp.leads.db.leads.entity.LeadsInvalidReason;
import com.inngke.bp.leads.db.leads.manager.LeadsInvalidReasonManager;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsInvalidReasonManagerImpl extends ServiceImpl<LeadsInvalidReasonDao, LeadsInvalidReason> implements LeadsInvalidReasonManager {

    @Override
    public void moveSort(Integer bid, LeadsInvalidReason prevLeadsInvalidReason, LeadsInvalidReason nextLeadsInvalidReason) {
        LeadsInvalidReason newPrevLeadsChannel = new LeadsInvalidReason();
        newPrevLeadsChannel.setId(prevLeadsInvalidReason.getId());
        newPrevLeadsChannel.setSort(nextLeadsInvalidReason.getSort());

        LeadsInvalidReason newNextLeadsChannel = new LeadsInvalidReason();
        newNextLeadsChannel.setId(nextLeadsInvalidReason.getId());
        newNextLeadsChannel.setSort(prevLeadsInvalidReason.getSort());
        updateBatchById(Lists.newArrayList(newPrevLeadsChannel, newNextLeadsChannel));
    }

    @Override
    public LeadsInvalidReason getById(Integer bid, Long id) {
        return this.getOne(Wrappers.<LeadsInvalidReason>query()
                .eq(LeadsInvalidReason.BID, bid)
                .eq(LeadsInvalidReason.ID, id));
    }

}

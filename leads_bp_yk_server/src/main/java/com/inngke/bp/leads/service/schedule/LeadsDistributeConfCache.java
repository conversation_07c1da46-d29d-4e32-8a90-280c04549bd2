package com.inngke.bp.leads.service.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConf;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfManager;
import com.inngke.common.core.config.privatization.PrivatizationDb;
import com.inngke.common.service.impl.BaseBidDataCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class LeadsDistributeConfCache extends BaseBidDataCacheService<Map<Integer, Map<Integer, List<Long>>>> {

    private static final Logger logger = LoggerFactory.getLogger(LeadsRegionCache.class);

    //Map<Bid,Map<regionId,List<StaffId>>>
    private final Map<Integer, Map<Integer, List<Long>>> bidRegionStaffsMap = Maps.newHashMap();

    @Autowired
    private LeadsDistributeConfManager leadsDistributeConfManager;

    @Scheduled(fixedRate = 70000)
    public void init() {
        loadData();
    }

    @Override
    protected String getDataName() {
        return "线索分配人员配置信息";
    }

    /**
     * 加载数据
     *
     * @param privatizationDb 私有化数据库实例
     * @param lastUpdateTime  上次更新时间，首次进入为null
     * @return 更新了多少数据
     */
    @Override
    protected int loadData(PrivatizationDb privatizationDb, LocalDateTime lastUpdateTime) {
        QueryWrapper<LeadsDistributeConf> queryWrapper = getBaseCustomerQueryWrapper();
        if (lastUpdateTime != null) {
            queryWrapper.ge(LeadsDistributeConf.UPDATE_TIME, lastUpdateTime);
        }
        List<LeadsDistributeConf> leadsDistributeConfs = leadsDistributeConfManager.list(queryWrapper);
        setLeadsDistributeConfsCache(leadsDistributeConfs);
        return leadsDistributeConfs.size();
    }

    public void setLeadsDistributeConfsCache(List<LeadsDistributeConf> leadsDistributeConfs) {
        leadsDistributeConfs.stream()
                //首先过滤出所有有效的数据，bId，regionId,staffIds必须都不为空的数据才作为最后的缓存数据，也为了下面分组操作提前进行判空
                .filter(item -> item.getRegionId() != null && item.getBid() != null && StringUtils.isNotEmpty(item.getStaffIds()))
                //对Bid先进行分组
                .collect(Collectors.groupingBy(LeadsDistributeConf::getBid, Collectors.collectingAndThen(Collectors.toList(), list -> list.stream()
                        //对地区进行分组
                        .collect(Collectors.groupingBy(LeadsDistributeConf::getRegionId, Collectors.collectingAndThen(Collectors.toList(), regions -> {
                            LeadsDistributeConf leadsDistributeConf = regions.get(0);
                            String staffIds = leadsDistributeConf.getStaffIds();
                            //根据staffIds字符串分割成Long类型的数组，作为map的value
                            List<Long> ids = Lists.newArrayList(staffIds.split(",")).stream().map(item -> Long.parseLong(item)).collect(Collectors.toList());
                            return ids;
                        })))))).forEach((k,v)->bidRegionStaffsMap.put(k,v));
    }

    private QueryWrapper<LeadsDistributeConf> getBaseCustomerQueryWrapper() {
        return Wrappers.<LeadsDistributeConf>query()
                .select(
                        LeadsDistributeConf.BID,
                        LeadsDistributeConf.REGION_ID,
                        LeadsDistributeConf.STAFF_IDS
                );
    }

    public List<Long> getStaffIds(Integer bId,Integer regionId){
        Map<Integer, List<Long>> integerListMap = bidRegionStaffsMap.get(bId);
        if(integerListMap != null){
            List<Long> staffIds = integerListMap.get(regionId);
            if(staffIds == null){
                staffIds = getExistStaffIdsFromMysql(bId, regionId);
                if(staffIds != null){
                    HashMap<Integer, List<Long>> objectObjectHashMap = Maps.newHashMap();
                    objectObjectHashMap.put(regionId,staffIds);
                    bidRegionStaffsMap.put(bId,objectObjectHashMap);
                }
            }
            return staffIds;
        }
        return null;
    }

    public List<Long> getExistStaffIdsFromMysql(Integer bId,Integer regionId){
        QueryWrapper<LeadsDistributeConf> query = getBaseCustomerQueryWrapper();
        query.eq(LeadsDistributeConf.BID,bId).eq(LeadsDistributeConf.REGION_ID,regionId);
        List<LeadsDistributeConf> list = leadsDistributeConfManager.list(query);
        if(list != null && list.size() > 0){
            String staffIds = list.get(0).getStaffIds();
            List<Long> ids = Lists.newArrayList(staffIds.split(",")).stream().map(item -> Long.parseLong(item)).collect(Collectors.toList());
            return ids;
        }
        return null;
    }


}

package com.inngke.bp.leads.client;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.request.ShortLinkGenerateRequest;
import com.inngke.ip.common.dto.response.ShortLinkGenerateDto;
import com.inngke.ip.common.service.ShortLinkService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-07-10 11:07
 **/
@Component
public class ShortLinkServiceForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private ShortLinkService shortLinkService;

    public ShortLinkGenerateDto generate(ShortLinkGenerateRequest request) {
        BaseResponse<ShortLinkGenerateDto> response = shortLinkService.generate(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

}

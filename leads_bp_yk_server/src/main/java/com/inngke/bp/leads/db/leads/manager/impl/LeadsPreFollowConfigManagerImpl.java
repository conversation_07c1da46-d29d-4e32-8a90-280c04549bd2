package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.dao.LeadsPreFollowConfigDao;
import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.db.leads.manager.LeadsPreFollowConfigManager;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/9/8
 **/
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsPreFollowConfigManagerImpl extends ServiceImpl<LeadsPreFollowConfigDao, LeadsPreFollowConfig> implements LeadsPreFollowConfigManager {
    @Override
    public LeadsPreFollowConfig getByStaffId(Integer bid, Long staffId) {
        if (staffId == null) {
            return null;
        }
        return this.getOne(Wrappers.<LeadsPreFollowConfig>query()
                .eq(LeadsPreFollowConfig.BID, bid)
                .eq(LeadsPreFollowConfig.STAFF_ID,staffId)
                .last("LIMIT 1")
        );
    }

    @Override
    public List<LeadsPreFollowConfig> getByStaffIds(Integer bid, Set<Long> staffIds) {
        if(CollectionUtils.isEmpty(staffIds)){
            return new ArrayList<>();
        }
        List<LeadsPreFollowConfig> list = list(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, bid)
                .in(LeadsPreFollowConfig.STAFF_ID, staffIds));

        return list;
    }
}

package com.inngke.bp.leads.client;

import com.inngke.bp.client.dto.request.client.GetClientFollowListRequest;
import com.inngke.bp.client.dto.request.client.GetStaffClientListByMobileWx;
import com.inngke.bp.client.dto.response.client.ClientDto;
import com.inngke.bp.client.dto.response.client.ClientFollowItemDto;
import com.inngke.bp.client.service.ClientGetService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/3 10:39
 */
@Component
@Slf4j
public class ClientGetClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientGetService clientGetService;

    @Resource
    private JsonService jsonService;

    public Integer followCountByClient(GetClientFollowListRequest request) {
        BaseResponse<Integer> response = clientGetService.followCountByClient(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public BasePaginationResponse<ClientFollowItemDto> getClientFollowList(GetClientFollowListRequest request){
        BaseResponse<BasePaginationResponse<ClientFollowItemDto>> response = clientGetService.getClientFollowList(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }


    public ClientDto findClientByMobileAndStaff(Integer bid, String mobile, Long distributeStaffId) {
        GetStaffClientListByMobileWx request = new GetStaffClientListByMobileWx();
        request.setBid(bid);
        request.setMobile(mobile);
        request.setStaffId(distributeStaffId);
        BaseResponse<ClientDto> response = clientGetService.getStaffClientListByMobileWx(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("com.inngke.bp.client.service.ClientGetService.getStaffClientListByMobileWx异常：{}", jsonService.toJson(response));
            return null;
        }
        return response.getData();
    }
}

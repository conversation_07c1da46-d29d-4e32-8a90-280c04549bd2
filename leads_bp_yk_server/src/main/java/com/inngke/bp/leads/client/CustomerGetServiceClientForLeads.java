package com.inngke.bp.leads.client;

import com.google.common.collect.Maps;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.request.customer.CustomerInfoGetByStaffIdRequest;
import com.inngke.bp.user.dto.request.customer.CustomerInfoGetRequest;
import com.inngke.bp.user.dto.request.customer.CustomerInfoListGetRequest;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.bp.user.service.CustomerService;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Component
public class CustomerGetServiceClientForLeads {
    private static final Logger logger = LoggerFactory.getLogger(CustomerGetServiceClientForLeads.class);

    @DubboReference(version = "1.0.0")
    private CustomerGetService customerGetService;

    @DubboReference(version = "1.0.0")
    private CustomerService customerService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private JsonService jsonService;

    public Map<Long, CustomerDto> getCustomerInfoByIds(int bid, Set<Long> customerIds, Set<String> fields) {

        CustomerInfoListGetRequest req = new CustomerInfoListGetRequest();
        req.setBid(bid);
        req.setCustomerIds(customerIds);
        req.setFields(fields);
        BaseResponse<Map<Long, CustomerDto>> resp = customerGetService.getCustomerInfoByIds(req);
        if (BaseResponse.responseSuccess(resp)) {
            return resp.getData() == null ? Maps.newHashMap() : resp.getData();
        }
        logger.warn("查询客户信息失败：req={}, resp={}", jsonService.toJson(req), jsonService.toJson(resp));
        return Maps.newHashMap();
    }

    public CustomerDto getStaffCustomer(int bid, Long staffId, Set<String> fields) {
        CustomerInfoGetByStaffIdRequest request = new CustomerInfoGetByStaffIdRequest();
        request.setStaffId(staffId);
        request.setFields(fields);
        request.setRetry(true);
        request.setOperatorId(0L);
        request.setBid(bid);
        BaseResponse<CustomerDto> response = customerGetService.getCustomerInfoByStaffId(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return response.getData();
        }
        logger.error("查询员工信息失败：req={},resp={}", jsonService.toJson(request), jsonService.toJson(response));
        return null;
    }

    public CustomerDto getCustomerDtoByStaffId(int bid, Long staffId) {
        StaffDto staffDto = staffClientForLeads.getStaffById(bid, staffId);
        if (Objects.isNull(staffDto)) {
            throw new InngkeServiceException("查询员工信息失败,id="+staffId);
        }

        CustomerInfoGetRequest customerInfoGetRequest = new CustomerInfoGetRequest();
        customerInfoGetRequest.setBid(bid);
        customerInfoGetRequest.setOperatorId(0L);
        customerInfoGetRequest.setCustomerId(staffDto.getCustomerId());
        BaseResponse<CustomerDto> baseResponse = customerGetService.getCustomerInfo(customerInfoGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(baseResponse)) {
            logger.warn("获取客户数据失败！request={},response={}", jsonService.toJson(customerInfoGetRequest), jsonService.toJson(baseResponse));
            throw new InngkeServiceException("获取客户数据失败！");
        }
        return baseResponse.getData();
    }

    public CustomerDto getCustomerById(Integer bid, Long id) {
        BaseIdRequest request = new BaseIdRequest();
        request.setBid(bid);
        request.setId(id);
        BaseResponse<CustomerDto> response = customerService.getCustomerById(request);
        if (!BaseResponse.responseSuccess(response)) {
//            throw new InngkeServiceException("查询客服信息失败");
            logger.info("查询客服信息失败:{}", jsonService.toJson(response));
            return null;
        }
        return response.getData();
    }
}

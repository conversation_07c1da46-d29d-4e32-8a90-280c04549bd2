package com.inngke.bp.leads.service.message.sender;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.FollowPlanContext;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class FollowPlanSenderService extends MessageSenderServiceAbs{

    private static final Logger logger = LoggerFactory.getLogger(FollowPlanSenderService.class);

    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.FOLLOW_TEMPLATE;
    }

    @Override
    public void init(MessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(MessageContext context) {
        FollowPlanContext ctx = context.transferContext();
        if (!checkAndInitSendTargetMemberData(ctx)) {
            logger.info("准备发送线索跟进提醒失败");
            return null;
        }

        Map<String, String> params = Maps.newHashMap();
        params.put("id", String.valueOf(ctx.getLeads().getId()));
        params.put("activeTab", "follow");

        return getTemplateRequestBuilder(ctx)
                .setTitle("客户跟进提醒")
                .setVar("title", "客户跟进计划提醒")
                .setVar("name", ctx.getLeads().getName())
                .setVar("mobile", ctx.getLeads().getMobile().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"))
                .setVar("followType", "跟进提醒")
                .setVar("followContent", ctx.getPlanContent())
                .setVar("content", ctx.getPlanContent())
                .setVar("remark", "点击前往小程序立即查看")
                .setMpPagePath(getMessageType().getPath(params))
                .build();
    }
}

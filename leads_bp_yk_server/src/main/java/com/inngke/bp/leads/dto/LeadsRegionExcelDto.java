package com.inngke.bp.leads.dto;

import com.inngke.common.utils.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10 AM 12:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsRegionExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 姓名
     */
    @Excel(name = "省份")
    private String province;

    /**
     * 渠道名
     */
    @Excel(name = "城市")
    private String city;

    /**
     * 手机号码
     */
    @Excel(name = "区域")
    private String area;

    /**
     * 省份名称
     */
    @Excel(name = "渠道")
    private String channelName;

    /**
     * 城市名称
     */
    @Excel(name = "员工姓名")
    private String staffName;

    /**
     * 区域名称
     */
    @Excel(name = "员工手机号码")
    private String staffMobile;

}

package com.inngke.bp.leads.mq.process.customer;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.request.UpdateLeadsStatusRecordRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.message.customer.CusromerChangeMessageDto;
import com.inngke.bp.leads.mq.process.CustomerProcess;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.user.dto.request.customer.CustomerInfoGetRequest;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

abstract class  BaseCustomerProcess implements CustomerProcess {
    private static final Logger logger = LoggerFactory.getLogger(BaseCustomerProcess.class);

    @Autowired
    protected JsonService jsonService;


    @Autowired
    protected LeadsManager leadsManager;

    @Autowired
    protected LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerGetService customerGetService;


    protected void changeLeadsStatus(CusromerChangeMessageDto message) {
        String msg = jsonService.toJson(message);
        logger.info("客户手机授权，修改线索状态{}",msg);
        Integer bid = message.getBid();
        String mobile = message.getMobile();
        Long customerId = message.getId();
        Long operatorId = message.getOperatorId();

        if(customerId != null && StringUtils.isNotEmpty(mobile)){
            CustomerDto customerDto = null;

            if (customerId != null && customerId != 0) {
                customerDto = getCustomerInfo(bid, operatorId, customerId);
                if (customerDto == null) {
                    logger.error("线索服务mq处理客户信息变更事件，根据customerId：【{}】无法获取到客户信息", customerId);
                }
            }

            if(customerDto == null){
                return;
            }
            customerId = customerDto.getId();

            //补充线索客户uid，客户授权手机号mq发送过来相对应的uid，根据手机号码匹配补充进入线索表
            UpdateWrapper<Leads> updateUid = Wrappers.<Leads>update().eq(Leads.BID, bid).eq(Leads.MOBILE,mobile).set(Leads.CUSTOMER_ID,customerId);
            leadsManager.update(updateUid);
        }
    }

    private CustomerDto getCustomerInfo(Integer bid, Long operatorId, Long guideId){
        CustomerInfoGetRequest guideInfo = new CustomerInfoGetRequest();
        guideInfo.setCustomerId(guideId);
        guideInfo.setOperatorId(operatorId);
        guideInfo.setBid(bid);
        BaseResponse<CustomerDto> customerInfoResp = customerGetService.getCustomerInfo(guideInfo);
        if (!BaseResponse.responseSuccessWithNonNullData(customerInfoResp)) {
            return null;
        }
        return customerInfoResp.getData();
    }
}

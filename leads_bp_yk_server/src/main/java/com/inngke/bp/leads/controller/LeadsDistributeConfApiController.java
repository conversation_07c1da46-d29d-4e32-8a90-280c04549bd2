package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.RegionConfDto;
import com.inngke.bp.leads.dto.response.StaffForwardConfDto;
import com.inngke.bp.leads.service.LeadsDistributeConfService;
import com.inngke.bp.leads.service.LeadsDistributeForwardService;
import com.inngke.bp.leads.service.LeadsStatisticsService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索下发配置
 * @since 2021/9/7 3:49 PM
 */
@RestController
@RequestMapping("/api/leads/distribute-conf")
public class LeadsDistributeConfApiController {
    @Autowired
    private LeadsDistributeConfService leadsDistributeConfService;

    @Autowired
    private LeadsStatisticsService leadsStatisticsService;

    @Autowired
    private LeadsDistributeForwardService leadsDistributeForwardService;


    /**
     * 获取行政区域树
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return 行政区域树
     */
    @GetMapping("/tree")
    public BaseResponse<List<RegionConfDto>> getRegionTree(
            @RequestAttribute JwtPayload jwtPayload,
            GetLeadsDistributeConfRequest request) {

        request.setBid(jwtPayload.getBid());
        request.setOperatorId(jwtPayload.getCid());
        return leadsDistributeConfService.getRegionTree(request);
    }

    /**
     * 保存区域接收人
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    保存请求参数
     * @return 是否保存成功
     */
    @PostMapping
    public BaseResponse<Boolean> save(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody RegionConfSaveRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsDistributeConfService.save(request);
    }


    /**
     * 批量导入区域接收人
     * @param bid
     * @param request
     * @return
     * @throws IOException
     */
    @PostMapping("/batchImport")
    public BaseResponse<List<String>> batchImport(
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestHeader int bid,
            @RequestBody @Validated LeadsBatchImportRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsDistributeConfService.batchImport(request);
    }



    /**
     * 清除区域接收人
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    保存请求参数
     * @return 是否保存成功
     */
    @DeleteMapping
    public BaseResponse<Boolean> clear(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody RegionConfClearRequest request
    ){
        request.setBid(bid);
        request.setOperatorId(operatorId);

        return leadsDistributeConfService.clear(request);
    }

    /**
     * 检查当前员工是否已被配置为自动转交人
     */
    @GetMapping("/is-forward")
    public BaseResponse<Boolean> isForwarder(@RequestAttribute JwtPayload jwtPayload){
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setBid(jwtPayload.getBid());
        request.setOperatorId(jwtPayload.getCid());
        request.setOperatorStaffId(jwtPayload.getSid());

        return leadsDistributeForwardService.isForwarder(request);
    }

    /**
     * 保存员工自动转交配置
     */
    @PostMapping("/forward")
    public BaseResponse<Boolean> saveForwardConf(@RequestAttribute JwtPayload jwtPayload,@RequestBody SaveForwardConfRequest request){
        request.setBid(jwtPayload.getBid());
        request.setStaffId(jwtPayload.getSid());

        return leadsDistributeForwardService.saveForwardConf(request);
    }

    /**
     * 关闭员工自动转交
     */
    @DeleteMapping("/forward")
    public BaseResponse<Boolean> closeForward(@RequestAttribute JwtPayload jwtPayload){
        SaveForwardConfRequest request = new SaveForwardConfRequest();
        request.setBid(jwtPayload.getBid());
        request.setStaffId(jwtPayload.getSid());

        return leadsDistributeForwardService.closeForward(request);
    }

    /**
     * 获取员工自动转交配置
     */
    @GetMapping("/forward")
    public BaseResponse<StaffForwardConfDto> getForwardConf(@RequestAttribute JwtPayload jwtPayload){
        BaseIdRequest request = new BaseIdRequest();
        request.setId(jwtPayload.getSid());
        request.setBid(jwtPayload.getBid());

        return leadsDistributeForwardService.getForwardConf(request);
    }

    /**
     * 获取已开启自动转交的员工ids
     */
    @GetMapping("/forward/staff")
    public BaseResponse<Set<Long>> getExistForwardStaffIds(@RequestAttribute JwtPayload jwtPayload){
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(jwtPayload.getBid());

        return leadsDistributeForwardService.getExistForwardStaffIds(request);
    }

}

package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.inngke.bp.client.dto.response.client.ClientFollowDto;
import com.inngke.bp.client.dto.response.client.ClientFollowItemDto;
import com.inngke.bp.client.request.ClientFollowOpenQuery;
import com.inngke.bp.client.service.ClientFollowOpenService;
import com.inngke.bp.client.service.ClientsFollowService;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/3 10:39
 */
@Component
@Slf4j
public class ClientGetClientFollowForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientsFollowService clientsFollowService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientFollowOpenService clientFollowOpenService;

    @Autowired
    private JsonService jsonService;

    public List<ClientFollowItemDto>  getClientFollowListByClientIds(Integer bid,List<Long> clientIds) {
        BaseIdsRequest request = new BaseIdsRequest();
        request.setBid(bid);
        request.setIds(clientIds);
        BaseResponse<List<ClientFollowItemDto>> response = clientsFollowService.getClientFollowListByClientIds(request);

        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return Lists.newArrayList();
        }
        return response.getData();
    }

    public ClientFollowDto getById(Integer bid, Long followId) {
        ClientFollowOpenQuery request = new ClientFollowOpenQuery();
        request.setFollowId(followId);
        request.setBid(bid);

        BaseResponse<List<ClientFollowDto>> response = clientFollowOpenService.followListByClient(request);
        log.info("ClientFollowChangeListenerForLeads process client follow response:{}", jsonService.toJson(response));
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return null;
        }

        return response.getData().stream().findFirst().orElse(null);
    }


    public ClientFollowDto getLastByClientId(Integer bid, Long clientId) {
        BaseIdRequest request = new BaseIdRequest();
        request.setId(clientId);
        request.setBid(bid);

        BaseResponse<ClientFollowDto> response = clientsFollowService.getClientLastFollowByClientId(request);
        log.info("ClientFollowChangeListenerForLeads process client follow response:{}", jsonService.toJson(response));
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return null;
        }

        return response.getData();
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索渠道区域接收人配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsDistributeChannelConf implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * bid
     */
    private Integer bid;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 线索渠道ids
     */
    private String channelIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String NAME = "name";

    public static final String CHANNEL_IDS = "channel_ids";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

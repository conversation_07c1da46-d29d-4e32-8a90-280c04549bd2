/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.inngke.common.utils.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsDraft implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 关联的用户ID，即customer.id
     */
    private Long customerId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 微信号
     */
    private String weChat;

    /**
     * 省份ID，0表示未匹配
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    private Integer channel;

    /**
     * 下单账号
     */
    private String orderAccount;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 订购商品数量
     */
    private Integer goodsNum;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款金额
     */
    private BigDecimal payAmount;

    /**
     * 订单留言
     */
    private String orderMessage;

    /**
     * 其它备注
     */
    private String remark;

    /**
     * 外部平台线索ID
     */
    private String tpLeadsId;

    /**
     * 广告活动名称
     */
    private String promotionName;

    /**
     * 报名时间
     */
    private LocalDateTime registryTime;

    /**
     * 需求时间：0=未指定 1=一个月内 2=三个月内 3=六个月内 4=一年内 5=一年以上
     */
    private Integer expectIn;

    /**
     * 装修风格，详见枚举
     */
    private Integer style;

    /**
     * 批次ID，0表示非批次导入
     */
    private Long batchId;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 线索来源
     */
    private Integer channelSource;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * qq号
     */
    private String qq;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 计划id
     */
    private String campaignId;

    /**
     * 计划名称
     */
    private String campaignName;

    /**
     * 广告主id
     */
    private String accountId;

    /**
     * 广告主名称
     */
    private String accountName;

    /**
     * 商品链接
     */
    private String goodsLink;

    /**
     * 线索提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 线索类型
     */
    private Integer type;

    /**
     * 企业标签
     */
    private String tags;

    /**
     * 企业标签
     */
    private String enterpriseTags;

    /**
     * 客户等级
     */
    private String level;

    private Integer levelId;

    /**
     * 创建人
     */
    private Long createStaffId;

    /**
     * 需求产品
     */
    private String demandProduct;

    /**
     * 平台ID
     */
    private String tpId;

    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String CUSTOMER_ID = "customer_id";

    public static final String NAME = "name";

    public static final String MOBILE = "mobile";

    public static final String WE_CHAT = "we_chat";

    public static final String PROVINCE_ID = "province_id";

    public static final String PROVINCE_NAME = "province_name";

    public static final String CITY_ID = "city_id";

    public static final String CITY_NAME = "city_name";

    public static final String AREA_ID = "area_id";

    public static final String AREA_NAME = "area_name";

    public static final String ADDRESS = "address";

    public static final String CHANNEL = "channel";

    public static final String ORDER_ACCOUNT = "order_account";

    public static final String ORDER_SN = "order_sn";

    public static final String GOODS_NAME = "goods_name";

    public static final String GOODS_NUM = "goods_num";

    public static final String PAY_TIME = "pay_time";

    public static final String PAY_AMOUNT = "pay_amount";

    public static final String ORDER_MESSAGE = "order_message";

    public static final String REMARK = "remark";

    public static final String TP_LEADS_ID = "tp_leads_id";

    public static final String PROMOTION_NAME = "promotion_name";

    public static final String REGISTRY_TIME = "registry_time";

    public static final String EXPECT_IN = "expect_in";

    public static final String STYLE = "style";

    public static final String BATCH_ID = "batch_id";

    public static final String ERROR_MSG = "error_msg";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CHANNEL_SOURCE = "channel_source";

    public static final String EXTERNAL_ID = "external_id";

    public static final String GENDER = "gender";

    public static final String AGE = "age";

    public static final String QQ = "qq";

    public static final String EMAIL = "email";

    public static final String CAMPAIGN_ID = "campaign_id";

    public static final String CAMPAIGN_NAME = "campaign_name";

    public static final String ACCOUNT_ID = "account_id";

    public static final String ACCOUNT_NAME = "account_name";

    public static final String SUBMIT_TIME = "submit_time";

    public static final String GOODS_LINK = "goods_link";

    public static final String CHANNEL_ID = "channel_id";

    private static final String TYPE = "type";

    private static final String TAGS = "tags";

    private static final String LEVEL = "level";

    private static final String CREATE_STAFF_ID = "create_staff_id";

    public static final String DEMANDPRODUCT = "demand_product";

    public static final String TP_ID = "tp_id";
}

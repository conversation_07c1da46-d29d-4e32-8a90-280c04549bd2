/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsChannel;
import com.inngke.bp.leads.db.leads.dao.LeadsChannelDao;
import com.inngke.bp.leads.db.leads.manager.LeadsChannelManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.ds.annotation.DS;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsChannelManagerImpl extends ServiceImpl<LeadsChannelDao, LeadsChannel> implements LeadsChannelManager {

    public static final int DEFAULT_VALUE = 10001;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(Integer bid, LeadsChannel leadsChannel, LeadsChannel newLeadsChannel) {
        Long newId = copyDefaultLeadsChannel(bid, leadsChannel);

        newLeadsChannel.setId(newId);
        updateById(newLeadsChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void moveSort(Integer bid, LeadsChannel prevLeadsChannel, LeadsChannel nextLeadsChannel) {
        Long prevId = copyDefaultLeadsChannel(bid, prevLeadsChannel);
        Long nextId = copyDefaultLeadsChannel(bid, nextLeadsChannel);

        prevLeadsChannel = getOne(getQueryWrapper(bid, prevId));
        nextLeadsChannel = getOne(getQueryWrapper(bid, nextId));

        LeadsChannel newPrevLeadsChannel = new LeadsChannel();
        newPrevLeadsChannel.setId(prevLeadsChannel.getId());
        newPrevLeadsChannel.setSort(nextLeadsChannel.getSort());

        LeadsChannel newNextLeadsChannel = new LeadsChannel();
        newNextLeadsChannel.setId(nextLeadsChannel.getId());
        newNextLeadsChannel.setSort(prevLeadsChannel.getSort());
        updateBatchById(Lists.newArrayList(newPrevLeadsChannel, newNextLeadsChannel));
    }

    private Long copyDefaultLeadsChannel(Integer bid, LeadsChannel model) {
        if (!model.getBid().equals(0)) {
            return model.getId();
        }

        LeadsChannel existCopyModel = getOne(Wrappers.<LeadsChannel>query().eq(LeadsChannel.BID, bid).eq(LeadsChannel.VALUE, model.getValue()).ne(LeadsChannel.STATUS, -1));
        if (existCopyModel != null) {
            return existCopyModel.getId();
        }

        Long newId = SnowflakeHelper.getId();
        LeadsChannel copyModel = new LeadsChannel();
        BeanUtils.copyProperties(model, copyModel);
        copyModel.setBid(bid);
        copyModel.setId(newId);

        List<LeadsChannel> list = list(new QueryWrapper<LeadsChannel>().eq(LeadsChannel.BID, bid)
                .eq(LeadsChannel.PARENT_ID, model.getId()));

        save(copyModel);

        List<LeadsChannel> updateList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                LeadsChannel leadsChannel = new LeadsChannel();
                leadsChannel.setId(item.getId());
                leadsChannel.setParentId(copyModel.getId());
                updateList.add(leadsChannel);
            });
            updateBatchById(updateList);
        }

        return newId;
    }

    private QueryWrapper<LeadsChannel> getQueryWrapper(Integer bid, Long id) {
        QueryWrapper<LeadsChannel> queryWrapper = Wrappers.<LeadsChannel>query().eq(LeadsChannel.BID, bid).eq(LeadsChannel.ID, id);
        return queryWrapper;
    }
}

package com.inngke.bp.leads.service.impl.tp.pull;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.api.LeadsTpFlyFishApiService;
import com.inngke.bp.leads.api.dto.response.FlyFishLeadsDto;
import com.inngke.bp.leads.core.utils.FlyFishApiUtils;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.entity.LeadsTpLog;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.dto.request.tp.FeiYuLeadsPushDto;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.bp.leads.service.LeadsTpEnumParseServiceImpl;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.consts.CommonServiceConsts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/24 15:57
 */
@Service
public class LeadsTpPullFlyFishServiceImpl extends LeadsTpPullServiceAbstract<FlyFishLeadsDto, FeiYuLeadsPushDto> {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpPullFlyFishServiceImpl.class);

    @Autowired
    private LeadsTpFlyFishApiService leadsTpFlyFishApiService;

    @Autowired
    private LeadsTpEnumParseServiceImpl leadsTpEnumParseService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public LeadsTpTypeEnum getType() {
        return LeadsTpTypeEnum.FEI_YU;
    }

    @Override
    protected Class<FlyFishLeadsDto> getTpLeadsDtoClazz() {
        return FlyFishLeadsDto.class;
    }

    @Override
    protected List<FlyFishLeadsDto> getTpLeadsList(String customStartTime, BaseBidOptRequest request) {
        List<LeadsTpOauth> allOauth = getAllLeadsTpOauth(request);
        logger.info("获取到所有的账户信息bid->{}:{}", request.getBid(), allOauth);
        if (CollectionUtils.isEmpty(allOauth)) {
            logger.warn("获取广告主账号为空");
            return Lists.newArrayList();
        }

        List<FlyFishLeadsDto> allFlyFishLeadsList = Lists.newArrayList();
        if (allOauth.size() == 1){
            pullLeads(allOauth.stream().findFirst().orElse(null), request, customStartTime, allFlyFishLeadsList, true);
        }else {
            for (LeadsTpOauth leadsTpOauth : allOauth) {
                try {
                    pullLeads(leadsTpOauth, request, customStartTime, allFlyFishLeadsList, false);
                } catch (Exception e) {
                    logger.info("获取飞鱼线索失败", e);
                }
            }
        }


        return allFlyFishLeadsList;
    }

    private void pullLeads(LeadsTpOauth leadsTpOauth, BaseBidOptRequest request,
                           String customStartTime, List<FlyFishLeadsDto> allFlyFishLeadsList, boolean onlyOne) {

        String accessToken = leadsTpOauth.getAccessToken();
        List<LeadsTpAccountInfo> accountList = onlyOne ? getAllAccount(request.getBid()) :
                getAllAccount(request.getBid(),leadsTpOauth.getAccountId());
        logger.info("获取到所有的广告主信息bid->{}:{}", request.getBid(), accountList);

        if (CollectionUtils.isEmpty(accountList)) {
            return;
        }

        Map<String, Long> accountAndCreateStaffMap = accountList.stream().collect(Collectors.toMap(LeadsTpAccountInfo::getAccountId, LeadsTpAccountInfo::getOperatorStaffId, (k1, k2) -> k1));

        String accountIdsString = String.valueOf(accountList.stream().map(LeadsTpAccountInfo::getAccountId).collect(Collectors.toList()));
        LocalDateTime startLocalDateTime = getStartTime();
        if (startLocalDateTime.isBefore(leadsTpOauth.getCreateTime())) {
            startLocalDateTime = leadsTpOauth.getCreateTime();
        }
        String startTime = DateTimeUtils.format(startLocalDateTime, DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
        if (!StringUtils.isEmpty(customStartTime)) {
            startTime = customStartTime;
        }
        String endTime = DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);

        List<FlyFishLeadsDto> flyFishLeadsDtoList;
        Integer page = 1;
        do {
            flyFishLeadsDtoList = FlyFishApiUtils.checkResponseAsList(
                    leadsTpFlyFishApiService.getLeads(accessToken, accountIdsString, startTime,
                            endTime, page, 100));

            if (!CollectionUtils.isEmpty(flyFishLeadsDtoList)){
                int size = allFlyFishLeadsList.size();
                flyFishLeadsDtoList.stream().filter((this::isChangeData)).map(leads -> {
                    String advertiserId = leads.getAdvertiserId();
                    Long createStaffId = accountAndCreateStaffMap.getOrDefault(advertiserId, 0L);
                    leads.setCreateStaffId(createStaffId);
                    return leads;
                }).forEach(allFlyFishLeadsList::add);
                logger.info("过滤掉无变更线索前后数据量:过滤前{},过滤后{}",
                        flyFishLeadsDtoList.size(), allFlyFishLeadsList.size() - size);
            }else {
                logger.info("获取到线索数据为空");
            }

            page++;
        } while (!CollectionUtils.isEmpty(flyFishLeadsDtoList));
    }

    @Override
    protected void saveLog(Integer bid, List<FlyFishLeadsDto> tpLeadsList) {
        List<LeadsTpLog> leadsTpLogList = Lists.newArrayList();

        tpLeadsList.forEach((feiYuLeads) -> {
            LeadsTpLog leadsTpLog = new LeadsTpLog();
            leadsTpLog.setType(LeadsTpTypeEnum.FEI_YU.getCode());
            leadsTpLog.setContent(jsonService.toJson(feiYuLeads));
            leadsTpLog.setTpId(feiYuLeads.getClueId());
            leadsTpLog.setBid(bid);
            leadsTpLogList.add(leadsTpLog);
        });

        leadsTpLogManager.saveOrUpdateBatch(bid, leadsTpLogList);
    }


    @Override
    protected void saveLeads(Integer bid, List<FeiYuLeadsPushDto> feiYuLeadsList) {
        feiYuLeadsList.forEach(feiYuLeadsPushDto -> {
                    try {
                        leadsTpConserveService.conserve(bid, feiYuLeadsPushDto);
                    } catch (Exception e) {
                        logger.error("保存线索失败:{}", jsonService.toJson(feiYuLeadsPushDto), e);
                    }
                }
        );
    }


    @Override
    protected FeiYuLeadsPushDto toLeadsPushDto(FlyFishLeadsDto flyFishLeadsDto) {
        FeiYuLeadsPushDto feiYuLeadsPushDto = new FeiYuLeadsPushDto();
        feiYuLeadsPushDto.setId(flyFishLeadsDto.getClueId());
        feiYuLeadsPushDto.setAdvId(flyFishLeadsDto.getAdvertiserId());
        feiYuLeadsPushDto.setName(flyFishLeadsDto.getName());
        feiYuLeadsPushDto.setTelPhone(flyFishLeadsDto.getTelephone());
        feiYuLeadsPushDto.setGender(String.valueOf(flyFishLeadsDto.getGender()));
        feiYuLeadsPushDto.setAge(flyFishLeadsDto.getAge());
        feiYuLeadsPushDto.setWeiXin(flyFishLeadsDto.getWeixin());
        feiYuLeadsPushDto.setQq(flyFishLeadsDto.getQq());
        feiYuLeadsPushDto.setEmail(flyFishLeadsDto.getEmail());
        feiYuLeadsPushDto.setCityName(flyFishLeadsDto.getCityName());
        feiYuLeadsPushDto.setCid(flyFishLeadsDto.getCreativeId());
        feiYuLeadsPushDto.setProvinceName(flyFishLeadsDto.getProvinceName());
        feiYuLeadsPushDto.setRemark(flyFishLeadsDto.getRemark());
        feiYuLeadsPushDto.setAddress(flyFishLeadsDto.getAddress());
        long milli = DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(flyFishLeadsDto.getCreateTimeDetail()));
        feiYuLeadsPushDto.setCreateTime(String.valueOf(milli));
        feiYuLeadsPushDto.setExternalUrl(flyFishLeadsDto.getExternalUrl());
        feiYuLeadsPushDto.setAdId(flyFishLeadsDto.getAdId());
        feiYuLeadsPushDto.setAdName(flyFishLeadsDto.getAdName());
        feiYuLeadsPushDto.setAdvName(flyFishLeadsDto.getAdvertiserName());
        feiYuLeadsPushDto.setSiteId(flyFishLeadsDto.getSiteId());
        feiYuLeadsPushDto.setSiteName(flyFishLeadsDto.getSiteName());
        String appName = flyFishLeadsDto.getAppName();
        appName = appName.replace("字节-", "");
        Integer clueSource = analysisChannel(LeadsChannelEnum.parseByName(appName));
        if (clueSource == 0) {
            //记录匹配失败的AppName
            saveParseFailAppName(flyFishLeadsDto.getAppName());
        }
        feiYuLeadsPushDto.setClueSource(clueSource);
        feiYuLeadsPushDto.setClueType(flyFishLeadsDto.getClueType());
        feiYuLeadsPushDto.setModuleId(flyFishLeadsDto.getModuleId());
        feiYuLeadsPushDto.setModuleName(flyFishLeadsDto.getModuleName());
        feiYuLeadsPushDto.setDate(flyFishLeadsDto.getDate());
        feiYuLeadsPushDto.setFormRemark(flyFishLeadsDto.getFormRemark());
        feiYuLeadsPushDto.setAppName(flyFishLeadsDto.getAppName());
        feiYuLeadsPushDto.setReqId(flyFishLeadsDto.getReqId());
        feiYuLeadsPushDto.setLocation(flyFishLeadsDto.getLocation());
        feiYuLeadsPushDto.setStoreId(flyFishLeadsDto.getStore().getStoreId());
        feiYuLeadsPushDto.setStoreName(flyFishLeadsDto.getStore().getStoreName());
        feiYuLeadsPushDto.setStorePackId(flyFishLeadsDto.getStore().getStorePackId());
        feiYuLeadsPushDto.setStorePackName(flyFishLeadsDto.getStore().getStorePackName());
        feiYuLeadsPushDto.setStoreLocation(flyFishLeadsDto.getStore().getStoreLocation());
        feiYuLeadsPushDto.setStoreAddress(flyFishLeadsDto.getStore().getStoreAddress());
        feiYuLeadsPushDto.setStoreRemark(flyFishLeadsDto.getStore().getStoreRemark());
        feiYuLeadsPushDto.setStorePackRemark(flyFishLeadsDto.getStore().getStorePackRemark());
        feiYuLeadsPushDto.setIntentionEstimation(flyFishLeadsDto.getIntentionEstimation());
        feiYuLeadsPushDto.setClueConvertStatus(flyFishLeadsDto.getConvertStatus());
        feiYuLeadsPushDto.setCountyName(flyFishLeadsDto.getCountryName());
        feiYuLeadsPushDto.setCreateStaffId(flyFishLeadsDto.getCreateStaffId());
        feiYuLeadsPushDto.setAccess(1);

        return feiYuLeadsPushDto;

    }

    @Override
    public String toJson(FlyFishLeadsDto obj) {
        return jsonService.toJson(obj);
    }

    @Override
    protected String getLeadsId(FlyFishLeadsDto leads) {
        return leads.getClueId();
    }


    private Integer analysisChannel(LeadsChannelEnum leadsChannelEnum) {
        for (Integer key : leadsTpEnumParseService.FLY_FISH_CHANNEL_MAP.keySet()) {
            if (leadsTpEnumParseService.FLY_FISH_CHANNEL_MAP.get(key).equals(leadsChannelEnum)) {
                return key;
            }
        }
        return 0;
    }

    private void saveParseFailAppName(String appName) {
        String key = CommonServiceConsts.APP_ID + InngkeAppConst.CLN_STR + "leads:fy_app_names";
        SetOperations setOperations = redisTemplate.opsForSet();
        setOperations.add(key, appName);
    }

}

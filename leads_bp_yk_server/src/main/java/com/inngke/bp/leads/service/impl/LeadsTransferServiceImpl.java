package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsHistoryDistribute;
import com.inngke.bp.leads.db.leads.manager.LeadsHistoryDistributeManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.enums.LeadsHistoryDistributeTypeEnum;
import com.inngke.bp.organize.dto.request.staff.StaffTransferRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.service.StaffTransferService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * LeadsTransferServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/18 20:33
 */
@DubboService(version = "1.0.0", timeout = 6000, group = "leads")
@Slf4j
public class LeadsTransferServiceImpl implements StaffTransferService {

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsHistoryDistributeManager leadsHistoryDistributeManager;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private StaffClientForLeads staffClient;

    @Override
    public BaseResponse<Integer> waitTransferCount(StaffTransferRequest request) {
        int count = leadsManager.count(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.DISTRIBUTE_STAFF_ID, request.getSourceId())

        );
        return BaseResponse.success(count);
    }

    @Override
    public BaseResponse<Integer> doTransfer(StaffTransferRequest request) {
        Long sourceStaffId = request.getSourceId();
        if (Objects.isNull(sourceStaffId) || sourceStaffId.equals(0L)) {
            throw new InngkeServiceException("旧负责人id列表为空");
        }
        StaffDto targetStaff = staffClient.getStaffById(request.getBid(), request.getTargetId());
        if (Objects.isNull(targetStaff)) {
            log.error("未找到目标员工");
            throw new InngkeServiceException("未找到目标员工");
        }
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.DISTRIBUTE_STAFF_ID, sourceStaffId)
                        .select(Leads.ID, Leads.DISTRIBUTE_STAFF_ID, Leads.BID, Leads.STATUS)
        );
        List<Leads> preFollowList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.PRE_FOLLOW_STAFF_ID, sourceStaffId)
                        .select(Leads.ID, Leads.BID)
        );
        if (CollectionUtils.isEmpty(leadsList) && CollectionUtils.isEmpty(preFollowList)) {
            log.info("员工【{}】转交线索数据，leadsList is empty", request.getTargetId());
            return BaseResponse.success(0);
        }

        Set<Long> leadsIds = leadsList.stream().map(Leads::getId).collect(Collectors.toSet());

        // 插入历史员工表
        Set<Long> excludeLeadsId = CollectionUtils.isEmpty(leadsIds) ? new HashSet<>() :
                leadsHistoryDistributeManager
                        .list(Wrappers.<LeadsHistoryDistribute>query()
                                .eq(LeadsHistoryDistribute.BID, request.getBid())
                                .eq(LeadsHistoryDistribute.TYPE, LeadsHistoryDistributeTypeEnum.DISTRIBUTE_LEADS.getCode())
                                .in(LeadsHistoryDistribute.LEADS_ID, leadsIds)
                                .eq(LeadsHistoryDistribute.DISTRIBUTE_STAFF_ID, request.getTargetId())
                                .select(LeadsHistoryDistribute.LEADS_ID))
                        .stream().map(LeadsHistoryDistribute::getLeadsId).collect(Collectors.toSet());

        Set<Long> finalLeadsIds = leadsIds;
        leadsIds.removeAll(excludeLeadsId);
        List<LeadsHistoryDistribute> historyDistributes = leadsList.stream().map(leads -> {
            Long leadsId = leads.getId();
            if (!leadsIds.contains(leadsId)){
                return null;
            }
            LeadsHistoryDistribute historyDistribute = new LeadsHistoryDistribute();
            historyDistribute.setBid(request.getBid());
            historyDistribute.setLeadsId(leadsId);
            historyDistribute.setDistributeStaffId(leads.getDistributeStaffId());
            historyDistribute.setType(LeadsHistoryDistributeTypeEnum.DISTRIBUTE_LEADS.getCode());
            historyDistribute.setCreateTime(LocalDateTime.now());
            return historyDistribute;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        Integer transferCount = leadsManager.transferLeads(leadsList, preFollowList, historyDistributes, request.getTargetId(), targetStaff.getName(), request.getAgentId());

        preFollowList.forEach(item -> finalLeadsIds.add(item.getId()));
        if (transferCount > 0) {
            AsyncUtils.runAsync(() -> {
//            StaffLeadsCountDto counter = new StaffLeadsCountDto();
//            counter.setStaffId(acceptStaffId);
//            counter.setCount(1);
//            counter.setMessageName(leads.getName());
//            counter.setMobile(leads.getMobile());
//            counter.setLeadsId(leads.getId());
//            List<StaffLeadsCountDto> staffCounter = Lists.newArrayList(counter);
//            leadsWxPubMessageService.batchSendDistributeMessage(bid, request.getOperatorId(), staffCounter, null, 0);

                //异步更新es数据
                LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
                esRequest.setBid(request.getBid());
                esRequest.setIds(Lists.newArrayList(finalLeadsIds));
                leadsEsService.updateDocs(esRequest);
            });
        }
        return BaseResponse.success(transferCount);
    }

}

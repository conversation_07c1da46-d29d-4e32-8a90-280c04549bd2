package com.inngke.bp.leads.notify.builder;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.notify.context.LeadsDistributeMessageContext;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * LeadsDistributeContentBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/16 17:48
 */
@Component(value = "leads_distribute_follow_notify")
@Slf4j
public class LeadsDistributeContentBuilder extends TemplateMessageContentBuilder<LeadsDistributeMessageContext> {
    @Autowired
    private StaffClientForLeads staffClientForLeads;
    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(LeadsDistributeMessageContext ctx) {
        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);

        return builder
                .setTitle(Objects.nonNull(ctx.getLeadsType()) && ctx.getLeadsType() == 1 ? "有合伙人提交了新的线索" : "有" + ctx.getStaffLeadsCount().getCount() + "条新的线索分配给您")
                .setVar("name", StringUtils.isNotBlank(ctx.getQyUserId()) ? ctx.getName() : "有" + ctx.getStaffLeadsCount().getCount() + "条新线索分配给你")
                .setVar("mobile", StringUtils.isBlank(ctx.getMobile()) ? "-" : ctx.getMobile())
                .setVar("followType", "总部派发")
                .setVar("followContent", "请在15分钟内跟进处理")
                .setVar("distributeTime", ctx.getFormatTime())
                .setVar("followRecommend", "请在15分钟内跟进处理以免错过最佳跟进时间!");
    }

}

package com.inngke.bp.leads.dto;

import java.io.Serializable;

/**
 * LeadsSnapshotDto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/6 16:33
 */
public class LeadsSnapshotDto implements Serializable {
    private Long id;

    private Integer bid;

    private Long lastFollowId;

    private Long lastFollowTime;
    private Long distributeAgentId;

    private Long distributeStaffId;

    private Long distributeTime;

    private Long distributeFollowTime;

    private Long preFollowStaffId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getLastFollowId() {
        return lastFollowId;
    }

    public Long getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(Long lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public void setLastFollowId(Long lastFollowId) {
        this.lastFollowId = lastFollowId;
    }

    public Long getDistributeAgentId() {
        return distributeAgentId;
    }

    public void setDistributeAgentId(Long distributeAgentId) {
        this.distributeAgentId = distributeAgentId;
    }

    public Long getDistributeStaffId() {
        return distributeStaffId;
    }

    public void setDistributeStaffId(Long distributeStaffId) {
        this.distributeStaffId = distributeStaffId;
    }

    public Long getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Long distributeTime) {
        this.distributeTime = distributeTime;
    }

    public Long getDistributeFollowTime() {
        return distributeFollowTime;
    }

    public void setDistributeFollowTime(Long distributeFollowTime) {
        this.distributeFollowTime = distributeFollowTime;
    }

    public Long getPreFollowStaffId() {
        return preFollowStaffId;
    }

    public void setPreFollowStaffId(Long preFollowStaffId) {
        this.preFollowStaffId = preFollowStaffId;
    }
}

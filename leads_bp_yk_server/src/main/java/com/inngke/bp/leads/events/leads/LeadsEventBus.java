package com.inngke.bp.leads.events.leads;

import com.google.common.eventbus.EventBus;

/**
 * LeadsEventBus
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/13 13:34
 */
public class LeadsEventBus {
    private final static EventBus eventBus = new EventBus();

    public static void post(Object event) {
        eventBus.post(event);
    }

    public static void register(Object handler) {
        eventBus.register(handler);
    }

    public static void unregister(Object handler) {
        eventBus.register(handler);
    }
}

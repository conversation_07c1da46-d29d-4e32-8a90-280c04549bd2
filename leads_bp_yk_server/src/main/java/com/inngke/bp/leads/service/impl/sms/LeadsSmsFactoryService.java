package com.inngke.bp.leads.service.impl.sms;

import com.inngke.bp.leads.service.LeadsSmsService;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6 11:20
 */
@Component
public class LeadsSmsFactoryService {

    @Autowired
    private List<LeadsSmsService> leadsSmsServiceList;

    public LeadsSmsService getInstance(Integer code) {
        for (LeadsSmsService leadsSmsService : leadsSmsServiceList) {
            if (leadsSmsService.getLeadsSmsTemplateEnum().getCode().equals(code)) {
                return leadsSmsService;
            }
        }

        throw new InngkeServiceException("未实现线索短信类型{" + code + "}");
    }

}

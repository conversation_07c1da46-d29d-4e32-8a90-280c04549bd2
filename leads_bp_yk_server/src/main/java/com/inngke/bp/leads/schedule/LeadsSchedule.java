package com.inngke.bp.leads.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.distribute.dto.request.DistributeCustomerTransferRequest;
import com.inngke.bp.distribute.service.DistributorLeadsService;
import com.inngke.bp.distribute.service.DistributorService;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.utils.LeadsCommonUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsConf;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsTransferRollbackTodo;
import com.inngke.bp.leads.db.leads.manager.LeadsConfManager;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsTransferRollbackTodoManager;
import com.inngke.bp.leads.dto.request.LeadsFollowEsBatchRequest;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsEventLogService;
import com.inngke.bp.leads.service.enums.LeadsNotifyMessageEnum;
import com.inngke.bp.leads.service.message.MessageManagerService;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.bp.leads.service.message.context.RemindersToFollowedContext;
import com.inngke.bp.organize.dto.request.merchant.MerchantDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.response.WxAppConfDto;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.service.impl.LeadsFollowServiceImpl.CACHE_LEADS_FOLLOW;

@Component
public class LeadsSchedule {

    private static final Logger logger = LoggerFactory.getLogger(LeadsSchedule.class);

    private final static String LOCK_ACTIVE_ACTIVITY = LeadsServiceConsts.LOCK_PREFIX + "leadsFollow";

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LockService lockService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @Autowired
    private MessageManagerService messageManagerService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private MerchantClientForLeads merchantClientForLeads;

    @Autowired
    private WxThirdPlatformServiceForLeads wxThirdPlatformServiceForLeads;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsConfManager leadsConfManager;

    @Autowired
    private CustomerGetServiceClientForLeads customerGetServiceClientForLeads;

    @Autowired
    private LeadsEventLogService leadsEventLogService;

    @Autowired
    private LeadsTransferRollbackTodoManager leadsTransferRollbackTodoManager;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private MerchantConfigClientForLeads merchantConfigClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributorLeadsService distributorLeadsService;

    private static final String LEADS_DAILY_NOTIFY_LOCK = LeadsServiceConsts.LOCK_PREFIX + "dalitNotify";

    private static final String LEADS_FOLLOW_NOTIFY_CLOCK = LeadsServiceConsts.LOCK_PREFIX + "followNotify";

    private static final String NOTIFY_HASH_CACHE_KEY = LeadsServiceConsts.APP_ID + ":notifyHash";

    private static final String NOTIFY_LIST_CACHE_KEY = LeadsServiceConsts.APP_ID + ":notifyList";

    /**
     * 每天下午5点短信通知有代联系线索的导购
     */
    @Scheduled(cron = "0 0 17 * * *")
    public void leadsDailyNotify() {
        Lock lock = lockService.getLock(LEADS_DAILY_NOTIFY_LOCK, 600);
        if (lock == null) {
            return;
        }
        logger.info("每日通知有代联系线索的导购开始");
        databasePrivatizationService.accept(privatizationDb -> {
            Integer privateBid = privatizationDb.getBids().iterator().next();
            // 查询待联系的线索
            List<Leads> leadsList = leadsManager.list(Wrappers.<Leads>query()
                    .eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                    .eq(Leads.CLIENT_ID, 0)
                    .ne(Leads.DISTRIBUTE_STAFF_ID, 0)
                    .ge(Leads.DISTRIBUTE_TIME, DateTimeUtils.toLocalDateTime("2023-01-01 00:00:00"))
                    .select(Leads.BID, Leads.DISTRIBUTE_STAFF_ID, Leads.ID, "count(*) as status")
                    .groupBy(Leads.BID, Leads.DISTRIBUTE_STAFF_ID));
            // 查询是否开启每日短信通知
            Map<Integer, Boolean> messageNotifyMap = leadsConfManager.list(new QueryWrapper<LeadsConf>()
                            .eq(LeadsConf.ENABLE, 1))
                    .stream()
                    .collect(Collectors.toMap(LeadsConf::getId, LeadsConf::getTextMessageNotify));
            Map<Integer, List<Leads>> leadsBidsMap = leadsList.stream().collect(Collectors.groupingBy(Leads::getBid));

            leadsBidsMap.forEach((bid, bidList) -> {
                Boolean messageNotifyBoolean = messageNotifyMap.get(bid);
                if (!Boolean.TRUE.equals(messageNotifyBoolean)) {
                    logger.info("每日通知有代联系线索的导购，不需要通知:{}", bid);
                    return;
                }
                // 判断商户状态是否正常
                if (!merchantStatus(bid)) {
                    logger.info("每日通知有代联系线索的导购，商户未继续使用:{}", bid);
                    return;
                }
                // 企业名称
                String enterpriseName = enterpriseName(bid);
                // 员工Id
                Set<Long> staffIds = bidList.stream().map(Leads::getDistributeStaffId).collect(Collectors.toSet());
                Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(bid, staffIds);
                if (StringUtils.isEmpty(enterpriseName)) {
                    logger.info("每日通知有代联系线索的导购，企业名称不存在：bid:{},enterpriseName:{}", bid, enterpriseName);
                    return;
                }
                // 员工分组
                Map<Long, List<Leads>> leadsStaffsMap = bidList.stream().collect(Collectors.groupingBy(Leads::getDistributeStaffId));
                leadsStaffsMap.forEach((staffId, list) -> {
                    Leads leads = list.get(0);
                    StaffDto staffDto = staffMap.get(staffId);
                    if (staffDto == null ||
                            StringUtils.isEmpty(staffDto.getMobile()) ||
                            !staffDto.getStatus().equals(StaffStatusEnum.OPENED.getCode())) {
                        logger.info("每日通知有代联系线索的导购，员工不需要发送短信：staffId:{},staffDto:{}", staffId, jsonService.toJson(staffDto));
                        return;
                    }
                    sendMsg(bid, leads.getStatus(), staffDto.getMobile(), enterpriseName, leads);
                });
            });
        });
        logger.info("每日通知有代联系线索的导购结束");
    }

    /**
     * 自定义设置线索跟进提醒时间定时任务
     */
    @Scheduled(cron = "59 * * * * *")
    public void customFollowNotify() {
        LocalDateTime now = LocalDateTime.now();
        Lock lock = lockService.getLock(LEADS_FOLLOW_NOTIFY_CLOCK, 300);
        if (lock == null) {
            return;
        }
        long startTime = System.currentTimeMillis();
        boolean notNotify = LeadsCommonUtil.notNotify();
        try {
            databasePrivatizationService.accept(privatizationDb -> {
                Integer privateBid = privatizationDb.getBids().iterator().next();
                List<Leads> list = leadsManager.list(new QueryWrapper<Leads>()
                        .eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                        .ge(Leads.DISTRIBUTE_TIME, LocalDateTime.now().minusDays(3).minusHours(1))
                        .eq(Leads.CLIENT_ID, 0)
                        .ne(Leads.DISTRIBUTE_STAFF_ID, 0)
                        .select(Leads.ID, Leads.BID, Leads.DISTRIBUTE_STAFF_ID, Leads.DISTRIBUTE_TIME, Leads.NAME, Leads.MOBILE));

                Map<Integer, Set<Long>> leadsFollowNotifyConfigMap = leadsFollowNotifyConfig();

                Map<Integer, List<Leads>> leadsBidMap = list.stream().collect(Collectors.groupingBy(Leads::getBid));

                // 消息补充
                customFollowNotifyRedis(notNotify);

                // bid分组
                leadsBidMap.forEach((bid, bidLeads) -> {
                    // 获取配置的通知时间
                    Set<Long> leadsFollowNotify = leadsFollowNotifyConfigMap.get(bid);
                    if (CollectionUtils.isEmpty(leadsFollowNotify)) {
                        return;
                    }
                    List<Leads> shouldNotifyLeads = bidLeads.stream()
                            .filter(item -> leadsFollowNotify.contains(customFollowNotifyMinute(item.getDistributeTime(), now)))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(shouldNotifyLeads)) {
                        return;
                    }
                    // 判断商户状态是否正常
                    if (!merchantStatus(bid)) {
                        logger.info("每日通知有代联系线索的导购，商户未继续使用:{}", bid);
                        return;
                    }
                    Set<Long> staffIds = shouldNotifyLeads.stream().map(Leads::getDistributeStaffId).collect(Collectors.toSet());
                    // 查询员工
                    Map<Long, StaffDto> staffDtoMap = staffClientForLeads.getStaffByIds(bid, staffIds);
                    Set<Long> customerIds = staffDtoMap.values().stream().map(StaffDto::getCustomerId).collect(Collectors.toSet());
                    // 查询用户
                    Map<Long, CustomerDto> customerDtoMap = customerGetServiceClientForLeads.getCustomerInfoByIds(bid, customerIds, Sets.newHashSet("id", "wxPubOpenId"));
                    Map<Long, List<Leads>> distributeStaffMap = shouldNotifyLeads.stream().collect(Collectors.groupingBy(Leads::getDistributeStaffId));
                    // staffId分组
                    distributeStaffMap.forEach((staffId, distributeLeadsList) -> {
                        // 分配时间分组
                        Map<Long, List<Leads>> leadsNotifyMap = new HashMap<>();
                        distributeLeadsList.forEach(item -> {
                            long minute = customFollowNotifyMinute(item.getDistributeTime(), now);
                            leadsNotifyMap.computeIfAbsent(minute, l -> new ArrayList<>()).add(item);
                        });
                        // 发送消息进行通知
                        leadsNotifyMap.forEach((time, leadsList) -> {
                            RemindersToFollowedContext messageContext = new RemindersToFollowedContext(bid, MessageTypeEnum.REMINDERS_TO_FOLLOWED);
                            messageContext.setLeadsList(leadsList);
                            messageContext.setTargetSid(staffId);
                            messageContext.setDeliverAfter(2);
                            StaffDto staffDto = staffDtoMap.get(staffId);
                            if (staffDto == null || !staffDto.getStatus().equals(StaffStatusEnum.OPENED.getCode())) {
                                logger.info("员工不存在,bid：{},staffId:{}", bid, staffId);
                                return;
                            }
                            CustomerDto customerDto = customerDtoMap.get(staffDto.getCustomerId());
                            messageContext.setTargetQyUserId(staffDto.getQyUserId());
                            if (customerDto != null) {
                                messageContext.setTargetWxPubOpenId(customerDto.getWxPubOpenId());
                            }
                            String timeOut = LeadsCommonUtil.timeOutMinute(time);
                            messageContext.setTimeOut(timeOut);

                            customFollowNotify(notNotify, messageContext, leadsList);
                        });
                    });
                });
            });
        } finally {
            redisTemplate.expire(LEADS_FOLLOW_NOTIFY_CLOCK, 2, TimeUnit.SECONDS);
            long time = System.currentTimeMillis() - startTime;
            if (time > 60000) {
                logger.info("自定义设置线索跟进提醒时间定时任务,60秒内未完成任务：{}", time);
            }
            logger.info("自定义设置线索跟进提醒时间定时任务执行耗时：{}", time);
        }
    }

    private void customFollowNotify(boolean notNotify, RemindersToFollowedContext messageContext, List<Leads> leadsList) {
        if (!notNotify) {
            messageManagerService.send(messageContext);
            return;
        }

        int hour = LocalDateTime.now().getHour();
        // 设置成当天7点2分过期
        int expireTime = (hour >= 22 ? (31 - hour) * 60 : hour * 60) + 2;
        Leads leads = leadsList.get(0);
        String leadsId = String.valueOf(leads.getId());
        String hashKey = NOTIFY_HASH_CACHE_KEY;
        String listKey = NOTIFY_LIST_CACHE_KEY;
        // 当天晚上22:00-次日凌晨07:00之间为免打扰时间，当处于该时间段时，不再给导购发送通知提醒
        TemplateMessageSendRequest request = messageManagerService.templateMessageSendRequest(messageContext);
        // 设置进redis缓存
        redisTemplate.opsForHash().put(hashKey, leadsId, jsonService.toJson(request));
        redisTemplate.expire(hashKey, expireTime, TimeUnit.MINUTES);
        redisTemplate.opsForList().leftPush(listKey, leadsId);
        redisTemplate.expire(listKey, expireTime, TimeUnit.MINUTES);
    }

    private void customFollowNotifyRedis(boolean notNotify) {
        if (notNotify) {
            return;
        }
        String hashKey = NOTIFY_HASH_CACHE_KEY;
        String listKey = NOTIFY_LIST_CACHE_KEY;
        Object o = null;
        Set<Object> set = new HashSet<>();
        while ((o = redisTemplate.opsForList().leftPop(listKey)) != null) {
            set.add(o);
        }
        if (CollectionUtils.isEmpty(set)) {
            return;
        }
        HashOperations hashOperations = redisTemplate.opsForHash();
        set.forEach(item -> {
            String leadsId = String.valueOf(item);
            Object jsonObj = hashOperations.get(hashKey, leadsId);
            if (jsonObj == null) {
                return;
            }
            String json = String.valueOf(jsonObj);
            TemplateMessageSendRequest request = jsonService.toObject(json, TemplateMessageSendRequest.class);
            RemindersToFollowedContext messageContext = new RemindersToFollowedContext(request.getBid(), MessageTypeEnum.REMINDERS_TO_FOLLOWED);
            messageContext.setTemplateMessageSendRequest(request);
            messageManagerService.send(messageContext);
        });
    }

    /**
     * 判断分配时间和当前时间的间隔，单位为分钟
     */
    private long customFollowNotifyMinute(LocalDateTime distributeTime, LocalDateTime now) {
        return Duration.between(distributeTime, now).getSeconds() / 60;
    }

    /**
     * 时间是否在当天晚上22:00-次日凌晨07:00之间
     */

    private Map<Integer, Set<Long>> leadsFollowNotifyConfig() {
        List<LeadsConf> list = leadsConfManager.list(new QueryWrapper<LeadsConf>()
                .eq(LeadsConf.ENABLE, 1));

        Map<Integer, Set<Long>> result = new HashMap<>();

        list.forEach(item -> {
            String[] split = item.getLeadsFollowNotify().split(InngkeAppConst.COMMA_STR);
            Set<Long> set = new HashSet<>(split.length);
            for (String s : split) {
                if (!StringUtils.isEmpty(s)) {
                    set.add(Long.valueOf(s));
                }
            }
            result.put(item.getId(), set);
        });
        return result;
    }


    /**
     * 判断商户是否还在使用
     */
    private boolean merchantStatus(int bid) {
        try {
            MerchantDto merchant = merchantClientForLeads.getMerchant(bid);
            if (merchant == null || !merchant.getStatus().equals(1)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.info("每日通知有代联系线索的导购，获取商户状态失败", e);
        }
        return false;
    }

    private void sendMsg(int bid, int leadsCount, String mobile, String enterpriseName, Leads leads) {
        try {
            MessageContext messageContext = new MessageContext(bid, MessageTypeEnum.LEADS_DAILY_NOTIFY);
            messageContext.setBid(bid);
            messageContext.setDeliverAfter(1);
            messageContext.setCount(leadsCount);
            // 员工手机号
            messageContext.setMobile(mobile);
            messageContext.setEnterpriseName("【" + enterpriseName + "】");
            messageContext.setLeads(leads);
            messageManagerService.send(messageContext);
        } catch (Exception e) {
            logger.info("每日通知有代联系线索的导购发送失败", e);
        }
    }


    private String enterpriseName(int bid) {
        try {
            // 获取企业简称
            MerchantDto merchant = merchantClientForLeads.getMerchant(bid);
            String enterpriseAbbreviation = merchant.getEnterpriseAbbreviation();
            if (!StringUtils.isEmpty(enterpriseAbbreviation)) {
                return enterpriseAbbreviation;
            }
            // 小程序名称
            WxAppConfDto wxAppConf = wxThirdPlatformServiceForLeads.getWxAppConf(bid, 0);
            String appInfo = wxAppConf.getAppInfo();
            if (StringUtils.isEmpty(appInfo)) {
                return null;
            }
            Map map = jsonService.toObject(appInfo, Map.class);
            enterpriseAbbreviation = (String) map.get("nick_name");
            return enterpriseAbbreviation;
        } catch (Exception e) {
            logger.info("每日通知有代联系线索的导购，获取企业名称失败", e);
        }
        return null;
    }

    /**
     * 每5秒执行执行
     * 设置线索根据记录的操作人身份
     */
    @Scheduled(cron = "0/30 * * * * *")
    public void sync() {
        Lock lock = lockService.getLock(LOCK_ACTIVE_ACTIVITY, 60 * 5);
        try {
            if (lock == null) {
                return;
            }
            BoundSetOperations boundSetOperations = redisTemplate.boundSetOps(CACHE_LEADS_FOLLOW);
            Set<String> leadsFollowIds = boundSetOperations.members();
            if(!CollectionUtils.isEmpty(leadsFollowIds)){
                Map<Integer,Set<Long>> customerRoleUserIdMap = Maps.newHashMap();
                Map<Integer,Set<Long>> guideRoleUserIdMap = Maps.newHashMap();
                leadsFollowIds.forEach(data->{
                    String[] split = data.split(InngkeAppConst.UNDERLINE_STR);
                    Integer bid = Integer.valueOf(split[0]);
                    Long leadFollowId = Long.valueOf(split[1]);

                    BidUtils.setBid(bid);
                    LeadsFollow leadsFollow = leadsFollowManager.getOne(
                            Wrappers.<LeadsFollow>query()
                                    .eq(LeadsFollow.BID, bid)
                                    .eq(LeadsFollow.ID, leadFollowId)
                    );

                    if (leadsFollow == null) {
                        return;
                    }

                    Long staffId = leadsFollow.getStaffId();

                    if (Long.valueOf(0).equals(staffId)) {
                        boundSetOperations.remove(data);
                        return;
                    }

                    UpdateWrapper<LeadsFollow> updateWrapper = Wrappers.<LeadsFollow>update()
                            .eq(LeadsFollow.BID, bid)
                            .eq(LeadsFollow.ID, leadFollowId);

                    if (listCustomerRoleUserIds(bid, customerRoleUserIdMap).contains(staffId)) {
                        updateWrapper.set(LeadsFollow.OPERATOR_ROLE, 2);
                    } else if (listGuideRoleUserIds(bid, guideRoleUserIdMap).contains(staffId)) {
                        updateWrapper.set(LeadsFollow.OPERATOR_ROLE, 1);
                    } else {
                        updateWrapper.set(LeadsFollow.OPERATOR_ROLE, 3);
                    }
                    leadsFollowManager.update(updateWrapper);
                    boundSetOperations.remove(data);
                });
            }
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
    }

    private Set<Long> listGuideRoleUserIds(Integer bid, Map<Integer, Set<Long>> guideRoleUserIdMap) {
        if (guideRoleUserIdMap.containsKey(bid)) {
            return guideRoleUserIdMap.get(bid);
        } else {
            Set<Long> customerRoleUserIds = rbacClientForLeads.listGuideRoleUserIds(bid);
            guideRoleUserIdMap.put(bid, customerRoleUserIds);
            return customerRoleUserIds;
        }
    }

    private Set<Long> listCustomerRoleUserIds(Integer bid, Map<Integer, Set<Long>> customerRoleUserIdMap) {
        if (customerRoleUserIdMap.containsKey(bid)) {
            return customerRoleUserIdMap.get(bid);
        } else {
            Set<Long> customerRoleUserIds = rbacClientForLeads.listCustomerRoleUserIds(bid);
            customerRoleUserIdMap.put(bid, customerRoleUserIds);
            return customerRoleUserIds;
        }
    }

    @Scheduled(cron = "0 24 0 * * *")
    public void rebuildLeadsEventLog() {
        LeadsFollowEsBatchRequest request = new LeadsFollowEsBatchRequest();
        request.setBid(1);

        leadsEventLogService.structureLeadsEventLogFromLeadsFollow(request);
    }

    @Scheduled(fixedDelay = 10 * 1000)
    public void executeForwardRollback() {
        Lock lock = lockService.getLock(LeadsServiceConsts.LOCK_PREFIX + "forward:rollback", 20);
        if (Objects.isNull(lock)) {
            return;
        }
        try {
            Map<Integer, Boolean> rollbackConfMap = Maps.newHashMap();
            databasePrivatizationService.accept(privatizationDb -> {
                List<LeadsTransferRollbackTodo> rollbackList = leadsTransferRollbackTodoManager.getRollbackList(privatizationDb.getBids().iterator().next());

                logger.info("leads转移回滚任务数量：{}", rollbackList.size());

                rollbackList.forEach(todo -> {
                    try {
                        if (!rollbackConfMap.computeIfAbsent(todo.getBid(), bid -> merchantConfigClientForLeads.isOpenForwardRollback(bid))) {
                            return;
                        }
                        Leads leads = leadsManager.getById(todo.getBid(), todo.getLeadsId());
                        if (!LeadsStatusEnum.DISTRIBUTED.equals(LeadsStatusEnum.parse(leads.getStatus()))) {
                            logger.info("线索状态不为待联系跳过 leads:{}", jsonService.toJson(leads));
                            leadsTransferRollbackTodoManager.removeById(todo.getId());
                            return;
                        }
                        StaffDto staff = staffClientForLeads.getStaffById(todo.getBid(), todo.getSourceStaffId());

                        boolean rollback = leadsTransferRollbackTodoManager.rollback(leads, staff, todo);

                        if (rollback) {
                            leadsEsService.sendLeadsChangeMq(leads.getBid(), Lists.newArrayList(leads.getId()), null, null, 5);

                            LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
                            esRequest.setBid(todo.getBid());
                            esRequest.setIds(Lists.newArrayList(leads.getId()));
                            leadsEsService.updateDocs(esRequest);

                            Long leadId = leads.getId();
                            DistributeCustomerTransferRequest distributeCustomerTransferRequest = new DistributeCustomerTransferRequest();
                            distributeCustomerTransferRequest.setLeadId(leadId);
                            distributeCustomerTransferRequest.setBid(todo.getBid());
                            distributeCustomerTransferRequest.setTargetGuideId(staff.getCustomerId());
                            distributorLeadsService.transfer(distributeCustomerTransferRequest);

                            //发送消息
                            MessageContext ctx = new MessageContext(todo.getBid(), MessageTypeEnum.LEADS_FORWARD_ROLLBACK);
                            ctx.setLeads(leads);
                            ctx.setBid(todo.getBid());
                            ctx.setTargetSid(todo.getTargetStaffId());
                            messageManagerService.send(ctx);
                        }
                    } catch (Exception e) {
                        logger.info("撤回失败", e);
                    }
                });
            });
        } finally {
            lock.unlock();
        }
    }
}

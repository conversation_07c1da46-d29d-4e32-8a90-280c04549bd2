package com.inngke.bp.leads.client;

import com.inngke.bp.distribute.dto.request.BindClientRequest;
import com.inngke.bp.distribute.dto.request.DistributorCustomerRequest;
import com.inngke.bp.distribute.dto.response.DistributorCustomerDto;
import com.inngke.bp.distribute.service.DistributorCustomerGetService;
import com.inngke.bp.distribute.service.DistributorCustomerRpcService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 9:49
 */
@Component
public class DistributorCustomerServiceClientForLeads {
    private static final Logger logger = LoggerFactory.getLogger(DistributorCustomerServiceClientForLeads.class);
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributorCustomerGetService distributorCustomerGetService;
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributorCustomerRpcService distributorCustomerRpcService;

    @Autowired
    private JsonService jsonService;

    public List<DistributorCustomerDto>  getList(DistributorCustomerRequest request){
        BaseResponse<List<DistributorCustomerDto>> listResp = distributorCustomerGetService.getList(request);
        if (!BaseResponse.responseSuccess(listResp)){
            logger.warn("获取合伙人客户信息是失败，request={}",request);
            return null;
        }
        List<DistributorCustomerDto> data = listResp.getData();
        if(CollectionUtils.isEmpty(data)){
            return null;
        }
        return listResp.getData();
    }


    public Boolean bindClient(Integer bid, Long leadsId, Long clientId) {
        BindClientRequest request = new BindClientRequest();
        request.setBid(bid);
        request.setClientId(clientId);
        request.setLeadsId(leadsId);
        BaseResponse<Boolean> response = distributorCustomerRpcService.bindClient(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            logger.info("报备客户绑定client异常：{}", jsonService.toJson(response));
            return false;
        }
        return response.getData();
    }

}

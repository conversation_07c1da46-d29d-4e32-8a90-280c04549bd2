package com.inngke.bp.leads.service.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/23 9:43
 */
public enum LeadsReportListDetailTypeEnum {
    /**
     * 1：总客户数、2：有效客户数、3：无效客户数、4：待联系、5：联系数、6：24小时联系数、7：成功联系数、8：量尺数、9：到店数、10：流失数、11：定金数、12：成交数
     */
    TOTAL_CUSTOMER(1, "总客户数"),
    /**
     * 有效客户数
     */
    EFFECTIVE_CUSTOMER(2, "有效客户数"),
    /**
     * 无效客户数
     */
    INVALID_CUSTOMER(3, "无效客户数"),
    /**
     * 待联系
     */
    WAIT_CONTRACT(4, "待联系"),
    /**
     * 联系数
     */
    CONTRACTED(5, "联系数"),
    /**
     * 24小时联系数
     */
    HOUR_24_CONTRACTED(6, "24小时联系数"),
    /**
     * 成功联系数
     */
    SUCCESS_CONTRACT(7, "成功联系数"),
    /**
     * 量尺数
     */
    MEASURED(8, "量尺数"),
    /**
     * 到店数
     */
    STORED(9, "到店数"),
    /**
     * 流失数
     */
    LOST(10, "流失数"),
    /**
     * 定金数
     */
    ORDERED(11, "定金数"),
    /**
     * 成交数
     */
    TRADED(12, "成交数"),
    /**
     * 已跟进数
     * 2022-11-24目前仅（转化数据-组织维度-客服）使用
     */
    FOLLOW_UP(13, "已跟进数"),
    /**
     * 待跟进数
     * 2022-11-24目前仅（转化数据-组织维度-客服）使用
     */
    NOT_FOLLOW_UP(14, "待跟进数"),
    /**
     * 下发数
     */
    DISTRIBUTE(15, "下发数"),
    /**
     * 待下发数
     * 2022-11-24目前仅（转化数据-组织维度-客服）使用
     */
    NOT_DISTRIBUTE(16, "待下发数"),
    ;


    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    LeadsReportListDetailTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 判断是否是开单类指标
     */
    public static boolean orderType(Integer code){
        return ORDERED.getCode().equals(code) || TRADED.getCode().equals(code);
    }


}

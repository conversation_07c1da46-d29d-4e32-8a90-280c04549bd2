/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 线索变更日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsLogDao extends BaseMapper<LeadsLog> {

}

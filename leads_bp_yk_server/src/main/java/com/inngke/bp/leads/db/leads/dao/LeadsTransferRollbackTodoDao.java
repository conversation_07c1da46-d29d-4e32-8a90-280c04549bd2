/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTransferRollbackTodo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 线索转交撤回代办 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsTransferRollbackTodoDao extends BaseMapper<LeadsTransferRollbackTodo> {

}

package com.inngke.bp.leads.service.impl.tp.oauth;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.core.config.LeadsTpTencentConfig;
import com.inngke.bp.leads.core.utils.TencentSdkUtils;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.dto.request.tp.GetTpAccessTokenRequest;
import com.inngke.bp.leads.dto.request.tp.GetTpLaunchOauthDataRequest;
import com.inngke.bp.leads.dto.request.tp.OauthCallbackRequest;
import com.inngke.bp.leads.dto.response.tp.TpLaunchOauthDataDto;
import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.StringUtils;
import com.tencent.ads.ApiException;
import com.tencent.ads.anno.AuthInfoAppend;
import com.tencent.ads.api.WechatAdvertiserApi;
import com.tencent.ads.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:25
 */
@Service
@AuthInfoAppend
public class LeadsTpOauthTencentServiceImpl extends LeadsTpOauthAbstract {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpOauthTencentServiceImpl.class);

    @Autowired
    private LeadsTpTencentConfig leadsTpTencentConfig;

    @Override
    public LeadsTpTypeEnum getType() {
        return LeadsTpTypeEnum.TENCENT;
    }

    @Override
    public BaseResponse<TpLaunchOauthDataDto> getLaunchOauthData(GetTpLaunchOauthDataRequest request) throws ApiException {
        String url = leadsTpTencentConfig.getOauthApi() +
                "?" +
                "client_id=" +
                leadsTpTencentConfig.getClientId() +
                "&redirect_uri=" +
                leadsTpTencentConfig.getRedirectUri() +
                "&state=" +
                request.getBid();

        TpLaunchOauthDataDto tpLaunchOauthDataDto = new TpLaunchOauthDataDto();
        tpLaunchOauthDataDto.setUrl(url);
        return BaseResponse.success(tpLaunchOauthDataDto);
    }

    @Override
    public BaseResponse<Boolean> oauthCallback(OauthCallbackRequest request) throws ApiException {
        //获取token
        OauthTokenResponseData tencentOauthData = getTokenFromTencent(request);

        //创建授权信息
        LeadsTpOauth oauthData = createOauthData(request.getBid(), tencentOauthData);

        //当账号下的广告主信息
        List<LeadsTpAccountInfo> oauthAccountInfoList = createOauthAccountInfoList(request.getBid(), tencentOauthData);

        return BaseResponse.success(leadsTpOauthManager.saveAccount(request.getBid(), oauthData, oauthAccountInfoList));
    }

    @Override
    public BaseResponse<String> getAccessToken(GetTpAccessTokenRequest request) {
        String accountId = request.getAccountId();
        Integer bid = request.getBid();

        String accessToken = getTokenFromCache(bid, accountId);
        if (StringUtils.isEmpty(accessToken)) {
            accessToken = getTokenFromDb(bid, accountId);
            if (StringUtils.isEmpty(accessToken)) {
                return BaseResponse.success(accessToken);
            }
            cacheToken(bid, accountId, accessToken);
        }

        return BaseResponse.success(accessToken);
    }

    /**
     * 获取token缓存key
     *
     * @param bid
     * @param accountId
     * @return
     */
    @Override
    protected String getAccessTokenCacheKey(Integer bid, String accountId) {
        return TP_ACCESS_TOKEN_CACHE_KEY + bid + "type:" + LeadsTpTypeEnum.TENCENT.getCode() + ":accountId:" + accountId;
    }


    /**
     * 获取accessToken
     *
     * @param request
     * @return
     */
    private OauthTokenResponseData getTokenFromTencent(OauthCallbackRequest request) throws ApiException {
        return TencentSdkUtils.getTencentAds().oauth().oauthToken(
                leadsTpTencentConfig.getClientId(),
                leadsTpTencentConfig.getClientSecret(),
                "authorization_code",
                request.getAuthorizationCode(),
                null, leadsTpTencentConfig.getRedirectUri()
        );
    }

    /**
     * 获取广告主账号信息
     *
     * @param oauthData
     * @return
     * @throws ApiException
     */
    private List<LeadsTpAccountInfo> getLeadsTpAccountInfo(Integer bid,OauthTokenResponseData oauthData) throws ApiException {

        Long accountId = oauthData.getAuthorizerInfo().getAccountId();
        List<String> fields = Lists.newArrayList("account_id", "corporation_name");
        if (AccountType.AGENCY.equals(oauthData.getAuthorizerInfo().getAccountType())) {
            accountId = null;
        }

        AdvertiserGetResponseData response = TencentSdkUtils.getTencentAds(oauthData.getAccessToken()).advertiser().
                advertiserGet(accountId, Lists.newArrayList(), fields, 1L, 100L);
        return response.getList().stream().map(advertiser -> {
            LeadsTpAccountInfo leadsTpAccountInfo = new LeadsTpAccountInfo();
            leadsTpAccountInfo.setAccountId(advertiser.getAccountId().toString());
            leadsTpAccountInfo.setAccountName(advertiser.getCorporationName());
            leadsTpAccountInfo.setAccountType(AccountType.ADVERTISER.getValue());
            leadsTpAccountInfo.setParentAccountId(oauthData.getAuthorizerInfo().getAccountId().toString());
            leadsTpAccountInfo.setType(LeadsTpTypeEnum.TENCENT.getCode());
            leadsTpAccountInfo.setBid(bid);
            return leadsTpAccountInfo;
        }).collect(Collectors.toList());
    }

    public List<LeadsTpAccountInfo> getLeadsTpAccountInfoWeChat(Integer bid, OauthTokenResponseData oauthData) throws ApiException {
        Long accountId = oauthData.getAuthorizerInfo().getAccountId();
        List<String> fields = Lists.newArrayList("account_id", "corporation_name","wechat_account_name");

        WechatAdvertiserSpecificationGetResponseData response = TencentSdkUtils.getTencentAds(oauthData.getAccessToken()).wechatAdvertiserSpecification()
                .wechatAdvertiserSpecificationGet(
                        accountId, Lists.newArrayList(), 1L, 100L, fields
                );
        return response.getList().stream().map(advertiser -> {
            LeadsTpAccountInfo leadsTpAccountInfo = new LeadsTpAccountInfo();
            leadsTpAccountInfo.setAccountId(advertiser.getAccountId().toString());
            leadsTpAccountInfo.setAccountType(AccountType.ADVERTISER.getValue());
            leadsTpAccountInfo.setParentAccountId(oauthData.getAuthorizerInfo().getAccountId().toString());
            leadsTpAccountInfo.setType(LeadsTpTypeEnum.TENCENT.getCode());
            leadsTpAccountInfo.setBid(bid);
            leadsTpAccountInfo.setAccountName(StringUtils.isEmpty(advertiser.getWechatAccountName()) ?
                    advertiser.getCorporationName() : advertiser.getWechatAccountName());

            return leadsTpAccountInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 创建授权数据
     *
     * @param bid
     * @param oauthData
     * @return
     */
    private LeadsTpOauth createOauthData(Integer bid, OauthTokenResponseData oauthData) {
        LeadsTpOauth leadsTpOauth = new LeadsTpOauth();
        leadsTpOauth.setAccountId(oauthData.getAuthorizerInfo().getAccountId().toString());
        leadsTpOauth.setAccessToken(oauthData.getAccessToken());
        leadsTpOauth.setType(LeadsTpTypeEnum.TENCENT.getCode());
        leadsTpOauth.setBid(bid);

        return leadsTpOauth;
    }

    /**
     * 根据广告主账号获取同主体广告主信息
     *
     * @param bid
     * @return
     */
    private List<LeadsTpAccountInfo> createOauthAccountInfoList(Integer bid, OauthTokenResponseData oauthData) throws ApiException {
        if (StringUtils.isEmpty(oauthData.getAuthorizerInfo().getWechatAccountId())) {
            return getLeadsTpAccountInfo(bid, oauthData);
        } else {
            return getLeadsTpAccountInfoWeChat(bid, oauthData);
        }
    }

}

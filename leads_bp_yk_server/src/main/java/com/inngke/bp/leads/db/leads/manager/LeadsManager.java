/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.db.leads.entity.*;
import com.inngke.bp.leads.dto.LeadsTransferResultDto;
import com.inngke.bp.leads.dto.request.GetPreFollowStaffReportRequest;
import com.inngke.bp.leads.dto.request.LeadsEditByInforRequest;
import com.inngke.bp.leads.dto.request.LeadsForwardRequest;
import com.inngke.bp.leads.dto.request.ListByMobileOrWeChatRequest;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
import com.inngke.bp.leads.dto.response.PreFollowStaffReportDto;
import com.inngke.bp.leads.enums.LeadsTransferEnum;
import com.inngke.bp.organize.dto.response.StaffDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface LeadsManager extends IService<Leads> {

    /**
     * 导入线索数据并更新批次号状态
     * @param leadsList 线索列表
     * @param leadsBatch 批次信息
     * @param operatorId
     * @return
     */
    Set<Long> importLeadsAndUpdateLeadsBatch(List<LeadsInformationExtend> leadsList, LeadsBatch leadsBatch, Long operatorId);

    /**
     * 批量保存
     * @param leadsList
     */
    int batchSaveLeads(List<Leads> leadsList, Long operator);

    /**
     * 修改线索状态
     * @param leads 线索数据
     * @param operatorId 操作员编号
     * @param followInfo 跟进信息
     * @param status 状态
     */
    void updateStatus(Leads leads, Long operatorId,String followInfo,Integer status);

    /**
     * 分页统计线索
     * @param wrapper
     * @return
     */
    List<LeadsStatisticsDto> pageStatistics(QueryWrapper<Leads> wrapper);

    Long addLeads(Leads leads,Long operatorId);

    void saveDistributeLeads(List<Leads> leadsList, Long operatorId);

    void updateLeads(Leads leads,Long operatorId);

    LeadsFollow pushBack(Leads leads, Long operatorId, Long operatorUserId, String reason, Long reasonId, List<String> pushBackImages);

    void batchDelete(Integer bId, List<Long> leadsIds, Long operatorId);

    /**
     * 线索转发
     * @param leads 线索信息
     * @param request 操作员编号
     * @param forwardStaffId 转发人id 用于组装跟进记录数据
     * @param forwardStaffName 转发人名称 用于组装跟进记录数据
     * @param acceptStaffName 接收人名称 用于组装跟进记录数据
     */
    LeadsFollow forward(Leads leads, LeadsForwardRequest request, Long forwardStaffId, String forwardStaffName, String acceptStaffName);

    /**
     * 查询分组后的数据量
     * @param queryWrapper 查询条件
     * @return 分组数量
     */
    Integer getGroupCount(QueryWrapper<Leads> queryWrapper);

    /**
     * 回收线索
     * @param leadsIds 线索id集合
     * @param operatorId 操作员编号
     * @param bid 商户编号
     */
    List<Long> recoveryLeads(List<Long> leadsIds,Long operatorId,Integer bid, Long staffId);

    Boolean updateLeads(Leads leads, LeadsFollow leadsFollow, LeadsLog leadsLog);

    Map<String, LocalDateTime> getMobileAndDistributeTime();

    Boolean editByInfo(LeadsEditByInforRequest request);

    Leads getById(Integer bid,Long id);

    /**
     * 员工转交线索数据
     *
     * @param leadsList 待转交线索列表
     * @param historyDistributes 历史员工数据
     * @param targetId 转交目标员工
     * @param agentId 转交目标员工的经销商id
     * @return 操作结果
     */
    Integer transferLeads(List<Leads> leadsList, List<Leads> preFollowList, List<LeadsHistoryDistribute> historyDistributes, Long targetId, String targetStaffName, Long agentId);

    /**
     * 转交线索
     *
     * @param leadsList 原始线索列表
     * @param targetStaff 转让目标员工id
     * @param leadsTransferEnum 转让业务类型
     * @return 转让后的结果
     */
    LeadsTransferResultDto transferLeads(List<Leads> leadsList, StaffDto targetStaff, LeadsTransferEnum leadsTransferEnum);

    Boolean doTransferLeads(LeadsTransferResultDto leadsTransferResultDto);

    List<PreFollowStaffReportDto> listByPreFollowStaffIds(GetPreFollowStaffReportRequest request, List<Long> staffIds);

    List<Leads> getByIds(Set<Long> leadsIds);

    List<Leads> selectBadCustomerLeads(Integer bid);

    /**
     * @param request
     * @return
     */
    List<Leads> listByKeyword(ListByMobileOrWeChatRequest request);

    /**
     * 根据客户获取线索
     *
     * @param bid 商户id
     * @param clientId 客户id
     * @return 关联客户的线索列表
     */
    List<Leads> findLeadsByClient(Integer bid, Long clientId);

    /**
     * 更新线索客户状态
     *
     * @param bid
     * @param id
     * @param status
     * @return
     */
    boolean updateClientStatus(Integer bid, Long id, Integer status);

    boolean updateLeadsFollowTimeByClientId(Integer bid, Long clientId, LocalDateTime createTime);

    List<Leads> findRelationClientAndNotContactLeads(Integer bid);

    List<Leads> findLeadsByClientIds(Integer bid, List<Long> ids);

    Long rejectPushBackRollbackLeads(LeadsPushBackLog leadsPushBackLog);
}

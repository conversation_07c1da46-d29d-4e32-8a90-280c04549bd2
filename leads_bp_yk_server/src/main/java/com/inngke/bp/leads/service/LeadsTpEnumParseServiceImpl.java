package com.inngke.bp.leads.service;

import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 线索各个平台来源字段映射
 *
 * <AUTHOR>
 * @date 2022/3/2 17:24
 */
@Component
public class LeadsTpEnumParseServiceImpl {
    /**
     * 腾讯广告 线索填写来源 映射
     */
    public final Map<String, LeadsInputSourceEnum> TENCENT_INPUT_SOURCE_ENUM_MAP =
            new HashMap<String, LeadsInputSourceEnum>() {{
                put("LEADS_TYPE_FORM", LeadsInputSourceEnum.FORM_SUBMISSION); //表单预约->表单提交
                put("LEADS_TYPE_ONLINE_CONSULT", LeadsInputSourceEnum.ONLINE_CONSULTATION);//在线咨询->在线咨询
                put("LEADS_TYPE_MAKE_PHONE_CALL", LeadsInputSourceEnum.ORDINARY_TELEPHONE);//普通电话->普通电话
                put("LEADS_TYPE_PHONE", LeadsInputSourceEnum.INTELLIGENT_CONSULTATION);//智能电话->智能电话
                put("LEADS_TYPE_PROMOTION_COUPON", LeadsInputSourceEnum.CARD_VOUCHER);//发券->卡券
                put("LEADS_TYPE_INTELLIGENT_TOOL", LeadsInputSourceEnum.INTELLIGENT_CONSULTATION);//智能咨询->智能咨询
                put("LEADS_TYPE_LOTTERY", LeadsInputSourceEnum.LUCK_DRAW);//抽奖->抽奖
                put("LEADS_TYPE_LANDING_PAGE_CLICK", LeadsInputSourceEnum.OTHER);//落地⻚点击->其他
                put("LEADS_TYPE_ONE_CLICK_AUTHORIZE", LeadsInputSourceEnum.OTHER);//⼀键授权->其他
                put("LEADS_TYPE_PAGE_SCAN_CODE", LeadsInputSourceEnum.CORP_CUSTOMER_SERVICE);//加企业微信客服->加企业微信客服
                put("LEADS_TYPE_PROMOTION_FOLLOW", LeadsInputSourceEnum.OTHER);//公众号关注->其他
            }};

    /**
     * 腾讯广告 线索填写来 源映射
     */
    public final Map<Integer, LeadsChannelEnum> TENCENT_CHANNEL_MAP = new HashMap<Integer, LeadsChannelEnum>() {{

    }};

    /**
     * 飞鱼 线索填写来源 映射
     */
    public final Map<Integer, LeadsInputSourceEnum> FLY_FISH_INPUT_SOURCE_ENUM_MAP =
            new HashMap<Integer, LeadsInputSourceEnum>() {{
                put(0, LeadsInputSourceEnum.FORM_SUBMISSION);// 字节-表单提交->表单提交
                put(1, LeadsInputSourceEnum.ONLINE_CONSULTATION);// 字节-在线咨询->在线咨询
                put(2, LeadsInputSourceEnum.SMART_PHONE);// 字节-智能电话->智能电话
                put(3, LeadsInputSourceEnum.PAGE_CALLBACK);// 字节-网页回呼->网页回呼
                put(4, LeadsInputSourceEnum.CARD_VOUCHER);// 字节-卡券->卡券
                put(5, LeadsInputSourceEnum.LUCK_DRAW);//节-抽奖->抽奖
            }};

    /**
     * 飞鱼 线索渠道来源 映射
     */
    public final Map<Integer, LeadsChannelEnum> FLY_FISH_CHANNEL_MAP = new HashMap<Integer, LeadsChannelEnum>() {{
        put(0, LeadsChannelEnum.ORANGE_BUILD);//字节-橙子建站->橙子建站
        put(1, LeadsChannelEnum.ORANGE_BUILD);//字节-橙子建->橙子建站
        put(2, LeadsChannelEnum.OTHER);//其他渠道-外部导入->其他
        put(5, LeadsChannelEnum.DOU_YIN);//字节-抖音企业号->抖音
        put(7, LeadsChannelEnum.JU_LIANG);//字节-巨量线->巨量线索
        put(8, LeadsChannelEnum.CLOUD_SHOP);//字节-云店->云店
        put(9, LeadsChannelEnum.START_IMG);//字节-星图->星图
        put(10, LeadsChannelEnum.GET_OFF_TREASURE);//字节-获客宝	获客宝
        put(11, LeadsChannelEnum.LIVE_IN_A_SMALL_GANG);//字节-住小帮	住小帮
        put(100, LeadsChannelEnum.TOU_TIAO);//今日头条
        put(200, LeadsChannelEnum.I_XI_GUA);//西瓜视频
    }};

    /**
     * AI客服 渠道来源 映射
     */
    public final Map<String, LeadsChannelEnum> AI_CUSTOMER_CHANNEL_MAP = new HashMap<String, LeadsChannelEnum>() {{
        put("wx", LeadsChannelEnum.THE_PUBLIC);//微信公众号->公众号
        put("wx_kf", LeadsChannelEnum.WE_CHAT_CS);// 微信客服->微信客服
        put("dy", LeadsChannelEnum.DOU_YIN);//抖音->抖音
    }};

    public final Map<String, Integer> TENCENT_GENDER_MAP = new HashMap<String, Integer>() {{
        put("GENDER_TYPE_UNKNOWN", 0);//未知
        put("GENDER_TYPE_MALE", 1);//男性
        put("GENDER_TYPE_FEMALE", 2);//⼥性
    }};


    /*------------------渠道来源--------------------*/

    public LeadsChannelEnum paresAiCustomerChannelEnum(String code) {
        if (!AI_CUSTOMER_CHANNEL_MAP.containsKey(code)) {
            return null;
        }

        return AI_CUSTOMER_CHANNEL_MAP.get(code);
    }

    public LeadsChannelEnum paresFlyFishChannelEnum(Integer code) {
        if (!FLY_FISH_CHANNEL_MAP.containsKey(code)) {
            return null;
        }

        return FLY_FISH_CHANNEL_MAP.get(code);
    }

    public LeadsChannelEnum paresTencentInputChannelEnum() {
        return LeadsChannelEnum.TENCENT_AD;
    }


    /*------------------线索来源--------------------*/

    public LeadsInputSourceEnum paresAiCustomerInputSourceEnum() {
        return null;
    }

    public LeadsInputSourceEnum paresFlyFishInputChannelEnum(Integer code) {
        if (!FLY_FISH_INPUT_SOURCE_ENUM_MAP.containsKey(code)) {
            return null;
        }

        return FLY_FISH_INPUT_SOURCE_ENUM_MAP.get(code);
    }

    public LeadsInputSourceEnum paresTencentInputChannelEnum(String code) {
        if (!TENCENT_INPUT_SOURCE_ENUM_MAP.containsKey(code)) {
            return null;
        }

        return TENCENT_INPUT_SOURCE_ENUM_MAP.get(code);
    }


    /*------------------其他--------------------*/

    public Integer parseTencentGenderEnum(String code) {
        if (!TENCENT_GENDER_MAP.containsKey(code)){
            return null;
        }

        return TENCENT_GENDER_MAP.get(code);
    }

}

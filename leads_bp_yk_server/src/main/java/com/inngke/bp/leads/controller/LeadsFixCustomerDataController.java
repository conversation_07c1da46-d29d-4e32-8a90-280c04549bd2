package com.inngke.bp.leads.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.response.LeadsFollowStatusDto;
import com.inngke.bp.leads.enums.LeadsPreFollowStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsFixDataService;
import com.inngke.bp.user.consts.UserServiceConsts;
import com.inngke.bp.user.dto.request.customer.CustomerMobilesQuery;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerService;
import com.inngke.common.core.config.privatization.PrivatizationDb;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.enums.EnvEnum;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * LeadsFixCustomerDataController
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 23:29
 */
@RestController
@RequestMapping("/api/fix-data")
@Slf4j
public class LeadsFixCustomerDataController {
    private static final Pattern pattern = Pattern.compile("(?<=\\【).+?(?=\\】)");

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerService customerService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LockService lockService;

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Resource
    private LeadsFixDataService leadsFixDataService;


    @PostMapping("/fix-customerId")
    public BaseResponse<Boolean> fixCustomerId(
            @RequestParam String sign,
            @RequestBody BaseIdsRequest request) {
        if (!StringUtils.equals(sign, "8ab87d4fb3600cc53fc0c5c958a250b1")) {
            throw new InngkeServiceException("验签失败");
        }
        String key = UserServiceConsts.APP_ID + ":lock:fixLeadsData";

        Lock lock = lockService.getLock(key, 2000);
        log.info("fix-data：尝试获取锁");

        if (lock == null) {
            log.warn("fix-data：获取锁失败");
            //没拿到锁
            return BaseResponse.error("拿锁失败");
        }
        try {
            log.info("fix-data：获取锁成功");
            if (EnvUtils.getEnv() == EnvEnum.PROD) {
                //生产环境需要执行每个私有部署数据库
                databasePrivatizationService.accept(privatizationDb -> this.fixData(privatizationDb, request.getIds()));
            } else {
                databasePrivatizationService.accept(privatizationDb -> this.fixData(privatizationDb, request.getIds()));
            }
        } finally {
            lock.unlock();
            log.info("fix-data：释放锁");
        }
        return BaseResponse.success(true);
    }

    @PostMapping("/fix-leads-status")
    public BaseResponse<Boolean> fixLeadsStatus() {
        String key = UserServiceConsts.APP_ID + ":lock:fixLeadsData";

        Lock lock = lockService.getLock(key, 2000);
        log.info("fix-data：尝试获取锁");

        if (lock == null) {
            log.warn("fix-data：获取锁失败");
            //没拿到锁
            return BaseResponse.error("拿锁失败");
        }
        try {
            log.info("fix-data：获取锁成功");
            if (EnvUtils.getEnv() == EnvEnum.PROD) {
                //生产环境需要执行每个私有部署数据库
                databasePrivatizationService.accept(privatizationDb -> this.fixLeadsStatusRecord(privatizationDb));
            } else {
                databasePrivatizationService.accept(privatizationDb -> this.fixLeadsStatusRecord(privatizationDb));
            }
        } finally {
            lock.unlock();
            log.info("fix-data：释放锁");
        }
        return BaseResponse.success(true);
    }

    @PostMapping("/fix-leads-contact-follow")
    public BaseResponse<Integer> fixLeadsContactFollow() {
        if (EnvUtils.getEnv() == EnvEnum.PROD) {
            //生产环境需要执行每个私有部署数据库
            databasePrivatizationService.accept(privatizationDb -> {
                Integer bid = privatizationDb.getBids().iterator().next();
                leadsFixDataService.fixLeadsContactFollow(bid);
            });
        } else {
            databasePrivatizationService.accept(privatizationDb -> {
                Integer bid = privatizationDb.getBids().iterator().next();
                leadsFixDataService.fixLeadsContactFollow(bid);
            });
        }

        return null;
    }

    @PostMapping("/fix-leads-push-back-log")
    public BaseResponse<Integer> fixLeadsPushBackLog() {
        if (EnvUtils.getEnv() == EnvEnum.PROD) {
            //生产环境需要执行每个私有部署数据库
            databasePrivatizationService.accept(privatizationDb -> {
                Integer bid = privatizationDb.getBids().iterator().next();
                leadsFixDataService.fixLeadsPushBackLog(bid);
            });
        } else {
            databasePrivatizationService.accept(privatizationDb -> {
                Integer bid = privatizationDb.getBids().iterator().next();
                leadsFixDataService.fixLeadsPushBackLog(bid);
            });
        }

        return null;
    }

    private BaseResponse<Integer> fixData(PrivatizationDb privatizationDb, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("ids is empty");
            return BaseResponse.error();
        }
        String code = privatizationDb.getCode();
        log.info("dbCode->{}", code);
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query().in(Leads.ID, ids)
                        .in(!CollectionUtils.isEmpty(privatizationDb.getBids()) && !privatizationDb.getBids().contains(0), Leads.BID, privatizationDb.getBids()));
        if (CollectionUtils.isEmpty(leadsList)) {
            log.info("没有需要修复的数据");
            return BaseResponse.success(0);
        }
        log.info("需要修复的数据量：{}", leadsList.size());

        Map<Integer, List<Leads>> leadsBidMap = leadsList.stream().collect(Collectors.groupingBy(Leads::getBid));
        leadsBidMap.forEach((bid, groupLeadsList) -> {
            Set<String> mobileList = groupLeadsList.stream().filter(item -> StringUtils.isNotBlank(item.getMobile())).map(Leads::getMobile).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(mobileList)) {
                log.info("mobileList is empty");
                return;
            }
            Map<String, CustomerDto> mobileCustomerMap = getCustomerByMobiles(bid, mobileList);
            groupLeadsList.forEach(leads -> {
                CustomerDto customerDto = mobileCustomerMap.get(leads.getMobile());
                leads.setCustomerId(Objects.nonNull(customerDto) ? customerDto.getId() : 0L);
//                leads.setCustomerUid(Objects.nonNull(customerDto) ? customerDto.getUid() : 0L);
            });
        });
        List<Leads> correctLeadsList = leadsBidMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        leadsManager.updateBatchById(correctLeadsList);
        return BaseResponse.success(correctLeadsList.size());
    }

    private Map<String, CustomerDto> getCustomerByMobiles(Integer bid, Set<String> mobileSet) {
        CustomerMobilesQuery customerRequest = new CustomerMobilesQuery();
        customerRequest.setBid(bid);
        customerRequest.setMobiles(mobileSet);
        BaseResponse<List<CustomerDto>> response = customerService.getCustomerByMobile(customerRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("根据手机号获取客户信息异常：{}", response);
        }
        return response.getData().stream().collect(Collectors.toMap(CustomerDto::getMobile, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 修复线索状态记录
     */
    private void fixLeadsStatusRecord(PrivatizationDb privatizationDb) {
        log.info("私有库name={}", privatizationDb.getName());
        List<Leads> clientLeads = leadsManager.list(
                Wrappers.<Leads>query()
//                        .in(Leads.STATUS, LeadsStatusEnum.getNonAllocatedLeadsStatus().add(LeadsStatusEnum.PRE_FOLLOW.getStatus()))
                        .isNull(Leads.FOLLOW_STATUSES)
                        .isNull(Leads.KF_FOLLOW_STATUSES)
                        .select(Leads.ID, Leads.BID, Leads.STATUS, Leads.PRE_FOLLOW_STATUS, Leads.PRE_FOLLOW_STAFF_ID)
        );
        log.info("处理客服线索,size={}", clientLeads.size());
        handelGuideLeads(clientLeads, false);
    }

    private void handelGuideLeads(List<Leads> guideleadses, Boolean isKf) {
        if (CollectionUtils.isEmpty(guideleadses)) {
            return;
        }
        List<List<Leads>> partition = Lists.partition(guideleadses, 1000);
        partition.forEach(
                leadses -> {
                    handelClientLeads(leadses);
                }
        );

    }

    private void handelClientLeads(List<Leads> leads) {
        if (CollectionUtils.isEmpty(leads)) {
            return;
        }
        Set<Long> leadsIds = leads.stream().map(Leads::getId).collect(Collectors.toSet());
        Map<Long, List<LeadsFollow>> kffollowMap = Maps.newHashMap();
        Map<Long, List<LeadsFollow>> followMap = Maps.newHashMap();
        List<LeadsFollow> list = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .select(LeadsFollow.LEADS_ID, LeadsFollow.FOLLOW_CONTENT, LeadsFollow.LEADS_STATUS, LeadsFollow.PRE_FOLLOW_STATUS)
        );
        list.stream().forEach(e -> kffollowMap.computeIfAbsent(e.getLeadsId(), leadsId -> Lists.newArrayList()).add(e));
    saveRecord(kffollowMap);
//        handelLeadsFollowContent();
    }

    private void saveRecord(Map<Long, List<LeadsFollow>> followMap) {
        if (CollectionUtils.isEmpty(followMap)){
            return;
        }
        ArrayList<Long> leadsIds = Lists.newArrayList();
        followMap.forEach(
                (k,v)->{
                    leadsIds.addAll(v.stream().map(LeadsFollow::getLeadsId).collect(Collectors.toSet()));
                }
        );
        Map<Long, Leads> leadsMap = leadsManager.list(
                Wrappers.<Leads>query()
                        .in(Leads.ID,leadsIds)
                        .select(Leads.ID,Leads.STATUS,Leads.PRE_FOLLOW_STAFF_ID)
        ).stream().collect(Collectors.toMap(Leads::getId, Function.identity()));

        followMap.forEach(
                (leadsId, contents) -> {
                    Leads newLeads = leadsMap.get(leadsId);
                    if (Objects.isNull(newLeads)) {
                        return;
                    }
                    boolean isKF = newLeads.getStatus().equals(LeadsStatusEnum.PRE_FOLLOW.getStatus())
                            || ((Objects.nonNull(newLeads.getPreFollowStaffId()) && newLeads.getPreFollowStaffId() >0));
                    String allStatusTxt = getAllStatusTxt(contents,isKF);
                    if (org.springframework.util.StringUtils.isEmpty(allStatusTxt)) {
                        return;
                    }
                    if (isKF) {
                        newLeads.setKfFollowStatuses(allStatusTxt);
                    } else {
                        newLeads.setFollowStatuses(allStatusTxt);
                    }
                    leadsMap.put(leadsId, newLeads);
                }
        );
        if (CollectionUtils.isEmpty(leadsMap)) {
            return;
        }
        List<Leads> newLeadsList = leadsMap.values().stream().filter(e -> !org.springframework.util.StringUtils.isEmpty(e.getFollowStatuses())
                || !org.springframework.util.StringUtils.isEmpty(e.getKfFollowStatuses())).collect(Collectors.toList());
        log.info("更新线索id={},是否为客服", leadsMap.keySet());
        if (CollectionUtils.isEmpty(newLeadsList)){
            return;
        }
        leadsManager.updateBatchById(leadsMap.values(),1000);
    }

    private String getAllStatusTxt(List<LeadsFollow> contents, Boolean iskf) {
        Set<Integer> kfStatus = Sets.newHashSet();
        Set<Integer> status = Sets.newHashSet();
        contents.forEach(
                content -> {
                    String followContent = content.getFollowContent();
                    List<String> strings = parseString(followContent);
                    if (iskf) {
                        strings.forEach(txt -> {
                            LeadsPreFollowStatusEnum statusEnum = LeadsPreFollowStatusEnum.parse(txt);
                            if (Objects.isNull(statusEnum)) {
                                return;
                            }
                            if (Lists.newArrayList(1).contains(statusEnum.getStatus())) {
                                return;
                            }
                            kfStatus.add(statusEnum.getStatus());
                        });
                    } else {
                        strings.forEach(txt -> {
                            LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(txt);
                            if (Objects.isNull(statusEnum)) {
                                return;
                            }
                            if (Lists.newArrayList(21, 1, 0, -1, -2, -3, -4, -5).contains(statusEnum.getStatus())) {
                                return;
                            }
                            status.add(statusEnum.getStatus());
                        });
                    }
                }
        );
        if (!CollectionUtils.isEmpty(kfStatus)) {
            return getStatusRecords(kfStatus, iskf);
        }

        if (!CollectionUtils.isEmpty(status)) {
            return getStatusRecords(status, iskf);
        }
        return null;
    }

    private String getStatusRecords(Set<Integer> statuses, Boolean isKf) {
        ArrayList<LeadsFollowStatusDto> leadsFollowStatusDtos = Lists.newArrayList();
        statuses.forEach(
                status -> {
                    LeadsFollowStatusDto leadsFollowStatusDto = new LeadsFollowStatusDto();
                    leadsFollowStatusDto.setId(status);
                    leadsFollowStatusDto.setName(isKf ? LeadsPreFollowStatusEnum.parse(status).getName()
                            : LeadsStatusEnum.parse(status).getName());
                    leadsFollowStatusDtos.add(leadsFollowStatusDto);
                }
        );

        return jsonService.toJson(leadsFollowStatusDtos);
    }

    /**
     * 解析跟进内容
     *
     * @param content
     * @return
     */
    private List<String> parseString(String content) {
        ArrayList<String> params = Lists.newArrayList();
        if (StringUtils.isEmpty(content)) {
            return params;
        }
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            String group = matcher.group();
            params.add(group);
        }
        return params;
    }

    /**
     * 检查是否为客服线索
     *
     * @param status
     * @return
     */
    private boolean checkPreFollowStatus(Integer status) {
        return Objects.nonNull(status) && status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus());
    }


    public static void main(String[] args) {
        Matcher matcher = pattern.matcher("管理员【陈祺】将线索状态由【待联系】修改为【已成交】");
        while (matcher.find()) {
            System.out.println(matcher.group());
        }
    }
}

package com.inngke.bp.leads.service.enums;

/**
 * LeadsNotifyMessageEnum
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/16 17:58
 */
public enum LeadsNotifyMessageEnum {

    DISTRIBUTE_LEADS("分配线索", "leads_distribute_follow_notify", "Clue/Clue/myClue/myClue"),

    ADMIN_NOTIFY_NOT_CONTACT_GUIDES("管理员通知所有线索待联系的导购", "admin_notify_not_contact_guides", "Clue/Clue/myClue/myClue"),
    ADMIN_ADD_LEADS_FOLLOW_NOTIFY("管理员添加线索跟进通知", "admin_add_leads_follow_notify", "Clue/Clue/myClue/myClue"),
    REPORT_LEADS("报备客户新增线索", "leads_follow_notification", "Clue/Clue/myClue/myClue"),
    LEADS_ORDER_REFUND_NOTIFY("订单类线索退款通知", "reminder_refund_notification", "Clue/Clue/myClue/myClue"),

    REJECT_LEADS_PUSH_BACK("驳回线索退回通知", "reject_leads_push_back", "Clue/Clue/myClue/myClue"),
    OTHERS_HELP_CONTACT_LEADS("其他人帮助联系线索通知导购", "others_help_contact_leads", "Clue/Clue/myClue/myClue");

    private final String name;

    private final String templateCode;

    private final String path;

    LeadsNotifyMessageEnum(String name, String templateCode, String path) {
        this.name = name;
        this.templateCode = templateCode;
        this.path = path;
    }

    public String getName() {
        return name;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public String getPath() {
        return path;
    }
}

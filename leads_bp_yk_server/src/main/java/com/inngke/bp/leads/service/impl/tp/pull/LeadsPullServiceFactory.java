package com.inngke.bp.leads.service.impl.tp.pull;

import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.bp.leads.service.LeadsPullService;
import com.inngke.bp.leads.service.LeadsTpOauthTypeService;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 15:07
 */
@Service
public class LeadsPullServiceFactory {

    @Autowired
    private List<LeadsPullService> leadsPullServiceList;

    public LeadsPullService getInstance(Integer type){
        LeadsTpTypeEnum leadsTpTypeEnum = LeadsTpTypeEnum.parse(type);

        if (leadsTpTypeEnum == null){
            throw new InngkeServiceException("get leadsPullService Fail type is null");
        }

        for (LeadsPullService leadsPullService : leadsPullServiceList) {
            if (leadsPullService.getType().equals(leadsTpTypeEnum)){
                return leadsPullService;
            }
        }

        throw new InngkeServiceException("get leadsPullService Fail type not implemented");
    }
}

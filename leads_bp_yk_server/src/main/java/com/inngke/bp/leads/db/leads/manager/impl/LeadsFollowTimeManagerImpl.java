/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.db.leads.dao.LeadsFollowTimeDao;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowTimeManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线索跟进数据报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsFollowTimeManagerImpl extends ServiceImpl<LeadsFollowTimeDao, LeadsFollowTime> implements LeadsFollowTimeManager {

}

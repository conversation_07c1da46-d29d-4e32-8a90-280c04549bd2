/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 线索配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户ID，即bid
     */
    private Integer id;

    /**
     * 线索模块是否开启：0=未开启 1=开启
     */
    private Boolean enable;

    /**
     * 线索下发方式：0=手工下发 1=按规则自动下发
     */
    private Integer distributeType;

    /**
     * 线索是否允许转发给其它员工
     */
    private Boolean forwardEnable;

    /**
     * 是否允许线索回退
     */
    private Boolean pushbackEnable;

    /**
     * 重复线索分配方式：0=分配给正在跟进的导购；1=分配给区域接收人
     */
    private Integer repeatDistributionWay;

    /**
     * 是否开启客服接待流程 false-未开启 true-开启
     */
    private Boolean preFollowEnable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 线索去重开关（1=打开 0=关闭）
     */
    private Integer openRepeatRemove;

    /**
     * 线索去重天数
     */
    private Integer repeatRemoveDay;

    /**
     * 线索退回原因是否必填1必填,0非必填
     */
    private Boolean pushbackReason;

    /**
     * 线索退回必填
     */
    private Boolean pushbackImage;

    /**
     * 线索标记为''无效线索''是否必填原因
     */
    private Boolean leadsInvalidReason;

    /**
     * 重复线索显示
     */
    private Boolean displayRepeat;

    private Boolean textMessageNotify;

    private String leadsFollowNotify;


    public static final String ID = "id";

    public static final String ENABLE = "enable";

    public static final String DISTRIBUTE_TYPE = "distribute_type";

    public static final String FORWARD_ENABLE = "forward_enable";

    public static final String PUSHBACK_ENABLE = "pushback_enable";

    public static final String REPEAT_DISTRIBUTION_WAY = "repeat_distribution_way";

    public static final String PUSHBACK_REASON = "pushback_reason";

    public static final String PUSHBACK_IMAGE = "pushback_image";

    public static final String LEADS_INVALID_REASON = "leads_invalid_reason";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String DISPLAY_REPEAT = "display_repeat";
}

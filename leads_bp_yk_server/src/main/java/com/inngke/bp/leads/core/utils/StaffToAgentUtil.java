package com.inngke.bp.leads.core.utils;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.common.dto.request.BaseIdsRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/24 15:24
 **/
@Component
@Slf4j
public class StaffToAgentUtil {

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    /**
     * 将线索集合中的分配人员id和经销商id去查询经销商
     * @param leadsList
     * @param bid
     * @return
     */
    public Map<Long, Long> getStaffAndAgent(List<Leads> leadsList, Integer bid) {
        List<Long> staffIds = leadsList.stream()
                .map(Leads::getDistributeStaffId)
                .filter(distributeStaffId -> distributeStaffId != 0L)
                .collect(Collectors.toList());

        return getStaffAndAgentByStaffIds(staffIds, bid);
    }

    /**
     *
     * @param staffIds
     * @param bid
     * @return 返回一个员工id为key,经销商id为value的map
     */
    public Map<Long, Long> getStaffAndAgentByStaffIds(List<Long> staffIds, Integer bid) {
        if (CollectionUtils.isEmpty(staffIds)){
            return Maps.newHashMap();
        }
        //获取id对应的经销商id键值对map,这个dubbo返回的数据中key和val都是不为null的
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(staffIds);
        //获取id对应的经销商id键值对map
        List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtoList = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        if (Objects.isNull(staffIdAndAgentIdDtoList) || staffIdAndAgentIdDtoList.isEmpty()) {
            return Maps.newHashMapWithExpectedSize(1);
        }


        return staffIdAndAgentIdDtoList
                .stream()
                .collect(Collectors.groupingBy(StaffIdAndAgentIdDto::getId, Collectors.collectingAndThen(Collectors.toList(), t -> t.get(0).getAgentId())));
    }

}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.client.dto.request.client.GetClientFollowListRequest;
import com.inngke.bp.client.dto.response.client.ClientFollowItemDto;
import com.inngke.bp.client.dto.response.common.LostReasonItemDto;
import com.inngke.bp.client.service.ClientService;
import com.inngke.bp.distribute.dto.request.DistributorCustomerSaveDocRequest;
import com.inngke.bp.distribute.dto.response.DistributorLeadDto;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.converter.LeadsFollowConverter;
import com.inngke.bp.leads.core.utils.LeadsCommonUtil;
import com.inngke.bp.leads.core.utils.LeadsFollowContentUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.*;
import com.inngke.bp.leads.dto.RewardEventDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsFollowDto;
import com.inngke.bp.leads.dto.response.LeadsFollowSimpleDto;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsLostReasonEnum;
import com.inngke.bp.leads.enums.LeadsPreFollowStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.notify.context.AdminCreateLeadsFollowMessageContext;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.bp.leads.service.LeadsFollowService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.leads.service.message.MessageManagerService;
import com.inngke.bp.leads.service.message.context.FollowPlanContext;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.plus.enums.RewardEventEnum;
import com.inngke.bp.store.dto.request.StoreOrderLeadsOrderTypeListRequest;
import com.inngke.bp.store.dto.response.StoreOrderTypeDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.bp.user.dto.request.customer.CustomerInfosGetByStaffIdsRequest;
import com.inngke.bp.user.dto.request.staff.QyUserTokenGetRequest;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.bp.user.service.QyWxSelfAppUserService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidOptPageRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.enums.ErrorCode;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.ValidateUtils;
import com.inngke.common.wx.core.utils.QyWxMessageBuilder;
import com.inngke.common.wx.core.utils.WxTemplateMessageBuilder;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.dto.response.WxMpSimpleInfoDto;
import com.inngke.ip.common.service.MqService;
import com.inngke.ip.common.service.wx.WxMpCommonService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.enums.LeadsStatusEnum.getNonAllocatedLeadsStatus;

/**
 * <AUTHOR>
 * @since 2021/9/8 6:21 PM
 */
@DubboService(version = "1.0.0")
public class LeadsFollowServiceImpl implements LeadsFollowService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsFollowServiceImpl.class);

    private static final String FOLLOW_TITLE = "添加跟进：";

    private static final String INVALID_FOLLOW_TITLE = "详细说明：";

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsLogManager leadsLogManager;

    // @Autowired
    // private LeadsStaffCache leadsStaffCache;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsCallLogManager leadsCallLogManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private DistributorServiceClientForLeads distributorServiceClientForLeads;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store_bp_yk:}")
    private StoreOrderService storeOrderService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerGetService customerGetService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private WxMpCommonService wxMpCommonService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private QyWxSelfAppUserService qyWxSelfAppUserService;

    @Autowired
    private ClientGetClientForLeads clientGetClientForLeads;

    @Autowired
    private MessageManagerService messageManagerService;

    private static final String MESSAGE_TEMPLATE = "OPENTM409367506";

    public final static String CACHE_LEADS_FOLLOW = LeadsServiceConsts.LOCK_PREFIX + "cache:leadsFollow";

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsInvalidReasonManager leadsInvalidReasonManager;

    @Autowired
    private ClientCommonClientForLeads clientCommonClientForLeads;

    @Autowired
    private LockService lockService;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Autowired
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.leads_bp_yk:}")
    private ClientService clientService;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    private static final String LEADS_FOLLOW_LOCK_KEY = LeadsServiceConsts.APP_ID + ":lock:leadsFollow";


    /**
     * 创建线索跟进
     *
     * @param request 跟进请求
     * @return 是否创建成功
     */
    @Override
    public BaseResponse<Boolean> createFollow(LeadsFollowCreateRequest request) {
        Lock lock = lockService.getLock(LEADS_FOLLOW_LOCK_KEY, 60);
        if (Objects.nonNull(request.getReminderTime()) && com.inngke.common.utils.StringUtils.isBlank(request.getPlanContent())) {
            request.setPlanContent("-");
        }

        if (lock == null) {
            return BaseResponse.error("线索正在操作中，请稍后再试");
        }

        try {
            Long staffId = request.getStaffId();
            //获取当前线索
            Leads leads = getLeadsInfo(request.getBid(), request.getId());
            if (leads == null) {
                return BaseResponse.error("添加跟进失败，无法找到对应的线索");
            }

            // 判断线索是否已经被回收
            if (LeadsStatusEnum.delStatus().contains(leads.getStatus())) {
                return BaseResponse.error(ErrorCode.NOT_FOUNT.getCode(), "线索已不存在");
            }

            checkLeadsStatusUpdated(request.getStatus(), leads);
            //先判断当前订单状
            Integer status = request.getStatus();
            String mobile = leads.getMobile();
            //如果状态为已完成判断当前员工是否有对应的开单记录
            if (status.equals(LeadsStatusEnum.TRADED.getStatus()) && request.isCheckTradedCount()) {
                //获取当前导购当前的开单数
                StoreOrderLeadsOrderTypeListRequest storeOrderLeadsOrderTypeListRequest = new StoreOrderLeadsOrderTypeListRequest();
                storeOrderLeadsOrderTypeListRequest.setBid(request.getBid());
                storeOrderLeadsOrderTypeListRequest.setOperatorId(request.getOperatorId());
                storeOrderLeadsOrderTypeListRequest.setLeadsId(request.getId());
                BaseResponse<List<StoreOrderTypeDto>> baseResponse = storeOrderService.getLeadsOrderTypeList(storeOrderLeadsOrderTypeListRequest);
                if (baseResponse.getCode() != 0) {
                    return BaseResponse.error("添加跟进失败");
                }
                List<StoreOrderTypeDto> orderTypeDtoList = baseResponse.getData().stream().filter(storeOrderTypeDto -> storeOrderTypeDto.getType().equals(2)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orderTypeDtoList)) {
                    return BaseResponse.error("需要为客户添加开单记录才可以将线索状态，更改为“" + LeadsStatusEnum.TRADED.getName() + "”");
                }
            }
            if (Objects.equals(leads.getStatus(),LeadsStatusEnum.DISTRIBUTED.getStatus())
                    && LeadsStatusEnum.getContactLeadsStatus().contains(request.getStatus())) {
                LeadsFollowCreateRequest leadsFollowCreateRequest = new LeadsFollowCreateRequest();
                BeanUtils.copyProperties(request, leadsFollowCreateRequest);
                leadsFollowCreateRequest.setStatus(LeadsStatusEnum.CONTACTED.getStatus());
                leadsFollowCreateRequest.setContent(InngkeAppConst.EMPTY_STR);

                save(leadsFollowCreateRequest, leads,LocalDateTime.now().minusSeconds(1L));
            }

            Boolean isSave = save(request, leads, null);
            //异步更新ES文档
            //当导购将转介绍客户的状态改为无效线索时，发送公众号消息通知给合伙人
            //if (leads.getChannel().equals(6) && request.getStatus().equals(-3)) {
            AsyncUtils.runAsync(() -> {
                LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
                esRequest.setBid(request.getBid());
                esRequest.setIds(Lists.newArrayList(request.getId()));
                // 这个参数应该是判断是否需要发送合伙人通知
                boolean notifyPartner = leads.getChannel().equals(LeadsChannelEnum.REPORT.getChannel()) && request.getStatus().equals(LeadsStatusEnum.INVALID.getStatus());
                esRequest.setNotifyPartner(notifyPartner);
                esRequest.setLeadsName(leads.getName());
                leadsEsService.updateDocs(esRequest);
            });
            //}
            //同步报备客户线索状态
            AsyncUtils.runAsync(() -> updateDistributorLeadsStatus(leads));

            if (!isSave) {
                return BaseResponse.error("添加跟进失败");
            }

            //发送公众号/企业微信消息
            if (!getNonAllocatedLeadsStatus().contains(leads.getStatus()) && !leads.getDistributeStaffId().equals(request.getStaffId())) {
                AdminCreateLeadsFollowMessageContext notifyContext = AdminCreateLeadsFollowMessageContext.init(request.getBid(), leads.getId(), request.getContent());
                TemplateMessageContentBuilder<AdminCreateLeadsFollowMessageContext> builder = templateMessageBuilderFactory.getBuilder(notifyContext);
                BaseResponse baseResponse = builder.sendMessage(notifyContext);
                logger.info("发送模版消息结果：{}", jsonService.toJson(baseResponse));
            }

            if (!StringUtils.isEmpty(request.getPlanContent())) {
                //发送跟进计划消息
                FollowPlanContext msgCtx = new FollowPlanContext(request.getBid());
                msgCtx.setTargetSid(leads.getDistributeStaffId());
                msgCtx.setLeads(leads);
                msgCtx.setDeliverAt(DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(request.getReminderTime())));
                msgCtx.setPlanContent(request.getPlanContent());
                messageManagerService.send(msgCtx);
            }
            if (leads.getClientId() > 0L) {
                AsyncUtils.runAsync(() -> {
                    // 更新client索引
                    BaseIdRequest clientBuildRequest = new BaseIdRequest();
                    clientBuildRequest.setId(leads.getClientId());
                    clientBuildRequest.setBid(request.getBid());
                    clientService.buildClientDocByClientId(clientBuildRequest);
                });
            }

        } finally {
            lock.unlock();
        }

        return BaseResponse.success(true);
    }

    private Boolean save(LeadsFollowCreateRequest request, Leads leads, LocalDateTime createTime) {
        Integer status = request.getStatus();
        //        handelFollowStatus(request, notifyPartner, leads);
        LeadsFollow leadsFollow = LeadsRequestToLeadsFollow(request);
        //设置
        //设置跟进内容
        String followContent = getFollowContent(request, leads);
        leadsFollow.setFollowContent(followContent);
        //发送报备客户状态修改，触发奖励事件,报备有效时触发 || 导购更改线索状态为“进店”或“量尺”时触发（其中一个标记均可触发）
        if ((!status.equals(leads.getStatus()) &&
                (status.equals(LeadsStatusEnum.STORED.getStatus())
                        || status.equals(LeadsStatusEnum.MEASURED.getStatus()) || status.equals(LeadsStatusEnum.INTENT.getStatus())))) {
            //获取分销员报备客户信息
            handelRewardEventMq(status, leads);
        }
        //更新线索状态
        leads.setStatus(request.getStatus());

        if (Objects.nonNull(createTime)) {
            leadsFollow.setCreateTime(createTime);
        }
        //有跟进计划将跟进计划内容添加到跟进内容中
        if (!StringUtils.isEmpty(request.getPlanContent())){
            addPlanContentToFollow(leadsFollow,request);
        }
        //保存跟进内容|| 更新当前线索状态+,如果该跟进修改了客户的报备状态，也同步进行修改
        Boolean isSave = leadsFollowManager.createFollow(leads, leadsFollow, request.getStatus());
        // 添加线索跟进Mq
        if (Boolean.TRUE.equals(isSave)) {
            mqServiceForLeads.sendLeadsFollowMq(leadsFollow);
        }

        if (Objects.nonNull(request.getStatus())){
            UpdateLeadsStatusRecordRequest updateLeadsStatusRecordRequest = new UpdateLeadsStatusRecordRequest();
            updateLeadsStatusRecordRequest.setStatus(request.getStatus());
            updateLeadsStatusRecordRequest.setLeadsId(request.getId());
            updateLeadsStatusRecordRequest.setBid(request.getBid());
            leadsServiceV2.updateLeadsStatusRecord(updateLeadsStatusRecordRequest);
        }
        return isSave;
    }


    private void addPlanContentToFollow(LeadsFollow leadsFollow, LeadsFollowCreateRequest request) {
        leadsFollow.setFollowContent(leadsFollow.getFollowContent() + InngkeAppConst.TURN_LINE
                + "并添加下次跟进计划" + InngkeAppConst.TURN_LINE
                + "提醒时间" + "：" + request.getReminderTime() + InngkeAppConst.TURN_LINE
                + "提醒内容" + "：" + request.getPlanContent());
    }


    private void handelRewardEventMq(Integer leadsStatus, Leads leads) {
        List<DistributorLeadDto> customerIdByLeads = distributorServiceClientForLeads.getCustomerIdByLeads(Lists.newArrayList(leads.getId()), leads.getBid());
        if (CollectionUtils.isEmpty(customerIdByLeads)) {
            return;
        }
        DistributorLeadDto distributorLeadDto = customerIdByLeads.get(0);
        RewardEventDto rewardEventDto = new RewardEventDto();
        rewardEventDto.setEvent(leadsStatus.equals(LeadsStatusEnum.INTENT.getStatus()) ? RewardEventEnum.REPORT_EFFECTIVE.getCode() : RewardEventEnum.MEET_CUSTOMER.getCode());
        rewardEventDto.setLeadsId(leads.getId());
        rewardEventDto.setGuideId(distributorLeadDto.getGuideId());
        rewardEventDto.setDistributorId(distributorLeadDto.getDistributorId());
        rewardEventDto.setBid(leads.getBid());
        sendMQ(leads.getBid(), rewardEventDto);
    }

    private void updateDistributorLeadsStatus(Leads leads) {
        List<DistributorLeadDto> customerLeads = distributorServiceClientForLeads.getCustomerIdByLeads(Lists.newArrayList(leads.getId()), leads.getBid());
        if (CollectionUtils.isEmpty(customerLeads)) {
            return;
        }
        DistributorLeadDto distributorLeadDto = customerLeads.get(0);
        //先查询报备客户线索
        DistributorCustomerSaveDocRequest distributorCustomerSaveDocRequest = new DistributorCustomerSaveDocRequest();
        distributorCustomerSaveDocRequest.setLeadsIds(Lists.newArrayList(distributorLeadDto.getLeadsId()));
        distributorCustomerSaveDocRequest.setBid(distributorLeadDto.getBid());
        Boolean update = distributorServiceClientForLeads.saveDocs(distributorCustomerSaveDocRequest);
        logger.info("更新客户线索，status={}", update);
    }

    @Override
    public BaseResponse<Boolean> createFollowForOpt(LeadsFollowCreateRequest request) {
        Integer bid = request.getBid();
        String content = request.getContent();
        //获取当前线索
        Leads leads = getLeadsInfo(bid, request.getId());
        if (leads == null) {
            return BaseResponse.error("添加跟进失败，无法找到对应的线索");
        }
        if (request.getStatus() != null
                && request.getStatus().equals(LeadsStatusEnum.LOST.getStatus())
                && Objects.isNull(request.getClientLostReasonType())
                && Objects.isNull(request.getReasonId())
                && ObjectUtils.isEmpty(request.getReason())) {
            return BaseResponse.error("流失原因不能为空");
        }
        checkLeadsStatusUpdated(request.getStatus(), leads);
        LeadsFollow leadsFollow = installLeadsFollow(request, leads);
        boolean isSave = leadsFollowManager.save(leadsFollow);
        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
        if (!isSave) {
            return BaseResponse.error("添加跟进失败");
        }
        // 发送线索跟进通知Mq
        mqServiceForLeads.sendLeadsFollowMq(leadsFollow);

        // 判断是否24小时内联系
        if (request.getStatus() != null && (request.getStatus().equals(LeadsStatusEnum.CONTACTED.getStatus()))) {
            LocalDateTime distributeTime = leads.getDistributeTime() == null ? leads.getDistributeFollowTime() : leads.getDistributeTime();
            LocalDateTime now = LocalDateTime.now();
            long hours = Duration.between(distributeTime, now).toHours();
            if (hours < 24) {
                leads.setContactIn24(1);
            }
        }
        //更新线索的最新跟进记录
        UpdateWrapper<Leads> updateWrapper = Wrappers.<Leads>update()
                .eq(Leads.BID, bid)
                .eq(Leads.ID, leads.getId());
        if (notPreFollowStatus(leads.getStatus())) {
            updateWrapper.set(Leads.STATUS, request.getStatus());
        }
        if (preFollowStatus(leads.getStatus())) {
            updateWrapper.set(Leads.PRE_FOLLOW_STATUS, request.getStatus());
        }
        leadsManager.update(
                updateWrapper
                        .set(Leads.LAST_FOLLOW_ID, leadsFollow.getId())
                        .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
        );
        //发送公众号/企业微信消息
        if (!getNonAllocatedLeadsStatus().contains(leads.getStatus()) && !leads.getDistributeStaffId().equals(request.getStaffId())) {
//            sendMessage(bid, leads, content);
            AdminCreateLeadsFollowMessageContext notifyContext = AdminCreateLeadsFollowMessageContext.init(bid, leads.getId(), content);
            TemplateMessageContentBuilder<AdminCreateLeadsFollowMessageContext> builder = templateMessageBuilderFactory.getBuilder(notifyContext);
            BaseResponse baseResponse = builder.sendMessage(notifyContext);
            logger.info("发送模版消息结果：{}", jsonService.toJson(baseResponse));
        }

        //更新线索状态记录
        if (Objects.nonNull(request.getStatus())){
            UpdateLeadsStatusRecordRequest updateLeadsStatusRecordRequest = new UpdateLeadsStatusRecordRequest();
            updateLeadsStatusRecordRequest.setStatus(request.getStatus());
            updateLeadsStatusRecordRequest.setLeadsId(request.getId());
            updateLeadsStatusRecordRequest.setBid(request.getBid());
            leadsServiceV2.updateLeadsStatusRecord(updateLeadsStatusRecordRequest);
        }

        //更新es
        LeadsUpdateRequest esRequest = new LeadsUpdateRequest();
        esRequest.setBid(request.getBid());
        esRequest.setIds(Lists.newArrayList(request.getId()));
        leadsEsService.updateDocs(esRequest);

        if (leads.getClientId() > 0L) {
            AsyncUtils.runAsync(() -> {
                // 更新client索引
                BaseIdRequest clientBuildRequest = new BaseIdRequest();
                clientBuildRequest.setId(leads.getClientId());
                clientBuildRequest.setBid(request.getBid());
                clientService.buildClientDocByClientId(clientBuildRequest);
            });
        }

        return BaseResponse.success(true);
    }


    private boolean preFollowStatus(Integer status) {
        return status != null && status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus());
    }

    private boolean notPreFollowStatus(Integer status) {
        return status != null && !status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus());
    }

    private void sendMessage(Integer bid, Leads leads, String content) {
        CustomerInfosGetByStaffIdsRequest customerInfosGetByStaffIdsRequest = new CustomerInfosGetByStaffIdsRequest();
        Long staffId = leads.getDistributeStaffId();
        String mobile = InngkeAppConst.EMPTY_STR;
        if (staffId == null) {
            logger.warn("后台管理员添加跟进记录发送公众号消息失败：失败原因：当前线索无跟进导购信息");
            return;
        }
        customerInfosGetByStaffIdsRequest.setStaffIds(Sets.newHashSet(staffId));
        customerInfosGetByStaffIdsRequest.setBid(bid);
        BaseResponse<List<CustomerDto>> resp = customerGetService.getCustomerInfoByStaffIds(customerInfosGetByStaffIdsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(resp)) {
            logger.info("后台管理员添加跟进记录发送公众号消息失败：失败原因，无法获取当前线索跟进导购对应的导购信息");
            return;
        }
        if (CollectionUtils.isEmpty(resp.getData())) {
            logger.info("后台管理员添加跟进记录发送公众号消息失败：失败原因，无法获取当前线索跟进导购对应的导购信息");
            return;
        }
        List<CustomerDto> data = resp.getData();
        CustomerDto customerDto = data.get(0);
        String wxPubOpenId = customerDto.getWxPubOpenId();

        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(bid);
        staffListRequest.setCustomerId(customerDto.getId());
        List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);
        String qyUserId = "";
        if (!CollectionUtils.isEmpty(staffList)) {
            qyUserId = staffList.get(0).getQyUserId();
        }

        BaseBidRequest req = new BaseBidRequest();
        req.setBid(bid);
        BaseResponse<WxMpSimpleInfoDto> wxMpAppInfoResp = wxMpCommonService.getWxMpAppInfo(req);
        if (!BaseResponse.responseSuccessWithNonNullData(wxMpAppInfoResp)) {
            logger.info("后台管理员添加跟进记录发送公众号消息失败：失败原因，无法当前商户的小程序appId");
            return;
        }
        int length = content.length();
        if (length > 25) {
            content = content.substring(0, 24) + "...";
        }

        WxMpSimpleInfoDto wxMpSimpleInfoDto = wxMpAppInfoResp.getData();

        if (!StringUtils.isEmpty(leads.getMobile()) && ValidateUtils.isMobile(leads.getMobile())) {
            mobile = leads.getMobile().replaceFirst(leads.getMobile().substring(3, 7), "****");
        }
        MqSendRequest messageSendRequest;
        if (StringUtils.isEmpty(wxPubOpenId) && StringUtils.isEmpty(qyUserId)) {
            logger.warn("员工[staffId={}]未绑定公众号/企业微信", staffId);
            return;
        } else if (!StringUtils.isEmpty(qyUserId)) {
            messageSendRequest = installQyUserMqSendRequest(bid, qyUserId, wxMpSimpleInfoDto, leads, content, mobile);
        } else {
            messageSendRequest = installPubMessageMqSendRequest(bid, wxPubOpenId, wxMpSimpleInfoDto, leads, content, mobile);
        }
        BaseResponse<Boolean> response = mqService.send(messageSendRequest);
        if (!BaseResponse.responseSuccess(response)) {
            String mqJson = jsonService.toJson(messageSendRequest);
            logger.warn("后台管理员添加跟进时发送导购线索跟进消息失败，request:{}", mqJson);
        }
    }

    private MqSendRequest installQyUserMqSendRequest(Integer bid, String qyUserId, WxMpSimpleInfoDto wxMpSimpleInfoDto, Leads leads, String content, String mobile) {
        QyUserTokenGetRequest qyUserTokenGetRequest = new QyUserTokenGetRequest();
        qyUserTokenGetRequest.setQyUserId(qyUserId);
        qyUserTokenGetRequest.setBid(bid);
        BaseResponse<String> userToken = qyWxSelfAppUserService.getUserToken(qyUserTokenGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(userToken)) {
            logger.error("发送企业微信失败，获取user对应的token失败");
            return null;
        }
        String pagePath = "Clue/Clue/myClue/myClue?activeTab=follow&staffId=" + leads.getDistributeStaffId() + "&id=" + leads.getId();
        QyWxMessageBuilder qyWxMessageBuilder = QyWxMessageBuilder.create();
        return qyWxMessageBuilder
                .setMsgType("miniprogram_notice")
                .setAppId(wxMpSimpleInfoDto.getAppId())
                .setTitle("线索处理提醒")
                .setPage(pagePath)
                .setToUser(qyUserId)
                .setToken(userToken.getData())
                .putContentItem("姓名", leads.getName())
                .putContentItem("手机", mobile)
                .putContentItem("提交时间", DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                .putContentItem("提交内容", content)
                .putContentItem("备注", "点击本条消息进入小程序查看线索详情")
                .getMqSendRequest(bid, 0L, QyWxMessageBuilder.QY_WX);
    }

    private MqSendRequest installPubMessageMqSendRequest(Integer bid, String wxPubOpenId, WxMpSimpleInfoDto wxMpSimpleInfoDto, Leads leads, String content, String mobile) {
        String pagePath = "Clue/Clue/myClue/myClue?activeTab=follow&staffId=" + leads.getDistributeStaffId() + "&id=" + leads.getId();
        return WxTemplateMessageBuilder.create(wxPubOpenId)
                .setMessageTemplateShortId(MESSAGE_TEMPLATE)
                .setMpLink(wxMpSimpleInfoDto.getAppId(), pagePath)
                .putData("first", "管理员添加了新的跟进内容")
                .putData("keyword1", leads.getName())
                .putData("keyword2", mobile)
                .putData("keyword3", DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                .putData("keyword4", content)
                .getMqSendRequest(bid, 0L, WxTemplateMessageBuilder.STR_WX_PUB);
    }

    private LeadsFollow installLeadsFollow(LeadsFollowCreateRequest request, Leads leads) {
        Integer status = checkPreFollowStatus(leads.getStatus()) ? leads.getPreFollowStatus() : leads.getStatus();
        String followContent = getFollowContent(request, leads);
        request.setContent(followContent);

        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(request.getBid());
        leadsFollow.setLeadsId(request.getId());
        leadsFollow.setFollowType(LeadsFollowTypeEnum.MANUAL.getCode());
        leadsFollow.setFollowContent(request.getContent());
        leadsFollow.setUserId(request.getOperatorId());
        if (notPreFollowStatus(request.getStatus())) {
            leadsFollow.setLeadsStatus(Optional.ofNullable(request.getStatus()).orElse(status));
        }
        if (preFollowStatus(request.getStatus())) {
            leadsFollow.setPreFollowStatus(Optional.ofNullable(request.getStatus()).orElse(leads.getPreFollowStatus()));
        }
        leadsFollow.setCreateTime(LocalDateTime.now());
        leadsFollow.setClientLostReasonType(request.getClientLostReasonType());
        if (!ObjectUtils.isEmpty(request.getReason())) {
            leadsFollow.setReason(request.getReason());
            leadsFollow.setReasonId(request.getReasonId());
        }else if (!ObjectUtils.isEmpty(request.getReasonId())) {
            leadsFollow.setReasonId(request.getReasonId());
            if (LeadsStatusEnum.LOST.getStatus() == request.getStatus()) {
                LostReasonItemDto lostReasonItemDto = clientCommonClientForLeads.getLostReasonsById(request.getBid(), request.getReasonId());
                if (ObjectUtils.isEmpty(lostReasonItemDto)) {
                    logger.warn("查询流失原因失败：id={}",request.getReasonId());
                }
                leadsFollow.setReason(lostReasonItemDto.getReason());
            }
            if (LeadsStatusEnum.INVALID.getStatus() == request.getStatus()) {
                leadsFollow.setReason(leadsInvalidReasonManager.getById(request.getBid(),request.getReasonId()).getReason());
            }

        }
        leadsFollow.setStaffId(request.getStaffId());
        if (!CollectionUtils.isEmpty(request.getImages())) {
            String imagesStr = Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(request.getImages());
            leadsFollow.setFollowImages(imagesStr);
        }
        return leadsFollow;
    }

    /**
     * 检查是否客服接待状态
     * @param status
     * @return
     */
    private boolean checkPreFollowStatus(Integer status) {
        return status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus());
    }

    private String buildDetailFollowContent(LeadsFollowCreateRequest request, Integer status, boolean isPreFollow) {
        String content = InngkeAppConst.EMPTY_STR;
        if (status != null && request.getStatus() != null && !status.equals(request.getStatus())) {
            String oldStatus = isPreFollow ? LeadsPreFollowStatusEnum.parse(status).getName() : LeadsStatusEnum.parse(status).getName();
            String newStatus = isPreFollow ? LeadsPreFollowStatusEnum.parse(request.getStatus()).getName() : LeadsStatusEnum.parse(request.getStatus()).getName();
            String followTitle;
            if (request.getStatus().equals(LeadsStatusEnum.INVALID.getStatus())
                    || request.getStatus().equals(LeadsStatusEnum.LOST.getStatus())
                    || request.getStatus().equals(LeadsStatusEnum.PUSH_BACK.getStatus())) {
                followTitle = INVALID_FOLLOW_TITLE;
            } else {
                followTitle = FOLLOW_TITLE;
            }
            content = "将线索状态由【" + oldStatus + "】"
                    + "修改为【" + newStatus + "】";
            if (!ObjectUtils.isEmpty(request.getContent())) {
                content += "\n" +
                        followTitle + (StringUtils.isEmpty(request.getContent()) ? InngkeAppConst.EMPTY_STR : request.getContent());
            }
        } else {
            content = FOLLOW_TITLE + request.getContent();
        }
        return content;
    }

    private String buildLostDetailFollowContent(LeadsFollowCreateRequest request, Integer status, boolean isPreFollow) {
        String content = InngkeAppConst.EMPTY_STR;
        String reason = !ObjectUtils.isEmpty(request.getClientLostReasonType()) ?
                LeadsLostReasonEnum.getNameByType(request.getClientLostReasonType()) : request.getReason();
        if (status != null && request.getStatus() != null && !status.equals(request.getStatus())) {
            String oldStatus = isPreFollow ? LeadsPreFollowStatusEnum.parse(status).getName() : LeadsStatusEnum.parse(status).getName();
            String newStatus = isPreFollow ? LeadsPreFollowStatusEnum.parse(request.getStatus()).getName() : LeadsStatusEnum.parse(request.getStatus()).getName();
            content = "将线索状态由【" + oldStatus + "】修改为【" + newStatus + "】";
            if (!ObjectUtils.isEmpty(request.getContent())) {
                content += "\n" + "详细说明：" + request.getContent();
            }

        } else if (request.getStatus() != null && request.getStatus().equals(LeadsStatusEnum.LOST.getStatus())) {
            if (!ObjectUtils.isEmpty(request.getContent())) {
                content = "详细说明：" + request.getContent();
            }
        } else {
            content = reason;
            if (!ObjectUtils.isEmpty(request.getContent())) {
                content += "\n" + "详细说明：" + request.getContent();
            }
        }
        return content;
    }

    private Leads getLeadsInfo(Integer bid, Long id) {
        return leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.ID, id)
                        .eq(Leads.BID, bid)
        );
    }

    /**
     * 查询某个线索的跟进记录
     *
     * @param query 筛选条件
     * @return 跟进记录列表
     */
    @Override
    public BaseResponse<List<LeadsFollowDto>> list(LeadsFollowQuery query) {
        Leads one = leadsManager.getOne(new QueryWrapper<Leads>()
                .eq(Leads.BID, query.getBid())
                .eq(Leads.ID, query.getId())
                .select(Leads.RECOVERY_FROM_IDS));
        Set<Long> ids = LeadsCommonUtil.strToIds(query.getId(), one == null ? null : one.getRecoveryFromIds());
        Integer bid = query.getBid();
        QueryWrapper<LeadsFollow> leadsListQueryWrapper = Wrappers.<LeadsFollow>query()
                .eq(LeadsFollow.BID, bid)
                .ne(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
                .in(LeadsFollow.LEADS_ID, ids)
                .apply("follow_content  NOT REGEXP '处回收'");
        //为空时设置默认值0
        Long maxLeadsFollowId = query.getMinLeadsFollowId();
        if (maxLeadsFollowId == null || maxLeadsFollowId == 0) {
            leadsListQueryWrapper.gt(LeadsFollow.ID, 0);
        } else {
            leadsListQueryWrapper.lt(LeadsFollow.ID, query.getMinLeadsFollowId());
        }


        if (query.getPageSize() != null) {
            leadsListQueryWrapper.last("limit " + query.getPageSize());
        }

        leadsListQueryWrapper.orderByDesc(LeadsFollow.CREATE_TIME);

        List<LeadsFollow> leadsFollows = leadsFollowManager.list(leadsListQueryWrapper);
        Set<Long> staffIds = leadsFollows.stream().map(LeadsFollow::getStaffId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(staffIds)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        Map<Long, String> staffNameMap = Maps.newHashMap();
        Map<Long, String> userMap = Maps.newHashMap();

        if (!CollectionUtils.isEmpty(staffIds)) {
            StaffListRequest staffListRequest = new StaffListRequest();
            staffListRequest.setBid(bid);
            staffListRequest.setIds(Lists.newArrayList(staffIds));
            List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);
            if (!CollectionUtils.isEmpty(staffList)) {
                staffList.forEach(staffDto -> {
                    staffNameMap.put(staffDto.getId(), StringUtils.isEmpty(staffDto.getName()) ? "" : staffDto.getName());
                });
            }
        }

        List<LeadsFollow> leadsUserIdGetList = leadsFollows.stream().filter(item -> item.getUserId() > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(leadsUserIdGetList)) {
            Set<Long> userIds = leadsUserIdGetList.stream().map(LeadsFollow::getUserId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(userIds)) {
                StaffListRequest staffListRequest = new StaffListRequest();
                staffListRequest.setBid(bid);
                staffListRequest.setCustomerIds(Lists.newArrayList(userIds));
                List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);
                if (!CollectionUtils.isEmpty(staffList)) {
                    staffList.forEach(staffDto -> {
                        userMap.put(staffDto.getCustomerId(), StringUtils.isEmpty(staffDto.getName()) ? "" : staffDto.getName());
                    });
                }
            }
        }
        List<LeadsFollowDto> leadsFollowToDtoList = getLeadsFollowToDtoList(leadsFollows, staffNameMap, userMap);

        //如果当前操作人和根据记录的员工相同，则省略根进人
        leadsFollowToDtoList.forEach(leadsFollowDto -> {
            if (Objects.nonNull(query.getOperatorStaffId())
                    && query.getOperatorStaffId().equals(leadsFollowDto.getStaffId())) {
                leadsFollowDto.setStaffName("");
            }
        });

        if (CollectionUtils.isEmpty(leadsFollowToDtoList)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        return BaseResponse.success(leadsFollowToDtoList);
    }

    @Override
    public BaseResponse<BasePaginationResponse<LeadsFollowDto>> getLeadsFollowList(LeadsFollowListRequest request) {
        Leads one = leadsManager.getOne(new QueryWrapper<Leads>()
                .eq(Leads.BID, request.getBid())
                .eq(Leads.ID, request.getId())
                .select(Leads.RECOVERY_FROM_IDS, Leads.CLIENT_ID, Leads.RELATION_CLIENT_TIME));
        if (one == null) {
            return BaseResponse.error("线索不存在");
        }
        Set<Long> ids = LeadsCommonUtil.strToIds(request.getId(), one.getRecoveryFromIds());
        Integer bid = request.getBid();
        QueryWrapper<LeadsFollow> leadsFollowQueryWrapper = getLeadsFollowListQuery(bid, ids);
        // 线索跟进总记录数
        int leadsCount = getLeadsFollowListCount(leadsFollowQueryWrapper);
        // 客户跟进记录条数
        int clientCount = getLeadsFollowListClientCount(bid, one.getClientId(), one.getRelationClientTime(), request.getWithClientFollow());

        int total = leadsCount + clientCount;

        List<LeadsFollow> clientFollowList = getLeadsFollowListClientFollow(bid, one.getClientId(), one.getRelationClientTime(), request, clientCount);

        List<LeadsFollow> leadsFollowList = getLeadsFollowListFollow(leadsFollowQueryWrapper, request, leadsCount, clientCount);
        // 合并线索
        List<LeadsFollow> leadsFollows = getLeadsFollowListMerge(clientFollowList, leadsFollowList, one.getRelationClientTime());

        Set<Long> staffIds = leadsFollows.stream().map(LeadsFollow::getStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNameMap = Maps.newHashMap();
        Map<Long, String> userMap = Maps.newHashMap();
        BasePaginationResponse<LeadsFollowDto> response = new BasePaginationResponse<>();
        if (CollectionUtils.isEmpty(staffIds)) {
            response.setList(Lists.newArrayList());
            response.setTotal(total);
            return BaseResponse.success(response);
        }

        if (!CollectionUtils.isEmpty(staffIds)) {
            StaffListRequest staffListRequest = new StaffListRequest();
            staffListRequest.setBid(bid);
            staffListRequest.setIds(Lists.newArrayList(staffIds));
            List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);
            if (!CollectionUtils.isEmpty(staffList)) {
                staffList.forEach(staffDto -> staffNameMap.put(staffDto.getId(), StringUtils.isEmpty(staffDto.getName()) ? "" : staffDto.getName()));
            }
        }


        List<LeadsFollow> leadsUserIdGetList = leadsFollows.stream().filter(item -> item.getUserId() > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(leadsUserIdGetList)) {
            Set<Long> userIds = leadsUserIdGetList.stream().map(LeadsFollow::getUserId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(userIds)) {
                StaffListRequest staffListRequest = new StaffListRequest();
                staffListRequest.setBid(bid);
                staffListRequest.setCustomerIds(Lists.newArrayList(userIds));
                List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);
                if (!CollectionUtils.isEmpty(staffList)) {
                    staffList.forEach(staffDto -> {
                        userMap.put(staffDto.getCustomerId(), StringUtils.isEmpty(staffDto.getName()) ? "" : staffDto.getName());
                    });
                }
            }
        }


        List<LeadsFollowDto> leadsFollowToDtoList = getLeadsFollowToDtoList(leadsFollows, staffNameMap, userMap);
        response.setTotal(total);
        response.setList(leadsFollowToDtoList);
        return BaseResponse.success(response);
    }

    private List<LeadsFollow> getLeadsFollowListMerge(List<LeadsFollow> clientFollowList, List<LeadsFollow> leadsFollowList, LocalDateTime clientTime){
        if(CollectionUtils.isEmpty(clientFollowList)){
            return leadsFollowList;
        }
        // 转客户后在线索添加的跟进记录也算客户的跟进记录
        List<LeadsFollow> relationClientFollow = leadsFollowList.stream().filter(follow -> clientTime != null && follow.getCreateTime().compareTo(clientTime) > 0).collect(Collectors.toList());
        clientFollowList.addAll(relationClientFollow);
        clientFollowList.sort((l1, l2) -> l2.getCreateTime().compareTo(l1.getCreateTime()));
        leadsFollowList.removeAll(relationClientFollow);

        List<LeadsFollow> result = new ArrayList<>();
        result.addAll(clientFollowList);
        result.addAll(leadsFollowList);

        return result;
    }

    private QueryWrapper<LeadsFollow> getLeadsFollowListQuery(int bid, Set<Long> ids){
        QueryWrapper<LeadsFollow> leadsFollowQueryWrapper = Wrappers.<LeadsFollow>query()
                .eq(LeadsFollow.BID, bid)
                .in(LeadsFollow.LEADS_ID, ids);
        leadsFollowQueryWrapper.orderByDesc(LeadsFollow.CREATE_TIME);

        return leadsFollowQueryWrapper;
    }

    private int getLeadsFollowListCount(QueryWrapper<LeadsFollow> queryWrapper){
        return leadsFollowManager.count(queryWrapper);
    }

    private List<LeadsFollow> getLeadsFollowListClientFollow(Integer bid,
                                                             Long clientId,
                                                             LocalDateTime clientTime,
                                                             LeadsFollowListRequest pageRequest,
                                                             int clientCount) {
        if (Boolean.FALSE.equals(pageRequest.getWithClientFollow())){
            return Lists.newArrayList();
        }
        if (clientId == null || clientId.equals(0L) || clientCount == 0) {
            return new ArrayList<>();
        }

        int skip = (pageRequest.getPageNo() - 1) * pageRequest.getPageSize();

        if (skip >= clientCount) {
            return new ArrayList<>();
        }

        int remainingSize = clientCount - skip;
        int pageSize = remainingSize > pageRequest.getPageSize() ? pageRequest.getPageSize() : remainingSize;

        GetClientFollowListRequest request = new GetClientFollowListRequest();
        request.setBid(bid);
        request.setId(clientId);
        request.setGeCreateTime(clientTime);
        request.setPageNo(pageRequest.getPageNo());
        request.setPageSize(pageSize);
        BasePaginationResponse<ClientFollowItemDto> clientFollowList = clientGetClientForLeads.getClientFollowList(request);

        return clientFollowList.getList().stream().map(clientFollowItemDto -> {
            LeadsFollow result = new LeadsFollow();
            result.setStaffId(clientFollowItemDto.getStaffId());
            result.setFollowContent(clientFollowItemDto.getContent());
            result.setLeadsStatus(LeadsStatusEnum.TO_DISTRIBUTE.getStatus());
            result.setBid(bid);
            result.setUserId(0L);
            if (clientFollowItemDto.getCreateTime() != null) {
                result.setCreateTime(DateTimeUtils.MillisToLocalDateTime(clientFollowItemDto.getCreateTime()));
            }
            return result;
        }).collect(Collectors.toList());
    }

    private int getLeadsFollowListClientCount(int bid, Long clientId, LocalDateTime clientTime, Boolean withClientFollow) {
        if (clientId.equals(0L)) {
            return 0;
        }
        if (Boolean.FALSE.equals(withClientFollow)) {
            return 0;
        }
        GetClientFollowListRequest request = new GetClientFollowListRequest();
        request.setBid(bid);
        request.setId(clientId);
        request.setGeCreateTime(clientTime);
        return clientGetClientForLeads.followCountByClient(request);
    }

    private List<LeadsFollow> getLeadsFollowListFollow(QueryWrapper<LeadsFollow> queryWrapper, BaseBidOptPageRequest request,
                                                       int leadsCount,
                                                       int clientCount) {
        if (leadsCount == 0) {
            return new ArrayList<>();
        }
        if (clientCount == 0) {
            queryWrapper.last("LIMIT " + (request.getPageNo() - 1) + "," + request.getPageSize());
            return leadsFollowManager.list(queryWrapper);
        }

        int skip = request.getPageNo() * request.getPageSize();

        int remainingSize = skip - clientCount;

        if (remainingSize <= 0) {
            return new ArrayList<>();
        }

        int remainder = remainingSize % request.getPageSize();

        int pageSize = remainder == 0 ? request.getPageSize() : remainder;

        queryWrapper.last("LIMIT " + (remainingSize - pageSize) + "," + pageSize);

        List<LeadsFollow> list = leadsFollowManager.list(queryWrapper);

        return list;
    }

    @Override
    public BaseResponse<Boolean> createContactFollow(PrivateVoiceRecordDTO request) {
        Long leadsId = request.getLeadsId();
        Leads leads = getOne(request.getBid(), leadsId);
        if (leads == null) {
            return BaseResponse.error("线索信息错误");
        }

        Boolean isFirst = (LeadsStatusEnum.DISTRIBUTED == LeadsStatusEnum.parse(leads.getStatus()));

        this.createLeadsFollowContent(leads, isFirst, request);
        if (leads.getClientId() > 0L) {
            AsyncUtils.runAsync(() -> {
                // 更新client索引
                BaseIdRequest clientBuildRequest = new BaseIdRequest();
                clientBuildRequest.setId(leads.getClientId());
                clientBuildRequest.setBid(request.getBid());
                clientService.buildClientDocByClientId(clientBuildRequest);
            });
        }
        return BaseResponse.success(true);
    }

    private Leads getOne(Integer bid, Long leadsId) {
        return leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, leadsId)
        );
    }

    @Override
    public BaseResponse<List<LeadsFollowDto>> getLastFollow(LeadsFollowListRequest request) {
        QueryWrapper<LeadsFollow> leadsFollowQueryWrapper = Wrappers.<LeadsFollow>query()
                .eq(LeadsFollow.BID, request.getBid())
                .in(LeadsFollow.LEADS_ID, request.getLeadsIds())
                .eq(Objects.nonNull(request.getLeadsStatus()), LeadsFollow.LEADS_STATUS, request.getLeadsStatus())
                .groupBy(LeadsFollow.LEADS_ID).select("max(id) as id");

        List<LeadsFollow> lastLeadsFollowList = leadsFollowManager.list(leadsFollowQueryWrapper);
        if (CollectionUtils.isEmpty(lastLeadsFollowList)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        List<Long> ids = lastLeadsFollowList.stream().map(LeadsFollow::getId).collect(Collectors.toList());
        List<LeadsFollow> leadsFollowList = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, request.getBid())
                        .in(LeadsFollow.ID, ids)
        );

        List<LeadsFollowDto> leadsFollowDtoList = leadsFollowList.stream().map(leadsFollow -> {
            LeadsFollowDto leadsFollowDto = new LeadsFollowDto();
            leadsFollowDto.setId(leadsFollow.getId());
            leadsFollowDto.setLeadsId(leadsFollow.getLeadsId());
            leadsFollowDto.setReason(leadsFollow.getReason());
            leadsFollowDto.setReasonId(leadsFollow.getReasonId());
            leadsFollowDto.setFollowContent(leadsFollow.getFollowContent());
            return leadsFollowDto;
        }).collect(Collectors.toList());
        return BaseResponse.success(leadsFollowDtoList);
    }

    @Override
    public BaseResponse<List<LeadsFollowSimpleDto>> getLeadsLatestFollowList(LeadsFollowListRequest request) {
        if (ObjectUtils.isEmpty(request.getIds())) {
            return BaseResponse.success(Lists.newArrayList());
        }
        List<LeadsFollow> leadsLatestFollowList = leadsFollowManager.getLeadsLatestFollowList(request.getBid(), request.getIds());
        List<LeadsFollowSimpleDto> leadsFollowSimpleDtoList = leadsLatestFollowList.stream()
                .map(i -> LeadsFollowConverter.toLeadsFollowSimpleDto(i)).collect(Collectors.toList());
        return BaseResponse.success(leadsFollowSimpleDtoList);
    }

    /**
     * 获取客户相关的线索跟进数
     *
     * @param request 客户id请求实体
     * @return 跟进数
     */
    @Override
    public BaseResponse<Integer> getLeadsFollowCountByClient(BaseIdRequest request) {
        List<Leads> leadsArray = leadsManager.findLeadsByClient(request.getBid(), request.getId());
        if (CollectionUtils.isEmpty(leadsArray)) {
            return BaseResponse.success(0);
        }
        Integer followCount = leadsFollowManager
                .getFollowCountByLeadsIds(request.getBid(), leadsArray.stream()
                        .map(Leads::getId)
                        .collect(Collectors.toSet()));
        return BaseResponse.success(followCount);
    }

    @Override
    public BaseResponse<Map<Long, Integer>> getLeadsFollowCountByClientIds(BaseIdsRequest request) {
        Map<Long, List<Leads>> clientLeadsGroup = leadsManager.findLeadsByClientIds(request.getBid(), request.getIds()).stream().collect(Collectors.groupingBy(Leads::getClientId));
        if (CollectionUtils.isEmpty(clientLeadsGroup)) {
            return BaseResponse.success(Maps.newHashMap());
        }
        Set<Long> leadsIds = clientLeadsGroup.values().stream().flatMap(Collection::stream).map(Leads::getId).collect(Collectors.toSet());

        Map<Long, Integer> followCountMapByLeadsIds = leadsFollowManager.getFollowCountMapByLeadsIds(request.getBid(), leadsIds);

        Map<Long, Integer> clientFollowCountMap = Maps.newHashMap();
        clientLeadsGroup.forEach((clientId, leadsList) ->
                clientFollowCountMap.put(clientId, leadsList.stream().map(Leads::getId).map(followCountMapByLeadsIds::get)
                        .filter(Objects::nonNull).mapToInt(Integer::valueOf).sum())
        );

        return BaseResponse.success(clientFollowCountMap);
    }

    @Override
    public BaseResponse<List<LeadsFollowDto>> findLeadsFollowByClient(BaseIdRequest request) {
        List<Leads> leadsArray = leadsManager.findLeadsByClient(request.getBid(), request.getId());
        if (CollectionUtils.isEmpty(leadsArray)) {
            return BaseResponse.success(Lists.newArrayList());
        }
        List<LeadsFollow> followArray = leadsFollowManager.findFollowByLeadsIds(request.getBid(), leadsArray.stream().map(Leads::getId).collect(Collectors.toSet()));
        followArray.stream().peek(follow -> {
            int index = com.inngke.common.utils.StringUtils.lastIndexOf(follow.getFollowContent(), "】");
            if (index > 0 && follow.getLeadsStatus().equals(LeadsStatusEnum.LOST.getStatus()) && !follow.getFollowContent().contains("流失原因") && !StringUtils.isEmpty(follow.getReason())) {
                follow.setFollowContent(new StringBuffer(follow.getFollowContent()).insert(index + 1, "\n流失原因：" + follow.getReason() + "\n").toString().replace("\n\n", "\n"));
            }
        });
        List<LeadsFollowDto> followDtoArray = followArray.stream().map(LeadsFollowConverter::toLeadsFollowDto).collect(Collectors.toList());

        return BaseResponse.success(followDtoArray);
    }

    @Override
    public BaseResponse<List<LeadsFollowDto>> batchGetFollow(BaseIdsRequest request) {
        return BaseResponse.success(
                leadsFollowManager.list(Wrappers.<LeadsFollow>query()
                        .in(LeadsFollow.LEADS_ID,request.getIds()).eq(LeadsFollow.BID,request.getBid())
                ).stream().map(LeadsFollowConverter::toLeadsFollowDto).collect(Collectors.toList())
        );
    }

    @Override
    public BaseResponse<List<LeadsFollowDto>> batchGetByFollowIds(BaseIdsRequest request) {
        return BaseResponse.success(
                leadsFollowManager.list(Wrappers.<LeadsFollow>query()
                        .in(LeadsFollow.ID, request.getIds()).eq(LeadsFollow.BID, request.getBid())
                ).stream().map(LeadsFollowConverter::toLeadsFollowDto).collect(Collectors.toList())
        );
    }

    @Override
    public BaseResponse<List<LeadsFollowDto>> getLeadsFollowListOpen(GetLeadsFollowListRequest request) {
        List<LeadsFollow> leadsFollowList = leadsFollowManager.getLeadsFollowList(
                request.getBid(), request.getLeadsId(), DateTimeUtils.MillisToLocalDateTime(request.getCreateTime()),
                request.getLimit());
        return BaseResponse.success(leadsFollowList.stream().map(LeadsFollowConverter::toLeadsFollowDto).collect(Collectors.toList()));
    }

    private Boolean createLeadsFollowContent(Leads leads, Boolean isFirst, PrivateVoiceRecordDTO dto) {
        String content = LeadsFollowContentUtil.initLeadsFollowContent(leads, isFirst, dto);

        LeadsStatusEnum newStatus = LeadsFollowContentUtil.getNewStatus(leads);
        //拼装跟进记录
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setLeadsId(leads.getId());
        leadsFollow.setBid(leads.getBid());
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setFollowContent(content);
        leadsFollow.setLeadsStatus(newStatus.getStatus());
        leadsFollow.setStaffId(0L);
        leadsFollow.setUserId(0L);
        leadsFollow.setCreateTime(LocalDateTime.now());
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());

        return leadsFollowManager.createContactFollow(leadsFollow);
    }

    /**
     * 校验线索状态，当newStatus>2时,线索历史记录必须修改过为已联系
     *
     */
    private void checkLeadsStatusUpdated(Integer status, Leads existsLeads) {
        LeadsStatusEnum newStatus = LeadsStatusEnum.parse(status);
        if (Objects.isNull(newStatus)) {
            return;
        }
        LeadsStatusEnum oldStatus = LeadsStatusEnum.parse(existsLeads.getStatus());
        if (newStatus == oldStatus) {
            return;
        }
        if (oldStatus.getStatus() >= LeadsStatusEnum.CONTACTED.getStatus()) {
            return;
        }
        if (newStatus.getStatus() == LeadsStatusEnum.DISTRIBUTED.getStatus() && existsLeads.getStatus() > newStatus.getStatus()) {
            throw new InngkeServiceException("线索状态【" + oldStatus.getName() + "】不允许修改为【" + newStatus.getName() + "】");
        }
//        if (newStatus.getStatus() > LeadsStatusEnum.CONTACTED.getStatus()) {
//            List<LeadsFollow> list = leadsFollowManager.list(
//                    Wrappers.<LeadsFollow>query()
//                            .eq(LeadsFollow.LEADS_ID, existsLeads.getId())
//                            .eq(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.CONTACTED.getStatus())
//            );
//            if (CollectionUtils.isEmpty(list)) {
//                throw new InngkeServiceException("线索需要为【" + LeadsStatusEnum.CONTACTED.getName() + "】才能修改为【" + newStatus.getName() + "】");
//            }
//        }
    }


    /**
     * 查询某个线索的跟进记录数
     *
     * @param request 筛选条件
     * @return 跟进记录数
     */
    @Override
    public BaseResponse<Integer> getLeadsFollowCount(LeadsGetRequest request) {
        int count = leadsFollowManager.count(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, request.getBid())
                        .ne(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
                        .eq(LeadsFollow.LEADS_ID, request.getId())
                        .apply("follow_content  NOT REGEXP '处回收|转交'")

        );
        return BaseResponse.success(count);
    }

    private List<LeadsFollowDto> getLeadsFollowToDtoList(List<LeadsFollow> leadsFollows, Map<Long, String> staffNameMap, Map<Long, String> userMap) {
        List<LeadsFollowDto> leadsFollowList = Lists.newArrayList();
        leadsFollows.forEach(
                leadsFollow -> {
                    LeadsFollowDto leadsFollowDto = new LeadsFollowDto();
                    if (!StringUtils.isEmpty(leadsFollow.getFollowImages())) {
                        List<String> images = Splitter.on(InngkeAppConst.COMMA_STR).trimResults().splitToList(leadsFollow.getFollowImages());
                        leadsFollowDto.setFollowImages(images);
                    }
                    leadsFollowDto.setFollowType(leadsFollow.getFollowType());
                    leadsFollowDto.setLeadsStatus(leadsFollow.getLeadsStatus());
                    leadsFollowDto.setLeadsId(leadsFollow.getLeadsId());
                    if (leadsFollow.getLeadsStatus().equals(LeadsStatusEnum.LOST.getStatus())) {
                        int index = com.inngke.common.utils.StringUtils.lastIndexOf(leadsFollow.getFollowContent(), "】");
                        if (index > 0 && leadsFollow.getLeadsStatus().equals(LeadsStatusEnum.LOST.getStatus()) && !leadsFollow.getFollowContent().contains("流失原因") && !StringUtils.isEmpty(leadsFollow.getReason())) {
                            leadsFollow.setFollowContent(new StringBuffer(leadsFollow.getFollowContent()).insert(index + 1, "\n流失原因：" + leadsFollow.getReason() + "\n").toString().replace("\n\n", "\n"));
                        }
                    }
                    if (leadsFollow.getLeadsStatus().equals(LeadsStatusEnum.INVALID.getStatus())) {
                        int index = com.inngke.common.utils.StringUtils.lastIndexOf(leadsFollow.getFollowContent(), "】");
                        if (index > 0 && leadsFollow.getLeadsStatus().equals(LeadsStatusEnum.INVALID.getStatus()) && !leadsFollow.getFollowContent().contains("无效原因") && !StringUtils.isEmpty(leadsFollow.getReason())) {
                            leadsFollow.setFollowContent(new StringBuffer(leadsFollow.getFollowContent()).insert(index + 1, "\n无效原因：" + leadsFollow.getReason() + "\n").toString().replace("\n\n", "\n"));
                        }
                    }
                    if (leadsFollow.getLeadsStatus().equals(LeadsStatusEnum.PUSH_BACK.getStatus())) {
                        if (!leadsFollow.getFollowContent().contains("退回原因：")) {
                            String[] split = StringUtils.split(leadsFollow.getFollowContent(), "\n");
                            if (!ObjectUtils.isEmpty(split)) {
                                List<String> followList = Lists.newArrayList(Arrays.asList(split));
                                followList.add(1, "\n退回原因：" + leadsFollow.getReason() + "\n");
                                leadsFollow.setFollowContent(followList.stream().collect(Collectors.joining("")));
                            } else {
                                leadsFollow.setFollowContent(leadsFollow.getFollowContent() + "\n退回原因：" + leadsFollow.getReason());
                            }
                        }else {
                            leadsFollow.setFollowContent(leadsFollow.getFollowContent());
                        }

                    }
                    leadsFollowDto.setFollowContent(leadsFollow.getFollowContent());
                    leadsFollowDto.setBid(leadsFollow.getBid());
                    leadsFollowDto.setId(leadsFollow.getId());
                    leadsFollowDto.setStaffId(leadsFollow.getStaffId());
                    leadsFollowDto.setCreateTime(leadsFollow.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
                    Long userId = leadsFollow.getUserId();
                    Long staffId = leadsFollow.getStaffId();

                   if (staffId != null && staffId > 0 && staffNameMap.get(leadsFollow.getStaffId()) != null) {
                        String staffName = staffNameMap.get(leadsFollow.getStaffId());
                        leadsFollowDto.setStaffName(staffName);
                    }else if (userId != null && userId > 0 && userMap.get(userId) != null) {
                        String username = userMap.get(userId);
                        leadsFollowDto.setStaffName(username);
                    }

                    leadsFollowList.add(leadsFollowDto);
                }
        );
        return leadsFollowList;
    }

    private LeadsFollow LeadsRequestToLeadsFollow(LeadsFollowCreateRequest request) {
        LocalDateTime current = LocalDateTime.now();
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(request.getBid());
        leadsFollow.setLeadsId(request.getId());
        leadsFollow.setLeadsStatus(request.getStatus());
        leadsFollow.setCreateTime(current);
        leadsFollow.setStaffId(request.getStaffId());
        StaffDto staffDto = staffClientForLeads.getStaffById(request.getBid(), request.getStaffId());
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setUserId(staffDto.getCustomerId());
        }
        Leads leads = getOne(request.getBid(), request.getId());
        if (Objects.nonNull(leads)) {
            leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        }
        leadsFollow.setClientLostReasonType(request.getClientLostReasonType());
        if (!ObjectUtils.isEmpty(request.getReason())) {
            leadsFollow.setReason(request.getReason());
            leadsFollow.setReasonId(request.getReasonId());
        }
        if (!CollectionUtils.isEmpty(request.getImages())) {
            String imagesStr = Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(request.getImages());
            leadsFollow.setFollowImages(imagesStr);
        }
        return leadsFollow;
    }

    private void sendMQ(Integer bid, RewardEventDto message) {
        AsyncUtils.runAsync(() -> {
            MqSendRequest req = new MqSendRequest();
            req.setTopic("reward_event");
            String payload = jsonService.toJson(message);
            req.setPayload(payload);
            req.setOperatorId(0L);
            req.setBid(bid);
            BaseResponse<Boolean> mqResponse = mqService.send(req);
            if (!BaseResponse.responseSuccess(mqResponse)) {
                logger.warn("发送合伙人奖励事件MQ失败！");
            } else {
                logger.info("MQ发送成功：{}", payload);
            }
        });
    }


    private String getFollowContent(LeadsFollowCreateRequest request, Leads leads) {
        Integer status = checkPreFollowStatus(leads.getStatus()) ? leads.getPreFollowStatus() : leads.getStatus();
        if (request.getStatus().equals(LeadsStatusEnum.LOST.getStatus()) && (Objects.nonNull(request.getClientLostReasonType()) || !ObjectUtils.isEmpty(request.getReason()))) {
            return buildLostDetailFollowContent(request, status, checkPreFollowStatus(leads.getStatus()));
        }
        return buildDetailFollowContent(request, status, checkPreFollowStatus(leads.getStatus()));
    }


}

package com.inngke.bp.leads.core.converter;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.core.utils.FileIdentifierUtil;
import com.inngke.bp.leads.core.utils.LeadsChannelUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsCallLog;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.LeadsExcelDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import com.inngke.bp.leads.enums.LeadsPreFollowStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.common.utils.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class LeadsConverter {

    private LeadsConverter() {};

    public static LeadsVo toLeadsListItem(Leads leads, Map<Long, Long> staffToAgentMap){
        // 判空
        if (Objects.isNull(leads)) {
            return null;
        }
        LeadsVo leadsvo = new LeadsVo();
        leadsvo.setId(leads.getId());
        leadsvo.setClientId(leads.getClientId());
        leadsvo.setLevelId(leads.getLevelId());
        leadsvo.setCreateTime(leads.getCreateTime() != null ? leads.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : System.currentTimeMillis());
        leadsvo.setBid(leads.getBid());
        leadsvo.setName(leads.getName());
        leadsvo.setMobile(leads.getMobile());
        leadsvo.setCustomerId(leads.getCustomerId());
        leadsvo.setCustomerUid(leads.getCustomerUid());
        leadsvo.setStatus(leads.getStatus());
        leadsvo.setProvinceId(leads.getProvinceId());
        leadsvo.setWeChat(leads.getWeChat());
        leadsvo.setGoodsLink(leads.getGoodsLink());
        leadsvo.setChannelId(Optional.ofNullable(leads.getChannelId()).orElse(0L));
        leadsvo.setProvinceName(leads.getProvinceName());
        leadsvo.setCityId(leads.getCityId());
        leadsvo.setCityName(leads.getCityName());
        leadsvo.setAreaId(leads.getAreaId());
        leadsvo.setAreaName(leads.getAreaName());
        leadsvo.setAddress(leads.getAddress());
        leadsvo.setChannel(leads.getChannel());
        leadsvo.setChannelType(leads.getChannelType());
        leadsvo.setChannelSource(leads.getChannelSource());
        leadsvo.setOrderAccount(leads.getOrderAccount());
        leadsvo.setOrderSn(leads.getOrderSn());
        leadsvo.setGoodsName(leads.getGoodsName());
        leadsvo.setGoodsNum(leads.getGoodsNum());
        leadsvo.setRecoveryFromIds(leads.getRecoveryFromIds());
        LocalDateTime payTime = leads.getPayTime();
        if(payTime != null){
            leadsvo.setPayTime(leads.getPayTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setPayAmount(leads.getPayAmount());
        leadsvo.setOrderMessage(leads.getOrderMessage());
        leadsvo.setRemark(leads.getRemark());
        leadsvo.setTpLeadsId(leads.getTpLeadsId());
        leadsvo.setPromotionName(leads.getPromotionName());
        LocalDateTime registryTime = leads.getRegistryTime();
        if(registryTime != null){
            leadsvo.setRegistryTime(registryTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setExpectIn(leads.getExpectIn());
        leadsvo.setShowPhone(leads.getShowPhone());
        leadsvo.setStyle(leads.getStyle());
        leadsvo.setBatchId(leads.getBatchId());

        Long distributeStaffId = leads.getDistributeStaffId();
        leadsvo.setDistributeStaffId(distributeStaffId);
        fillDistributeAgentId(distributeStaffId, leadsvo, staffToAgentMap);

        leadsvo.setErrorMsg(leads.getErrorMsg());
        LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(leads.getStatus());
        if (Objects.nonNull(leads.getClientId()) && !Objects.equals(leads.getClientId(), 0L)) {
            leadsvo.setStatusText("已转客户");
        }else {
            leadsvo.setStatusText(statusEnum == null ? InngkeAppConst.EMPTY_STR : statusEnum.getName());
        }
        leadsvo.setChannelText(LeadsChannelUtil.getName(leads.getBid(), leads.getChannel()));
        LocalDateTime distributeTime = leads.getDistributeTime();
        if(distributeTime != null ){
            leadsvo.setDistributeTime(distributeTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        LocalDateTime updateTime = leads.getUpdateTime();
        if(updateTime != null){
            leadsvo.setUpdateTime(updateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }

        LocalDateTime pushBackTime = leads.getPushBackTime();
        if(pushBackTime != null ){
            leadsvo.setPushBackTime(pushBackTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        //新增和修改时如果有自动分配员工则返回自动分配状态给前段
        if(distributeStaffId != null || leads.getPreFollowStaffId() != null){
            leadsvo.setDistributeStatus(1);
        }
        leadsvo.setExtData(leads.getExtData());
        leadsvo.setChannelTypeText(LeadsDataSourceEnum.parse(leads.getChannelType()).getName());
        if (!StringUtils.isEmpty(leads.getTags())){
            leadsvo.setTags(Lists.newArrayList(leads.getTags().split(",")));
            leadsvo.setTagsSize(leadsvo.getTags().size());
        }else {
            leadsvo.setTagsSize(0);
        }
        leadsvo.setPushBackStaffId(leads.getPushBackStaffId());
        leadsvo.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsvo.setPreFollowStatus(leads.getPreFollowStatus());
        LeadsPreFollowStatusEnum preFollowStatusEnum = LeadsPreFollowStatusEnum.parse(leads.getPreFollowStatus());
        leadsvo.setPreFollowStatusText(preFollowStatusEnum == null ? InngkeAppConst.EMPTY_STR : preFollowStatusEnum.getName());
        leadsvo.setLevel(leads.getLevel());
        leadsvo.setType(leads.getType());
        leadsvo.setCreateStaffId(leads.getCreateStaffId());

        LeadsInputSourceEnum leadsInputSourceEnum = LeadsInputSourceEnum.parse(leads.getChannelSource());
        leadsvo.setChannelSourceText(leadsInputSourceEnum == null ? null : leadsInputSourceEnum.getName());

        if(leads.getDistributeFollowTime() != null) {
            leadsvo.setDistributeFollowTime(DateTimeUtils.format(leads.getDistributeFollowTime(),DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        }
        LocalDateTime lastContactTime = leads.getLastContactTime();
        if (!Objects.isNull(lastContactTime)){
            leadsvo.setLastContactTime(DateTimeUtils.getMilli(lastContactTime));
        }
        leadsvo.setLevel(leads.getLevel());
        leadsvo.setClientStatus(leads.getClientStatus());
        leadsvo.setDistributeTime(DateTimeUtils.getMilli(leads.getDistributeTime()));
        leadsvo.setAttachmentList(
                Optional.ofNullable(leads.getAttachmentList()).map(attachmentList ->
                                JsonUtil.jsonToList(attachmentList, LeadsAttachment.class))
                        .orElse(Lists.newArrayList())
        );
        leadsvo.setLastFollowTime(Objects.isNull(leads.getLastFollowTime()) ? null : DateTimeUtils.getMilli(leads.getLastFollowTime()));
        return leadsvo;
    }

    public static LeadsVo toLeadsListItem(Leads leads){
        // 判空
        if (ObjectUtils.isEmpty(leads)) {
            return null;
        }
        LeadsVo leadsvo = new LeadsVo();
        leadsvo.setId(leads.getId());
        leadsvo.setLevelId(leads.getLevelId());
        leadsvo.setCreateTime(leads.getCreateTime() != null ? leads.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : System.currentTimeMillis());
        leadsvo.setBid(leads.getBid());
        leadsvo.setName(leads.getName());
        leadsvo.setMobile(leads.getMobile());
        leadsvo.setCustomerId(leads.getCustomerId());
        leadsvo.setCustomerUid(leads.getCustomerUid());
        leadsvo.setStatus(leads.getStatus());
        leadsvo.setProvinceId(leads.getProvinceId());
        leadsvo.setWeChat(leads.getWeChat());
        leadsvo.setGoodsLink(leads.getGoodsLink());
        leadsvo.setChannelId(Optional.ofNullable(leads.getChannelId()).orElse(0L));
        leadsvo.setProvinceName(leads.getProvinceName());
        leadsvo.setCityId(leads.getCityId());
        leadsvo.setCityName(leads.getCityName());
        leadsvo.setAreaId(leads.getAreaId());
        leadsvo.setAreaName(leads.getAreaName());
        leadsvo.setAddress(leads.getAddress());
        leadsvo.setChannel(leads.getChannel());
        leadsvo.setChannelType(leads.getChannelType());
        leadsvo.setChannelSource(leads.getChannelSource());
        leadsvo.setOrderAccount(leads.getOrderAccount());
        leadsvo.setOrderSn(leads.getOrderSn());
        leadsvo.setGoodsName(leads.getGoodsName());
        leadsvo.setGoodsNum(leads.getGoodsNum());
        LocalDateTime payTime = leads.getPayTime();
        if(payTime != null){
            leadsvo.setPayTime(leads.getPayTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setPayAmount(leads.getPayAmount());
        leadsvo.setOrderMessage(leads.getOrderMessage());
        leadsvo.setRemark(leads.getRemark());
        leadsvo.setTpLeadsId(leads.getTpLeadsId());
        leadsvo.setPromotionName(leads.getPromotionName());
        LocalDateTime registryTime = leads.getRegistryTime();
        if(registryTime != null){
            leadsvo.setRegistryTime(registryTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setExpectIn(leads.getExpectIn());
        leadsvo.setShowPhone(leads.getShowPhone());
        leadsvo.setStyle(leads.getStyle());
        leadsvo.setBatchId(leads.getBatchId());

        Long distributeStaffId = leads.getDistributeStaffId();
        leadsvo.setDistributeStaffId(distributeStaffId);

        leadsvo.setErrorMsg(leads.getErrorMsg());
        LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(leads.getStatus());
        if (Objects.nonNull(leads.getClientId()) && !Objects.equals(leads.getClientId(), 0L)) {
            leadsvo.setStatusText("已转客户");
        }else {
            leadsvo.setStatusText(statusEnum == null ? InngkeAppConst.EMPTY_STR : statusEnum.getName());
        }
        leadsvo.setChannelText(LeadsChannelUtil.getName(leads.getBid(), leads.getChannel()));
        LocalDateTime distributeTime = leads.getDistributeTime();
        if(distributeTime != null ){
            leadsvo.setDistributeTime(distributeTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        LocalDateTime updateTime = leads.getUpdateTime();
        if(updateTime != null){
            leadsvo.setUpdateTime(updateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }

        LocalDateTime lastContactTime = leads.getLastContactTime();
        if (!ObjectUtils.isEmpty(lastContactTime)){
            leadsvo.setLastContactTime(DateTimeUtils.getMilli(lastContactTime));
        }

        LocalDateTime pushBackTime = leads.getPushBackTime();
        if(pushBackTime != null ){
            leadsvo.setPushBackTime(pushBackTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        //新增和修改时如果有自动分配员工则返回自动分配状态给前段
        if(distributeStaffId != null || leads.getPreFollowStaffId() != null){
            leadsvo.setDistributeStatus(1);
        }
        leadsvo.setExtData(leads.getExtData());
        leadsvo.setChannelTypeText(LeadsDataSourceEnum.parse(leads.getChannelType()).getName());
        if (!StringUtils.isEmpty(leads.getTags())){
            leadsvo.setTags(Lists.newArrayList(leads.getTags().split(",")));
            leadsvo.setTagsSize(leadsvo.getTags().size());
        }else {
            leadsvo.setTagsSize(0);
        }
        leadsvo.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsvo.setPreFollowStatus(leads.getPreFollowStatus());
        LeadsPreFollowStatusEnum preFollowStatusEnum = LeadsPreFollowStatusEnum.parse(leads.getPreFollowStatus());
        leadsvo.setPreFollowStatusText(preFollowStatusEnum == null ? InngkeAppConst.EMPTY_STR : preFollowStatusEnum.getName());
        leadsvo.setLevel(leads.getLevel());
        leadsvo.setType(leads.getType());
        leadsvo.setCreateStaffId(leads.getCreateStaffId());
        leadsvo.setPushBackStaffId(leads.getPushBackStaffId());
        return leadsvo;
    }

    public static Leads toAddLeads(LeadsAddRequest leadsAddRequest){
        if (Objects.isNull(leadsAddRequest)) {
            return null;
        }
        Leads leads = new Leads();
        leads.setBid(leadsAddRequest.getBid());
        leads.setCreateTime(LocalDateTime.now());
        leads.setName(leadsAddRequest.getName());
        leads.setMobile(leadsAddRequest.getMobile());
        leads.setWeChat(leadsAddRequest.getWeChat());
        leads.setChannelId(Optional.ofNullable(leadsAddRequest.getChannelId()).orElse(0L));
        //当新增和修改的时候线索状态为0：待分配
        leads.setStatus(LeadsStatusEnum.TO_DISTRIBUTE.getStatus());
        leads.setProvinceId(leadsAddRequest.getProvinceId());
        leads.setProvinceName(leadsAddRequest.getProvinceName());
        leads.setCityId(leadsAddRequest.getCityId());
        leads.setCityName(leadsAddRequest.getCityName());
        leads.setAreaId(leadsAddRequest.getAreaId());
        leads.setAreaName(leadsAddRequest.getAreaName());
        leads.setAddress(leadsAddRequest.getAddress());
        leads.setChannel(leadsAddRequest.getChannel());
        leads.setChannelType(leadsAddRequest.getChannelType());
        leads.setChannelSource(leadsAddRequest.getChannelSource());
        leads.setOrderAccount(leadsAddRequest.getOrderAccount());
        leads.setOrderSn(leadsAddRequest.getOrderSn());
        leads.setGoodsName(leadsAddRequest.getGoodsName());
        leads.setGoodsNum(leadsAddRequest.getGoodsNum());
        leads.setGoodsLink(leadsAddRequest.getGoodsLink());
        Long payTime = leadsAddRequest.getPayTime();
        if(payTime != null){
            leads.setPayTime(LocalDateTime.ofEpochSecond(leadsAddRequest.getPayTime()/1000,0, ZoneOffset.ofHours(8)));
        }
        leads.setPayAmount(leadsAddRequest.getPayAmount());
        leads.setOrderMessage(leadsAddRequest.getOrderMessage());
        leads.setRemark(leadsAddRequest.getRemark());
        leads.setTpLeadsId(leadsAddRequest.getTpLeadsId());
        leads.setPromotionName(leadsAddRequest.getPromotionName());
        Long registryTime = leadsAddRequest.getRegistryTime();
        if(registryTime != null ){
            leads.setRegistryTime(LocalDateTime.ofEpochSecond(leadsAddRequest.getRegistryTime()/1000,0, ZoneOffset.ofHours(8)));
        }
        leads.setExpectIn(leadsAddRequest.getExpectIn());
        leads.setStyle(leadsAddRequest.getStyle());
        leads.setBatchId(leadsAddRequest.getBatchId());
        if (!CollectionUtils.isEmpty(leadsAddRequest.getTags())){
            leads.setTags(String.join(",",leadsAddRequest.getTags()));
        }
        if (!CollectionUtils.isEmpty(leadsAddRequest.getEnterpriseTags())){
            leads.setEnterpriseTags(String.join(",",leadsAddRequest.getEnterpriseTags()));
        }
        if (!StringUtils.isEmpty(leadsAddRequest.getDemandProduct())){
            leads.setDemandProduct(leadsAddRequest.getDemandProduct());
        }
        //2022-10-24添加字段
        leads.setCreateStaffId(leadsAddRequest.getCreateStaffId());
        leads.setReportStaffId(leadsAddRequest.getReportStaffId());
        leads.setType(leadsAddRequest.getType());
        leads.setLevel(leadsAddRequest.getLevel());
        leads.setLevelId(leadsAddRequest.getLevelId());
        leads.setTags(String.join(InngkeAppConst.COMMA_STR, Optional.ofNullable(leadsAddRequest.getTags()).orElse(Lists.newArrayList())));
        leads.setEnterpriseTags(String.join(InngkeAppConst.COMMA_STR, Optional.ofNullable(leadsAddRequest.getEnterpriseTags())
                .orElse(Lists.newArrayList())));
        leads.setExternalTags(String.join(InngkeAppConst.COMMA_STR, Optional.ofNullable(leadsAddRequest.getExternalTags()).orElse(Lists.newArrayList())));

        leads.setTpId(leadsAddRequest.getTpId());

        // 处理附件的类型
        List<LeadsAttachment> attachmentList = Optional.ofNullable(leadsAddRequest.getAttachmentList()).orElse(Lists.newArrayList());
        for (LeadsAttachment attachment : attachmentList) {
            if (StringUtils.isBlank(attachment.getFileType())) {
                String fileType = FileIdentifierUtil.getNetworkFileType(attachment.getUrl());
                attachment.setFileType(fileType);
            }
        }
        leads.setProductIds(CollectionUtils.isEmpty(leadsAddRequest.getProductIds()) ? "" : Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(leadsAddRequest.getProductIds()));
        leads.setAttachmentList(JsonUtil.toJsonString(attachmentList));

        return leads;
    }

    public static LeadsExcelDto leadsDraftExcelDto(Leads leads){
        LeadsExcelDto leadsDraftExcelDto = new LeadsExcelDto();
        leadsDraftExcelDto.setBid(leads.getBid());
        leadsDraftExcelDto.setName(leads.getName());
        leadsDraftExcelDto.setMobile(leads.getMobile());
        if (Objects.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus(), leads.getStatus())) {
            leadsDraftExcelDto.setStatus(LeadsStatusEnum.parse(leads.getPreFollowStatus()).getName());
        }else {
            leadsDraftExcelDto.setStatus(LeadsStatusEnum.parse(leads.getStatus()).getName());
        }
        leadsDraftExcelDto.setProvinceId(leads.getProvinceId());
        leadsDraftExcelDto.setProvinceName(leads.getProvinceName());
        leadsDraftExcelDto.setCityId(leads.getCityId());
        leadsDraftExcelDto.setCityName(leads.getCityName());
        leadsDraftExcelDto.setAreaId(leads.getAreaId());
        leadsDraftExcelDto.setAreaName(leads.getAreaName());
        leadsDraftExcelDto.setAddress(leads.getAddress());
        leadsDraftExcelDto.setGoodsName(leads.getGoodsName());
        leadsDraftExcelDto.setOrderSn(leads.getOrderSn());
        leadsDraftExcelDto.setPrice(leads.getPayAmount().toString());
        leadsDraftExcelDto.setChannelName(LeadsChannelUtil.getName(leads.getBid(), leads.getChannel()));
        leadsDraftExcelDto.setTags(leads.getTags());
        LocalDateTime distributeTime = leads.getDistributeTime();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime createTime = leads.getCreateTime();
        if(createTime != null){
            leadsDraftExcelDto.setCreateTime(dtf.format(createTime));
        }
        if( distributeTime != null){
            leadsDraftExcelDto.setDistributeTime(dtf.format(distributeTime));
        }
        leadsDraftExcelDto.setOrderAccount(leads.getOrderAccount());
        return leadsDraftExcelDto;
    }

    public static Leads toUpdateLeads(LeadsUpdateRequest leadsUpdateRequest){
        Leads leads = toAddLeads(leadsUpdateRequest);
        leads.setId(leadsUpdateRequest.getId());
        leads.setCreateTime(null);
        return leads;
    }

    public static LeadsDistributePreviewDto toLeadsDistributePreviewDto(Leads leads){
        LeadsDistributePreviewDto leadsDistributePreviewDto = new LeadsDistributePreviewDto();
        leadsDistributePreviewDto.setId(leads.getId());
        leadsDistributePreviewDto.setBid(leads.getBid());
        leadsDistributePreviewDto.setName(leads.getName());
        leadsDistributePreviewDto.setMobile(leads.getMobile());
        leadsDistributePreviewDto.setProvinceName(leads.getProvinceName());
        leadsDistributePreviewDto.setCityName(leads.getCityName());
        leadsDistributePreviewDto.setAreaName(leads.getAreaName());
        leadsDistributePreviewDto.setDistributeAgentName("");
        leadsDistributePreviewDto.setDistributeStaffName("");
        return leadsDistributePreviewDto;
    }

    public static LeadsEsDto toLeadsEsDto(Leads leads) {
        if (Objects.isNull(leads)) {
            return null;
        }
        Long distributeStaffId = leads.getDistributeStaffId();
        LeadsEsDto leadsEsDto = new LeadsEsDto();
        leadsEsDto.setId(String.valueOf(leads.getId()));
        leadsEsDto.setLeadsId(leads.getId());
        leadsEsDto.setBid(leads.getBid());
        leadsEsDto.setCustomerId(leads.getCustomerId());
        leadsEsDto.setCustomerUid(leads.getCustomerUid());
        leadsEsDto.setChannelId(Optional.ofNullable(leads.getChannelId()).orElse(0L));
        leadsEsDto.setName(leads.getName());
        leadsEsDto.setMobile(leads.getMobile());
        leadsEsDto.setStatus(leads.getStatus());
        leadsEsDto.setProvinceId(leads.getProvinceId());
        leadsEsDto.setProvinceName(leads.getProvinceName());
        leadsEsDto.setCityId(leads.getCityId());
        leadsEsDto.setCityName(leads.getCityName());
        leadsEsDto.setAreaId(leads.getAreaId());
        leadsEsDto.setAreaName(leads.getAreaName());
        leadsEsDto.setAddress(leads.getAddress());
        leadsEsDto.setChannel(leads.getChannel());
        leadsEsDto.setContactIn24(leads.getContactIn24());
        leadsEsDto.setDistributeStaffId(distributeStaffId);
        leadsEsDto.setDistributeStaffName(null);
        leadsEsDto.setDistributeAgentName(null);
        leadsEsDto.setDistributeTime(DateTimeUtils.getMilli(leads.getDistributeTime()));
        leadsEsDto.setPushBackTime(DateTimeUtils.getMilli(leads.getPushBackTime()));
        leadsEsDto.setGoodsLink(leads.getGoodsLink());
        leadsEsDto.setDeptIds(null);
        leadsEsDto.setStaffIds(null);
        leadsEsDto.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsEsDto.setPreFollowStatus(leads.getPreFollowStatus());
        leadsEsDto.setCreateTime(DateTimeUtils.getMilli(leads.getCreateTime()));
        leadsEsDto.setUpdateTime(DateTimeUtils.getMilli(leads.getUpdateTime()));
        leadsEsDto.setOrderSn(leads.getOrderSn());
        leadsEsDto.setChannelType(leads.getChannelType());
        leadsEsDto.setChannelSource(leads.getChannelSource());
        leadsEsDto.setTags(leads.getTags());
        leadsEsDto.setGoodsName(leads.getGoodsName());
        leadsEsDto.setOrderAccount(leads.getOrderAccount());
        leadsEsDto.setExtData(leads.getExtData());
        leadsEsDto.setPayAmount(leads.getPayAmount().toString());
        leadsEsDto.setLevel(leads.getLevel());
        leadsEsDto.setLevelId(leads.getLevelId());
        leadsEsDto.setCreateStaffId(leads.getCreateStaffId());
        leadsEsDto.setReportStaffId(leads.getReportStaffId());
        leadsEsDto.setPushBackStaffId(leads.getPushBackStaffId());
        leadsEsDto.setErrorMsg(leads.getErrorMsg());
        if(leads.getDistributeFollowTime() != null){
            leadsEsDto.setDistributeFollowTime(DateTimeUtils.getMilli(leads.getDistributeFollowTime()));
        }
        leadsEsDto.setWeChatId(leads.getWeChat());
        if (StringUtils.isNotEmpty(leads.getTags())){
            leadsEsDto.setTagsList(Splitter.on(InngkeAppConst.COMMA_STR).trimResults().splitToList(leads.getTags()));
        }
        if (StringUtils.isNotEmpty(leads.getEnterpriseTags())){
            leadsEsDto.setEnterpriseTags(Splitter.on(InngkeAppConst.COMMA_STR).trimResults().splitToList(leads.getEnterpriseTags()));
        }
        leadsEsDto.setProductIds(org.apache.commons.lang3.StringUtils.isBlank(leads.getProductIds()) ?
                Lists.newArrayList() :
                Splitter.on(InngkeAppConst.COMMA_STR).trimResults().splitToList(leads.getProductIds()).stream()
                        .map(Long::valueOf).collect(Collectors.toList())
        );

        leadsEsDto.setClientId(leads.getClientId());
        leadsEsDto.setClientStatus(leads.getClientStatus());
        leadsEsDto.setLastFollowTime(DateTimeUtils.getMilli(leads.getLastFollowTime()));
        return leadsEsDto;
    }
    /*
     * 从外部传进来一个经销商编号
     */
    public static LeadsEsDto toLeadsEsDto(Leads leads, Map<Long, Long> staffToAgentMap) {
        LeadsEsDto leadsEsDto = LeadsConverter.toLeadsEsDto(leads);
        Long distributeStaffId = leadsEsDto.getDistributeStaffId();
        fillDistributeAgentId(distributeStaffId, leadsEsDto, staffToAgentMap);
        return leadsEsDto;
    }

    public static LeadsEsDto toLeadsEsDto(LeadsEsDto leadsEsDto, Map<Long, Long> staffToAgentMap, Map<Long, LeadsExtInformation> leadsExtInformationMap) {
        if (Objects.isNull(leadsEsDto)) {
            return null;
        }
        Long distributeStaffId = leadsEsDto.getDistributeStaffId();
        fillDistributeAgentId(distributeStaffId, leadsEsDto, staffToAgentMap);
        fillPushBackAgentId(leadsEsDto.getPushBackStaffId(), leadsEsDto, staffToAgentMap);
        fillExtInfo(leadsExtInformationMap, leadsEsDto);

        return leadsEsDto;
    }

    private static void fillExtInfo(Map<Long, LeadsExtInformation> leadsExtInformationMap, LeadsEsDto leadsEsDto) {
        if (CollectionUtils.isEmpty(leadsExtInformationMap)){
            return;
        }
        Long leadsId = leadsEsDto.getLeadsId();
        LeadsExtInformation leadsExtInformation = leadsExtInformationMap.get(leadsId);
        if (Objects.isNull(leadsExtInformation)){
            return;
        }
        leadsEsDto.setCampaignName(leadsExtInformation.getCampaignName());
    }

    private static void fillPushBackAgentId(Long distributeStaffId, LeadsEsDto leadsEsDto, Map<Long, Long> staffToAgentMap) {
        if (Objects.nonNull(distributeStaffId) && distributeStaffId != 0) {
            leadsEsDto.setPushBackAgentId(staffToAgentMap.get(distributeStaffId));
        }
    }

    public static void fillDistributeAgentId(Long distributeStaffId, LeadsEsDto leadsEsDto, Map<Long, Long> staffToAgentMap) {
        if (Objects.nonNull(distributeStaffId) && distributeStaffId != 0) {
            leadsEsDto.setDistributeAgentId(staffToAgentMap.get(distributeStaffId));
        }
    }

    public static void fillDistributeAgentId(Long distributeStaffId, LeadsVo leadsVo, Map<Long, Long> staffToAgentMap) {
        if (Objects.nonNull(distributeStaffId) && distributeStaffId != 0) {
            leadsVo.setDistributeAgentId(staffToAgentMap.get(distributeStaffId));
        }
    }

    public static Leads toLeads(LeadsUpdateRequest request) {
        Leads leads = new Leads();
        leads.setId(request.getId());
        leads.setLevelId(request.getLevelId());
        leads.setBid(request.getBid());
        leads.setName(request.getName());
        leads.setMobile(request.getMobile());
        leads.setChannelId(Optional.ofNullable(request.getChannelId()).orElse(0L));
        leads.setStatus(request.getStatus());
        leads.setProvinceId(Optional.ofNullable(request.getProvinceId()).orElse(0));
        leads.setProvinceName(request.getProvinceName());
        leads.setCityId(Optional.ofNullable(request.getCityId()).orElse(0));
        leads.setCityName(request.getCityName());
        leads.setAreaId(Optional.ofNullable(request.getAreaId()).orElse(0));
        leads.setAreaName(request.getAreaName());
        leads.setAddress(request.getAddress());
        leads.setChannel(request.getChannel());
        leads.setOrderAccount(request.getOrderAccount());
        leads.setOrderSn(request.getOrderSn());
        leads.setGoodsName(request.getGoodsName());
        leads.setGoodsNum(Optional.ofNullable(request.getGoodsNum()).orElse(0));
        leads.setPayTime(Optional.ofNullable(DateTimeUtils.MillisToLocalDateTime(request.getPayTime()))
                .orElse(DateTimeUtils.toLocalDateTime("")));
        leads.setPayAmount(Optional.ofNullable(request.getPayAmount()).orElse(BigDecimal.valueOf(0L)));
        leads.setOrderMessage(request.getOrderMessage());
        leads.setRemark(request.getRemark());
        leads.setTpLeadsId(request.getTpLeadsId());
        leads.setPromotionName(request.getPromotionName());
        leads.setRegistryTime(DateTimeUtils.MillisToLocalDateTime(request.getRegistryTime()));
        leads.setExpectIn(request.getExpectIn());
        leads.setStyle(request.getStyle());
        leads.setBatchId(request.getBatchId());
        leads.setExtData(request.getExtData());
        leads.setWeChat(request.getWeChat());
        leads.setGoodsLink(request.getGoodsLink());
        leads.setChannelSource(request.getChannelSource());
        leads.setPreFollowStaffId(request.getPreFollowStaffId());
        leads.setPreFollowStatus(request.getPreFollowStatus());
        leads.setDemandProduct(request.getDemandProduct());

        //2022-10-24添加字段
        leads.setType(request.getType());
        leads.setLevel(request.getLevel());
        if (Objects.nonNull(request.getTags())){
            leads.setTags(String.join(InngkeAppConst.COMMA_STR, request.getTags()));
        }
        if (Objects.nonNull(request.getEnterpriseTags())){
            leads.setEnterpriseTags(String.join(InngkeAppConst.COMMA_STR,request.getEnterpriseTags()));
        }
        if (Objects.nonNull(request.getExternalTags())){
            leads.setExternalTags(String.join(InngkeAppConst.COMMA_STR, request.getExternalTags()));
        }

        leads.setProductIds(CollectionUtils.isEmpty(request.getProductIds()) ? "" : Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(request.getProductIds()));

        //2023-03-25   平台ID
        leads.setTpId(request.getTpId());

        // 处理附件的类型
        List<LeadsAttachment> attachmentList = Optional.ofNullable(request.getAttachmentList()).orElse(Lists.newArrayList());
        for (LeadsAttachment attachment : attachmentList) {
            if (StringUtils.isBlank(attachment.getFileType())) {
                String fileType = FileIdentifierUtil.getNetworkFileType(attachment.getUrl());
                attachment.setFileType(fileType);
            }
        }
        leads.setAttachmentList(JsonUtil.toJsonString(attachmentList));

        return leads;
    }

    public static LeadsCallLogDto leadsCallLogDto(LeadsCallLog callLog) {
        LeadsCallLogDto leadsCallLogDto = new LeadsCallLogDto();
        leadsCallLogDto.setId(callLog.getId());
        leadsCallLogDto.setBid(callLog.getBid());
        leadsCallLogDto.setLeadsId(callLog.getLeadsId());
        leadsCallLogDto.setVoiceId(callLog.getVoiceId());
        return leadsCallLogDto;
    }

    public static LeadsNameDto toLeadsNameDto(Leads leads) {
        if (Objects.isNull(leads)) {
            return null;
        }
        LeadsNameDto leadsNameDto = new LeadsNameDto();
        leadsNameDto.setId(leads.getId());
        leadsNameDto.setName(leads.getName());
        leadsNameDto.setProvinceId(leads.getProvinceId());
        leadsNameDto.setProvinceName(leads.getProvinceName());
        leadsNameDto.setCityId(leads.getCityId());
        leadsNameDto.setCityName(leads.getCityName());
        leadsNameDto.setAreaId(leads.getAreaId());
        leadsNameDto.setAreaName(leads.getAreaName());
        leadsNameDto.setAddress(leads.getAddress());
        return leadsNameDto;
    }

    public static void updateWrapperSetLeadsInfo(UpdateWrapper<Leads> updateWrapper, Leads leads){
        if(Objects.nonNull(leads.getMobile())){
            updateWrapper.set(Leads.MOBILE,leads.getMobile());
        }
        if (Objects.nonNull(leads.getWeChat())) {
            updateWrapper.set(Leads.WE_CHAT, leads.getWeChat());
        }

        String provinceName = leads.getProvinceName();
        updateWrapper.set(Leads.PROVINCE_NAME, provinceName);

        Integer provinceId = StringUtils.isEmpty(provinceName) ? 0 : leads.getProvinceId();
        updateWrapper.set(Leads.PROVINCE_ID, provinceId);

        String cityName = leads.getCityName();
        updateWrapper.set(Leads.CITY_NAME, cityName);

        Integer cityId = StringUtils.isEmpty(cityName) ? 0 : leads.getCityId();
        updateWrapper.set(Leads.CITY_ID, cityId);

        String areaName = leads.getAreaName();
        updateWrapper.set(Leads.AREA_NAME, areaName);

        Integer areaId = StringUtils.isEmpty(areaName) ? 0 : leads.getAreaId();
        updateWrapper.set(Leads.AREA_ID, areaId);

        updateWrapper.set(Leads.ADDRESS,leads.getAddress());

        if(Objects.nonNull(leads.getRemark())){
            updateWrapper.set(Leads.REMARK,leads.getRemark());
        }

        if (StringUtils.isNotEmpty(leads.getDemandProduct())){
            updateWrapper.set(Leads.DEMAND_PRODUCT,leads.getDemandProduct());
        }

        if (Objects.nonNull(leads.getLevelId())) {
            updateWrapper.set(Leads.LEVEL_ID, leads.getLevelId());
        }
    }

    public static LeadsFollowTimeDto toLeadsFollowTimeDto(LeadsFollowTime leadsFollowTime) {
        LeadsFollowTimeDto leadsFollowTimeDto = new LeadsFollowTimeDto();
        leadsFollowTimeDto.setId(leadsFollowTime.getId());
        leadsFollowTimeDto.setBid(leadsFollowTime.getBid());
        leadsFollowTimeDto.setAgentId(leadsFollowTime.getAgentId());
        leadsFollowTimeDto.setStaffId(leadsFollowTime.getStaffId());
        leadsFollowTimeDto.setDepartmentId(leadsFollowTime.getDepartmentId());
        leadsFollowTimeDto.setDistributeTime(leadsFollowTime.getDistributeTime());
        leadsFollowTimeDto.setStateNoAvail(leadsFollowTime.getStateNoAvail());
        leadsFollowTimeDto.setStateContact(leadsFollowTime.getStateContact());
        leadsFollowTimeDto.setStateContactSuccess(leadsFollowTime.getStateContactSuccess());
        leadsFollowTimeDto.setStateMeasuring(leadsFollowTime.getStateMeasuring());
        leadsFollowTimeDto.setStateArrivalStore(leadsFollowTime.getStateArrivalStore());
        leadsFollowTimeDto.setStateDeposit(leadsFollowTime.getStateDeposit());
        leadsFollowTimeDto.setStateOrderSuccess(leadsFollowTime.getStateOrderSuccess());
        leadsFollowTimeDto.setStateOfferPrice(leadsFollowTime.getStateOfferPrice());
        leadsFollowTimeDto.setStateInstall(leadsFollowTime.getStateInstall());
        leadsFollowTimeDto.setTransferClientTime(leadsFollowTime.getTransferClientTime());
        return leadsFollowTimeDto;
    }

    public static LeadsBasicDto toLeadsBasicDto(Leads leads) {
        if (Objects.isNull(leads)) {
            return null;
        }

        LeadsBasicDto leadsBasicDto = new LeadsBasicDto();
        leadsBasicDto.setId(leads.getId());
        leadsBasicDto.setBid(leads.getBid());
        leadsBasicDto.setCustomerId(leads.getCustomerId());
        leadsBasicDto.setCustomerUid(leads.getCustomerUid());
        leadsBasicDto.setChannelId(leads.getChannelId());
        leadsBasicDto.setClientId(leads.getClientId());
        leadsBasicDto.setName(leads.getName());
        leadsBasicDto.setMobile(leads.getMobile());
        leadsBasicDto.setWeChat(leads.getWeChat());
        leadsBasicDto.setStatus(leads.getStatus());
        leadsBasicDto.setProvinceId(leads.getProvinceId());
        leadsBasicDto.setProvinceName(leads.getProvinceName());
        leadsBasicDto.setCityId(leads.getCityId());
        leadsBasicDto.setCityName(leads.getCityName());
        leadsBasicDto.setAreaId(leads.getAreaId());
        leadsBasicDto.setAreaName(leads.getAreaName());
        leadsBasicDto.setAddress(leads.getAddress());
        leadsBasicDto.setChannel(leads.getChannel());
        leadsBasicDto.setChannelType(leads.getChannelType());
        leadsBasicDto.setChannelSource(leads.getChannelSource());
        leadsBasicDto.setTpLeadsId(leads.getTpLeadsId());
        leadsBasicDto.setBatchId(leads.getBatchId());
        leadsBasicDto.setDistributeStaffId(leads.getDistributeStaffId());
        leadsBasicDto.setDistributeTime(leads.getDistributeTime());
        leadsBasicDto.setDistributeFollowTime(leads.getDistributeFollowTime());
        leadsBasicDto.setPushBackTime(leads.getPushBackTime());
        leadsBasicDto.setErrorMsg(leads.getErrorMsg());
        leadsBasicDto.setLastFollowId(leads.getLastFollowId());
        leadsBasicDto.setExtData(leads.getExtData());
        leadsBasicDto.setTags(Sets.newHashSet(Splitter.on(InngkeAppConst.COMMA_STR).trimResults().splitToList(leads.getTags())));
        leadsBasicDto.setShowPhone(leads.getShowPhone());
        leadsBasicDto.setRecoveryFrom(leads.getRecoveryFrom());
        leadsBasicDto.setFirstContactTime(leads.getFirstContactTime());
        leadsBasicDto.setLastContactTime(leads.getLastContactTime());
        leadsBasicDto.setCreateTime(leads.getCreateTime());
        leadsBasicDto.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsBasicDto.setPreFollowStatus(leads.getPreFollowStatus());
        leadsBasicDto.setType(leads.getType());
        leadsBasicDto.setCreateStaffId(leads.getCreateStaffId());
        leadsBasicDto.setLevel(leads.getLevel());
        leadsBasicDto.setPushBackStaffId(leads.getPushBackStaffId());
        leadsBasicDto.setRecoveryFromIds(leads.getRecoveryFromIds());
        leadsBasicDto.setFollowStatuses(leads.getFollowStatuses());
        leadsBasicDto.setKfFollowStatuses(leads.getKfFollowStatuses());
        leadsBasicDto.setRemark(leads.getRemark());
        return leadsBasicDto;
    }


    public static com.inngke.bp.client.request.LeadsBasicDto leadsBasicDtoToClientLeadsBasicDto(LeadsBasicDto dto) {
        com.inngke.bp.client.request.LeadsBasicDto leadsBasicDto = new com.inngke.bp.client.request.LeadsBasicDto();
        leadsBasicDto.setId(dto.getId());
        leadsBasicDto.setBid(dto.getBid());
        leadsBasicDto.setCustomerId(dto.getCustomerId());
        leadsBasicDto.setCustomerUid(dto.getCustomerUid());
        leadsBasicDto.setChannelId(dto.getChannelId());
        leadsBasicDto.setClientId(dto.getClientId());
        leadsBasicDto.setName(dto.getName());
        leadsBasicDto.setMobile(dto.getMobile());
        leadsBasicDto.setWeChat(dto.getWeChat());
        leadsBasicDto.setStatus(dto.getStatus());
        leadsBasicDto.setProvinceId(dto.getProvinceId());
        leadsBasicDto.setProvinceName(dto.getProvinceName());
        leadsBasicDto.setCityId(dto.getCityId());
        leadsBasicDto.setCityName(dto.getCityName());
        leadsBasicDto.setAreaId(dto.getAreaId());
        leadsBasicDto.setAreaName(dto.getAreaName());
        leadsBasicDto.setAddress(dto.getAddress());
        leadsBasicDto.setChannel(dto.getChannel());
        leadsBasicDto.setChannelType(dto.getChannelType());
        leadsBasicDto.setChannelSource(dto.getChannelSource());
        leadsBasicDto.setTpLeadsId(dto.getTpLeadsId());
        leadsBasicDto.setBatchId(dto.getBatchId());
        leadsBasicDto.setDistributeStaffId(dto.getDistributeStaffId());
        leadsBasicDto.setDistributeTime(dto.getDistributeTime());
        leadsBasicDto.setDistributeFollowTime(dto.getDistributeFollowTime());
        leadsBasicDto.setPushBackTime(dto.getPushBackTime());
        leadsBasicDto.setErrorMsg(dto.getErrorMsg());
        leadsBasicDto.setLastFollowId(dto.getLastFollowId());
        leadsBasicDto.setExtData(dto.getExtData());
        leadsBasicDto.setTags(dto.getTags());
        leadsBasicDto.setShowPhone(dto.getShowPhone());
        leadsBasicDto.setRecoveryFrom(dto.getRecoveryFrom());
        leadsBasicDto.setFirstContactTime(dto.getFirstContactTime());
        leadsBasicDto.setLastContactTime(dto.getLastContactTime());
        leadsBasicDto.setCreateTime(dto.getCreateTime());
        leadsBasicDto.setPreFollowStaffId(dto.getPreFollowStaffId());
        leadsBasicDto.setPreFollowStatus(dto.getPreFollowStatus());
        leadsBasicDto.setType(dto.getType());
        leadsBasicDto.setCreateStaffId(dto.getCreateStaffId());
        leadsBasicDto.setLevel(dto.getLevel());
        leadsBasicDto.setPushBackStaffId(dto.getPushBackStaffId());
        leadsBasicDto.setRecoveryFromIds(dto.getRecoveryFromIds());
        leadsBasicDto.setFollowStatuses(dto.getFollowStatuses());
        leadsBasicDto.setKfFollowStatuses(dto.getKfFollowStatuses());
        leadsBasicDto.setBid(dto.getBid());
        leadsBasicDto.setRemark(dto.getRemark());
        return leadsBasicDto;

    }

    public static SimpleLeadsDto toSimpleLeads(Leads leads) {
        if (Objects.isNull(leads)) {
            return null;
        }
        SimpleLeadsDto simpleLeadsDto = new SimpleLeadsDto();
        simpleLeadsDto.setId(leads.getId());
        simpleLeadsDto.setBid(leads.getBid());
        simpleLeadsDto.setName(leads.getName());
        simpleLeadsDto.setMobile(leads.getMobile());
        simpleLeadsDto.setStatus(leads.getStatus());
        simpleLeadsDto.setRelationClientTime(leads.getRelationClientTime());
        return simpleLeadsDto;

    }

    public static LeadsInformationAddRequest toLeadsInformationAddRequest(GuideCreateLeadsRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        LeadsInformationAddRequest leadsInformationAddRequest = new LeadsInformationAddRequest();
        leadsInformationAddRequest.setName(request.getName());
        leadsInformationAddRequest.setMobile(request.getMobile());
        leadsInformationAddRequest.setProvinceId(request.getProvinceId());
        leadsInformationAddRequest.setProvinceName(request.getProvinceName());
        leadsInformationAddRequest.setCityId(request.getCityId());
        leadsInformationAddRequest.setCityName(request.getCityName());
        leadsInformationAddRequest.setAreaId(request.getAreaId());
        leadsInformationAddRequest.setAreaName(request.getAreaName());
        leadsInformationAddRequest.setAddress(request.getAddress());
        leadsInformationAddRequest.setChannel(request.getChannel());
        leadsInformationAddRequest.setChannelId(request.getChannelId());
        leadsInformationAddRequest.setRemark(request.getRemark());
        leadsInformationAddRequest.setWeChat(request.getWeChat());
        leadsInformationAddRequest.setType(request.getType());
        leadsInformationAddRequest.setCreateStaffId(request.getOperatorStaffId());
        leadsInformationAddRequest.setReportStaffId(request.getOperatorStaffId());
        leadsInformationAddRequest.setLevelId(request.getLevelId());
        leadsInformationAddRequest.setProductIds(Optional.ofNullable(request.getProductIds()).map(Lists::newArrayList).orElse(Lists.newArrayList()));
        leadsInformationAddRequest.setNeedSendMQ(Boolean.TRUE);
        leadsInformationAddRequest.setOperatorId(request.getOperatorId());
        leadsInformationAddRequest.setBid(request.getBid());
        return leadsInformationAddRequest;

    }

    public static Leads toLeads(GuideCreateLeadsRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        Leads leads = new Leads();
        leads.setId(SnowflakeHelper.getId());
        leads.setBid(request.getBid());
        leads.setName(request.getName());
        leads.setMobile(request.getMobile());
        leads.setWeChat(request.getWeChat());
        leads.setStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
        leads.setChannelSource(LeadsInputSourceEnum.FORM_SUBMISSION.getCode());
        leads.setProvinceId(request.getProvinceId());
        leads.setProvinceName(request.getProvinceName());
        leads.setCityId(request.getCityId());
        leads.setCityName(request.getCityName());
        leads.setAreaId(request.getAreaId());
        leads.setAreaName(request.getAreaName());
        leads.setAddress(request.getAddress());
        leads.setChannel(request.getChannel());
        leads.setChannelId(request.getChannelId());
        leads.setRemark(request.getRemark());
        leads.setCreateTime(LocalDateTime.now());
        leads.setUpdateTime(LocalDateTime.now());
        leads.setType(request.getType());
        leads.setCreateStaffId(request.getOperatorStaffId());
        leads.setDistributeStaffId(request.getOperatorStaffId());
        leads.setDistributeTime(LocalDateTime.now());
        leads.setLevelId(request.getLevelId());
        leads.setProductIds(Optional.ofNullable(request.getProductIds()).map(element -> Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(element)).orElse(""));
        return leads;

    }
}


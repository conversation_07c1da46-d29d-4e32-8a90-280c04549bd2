/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索跟进数据报表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsFollowTime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线索ID，即leads.id
     */
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 经销商ID
     */
    private Long agentId;

    /**
     * 跟进人员工ID，即staffId
     */
    private Long staffId;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 线索分配的时间
     */
    private Long distributeTime;

    /**
     * 被标识为无效的时间
     */
    private Long stateNoAvail;

    /**
     * 首次联系时间
     */
    private Long stateContact;

    /**
     * 成功联系时间
     */
    private Long stateContactSuccess;

    /**
     * 量尺时间
     */
    private Long stateMeasuring;

    /**
     * 到店时间
     */
    private Long stateArrivalStore;

    /**
     * 定金记录创建时间
     */
    private Long stateDeposit;

    /**
     * 成交时间
     */
    private Long stateOrderSuccess;

    /**
     * 报价时间
     */
    private Long stateOfferPrice;

    /**
     * 安装时间
     */
    private Long stateInstall;

    /**
     * 转客户时间
     */
    private Long transferClientTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String AGENT_ID = "agent_id";

    public static final String STAFF_ID = "staff_id";

    public static final String DEPARTMENT_ID = "department_id";

    public static final String DISTRIBUTE_TIME = "distribute_time";

    public static final String STATE_NO_AVAIL = "state_no_avail";

    public static final String STATE_CONTACT = "state_contact";

    public static final String STATE_CONTACT_SUCCESS = "state_contact_success";

    public static final String STATE_MEASURING = "state_measuring";

    public static final String STATE_ARRIVAL_STORE = "state_arrival_store";

    public static final String STATE_DEPOSIT = "state_deposit";

    public static final String STATE_ORDER_SUCCESS = "state_order_success";

    public static final String STATE_OFFER_PRICE = "state_offer_price";

    public static final String STATE_INSTALL = "state_install";

    public static final String TRANSFER_CLIENT_TIME = "transfer_client_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

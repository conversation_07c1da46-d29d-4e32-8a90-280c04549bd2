package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static com.inngke.bp.leads.service.impl.LeadsFollowServiceImpl.CACHE_LEADS_FOLLOW;

/**
 *
 * <AUTHOR>
 * @since 2023-6-1 11:29:32
 */
@Component
public class LeadsFollowCacheServiceImpl implements LeadsFollowCacheService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public BaseResponse add(Integer bid, Long leadsFollowId) {
        AsyncUtils.runAsync(() -> {
            redisTemplate.boundSetOps(CACHE_LEADS_FOLLOW).add(bid + InngkeAppConst.UNDERLINE_STR + leadsFollowId);
            redisTemplate.expire(CACHE_LEADS_FOLLOW, 60 * 60, TimeUnit.SECONDS);
        });
        return BaseResponse.success();
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.bp.leads.dto.request.GetPreFollowStaffReportRequest;
import com.inngke.bp.leads.dto.request.ListByMobileOrWeChatRequest;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
import com.inngke.bp.leads.dto.response.PreFollowStaffReportDto;
import com.inngke.common.ds.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsDao extends BaseMapper<Leads> {

    /**
     * 统计线索
     * @param pageParam
     * @param wrapper
     * @return
     */
    @Select("SELECT " +
            "bid, " +
            "min(distribute_time) as earliestDistributeTime, " +
            "group_concat(mobile) as mobiles, " +
            "group_concat(customer_uid) as customerUids, " +
            "distribute_agent_id AS agentId, " +
            "distribute_staff_id AS staffId, " +
            "group_concat(mobile), " +
            "group_concat(customer_uid), " +
            "COUNT( * ) AS leadsCount " +
            "FROM " +
            "leads ${ew.customSqlSegment}")
    List<LeadsStatisticsDto> pageStatistics(@Param(Constants.WRAPPER) QueryWrapper<Leads> wrapper);

    /**
     * 查询分组后的数据量
     * @param queryWrapper
     * @return
     */
    @Select("SELECT COUNT(*) FROM (SELECT COUNT(*) FROM leads ${ew.customSqlSegment}) A")
    Integer getGroupCount(@Param(Constants.WRAPPER)QueryWrapper<Leads> queryWrapper);

    @Select("SELECT mobile, min(distribute_time) FROM leads GROUP BY mobile")
    Map<String, LocalDateTime> getMobileAndDistributeTime();

    @Select("<script>" +
            "SELECT pre_follow_staff_id `staffId`,mobile FROM `leads` " +
            "   <where>" +
            "bid = #{request.bid} " +
            "and status not in(-4,-5) " +
            "and pay_time &gt;  #{request.startTime} " +
            "and pay_time &lt;  #{request.endTime} " +
            "and pre_follow_staff_id in \n" +
            "        <foreach item=\"item\" index=\"index\" collection=\"staffIds\"\n" +
            "                    open=\"(\" separator=\",\" close=\")\">\n" +
            "            #{item}\n" +
            " </foreach>" +
            "  </where>" +
            "</script>"
    )
    List<PreFollowStaffReportDto> getStatisticsByPreFollowStaffIds(@Param("request") GetPreFollowStaffReportRequest request, @Param("staffIds") List<Long> staffIds);

    @Select("<script>" +
            "select l.* from leads_bp_yk.leads l " +
            "left join user_bp_yk.customer c on l.customer_id = c.id " +
            "where l.customer_id > 0 and l.bid != c.bid " +
            "</script>")
    List<Leads> selectBadCustomerLeads();


    /**
     * 根据手机号或者微信查询线索
     * @param
     * @param
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "\tid,name,mobile,we_chat,create_time\n" +
            "FROM\n" +
            "\tleads \n" +
            "WHERE\n" +
            "\t( bid = #{request.bid} AND mobile = #{request.mobile}  and status not in(-5,-4) " +
            "<if test='request.id != null and request.id !=\"\"'>" +
                "and id != #{request.id} " +
            "</if> " +
            ")"+
            "<if test='request.excludeIds != null and request.excludeIds.size>0 '>" +
            "and id not in \n" +
            "        <foreach item=\"item\" index=\"index\" collection=\"request.excludeIds\"\n" +
            "                    open=\"(\" separator=\",\" close=\")\">\n" +
            "            #{item}\n" +
            " </foreach>" +
            "</if> " +

            "UNION\n" +

            "SELECT\n" +
            "\tid,name,mobile,we_chat,create_time\n" +
            "FROM\n" +
            "\tleads \n" +
            "WHERE\n" +
            "\t( bid = #{request.bid} AND we_chat = #{request.weChat} and status not in(-5,-4) " +
            "<if test='request.id != null and request.id !=\"\"'>" +
                "and id != #{request.id} " +
            "</if> " +
            ")"+
            "<if test='request.excludeIds != null and request.excludeIds.size>0 '>" +
            "and id not in \n" +
            "        <foreach item=\"item\" index=\"index\" collection=\"request.excludeIds\"\n" +
            "                    open=\"(\" separator=\",\" close=\")\">\n" +
            "            #{item}\n" +
            " </foreach>" +
            "</if> " +
            "</script>"
    )
    List<Leads> listByMobileOrWeChat(@Param("request") ListByMobileOrWeChatRequest request);

    @Select("<script>" +
            "SELECT l.id, l.bid, lf.leads_status AS follow_status " +
            "FROM leads AS l " +
            "         LEFT JOIN leads_follow lf on l.id = lf.leads_id AND lf.leads_status = 2 " +
            "WHERE l.client_id > 0 " +
            "HAVING follow_status IS NULL" +
            "</script>")
    List<Leads> findRelationClientAndNotContactLeads(Integer bid);
}

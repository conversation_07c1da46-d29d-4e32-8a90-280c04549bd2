/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.dao.LeadsBackReasonDao;
import com.inngke.bp.leads.db.leads.entity.LeadsBackReason;
import com.inngke.bp.leads.db.leads.entity.LeadsInvalidReason;
import com.inngke.bp.leads.db.leads.manager.LeadsBackReasonManager;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsBackReasonManagerImpl extends ServiceImpl<LeadsBackReasonDao, LeadsBackReason> implements LeadsBackReasonManager {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveSort(Integer bid, LeadsBackReason prevLeadsBackReason, LeadsBackReason nextLeadsBackReason) {
        LeadsBackReason newPrevLeadsChannel = new LeadsBackReason();
        newPrevLeadsChannel.setId(prevLeadsBackReason.getId());
        newPrevLeadsChannel.setSort(nextLeadsBackReason.getSort());

        LeadsBackReason newNextLeadsChannel = new LeadsBackReason();
        newNextLeadsChannel.setId(nextLeadsBackReason.getId());
        newNextLeadsChannel.setSort(prevLeadsBackReason.getSort());
        updateBatchById(Lists.newArrayList(newPrevLeadsChannel,newNextLeadsChannel));
    }

    @Override
    public LeadsBackReason getById(Integer bid, Long id) {
        return this.getOne(Wrappers.<LeadsBackReason>query()
                .eq(LeadsBackReason.BID, bid)
                .eq(LeadsBackReason.ID, id));
    }
}

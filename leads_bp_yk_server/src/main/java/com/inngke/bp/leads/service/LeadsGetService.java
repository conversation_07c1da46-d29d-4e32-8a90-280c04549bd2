package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsListItemDto;
import com.inngke.bp.leads.dto.response.LeadsListVo;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface LeadsGetService {

    /**
     * 获取线索列表
     *
     * @return com.inngke.bp.leads.dto.response.LeadsListItemDto
     */
    BaseResponse<LeadsListVo> search(SearchLeadsRequest request);

    /**
     * 获取线索列表(供后端)
     */
    BaseResponse<List<LeadsEsDto>> searchByEs(SearchLeadsRequest request);

    BaseResponse<List<LeadsEsDto>> batchGetLeads(BaseIdRequest request);
}

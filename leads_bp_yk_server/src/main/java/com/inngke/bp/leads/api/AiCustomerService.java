package com.inngke.bp.leads.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.bp.leads.api.dto.GetLeadsListRequest;
import com.inngke.bp.leads.dto.platform.CustomerServiceResponse;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @date 2022/2/28 13:38
 */
@RetrofitClient(
        baseUrl = "https://ai.youju360.com/publicapi/",
        callTimeoutMs = 100000,
        connectTimeoutMs = 991000,
        readTimeoutMs = 991000,
        converterFactories = AiCustomerConverterFactories.class,
        logLevel = LogLevel.INFO,
        logStrategy = LogStrategy.BODY
)
public interface AiCustomerService {

    /**
     * 获取Ai客服线索
     * <p>
     * 接口文档: https://docs.qq.com/doc/DUFVsd2FlVHhsT1VI
     */
    @POST("user/index")
    CustomerServiceResponse getLeadsList(@Body GetLeadsListRequest getLeadsListRequest);
}

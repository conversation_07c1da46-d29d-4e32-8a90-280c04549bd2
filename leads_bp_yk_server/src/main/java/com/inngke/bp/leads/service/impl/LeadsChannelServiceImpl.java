package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.core.utils.LeadsCommonUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsChannel;
import com.inngke.bp.leads.db.leads.manager.LeadsChannelManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.impl.LeadsChannelManagerImpl;
import com.inngke.bp.leads.dto.request.LeadsChannelListRequest;
import com.inngke.bp.leads.dto.request.LeadsChannelMoveSortRequest;
import com.inngke.bp.leads.dto.request.LeadsChannelSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsChannelDto;
import com.inngke.bp.leads.dto.response.LeadsChannelValueDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsChannelService;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.consts.LeadsServiceConsts.LOCK_PREFIX;

@Service
@DubboService(version = "1.0.0")
@Slf4j
public class LeadsChannelServiceImpl implements LeadsChannelService {
    private static final String LOCK = LOCK_PREFIX + "leadsChannel_%s";
    @Autowired
    private LeadsChannelManager leadsChannelManager;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LockService lockService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsChannelCacheFactory leadsChannelCacheFactory;

    @Override
    public BaseResponse<List<LeadsChannelDto>> getList(LeadsChannelListRequest request) {
        QueryWrapper<LeadsChannel> queryWrapper = getQueryWrapper(request);
        List<LeadsChannel> leadsChannelList = leadsChannelManager.list(queryWrapper);
        List<LeadsChannelDto> leadsChannelDtoList = dealLeadsChannelList(request, leadsChannelList);
        return BaseResponse.success(buildChildren(leadsChannelDtoList));
    }

    private List<LeadsChannelDto> buildChildren(List<LeadsChannelDto> leadsChannelDtoList) {
        if (CollectionUtils.isEmpty(leadsChannelDtoList)) {
            return new ArrayList<>();
        }

        Map<Long, List<LeadsChannelDto>> map = new HashMap<>();
        leadsChannelDtoList.forEach(item -> map.computeIfAbsent(item.getParentId(), l -> new ArrayList<>()).add(item));
        List<LeadsChannelDto> result = map.get(0L);

        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }

        for (LeadsChannelDto dto : result) {
            dto.setChildren(map.getOrDefault(dto.getId(), new ArrayList<>()));
        }

        return result;
    }

    @Override
    public BaseResponse save(LeadsChannelSaveRequest request) {
        Integer bid = request.getBid();
        Long id = request.getId();
        request.setName(LeadsCommonUtil.trim(request.getName()));
        Lock lock = lockService.getLock(String.format(LOCK, bid), 60);
        if (lock == null) {
            return BaseResponse.error("同时操作中，请稍后刷新下再操作！");
        }

        try {
            checkName2(request);
            checkDelete(request);
            LeadsChannel newLeadsChannel = toLeadsChannel(request);
            if (Objects.isNull(id)) {
                checkCount(request);
                int count = leadsChannelManager.count(Wrappers.<LeadsChannel>query().eq(LeadsChannel.BID, bid).ge(LeadsChannel.VALUE, LeadsChannelManagerImpl.DEFAULT_VALUE));
                newLeadsChannel.setValue(LeadsChannelManagerImpl.DEFAULT_VALUE + count);
                newLeadsChannel.setSort(LeadsChannelManagerImpl.DEFAULT_VALUE + count);
                newLeadsChannel.setId(SnowflakeHelper.getId());
                newLeadsChannel.setParentId(request.getParentId());
                leadsChannelManager.save(newLeadsChannel);
                leadsChannelCacheFactory.incCacheVersion(bid);
                return BaseResponse.success();
            }
            LeadsChannel leadsChannel = leadsChannelManager.getOne(getQueryWrapper(bid, id));
            if (Objects.isNull(leadsChannel)) {
                return BaseResponse.error("数据不存在！");
            }
            if (leadsChannel.getEditType().equals(-1)) {
                return BaseResponse.error("数据不可编辑！");
            }
            if (leadsChannel.getEditType().equals(0) && !StringUtils.isEmpty(newLeadsChannel.getName())) {
                return BaseResponse.error("数据不允许编辑！");
            }
            if (leadsChannel.getEditType().equals(0) && !Objects.isNull(newLeadsChannel.getStatus())) {
                return BaseResponse.error("数据不允许编辑！");
            }
            if (!Objects.isNull(newLeadsChannel.getStatus()) && newLeadsChannel.getStatus().equals(-1)) {
                int count = leadsManager.count(Wrappers.<Leads>query().eq(Leads.BID, bid).eq(Leads.CHANNEL, leadsChannel.getValue()).notIn(Leads.STATUS, LeadsStatusEnum.DELETED.getStatus(), LeadsStatusEnum.RECOVERY.getStatus()));
                if (count > 0) {
//                    return BaseResponse.error("系统内已有线索渠道来源为\"" + leadsChannel.getName() + "\"，暂时无法删除，如需删除，请先修改线索渠道来源");
                    return BaseResponse.error("已有线索被标记为该渠道来源，暂时不能删除!");
                }
            }
            leadsChannelManager.save(bid, leadsChannel, newLeadsChannel);
            leadsChannelCacheFactory.incCacheVersion(bid);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        return BaseResponse.success();
    }

    private void checkDelete(LeadsChannelSaveRequest request) {
        Integer status = request.getStatus();
        if (status == null || !status.equals(-1)) {
            return;
        }
        List<LeadsChannelDto> list = getList(request.getBid(), request.getOperatorId());
        LeadsChannelDto leadsChannelByParentId = getLeadsChannelByParentId(list, request.getId());
        if (leadsChannelByParentId != null && !CollectionUtils.isEmpty(leadsChannelByParentId.getChildren())) {
            throw new InngkeServiceException("请先删除子渠道");
        }
    }

    @Override
    public BaseResponse switchSort(LeadsChannelMoveSortRequest request) {
        Integer bid = request.getBid();
        Lock lock = lockService.getLock(String.format(LOCK, bid), 60);
        if (lock == null) {
            return BaseResponse.error("同时操作中，请稍后刷新下再操作！");
        }
        try {
            Long prevId = request.getPrevId();
            Long nextId = request.getNextId();

            LeadsChannel prevLeadsChannel = leadsChannelManager.getOne(getQueryWrapper(bid, prevId));
            LeadsChannel nextLeadsChannel = leadsChannelManager.getOne(getQueryWrapper(bid, nextId));
            if (Objects.isNull(prevLeadsChannel) || Objects.isNull(nextLeadsChannel)) {
                return BaseResponse.error("数据不存在！");
            }
            if (prevLeadsChannel.getEditType().equals(-1) || nextLeadsChannel.getEditType().equals(-1)) {
                return BaseResponse.error("数据不可编辑！");
            }
            leadsChannelManager.moveSort(bid, prevLeadsChannel, nextLeadsChannel);
            leadsChannelCacheFactory.incCacheVersion(bid);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<List<LeadsChannelValueDto>> getValueList(BaseBidOptRequest request) {
        LeadsChannelCacheFactory.LeadsChannelValueCache cache = leadsChannelCacheFactory.getCache(request.getBid());
        return BaseResponse.success(cache.getList());
    }

    @Override
    public BaseResponse<List<LeadsChannelDto>> findChildrenChannel(BaseIdRequest request) {
        List<LeadsChannel> childrenChannels = leadsChannelManager.list(
                Wrappers.<LeadsChannel>query()
                        .in(LeadsChannel.BID, Sets.newHashSet(0, request.getBid()))
                        .eq(LeadsChannel.PARENT_ID, request.getId())
                        .eq(LeadsChannel.STATUS, 1)
        );
        return BaseResponse.success(childrenChannels.stream().map(this::toLeadsChannelDto).collect(Collectors.toList()));
    }

    private void checkCount(LeadsChannelSaveRequest request) {
        Long parentId = request.getParentId();
        List<LeadsChannelDto> list = getList(request.getBid(), request.getOperatorId());
        boolean root = (parentId == null || parentId.equals(0L));
        if (root && list.size() >= 100) {
            throw new InngkeServiceException("一级渠道最多只能添加100个");
        }
        if (root) {
            return;
        }
        LeadsChannelDto leadsChannelByParentId = getLeadsChannelByParentId(list, parentId);
        if (leadsChannelByParentId == null) {
            throw new InngkeServiceException("父级渠道不存在");
        }
        if (!leadsChannelByParentId.getParentId().equals(0L)) {
            throw new InngkeServiceException("只能添加二级子分类");
        }
        if (!CollectionUtils.isEmpty(leadsChannelByParentId.getChildren()) && leadsChannelByParentId.getChildren().size() >= 30) {
            throw new InngkeServiceException("子分类最多只能添加30个");
        }
    }

    private List<LeadsChannelDto> getList(int bid, Long operatorId) {
        LeadsChannelListRequest leadsChannelListRequest = new LeadsChannelListRequest();
        leadsChannelListRequest.setBid(bid);
        leadsChannelListRequest.setOperatorId(operatorId);
        leadsChannelListRequest.setGtEditType(-1);
        BaseResponse<List<LeadsChannelDto>> baseResponse = getList(leadsChannelListRequest);
        if (!BaseResponse.responseSuccess(baseResponse)) {
            log.warn("检查渠道数失败！request={}，response={}", jsonService.toJson(leadsChannelListRequest), jsonService.toJson(baseResponse));
            throw new InngkeServiceException("检查渠道数失败！");
        }
        return baseResponse.getData();
    }

    private void leadsChannelName(List<LeadsChannelDto> list, String name, Long id) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (LeadsChannelDto leadsChannelDto : list) {
            if (leadsChannelDto.getName().equals(name) && !leadsChannelDto.getId().equals(id)) {
                throw new InngkeServiceException("同一层级渠道名称不能重复");
            }
        }
    }

    private LeadsChannelDto getLeadsChannelByParentId(List<LeadsChannelDto> list, Long parentId) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (LeadsChannelDto leadsChannelDto : list) {
            if (leadsChannelDto.getId().equals(parentId)) {
                return leadsChannelDto;
            }
        }
        return null;
    }

    private void checkName(LeadsChannelSaveRequest request) {
        Integer bid = request.getBid();
        Long id = request.getId();
        String name = request.getName();
        if (StringUtils.isEmpty(name)) {
            return;
        }
        List<Integer> bids = getBids(bid);
        QueryWrapper<LeadsChannel> queryWrapper = Wrappers.<LeadsChannel>query()
                .in(LeadsChannel.BID, bids)
                .eq(LeadsChannel.NAME, name)
                .ne(LeadsChannel.STATUS, -1);
        if (!Objects.isNull(id)) {
            queryWrapper.ne(LeadsChannel.ID, id);
        }
        List<LeadsChannel> leadsChannelList = leadsChannelManager.list(queryWrapper);
        if (CollectionUtils.isEmpty(leadsChannelList)) {
            return;
        }

        List<Integer> values = leadsChannelList.stream().filter(leadsChannel -> leadsChannel.getBid().equals(0)).map(LeadsChannel::getValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(values)) {
            long count = leadsChannelList.stream().filter(leadsChannel -> !leadsChannel.getBid().equals(0)).count();
            if (count > 0) {
                throw new InngkeServiceException("渠道名称不能重复");
            }
        }else {
            int count = leadsChannelManager.count(
                    Wrappers.<LeadsChannel>query()
                            .eq(LeadsChannel.BID, bid)
                            .eq(LeadsChannel.NAME, name)
                            .in(LeadsChannel.VALUE, values)
                            .ne(!Objects.isNull(id), LeadsChannel.ID, id)
            );
            if (count == 0) {
                throw new InngkeServiceException("渠道名称不能重复");
            }
        }
    }

    private void checkName2(LeadsChannelSaveRequest request) {
        List<LeadsChannelDto> list = getList(request.getBid(), request.getOperatorId());
        Long parentId = request.getParentId();
        if (!Integer.valueOf(-1).equals(request.getStatus()) && StringUtils.isEmpty(request.getName())) {
            throw new InngkeServiceException("渠道名称不能为空");
        }
        if (parentId == null || parentId.equals(0L)) {
            leadsChannelName(list, request.getName(), request.getId());
            return;
        }
        LeadsChannelDto leadsChannelByParentId = getLeadsChannelByParentId(list, parentId);
        if (leadsChannelByParentId == null) {
            return;
        }
        leadsChannelName(leadsChannelByParentId.getChildren(), request.getName(), request.getId());
    }

    private QueryWrapper<LeadsChannel> getQueryWrapper(Integer bid, Long id) {
        List<Integer> bids = getBids(bid);
        QueryWrapper<LeadsChannel> queryWrapper = Wrappers.<LeadsChannel>query().in(LeadsChannel.BID, bids).eq(LeadsChannel.ID, id).ne(LeadsChannel.STATUS, -1);
        return queryWrapper;
    }

    private QueryWrapper<LeadsChannel> getQueryWrapper(LeadsChannelListRequest request) {
        List<Integer> bids = getBids(request.getBid());
        QueryWrapper<LeadsChannel> queryWrapper = Wrappers.<LeadsChannel>query().in(LeadsChannel.BID, bids).orderByAsc(LeadsChannel.SORT);

        Long id = request.getId();
        if (!Objects.isNull(id)) {
            queryWrapper.eq(LeadsChannel.ID, id);
        }

        Integer gtEditType = request.getGtEditType();
        if (!Objects.isNull(gtEditType)) {
            queryWrapper.gt(LeadsChannel.EDIT_TYPE, gtEditType);
        }
        return queryWrapper;
    }

    private List<Integer> getBids(Integer bid) {
        return Lists.newArrayList(0, bid);
    }

    private List<LeadsChannelDto> dealLeadsChannelList(LeadsChannelListRequest request, List<LeadsChannel> leadsChannelList) {
        Integer status = request.getStatus();
        Set<Integer> values = leadsChannelList.stream().filter(leadsChannel -> !leadsChannel.getBid().equals(0)).map(LeadsChannel::getValue).collect(Collectors.toSet());
        return leadsChannelList.stream().filter(leadsChannel -> {
            if (!leadsChannel.getBid().equals(0)) {
                return true;
            }
            return !values.contains(leadsChannel.getValue());
        }).filter(leadsChannel -> {
            if (status != null) {
                return leadsChannel.getStatus().equals(status);
            }
            return !leadsChannel.getStatus().equals(-1);
        }).map(this::toLeadsChannelDto).collect(Collectors.toList());
    }

    private LeadsChannelDto toLeadsChannelDto(LeadsChannel model) {
        LeadsChannelDto toModel = new LeadsChannelDto();
        toModel.setId(model.getId());
        toModel.setName(model.getName());
        toModel.setValue(model.getValue());
        toModel.setStatus(model.getStatus());
        toModel.setSort(model.getSort());
        toModel.setEditType(model.getEditType());
        toModel.setCreateTime(DateTimeUtils.getMilli(model.getCreateTime()));
        toModel.setParentId(model.getParentId());
        return toModel;
    }

    private LeadsChannel toLeadsChannel(LeadsChannelSaveRequest model) {
        LeadsChannel toModel = new LeadsChannel();
        toModel.setBid(model.getBid());
        toModel.setId(model.getId());
        toModel.setName(model.getName());
        toModel.setStatus(model.getStatus());
        return toModel;
    }
}

package com.inngke.bp.leads.client;

import com.inngke.bp.distribute.dto.request.DistributorCustomerSaveDocRequest;
import com.inngke.bp.distribute.dto.request.GetDistributorDtoRequest;
import com.inngke.bp.distribute.dto.response.DistributorLeadDto;
import com.inngke.bp.distribute.dto.response.GetDistributorDto;
import com.inngke.bp.distribute.service.DistributorLeadsService;
import com.inngke.bp.distribute.service.DistributorService;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/22 16:34
 */
@Component
public class DistributorServiceClientForLeads {
    private static final Logger logger = LoggerFactory.getLogger(DistributorServiceClientForLeads.class);

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributorLeadsService distributorLeadsService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.distribute_bp_yk:}")
    private DistributorService distributorService;

    public List<DistributorLeadDto> getCustomerIdByLeads(List<Long> leadsIds, Integer bid) {
        BaseResponse<List<DistributorLeadDto>> customerIdByLeadsResp = distributorLeadsService.getCustomerIdByLeads(leadsIds, bid);
        if (!BaseResponse.responseSuccessWithNonNullData(customerIdByLeadsResp)) {
            logger.error("通过线索id集合获取报备客户信息失败！，leadsId={},bid={}", leadsIds, bid);
            return null;
        }
        return customerIdByLeadsResp.getData();
    }

    public Boolean saveDocs(DistributorCustomerSaveDocRequest request){
        BaseResponse<Boolean> response = distributorLeadsService.saveDoc(request);
        if(!BaseResponse.responseSuccessWithNonNullData(response)){
            logger.warn("更新报备客户es失败，request={}",request);
            return null;
        }
        return response.getData();
    }

    public GetDistributorDto getDistributorDto(Integer bid, Long guideId,Long distributorId) {
        if(guideId == null && distributorId == null){
            return null;
        }
        GetDistributorDtoRequest distributorIdGetRequest = new GetDistributorDtoRequest();
        distributorIdGetRequest.setBid(bid);
        distributorIdGetRequest.setGuideId(guideId);
        distributorIdGetRequest.setDistributorId(distributorId);
        BaseResponse<List<GetDistributorDto>> distributorDto = distributorService.getDistributorDto(distributorIdGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(distributorDto)) {
            return null;
        }
        List<GetDistributorDto> data = distributorDto.getData();
        if(CollectionUtils.isEmpty(data)){
            return null;
        }
        Integer normal = 1;
        for (GetDistributorDto datum : data) {
            if(normal.equals(datum.getStatus())){
                return datum;
            }
        }
        return data.get(0);
    }

}

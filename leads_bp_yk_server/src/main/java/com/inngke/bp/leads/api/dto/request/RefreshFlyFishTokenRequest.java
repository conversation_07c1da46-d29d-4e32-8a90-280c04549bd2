package com.inngke.bp.leads.api.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:19
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class RefreshFlyFishTokenRequest extends GetFlyFishAccessTokenRequest{

    /**
     * 刷新token,从"获取Access Token"和“刷新Access Token”的返回结果中得到)，刷新后会过期，请及时保存最新的token
     */
    @JsonProperty("refresh_token")
    private String refreshToken;
}

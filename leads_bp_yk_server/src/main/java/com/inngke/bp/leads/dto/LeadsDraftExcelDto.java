package com.inngke.bp.leads.dto;

import com.inngke.common.utils.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10 AM 12:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsDraftExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id
     */
    private Long customerId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 渠道名
     */
    @Excel(name = "需求产品渠道")
    private String shopChannelName;

    @Excel(name = "需求产品")
    private String demandProduct;

    @Excel(name = "平台ID")
    private String tpId;

    /**
     * 渠道ID
     */
    private Long shopChannelId;

    /**
     * 手机号码
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * 省份ID，0表示未匹配
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    @Excel(name = "省")
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    @Excel(name = "市")
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    @Excel(name = "区/县")
    private String areaName;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    @Excel(name = "渠道来源")
    private String channelName;

    /**
     * 下单账号
     */
    @Excel(name = "下单账号")
    private String orderAccount;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderSn;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称")
    private String goodsName;

    /**
     * 订购商品数量
     */
    @Excel(name = "订购数量")
    private Integer goodsNum;

    /**
     * 付款时间
     */
    @Excel(name = "付款时间")
    private String payTime;

    /**
     * 付款金额
     */
    @Excel(name = "付款金额")
    private BigDecimal payAmount;

    /**
     * 订单留言
     */
    @Excel(name = "订单留言")
    private String orderMessage;

    /**
     * 其它备注
     */
    @Excel(name = "其他备注")
    private String remark;

    /**
     * 外部平台线索ID
     */
    @Excel(name = "线索id")
    private String tpLeadsId;

    /**
     * 广告活动名称
     */
    @Excel(name = "广告活动名称")
    private String promotionName;

    @Excel(name = "创建时间")
    private String registryTime;

    @Excel(name = "线索来源")
    private String channelSource;

    @Excel(name = "性别")
    private String gender;

    @Excel(name = "年龄")
    private Integer age;

    @Excel(name = "QQ号")
    private String qq;

    @Excel(name = "邮箱")
    private String email;

    @Excel(name = "计划id")
    private String campaignId;

    @Excel(name = "计划名称")
    private String campaignName;

    @Excel(name = "广告主id")
    private String accountId;

    @Excel(name = "广告主名称")
    private String accountName;

    @Excel(name = "留资时间")
    private String submitTime;

    @Excel(name = "商品链接")
    private String goodsLink;

    @Excel(name = "线索类型")
    private String type;

    @Excel(name = "客户标签")
    private String tags;

    @Excel(name = "企业标签")
    private String enterpriseTags;

    @Excel(name = "客户等级")
    private String level;

    @Excel(name = "微信号")
    private String weChat;

    @Excel(name = "导入失败原因")
    private String errorMsg;


}

package com.inngke.bp.leads.core.converter;

import com.inngke.bp.client.request.DemandCreateRequest;

/**
 * DemandConverterOfLeads
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/14 10:26
 */
public class DemandConverterOfLeads {
    private DemandConverterOfLeads() {}

    public static DemandCreateRequest leadsDemandCreateRequestToClientDemandCreateRequest(Integer bid, com.inngke.bp.leads.dto.request.DemandCreateRequest request) {
        DemandCreateRequest demandCreateRequest = new DemandCreateRequest();
        demandCreateRequest.setBid(bid);
        demandCreateRequest.setDemandName(request.getDemandName());
        demandCreateRequest.setProvinceId(request.getProvinceId());
        demandCreateRequest.setCityId(request.getCityId());
        demandCreateRequest.setAreaId(request.getAreaId());
        demandCreateRequest.setAddress(request.getAddress());
        demandCreateRequest.setOperatorId(request.getOperatorId());
        demandCreateRequest.setCommunityId(request.getCommunityId());
        demandCreateRequest.setHousingArea(request.getHousingArea());
        return demandCreateRequest;
    }
}

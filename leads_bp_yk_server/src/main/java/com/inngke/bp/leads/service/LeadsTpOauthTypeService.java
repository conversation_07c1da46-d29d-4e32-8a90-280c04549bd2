package com.inngke.bp.leads.service;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.dto.request.tp.GetTpAccessTokenRequest;
import com.inngke.bp.leads.dto.request.tp.GetTpLaunchOauthDataRequest;
import com.inngke.bp.leads.dto.request.tp.OauthCallbackRequest;
import com.inngke.bp.leads.dto.response.tp.TpLaunchOauthDataDto;
import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.common.dto.response.BaseResponse;
import com.tencent.ads.ApiException;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:30
 */
public interface LeadsTpOauthTypeService {

    String TP_ACCESS_TOKEN_CACHE_KEY = LeadsServiceConsts.APP_ID + ":tpAccessToken:" + "bid:";

    LeadsTpTypeEnum getType();

    /**
     * 获取发起授权数据
     *
     * @return
     */
    BaseResponse<TpLaunchOauthDataDto> getLaunchOauthData(GetTpLaunchOauthDataRequest request) throws ApiException;


    /**
     * 预授权回调
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> oauthCallback(OauthCallbackRequest request) throws ApiException;


    BaseResponse<String> getAccessToken(GetTpAccessTokenRequest request);
}

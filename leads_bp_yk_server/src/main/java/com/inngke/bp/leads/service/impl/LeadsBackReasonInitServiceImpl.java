package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.MerchantClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsBackReason;
import com.inngke.bp.leads.db.leads.manager.LeadsBackReasonManager;
import com.inngke.bp.leads.dto.request.LeadsBackReasonSaveRequest;
import com.inngke.bp.leads.service.LeadsBackReasonInitService;
import com.inngke.bp.leads.service.LeadsBackReasonService;
import com.inngke.bp.user.consts.UserServiceConsts;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8 3:56 PM
 */
@DubboService(version = "1.0.0")
@Service
@Slf4j
public class LeadsBackReasonInitServiceImpl implements LeadsBackReasonInitService {

    String key = UserServiceConsts.APP_ID + ":lock:LeadsBackReasonInit";

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @Autowired
    private LeadsBackReasonService leadsBackReasonService;

    @Autowired
    private MerchantClientForLeads merchantClientForLeads;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsBackReasonManager leadsBackReasonManager;

    @Autowired
    private LockService lockService;



    @Override
    public BaseResponse init() {
        Lock lock = lockService.getLock(key, 60 * 5);
        if (lock == null) {
            return BaseResponse.error("拿锁失败");
        }
        try {
            List<String> reasonList = getInitReasons();
            merchantClientForLeads.getMerchantList().forEach(bid ->{
                BaseBidRequest request = new BaseBidRequest();
                request.setBid(bid);

                List<LeadsBackReason> leadsBackReasons = leadsBackReasonManager.list(Wrappers.<LeadsBackReason>query()
                        .eq(LeadsBackReason.BID, request.getBid())
                        .eq(LeadsBackReason.ENABLE, 1));

                if (leadsBackReasons.size() > 0) {
                    return;
                }
                reasonList.forEach(reason -> {
                    LeadsBackReasonSaveRequest leadsInvalidReasonSaveRequest = new LeadsBackReasonSaveRequest();
                    leadsInvalidReasonSaveRequest.setReason(reason);
                    leadsInvalidReasonSaveRequest.setBid(bid);
                    leadsBackReasonService.save(leadsInvalidReasonSaveRequest);
                });
            });
        } finally {
            lock.unlock();
        }

        return BaseResponse.success();
    }

    private List<String> getInitReasons() {
        List<String> reasonList = Lists.newArrayList();
        reasonList.add("不在门店服务范围内");
        reasonList.add("已在其他渠道下单");
        reasonList.add("其他");
        return reasonList;
    }

}

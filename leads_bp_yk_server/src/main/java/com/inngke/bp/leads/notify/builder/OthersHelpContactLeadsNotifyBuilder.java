package com.inngke.bp.leads.notify.builder;

import com.inngke.bp.leads.notify.context.OthersHelpContactLeadsNotifyContext;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * OthersHelpContactLeadsNotifyBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/11 14:19
 */
@Slf4j
@Component("others_help_contact_leads")
public class OthersHelpContactLeadsNotifyBuilder extends TemplateMessageContentBuilder<OthersHelpContactLeadsNotifyContext> {
    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(OthersHelpContactLeadsNotifyContext ctx) {
        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);
        return builder
                .setTitle("客户跟进结果通知")
                .setVar("name", ctx.getName())
                .setVar("mobile", ctx.getMobile())
                .setVar("followType", "已联系")
                .setVar("followContent", ctx.getOthersName().length() < 8 ? ctx.getOthersName() + "已联系客户" : ctx.getOthersName().substring(5) + "..." + "已联系客户");
    }
}

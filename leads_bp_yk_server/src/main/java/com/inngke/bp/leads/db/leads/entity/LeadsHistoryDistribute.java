/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.inngke.bp.leads.service.enums.LeadsHistoryDistributeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsHistoryDistribute implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * @see LeadsHistoryDistributeTypeEnum
     */
    private Integer type;

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 分配给哪个员工，即staffId
     */
    private Long distributeStaffId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String TYPE = "type";

    public static final String LEADS_ID = "leads_id";

    public static final String DISTRIBUTE_STAFF_ID = "distribute_staff_id";

    public static final String CREATE_TIME = "create_time";

}

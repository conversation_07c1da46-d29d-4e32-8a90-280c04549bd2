package com.inngke.bp.leads.service.message.context;

import com.inngke.bp.leads.service.message.MessageTypeEnum;

public class FollowPlanContext extends MessageContext{

    private String planContent;

    public FollowPlanContext(int bid, MessageTypeEnum messageType) {
        super(bid, messageType);
    }

    public FollowPlanContext(int bid) {
        super(bid, MessageTypeEnum.FOLLOW_TEMPLATE);
    }


    public String getPlanContent() {
        return planContent;
    }


    public void setPlanContent(String planContent) {
        this.planContent = planContent;
    }
}

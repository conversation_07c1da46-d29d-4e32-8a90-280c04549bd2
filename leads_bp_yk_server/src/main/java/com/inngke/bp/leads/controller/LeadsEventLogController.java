/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.dto.request.LeadsFollowEsBatchRequest;
import com.inngke.bp.leads.service.LeadsEventLogService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 线索事件 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@RestController
@RequestMapping("/api/event-log/")
public class LeadsEventLogController {

    @Autowired
    private LeadsEventLogService leadsEventLogService;

    @GetMapping("transfer-follow")
    public BaseResponse structureLeadsEventLogFromLeadsFollow(
            @RequestHeader(InngkeApiConst.STR_BID) int bid
    ) {
        LeadsFollowEsBatchRequest request = new LeadsFollowEsBatchRequest();
        request.setLastLeadsFollowId(0L);
        request.setBid(bid);

        return  leadsEventLogService.structureLeadsEventLogFromLeadsFollow(request);
    }

}


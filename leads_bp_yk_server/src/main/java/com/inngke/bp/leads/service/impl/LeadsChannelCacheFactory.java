package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.dto.request.LeadsChannelListRequest;
import com.inngke.bp.leads.dto.response.LeadsChannelDto;
import com.inngke.bp.leads.dto.response.LeadsChannelValueDto;
import com.inngke.bp.leads.service.LeadsChannelService;
import com.inngke.common.cache.service.impl.BaseDupVersionCacheFactory;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LeadsChannelCacheFactory extends BaseDupVersionCacheFactory<LeadsChannelValueDto, LeadsChannelCacheFactory.LeadsChannelValueCache> {

    private static final String CACHE_PREFIX = LeadsServiceConsts.APP_ID + ":leadsChannelValue:";

    public static LeadsChannelCacheFactory cacheFactory;

    @PostConstruct
    private void init() {
        cacheFactory = this;
    }

    @Autowired
    private LeadsChannelService leadsChannelService;


    @Override
    public String getCacheVersionKey(int bid) {
        return CACHE_PREFIX + bid;
    }

    @Override
    public String getCacheDataKey(int bid, Long version) {
        return CACHE_PREFIX + bid + InngkeAppConst.UNDERLINE_STR + version;
    }

    @Override
    public LeadsChannelValueCache newInstance(int bid, Long version) {
        return new LeadsChannelValueCache(bid, version);
    }

    public class LeadsChannelValueCache extends BaseDupVersionCacheFactory.BaseCache {

        private List<LeadsChannelValueDto> list = Lists.newArrayList();

        private Map<Integer, String> leadsChannelMap = new HashMap<>();

        private LeadsChannelValueCache(int bid, Long version) {
            super(bid, version);
        }

        @Override
        protected void build() {
            LeadsChannelListRequest leadsChannelListRequest = new LeadsChannelListRequest();
            leadsChannelListRequest.setBid(getBid());
            BaseResponse<List<LeadsChannelDto>> baseResponse = leadsChannelService.getList(leadsChannelListRequest);
            if (!BaseResponse.responseSuccess(baseResponse)) {
                log.error("获取渠道数据失败！request={}，response={}", jsonService.toJson(leadsChannelListRequest), jsonService.toJson(baseResponse));
                throw new InngkeErrorException("获取渠道数据失败！");
            }
            List<LeadsChannelDto> data = baseResponse.getData();
            List<LeadsChannelValueDto> listTemp = new ArrayList<>(data.size());
            Map<Integer, String> leadsChannelMapTemp = new HashMap<>(data.size());
            getChannelList(data, listTemp, leadsChannelMapTemp, "");
            list = listTemp;
            leadsChannelMap = leadsChannelMapTemp;
        }

        public String getNameByValue(Integer code) {
            if (!CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(leadsChannelMap)) {
                build();
            }
            return leadsChannelMap.get(code);
        }

        private void getChannelList(List<LeadsChannelDto> data, List<LeadsChannelValueDto> result, Map<Integer, String> map, String parentName) {
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            for (LeadsChannelDto leadsChannelDto : data) {
                result.add(leadsChannelValueDto(leadsChannelDto));
                if (StringUtils.isEmpty(parentName)) {
                    map.put(leadsChannelDto.getValue(), leadsChannelDto.getName());
                } else {
                    map.put(leadsChannelDto.getValue(), parentName + InngkeAppConst.MIDDLE_LINE_STR + leadsChannelDto.getName());
                }
                getChannelList(leadsChannelDto.getChildren(), result, map, leadsChannelDto.getName());
            }
        }

        private LeadsChannelValueDto leadsChannelValueDto(LeadsChannelDto dto) {
            LeadsChannelValueDto leadsChannelValueDto = new LeadsChannelValueDto();
            leadsChannelValueDto.setName(dto.getName());
            leadsChannelValueDto.setValue(dto.getValue());
            leadsChannelValueDto.setParentId(dto.getParentId());
            leadsChannelValueDto.setId(dto.getId());
            return leadsChannelValueDto;
        }

        @Override
        protected String getCacheDataStr() {
            return jsonService.toJson((Serializable) list);
        }

        @Override
        protected void buildFromCacheStr(String str) {
            list = jsonService.toObjectList(str, LeadsChannelValueDto.class);
        }

        public List<LeadsChannelValueDto> getList() {
            return list;
        }
    }
}

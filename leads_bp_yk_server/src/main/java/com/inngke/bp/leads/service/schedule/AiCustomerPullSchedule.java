package com.inngke.bp.leads.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.common.db.card.entity.Shop;
import com.inngke.bp.common.db.card.manager.ShopManager;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.dto.request.PullAiCustomerLeadsRequest;
import com.inngke.bp.leads.service.AiCustomerServiceLeadsService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.BidUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/3/3 10:03
 */
@Component
public class AiCustomerPullSchedule {

    @Autowired
    private AiCustomerServiceLeadsService aiCustomerServiceLeadsService;

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @Autowired
    private ShopManager shopManager;

    @Autowired
    private LockService lockService;

    @Scheduled(fixedDelay = 60000 * 15,initialDelay = 60000 * 15)
    public void pullAiCustomerLeads() {
        Lock lock = lockService.getLock(LeadsServiceConsts.APP_ID + InngkeAppConst.CLN_STR + "pullAiCustomer", 60 * 15);
        if (lock == null){
            return;
        }
        databasePrivatizationService.accept(privatizationDb -> shopManager.list(Wrappers.<Shop>query()
                .eq(Shop.ENABLE, 1).select(Shop.ID)).forEach(shop -> {
                    Integer bid = shop.getId();
                    PullAiCustomerLeadsRequest request = new PullAiCustomerLeadsRequest();
                    request.setBid(bid);
                    aiCustomerServiceLeadsService.pullLeads(request);
                })
        );
    }
}

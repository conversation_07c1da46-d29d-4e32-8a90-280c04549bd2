package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.inngke.bp.client.dto.response.common.ClientLevelItemDto;
import com.inngke.bp.client.dto.response.common.LostReasonItemDto;
import com.inngke.bp.client.service.ClientCommonService;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ClientCommonClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientCommonService clientCommonService;

    public List<ClientLevelItemDto> getClientLevelList(int bid){
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);

        BaseResponse<List<ClientLevelItemDto>> response = clientCommonService.getClientLevelList(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)){
            return Lists.newArrayList();
        }

        return response.getData();
    }

    public List<LostReasonItemDto> getLostReasonsList(Integer bid) {
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        BaseResponse<List<LostReasonItemDto>> response = clientCommonService.getLostReasonList(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)){
            return Lists.newArrayList();
        }

        return response.getData();
    }

    public LostReasonItemDto getLostReasonsById(Integer bid,Long id) {
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        BaseResponse<List<LostReasonItemDto>> response = clientCommonService.getLostReasonList(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)){
            return null;
        }

        return response.getData().stream().filter(i -> Long.valueOf(i.getId()).equals(id)).findFirst().orElse(null);
    }
}

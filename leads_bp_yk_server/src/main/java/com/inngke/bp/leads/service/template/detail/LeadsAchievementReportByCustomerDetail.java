package com.inngke.bp.leads.service.template.detail;

import com.inngke.bp.leads.service.template.leadsAchievement.LeadsAchievementReportContext;
import com.inngke.common.exception.InngkeServiceException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/23 14:13
 */
@Component
public class LeadsAchievementReportByCustomerDetail extends LeadsAchievementDetail {

    @Override
    protected void checkPara(LeadsAchievementReportContext context) {
        if (context.getSelectStaffId() == null) {
            throw new InngkeServiceException("selectStaffId不能为空");
        }
    }

    @Override
    public String getDimensionCode() {
        return "customerServiceDetail";
    }

    @Override
    protected void afterQueryBuildPlugin(LeadsAchievementReportContext context) {
        BoolQueryBuilder qb = context.getBoolQueryBuilder();
        qb.mustNot(QueryBuilders.termQuery("preFollowStaffId", 0L));
        qb.mustNot(QueryBuilders.termQuery("eventId", 19));

        Set<Long> manageDepartment = getManageDepartment(context.getBid(), null, context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("preFollowDepartmentId", manageDepartment));


        if (Objects.nonNull(context.getSelectStaffId())) {
            qb.must(QueryBuilders.termQuery("preFollowStaffId", context.getSelectStaffId()));
        }
    }

    @Override
    protected void setTimeAndAmountBoolQueryBuilder(BoolQueryBuilder qb,LeadsAchievementReportContext context) {
        qb.must(QueryBuilders.termQuery("preFollowStaffId", context.getSelectStaffId()));

        Set<Long> manageDepartment = getManageDepartment(context.getBid(), null, context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("preFollowDepartmentId", manageDepartment));
    }
}

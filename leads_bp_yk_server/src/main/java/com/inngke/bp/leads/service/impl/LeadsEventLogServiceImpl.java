package com.inngke.bp.leads.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.client.StoreOrderClientForLeads;
import com.inngke.bp.leads.core.converter.LeadsFollowConverter;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsEventConf;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsEventConfManager;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsEventChangeRequest;
import com.inngke.bp.leads.dto.request.LeadsFollowEsBatchRequest;
import com.inngke.bp.leads.dto.request.StoreOrderCreateEventRequest;
import com.inngke.bp.leads.dto.response.LeadsEventEsDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsDepartmentCacheDisposable;
import com.inngke.bp.leads.service.LeadsEventLogService;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.service.DepartmentService;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.enums.EnvEnum;
import com.inngke.common.es.dto.IndexDocDto;
import com.inngke.common.es.service.EsDocService;
import com.inngke.common.es.service.EsIndexService;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.flush.FlushRequest;
import org.elasticsearch.action.admin.indices.flush.FlushResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 线索跟进记录es管理
 */
@DubboService(version = "1.0.0")
@Service
@Slf4j
public class LeadsEventLogServiceImpl implements LeadsEventLogService {

    private static Map<Integer, LeadsEventConf> LEADS_STATUS_EVENT_LOG_MAP;

    private static final Integer NEWEST_ES_INDEX_SIZE = 3;

    private static final List<Integer> NO_STATISTICS_LEADS_STATUS = Lists.newArrayList(
            LeadsStatusEnum.DELETED.getStatus(),LeadsStatusEnum.RECOVERY.getStatus(),LeadsStatusEnum.PUSH_BACK.getStatus());

    private static final String INDEX_MAPPING ="{ \"properties\": { \"payAmount\": { \"type\": \"double\" }, \"deposit\":{ \"type\": \"double\" } } }";

    private static final String INDEX_SETTINGS = "{ \"index\" : { \"max_result_window\" : 1000000, \"refresh_interval\" : \"1s\" } }";

    private static final String EVENT_LOG_INDEX_NAME = "leads-event-log";

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsEventConfManager leadsEventConfManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private StoreOrderClientForLeads storeOrderClientForLeads;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private EsIndexService esIndexService;

    @Autowired
    private EsDocService esDocService;

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private DepartmentService departmentService;

    /**
     * 通过线索跟进记录构建线索事件es
     */
    @Override
    public BaseResponse<Boolean> structureLeadsEventLogFromLeadsFollow(LeadsFollowEsBatchRequest request) {

        String indexName = esIndexService.createIndex(EVENT_LOG_INDEX_NAME, INDEX_SETTINGS, INDEX_MAPPING);

        try {
            if (EnvUtils.getEnv() == EnvEnum.PROD) {
                databasePrivatizationService.accept(privatizationDb -> {
                    Integer bid = privatizationDb.getBids().iterator().next();
                    log.info("[{}]准备创建索引", privatizationDb.getCode());
                    request.setBid(bid);
                    addEsDocs(indexName, request);
                });
            }else {
                log.info("[yk]准备创建索引");
                addEsDocs(indexName, request);
            }
        }catch (Exception e){
            esIndexService.deleteIndex(indexName);
            throw e;
        }

        FlushRequest flushRequest = new FlushRequest();
        try {
            log.info("flush...");
            FlushResponse resp = client.indices().flush(flushRequest, RequestOptions.DEFAULT);
            RestStatus status = resp.getStatus();
            if (status != RestStatus.OK) {
                log.error("flush失败：resp={}", jsonService.toJson((Serializable) resp));
            }
        } catch (IOException e) {
            log.error("flush失败", e);
        }

        try {
            AsyncUtils.sleep(10000);
            esIndexService.onlineIndex(indexName, EVENT_LOG_INDEX_NAME);
            esIndexUpdate(EVENT_LOG_INDEX_NAME);
        } catch (Exception e) {
            log.error("校验ES索引发生错误", e);
            return BaseResponse.error("校验ES索引发生错误");
        }

        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Boolean> handleStatusChangeUpdateLeadsEventLog(LeadsEventChangeRequest request) {
        log.info("leads-canal-callback request:{}",jsonService.toJson(request));
        Integer afterStatus = request.getAfterStatus();
        LeadsEventEsDto leadsEventEsDto = new LeadsEventEsDto();
        leadsEventEsDto.setId(snowflakeIdService.getId());
        leadsEventEsDto.setBid(request.getBid());
        leadsEventEsDto.setEventType(1);
        leadsEventEsDto.setEventId(toEventId(afterStatus));
        leadsEventEsDto.setLeadsId(request.getLeadsId());

        //线索详情
        Leads leads = leadsManager.getById(request.getLeadsId());
        log.info("leads-canal-callback leads:{}",jsonService.toJson(leads));
        //判断线索状态是否不需要再统计数据
        if (!filterLeadStatusIsNotCounted(leads)){
            deleteLeadsEventLog(request.getBid(),request.getLeadsId());
            return BaseResponse.success(true);
        }

        //员工详情
        Map<Long, StaffDto> staffMap = getStaffMap(request.getBid(),
                Sets.newHashSet(leads.getPreFollowStaffId(), leads.getDistributeStaffId()));


        if (ObjectUtils.isEmpty(leads)) {
            log.error("未找到线索{}", request.getLeadsId());
            return null;
        }

        //线索创建时间/分配时间
        leadsEventEsDto.setCreateTime(DateTimeUtils.getMilli(LocalDateTime.now()));
        leadsEventEsDto.setLeadsCreateTime(DateTimeUtils.getMilli(leads.getCreateTime()));
        leadsEventEsDto.setLeadsDistributeTime(DateTimeUtils.getMilli(leads.getDistributeTime()));

        StaffDto emptyStaff = new StaffDto();

        leadsEventEsDto.setFollowStaffId(leads.getDistributeStaffId());
        //跟进人所在部门
        leadsEventEsDto.setFollowDepartmentId(
                staffMap.getOrDefault(leads.getDistributeStaffId(), emptyStaff).getDepartmentId());

        //若线索被客服接待过添加客服部门ID
        if (!ObjectUtils.isEmpty(leads.getPreFollowStaffId())) {
            Long preStaffId = leads.getPreFollowStaffId();
            leadsEventEsDto.setPreFollowStaffId(preStaffId);
            leadsEventEsDto.setPreFollowDepartmentId(staffMap.getOrDefault(preStaffId, emptyStaff).getDepartmentId());
        }

        //填充父级部门
        leadsEventEsDto.setFollowDepartmentIds(
                getParentDepartmentIds(leadsEventEsDto.getFollowDepartmentId(), leadsEventEsDto.getBid()));
        leadsEventEsDto.setPreFollowDepartmentIds(
                getParentDepartmentIds(leadsEventEsDto.getPreFollowDepartmentId(), leadsEventEsDto.getBid()));

        esDocService.addDocs(EVENT_LOG_INDEX_NAME, Lists.newArrayList(toIndexDocDto(leadsEventEsDto)));

        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<Boolean> handleStoreOrderCreateAddLeadsEventLog(StoreOrderCreateEventRequest request) {
        log.info("leads-order-canal-callback request:{}",jsonService.toJson(request));
        Leads leads = getLeadsId(request.getBid(), request.getLeadsId());
        if (ObjectUtils.isEmpty(leads)){
            return BaseResponse.success(false);
        }

        LeadsEventEsDto leadsEventEsDto = new LeadsEventEsDto();
        leadsEventEsDto.setBid(request.getBid());
        leadsEventEsDto.setId(snowflakeIdService.getId());
        leadsEventEsDto.setEventType(2);
        leadsEventEsDto.setEventId(20);
        leadsEventEsDto.setLeadsId(leads.getId());
        Long followStaffId = ObjectUtils.isEmpty(request.getStaffId()) || request.getStaffId().equals(0L) ?
                leads.getDistributeStaffId() : request.getStaffId();
        leadsEventEsDto.setFollowStaffId(followStaffId);
        leadsEventEsDto.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsEventEsDto.setCreateTime(DateTimeUtils.getMilli(LocalDateTime.now()));
        leadsEventEsDto.setLeadsCreateTime(DateTimeUtils.getMilli(leads.getCreateTime()));
        leadsEventEsDto.setLeadsDistributeTime(DateTimeUtils.getMilli(leads.getDistributeTime()));
        leadsEventEsDto.setDeposit(request.getDeposit());
        leadsEventEsDto.setPayAmount(request.getPayAmount());

        //员工详情
        Map<Long, StaffDto> staffMap = getStaffMap(request.getBid(),
                Sets.newHashSet(leadsEventEsDto.getPreFollowStaffId(), leadsEventEsDto.getFollowStaffId()));

        //跟进人所在部门
        fillInDepartmentInfo(leadsEventEsDto,staffMap,leadsEventEsDto.getFollowStaffId(),leadsEventEsDto.getPreFollowStaffId());

        esDocService.addDocs(EVENT_LOG_INDEX_NAME, Lists.newArrayList(toIndexDocDto(leadsEventEsDto)));

        return BaseResponse.success(true);
    }

    private void deleteLeadsEventLog(Integer bid, Long leadsId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("bid", bid))
                .must(QueryBuilders.termQuery("leadsId",leadsId));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(queryBuilder).size(1000);

        SearchRequest searchRequest = new SearchRequest().indices(EVENT_LOG_INDEX_NAME).source(searchSourceBuilder);

        SearchResponse searchResponse = esDocService.search(searchRequest);

        if (!ObjectUtils.isEmpty(searchResponse.getHits()) && !NumberUtil.equals(searchResponse.getHits().getTotalHits().value, 0L)) {
            List<IndexDocDto> deleteDocList = Arrays.stream(searchResponse.getHits().getHits())
                    .map(dataStr -> jsonService.toObject(dataStr.getSourceAsString(), LeadsEventEsDto.class))
                    .map(leadsEventEsDto -> {
                        IndexDocDto indexDocDto = new IndexDocDto();
                        indexDocDto.setDocId(leadsEventEsDto.getId().toString());
                        return indexDocDto;
                    })
                    .collect(Collectors.toList());
            esDocService.deleteDocs(EVENT_LOG_INDEX_NAME,deleteDocList);
        }
    }

    /**
     * 添加索引文档
     */
    private void addEsDocs(String indexName, LeadsFollowEsBatchRequest request) {

        LeadsDepartmentCacheDisposable leadsDepartmentCache = new LeadsDepartmentCacheDisposable(1,departmentService);

        //构建线索下发数
        addEsDocsFromLeadsIssued(indexName,leadsDepartmentCache,request);

        //通过跟进记录构建
        addEsDocsFromLeadsFollow(indexName,leadsDepartmentCache,request);

        //通过门店订单构建
        addEsDocsFromStoreOrder(indexName,leadsDepartmentCache,request);
    }

    private void addEsDocsFromLeadsIssued(String indexName, LeadsDepartmentCacheDisposable departmentCache, LeadsFollowEsBatchRequest request) {
        Long lastLeadsId = 0L;

        List<Leads> leadsList = leadsManager.list(Wrappers.<Leads>query()
                .gt(Leads.ID, lastLeadsId)
                .notIn(Leads.STATUS, NO_STATISTICS_LEADS_STATUS)
                .ne(Leads.DISTRIBUTE_STAFF_ID,0)
                .last("LIMIT " + request.getPageSize())
        );


        while (!CollectionUtils.isEmpty(leadsList)){
            lastLeadsId = leadsList.get(leadsList.size() - 1).getId();

            List<LeadsEventEsDto> leadsEventEsDtoList = perfectFieldToLeadsEventLogFromLeads(leadsList, departmentCache);

            esDocService.addDocs(indexName,
                    leadsEventEsDtoList.stream().map(this::toIndexDocDto).collect(Collectors.toList()));

            leadsList = leadsManager.list(Wrappers.<Leads>query()
                    .gt(Leads.ID, lastLeadsId)
                    .notIn(Leads.STATUS, NO_STATISTICS_LEADS_STATUS)
                    .ne(Leads.DISTRIBUTE_STAFF_ID,0)
                    .last("LIMIT " + request.getPageSize())
            );
        }
    }

    private void addEsDocsFromStoreOrder(String indexName, LeadsDepartmentCacheDisposable departmentCache, LeadsFollowEsBatchRequest request) {
        Long lastLeadsId = 0L;
        //可能有开单记录的线索列表
        List<Leads> hasOrderLeadsList = leadsManager.list(Wrappers.<Leads>query()
                .gt(Leads.ID, lastLeadsId)
                .notIn(Leads.STATUS, NO_STATISTICS_LEADS_STATUS)
                .select(Leads.ID, Leads.BID, Leads.MOBILE, Leads.PRE_FOLLOW_STAFF_ID)
                .last("LIMIT " + request.getPageSize())
        );
        while (!CollectionUtils.isEmpty(hasOrderLeadsList)) {
            lastLeadsId = hasOrderLeadsList.get(hasOrderLeadsList.size() - 1).getId();

            List<LeadsEventEsDto> eventEsDtoList = perfectFieldToLeadsEventLogFromOrder(departmentCache,hasOrderLeadsList);

            esDocService.addDocs(indexName,
                    eventEsDtoList.stream().map(this::toIndexDocDto).collect(Collectors.toList()));

            hasOrderLeadsList = leadsManager.list(Wrappers.<Leads>query()
                    .gt(Leads.ID, lastLeadsId)
                    .notIn(Leads.STATUS, NO_STATISTICS_LEADS_STATUS)
                    .select(Leads.ID, Leads.BID, Leads.MOBILE, Leads.PRE_FOLLOW_STAFF_ID)
                    .last("LIMIT " + request.getPageSize())
            );
        }

    }

    private List<LeadsEventEsDto> perfectFieldToLeadsEventLogFromOrder(LeadsDepartmentCacheDisposable departmentCache, List<Leads> leadsList){
        Map<Long, Leads> leadsMap = leadsList.stream().collect(Collectors.toMap(Leads::getId, Function.identity()));

        //获取线索关联的开单记录
        Map<Long, List<StoreOrderDto>> leadsStoreOrderMap = getLeadsStoreOrderAnalysisMobile(leadsMap);

        Map<Integer,Set<Long>> bidStaffMap = Maps.newHashMap();
        //开单的员工Ids
        leadsStoreOrderMap.values().stream().flatMap(Collection::stream).forEach(storeOrder -> {
            if (!ObjectUtils.isEmpty(storeOrder.getStaffId())) {
                Set<Long> staffIds = bidStaffMap.computeIfAbsent(storeOrder.getBid(), bid -> Sets.newHashSet());
                staffIds.add(storeOrder.getStaffId());
            }
        });
        //客服的员工Ids
        leadsMap.values().forEach(leads -> {
            if (!ObjectUtils.isEmpty(leads.getPreFollowStaffId())){
                Set<Long> staffIds = bidStaffMap.computeIfAbsent(leads.getBid(), bid -> Sets.newHashSet());
                staffIds.add(leads.getPreFollowStaffId());
            }
        });

        Map<Long, StaffDto> staffMap = getStaffMap(bidStaffMap);
        Set<Long> storeOrderLeadsIds = leadsStoreOrderMap.keySet();
        if (CollectionUtils.isEmpty(storeOrderLeadsIds)){
            return Lists.newArrayList();
        }
        leadsMap.putAll(leadsManager.list(Wrappers.<Leads>query().in(Leads.ID,storeOrderLeadsIds))
                .stream().collect(Collectors.toMap(Leads::getId,Function.identity())));

        return leadsStoreOrderMap.keySet().stream()
                .filter(leadsId -> filterLeadStatusIsNotCounted(leadsId, leadsMap))
                .map(leadsId -> {
                    List<StoreOrderDto> leadsStoreOrderList = leadsStoreOrderMap.get(leadsId);
                    return leadsStoreOrderList.stream().map(storeOrderDto -> {
                        LeadsEventEsDto leadsEventEsDto = LeadsFollowConverter.toLeadsEventEsDto(storeOrderDto);
                        if (ObjectUtils.isEmpty(leadsEventEsDto.getLeadsId()) || leadsEventEsDto.getLeadsId() == 0L) {
                            leadsEventEsDto.setLeadsId(leadsId);
                        }
                        leadsEventEsDto.setId(snowflakeIdService.getId());
                        Leads leads = leadsMap.get(leadsId);

                        if (!ObjectUtils.isEmpty(leads)) {
                            StaffDto followStaffDto = staffMap.get(storeOrderDto.getStaffId());
                            if (!ObjectUtils.isEmpty(followStaffDto)) {
                                leadsEventEsDto.setFollowDepartmentId(followStaffDto.getDepartmentId());
                                leadsEventEsDto.setFollowDepartmentIds(
                                        departmentCache.getParentDepartmentIds(leadsEventEsDto.getBid(),
                                                leadsEventEsDto.getFollowDepartmentId())
                                );
                            }

                            Long preFollowStaffId = leads.getPreFollowStaffId();

                            //客服员工部门
                            if (!ObjectUtils.isEmpty(preFollowStaffId)) {
                                StaffDto preFollowStaffDto = staffMap.get(preFollowStaffId);
                                leadsEventEsDto.setPreFollowStaffId(preFollowStaffId);
                                if (!ObjectUtils.isEmpty(preFollowStaffDto)) {
                                    leadsEventEsDto.setPreFollowDepartmentId(preFollowStaffDto.getDepartmentId());
                                    leadsEventEsDto.setPreFollowDepartmentIds(
                                            departmentCache.getParentDepartmentIds(leadsEventEsDto.getBid(),
                                                    leadsEventEsDto.getPreFollowDepartmentId())
                                    );
                                }
                            }
                        }

                        leadsEventEsDto.setLeadsCreateTime(getLeadsCreateTime(leadsId, leadsMap));
                        leadsEventEsDto.setLeadsDistributeTime(getLeadsDistributeTime(leadsId, leadsMap));
                        return leadsEventEsDto;
                    }).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<LeadsEventEsDto> perfectFieldToLeadsEventLogFromLeads(List<Leads> leadsList, LeadsDepartmentCacheDisposable departmentCache){

        Map<Integer,Set<Long>> bidStaffIdMap = Maps.newHashMap();
        leadsList.forEach(leads -> {
            Set<Long> staffIds = bidStaffIdMap.computeIfAbsent(leads.getBid(), bid -> Sets.newHashSet());
            staffIds.add(leads.getDistributeStaffId());
            if (!ObjectUtils.isEmpty(leads.getPreFollowStaffId())){
                staffIds.add(leads.getPreFollowStaffId());
            }
        });
        Map<Long, StaffDto> staffMap = getStaffMap(bidStaffIdMap);
        return leadsList.stream().map(leads -> {
            LeadsEventEsDto leadsEventEsDto = new LeadsEventEsDto();
            leadsEventEsDto.setId(snowflakeIdService.getId());
            leadsEventEsDto.setEventType(1);
            leadsEventEsDto.setLeadsId(leads.getId());
            leadsEventEsDto.setEventId(8);
            leadsEventEsDto.setCreateTime(DateTimeUtils.getMilli(leads.getDistributeTime()));
            leadsEventEsDto.setLeadsCreateTime(DateTimeUtils.getMilli(leads.getCreateTime()));
            leadsEventEsDto.setLeadsDistributeTime(DateTimeUtils.getMilli(leads.getDistributeTime()));
            leadsEventEsDto.setBid(leads.getBid());

            StaffDto preFollowStaffDto = staffMap.get(leads.getPreFollowStaffId());
            leadsEventEsDto.setPreFollowStaffId(leads.getPreFollowStaffId());
            if (!ObjectUtils.isEmpty(preFollowStaffDto)) {
                leadsEventEsDto.setPreFollowDepartmentId(preFollowStaffDto.getDepartmentId());
                leadsEventEsDto.setPreFollowDepartmentIds(
                        departmentCache.getParentDepartmentIds(leadsEventEsDto.getBid(),
                                leadsEventEsDto.getPreFollowDepartmentId())
                );
            }

            StaffDto followStaff = staffMap.get(leads.getDistributeStaffId());
            leadsEventEsDto.setFollowStaffId(leads.getDistributeStaffId());
            if (!ObjectUtils.isEmpty(followStaff)) {
                leadsEventEsDto.setFollowDepartmentId(followStaff.getDepartmentId());
                leadsEventEsDto.setFollowDepartmentIds(
                        departmentCache.getParentDepartmentIds(leadsEventEsDto.getBid(),
                                leadsEventEsDto.getFollowDepartmentId())
                );
            }

            return leadsEventEsDto;
        }).collect(Collectors.toList());
    }

    private Map<Long, List<StoreOrderDto>> getLeadsStoreOrderAnalysisMobile(Map<Long, Leads> leadsMap){
        Map<Long, List<StoreOrderDto>> leadsStoreOrderMap = getLeadsStoreOrderMap(leadsMap);
        Map<String, List<StoreOrderDto>> mobileStoreOrderMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(leadsStoreOrderMap.get(0L))){
             mobileStoreOrderMap = leadsStoreOrderMap.get(0L).stream().collect(Collectors.groupingBy(StoreOrderDto::getCustomerMobile));
        }
        //将通过手机号匹配到的开单归到线索id
        Map<String, List<StoreOrderDto>> finalMobileStoreOrderMap = mobileStoreOrderMap;
        leadsMap.values().forEach(leads -> {
            List<StoreOrderDto> storeOrderDtoList = finalMobileStoreOrderMap.get(leads.getMobile());
            if (!CollectionUtils.isEmpty(storeOrderDtoList)){
                //开单需在线索创建之后才算作是这条线索的开单
                for (StoreOrderDto storeOrderDto : storeOrderDtoList) {
                    if (!leads.getBid().equals(storeOrderDto.getBid())){
                        continue;
                    }
                    if (storeOrderDto.getCreateTime() < DateTimeUtils.getMilli(leads.getCreateTime())){
                        continue;
                    }
                    if (storeOrderDto.getLeadsId() == null || storeOrderDto.getLeadsId() == 0L ||
                            storeOrderDto.getLeadsId().equals(leads.getId())) {
                        List<StoreOrderDto> leadsOrderList = leadsStoreOrderMap.computeIfAbsent(leads.getId(), key -> Lists.newArrayList());
                        leadsOrderList.add(storeOrderDto);
                    }
                }
            }
        });

        return leadsStoreOrderMap;
    }

    private void addEsDocsFromLeadsFollow(String indexName, LeadsDepartmentCacheDisposable leadsDepartmentCache, LeadsFollowEsBatchRequest request){

        Long lastLeadsFollowId = request.getLastLeadsFollowId();

        List<LeadsFollow> leadsFollowList = leadsFollowManager
                .getListByLastId(lastLeadsFollowId, request.getPageSize());

        while (!CollectionUtils.isEmpty(leadsFollowList)) {

            lastLeadsFollowId = leadsFollowList.get(leadsFollowList.size() - 1).getId();

            List<LeadsEventEsDto> eventEsDtoList = perfectFieldToLeadsEventLog(leadsDepartmentCache,leadsFollowList);

            esDocService.addDocs(indexName,
                    eventEsDtoList.stream().map(this::toIndexDocDto).collect(Collectors.toList()));

            leadsFollowList = leadsFollowManager
                    .getListByLastId(lastLeadsFollowId, request.getPageSize());
        }
    }

    private List<LeadsEventEsDto> perfectFieldToLeadsEventLog(LeadsDepartmentCacheDisposable leadsDepartmentCache, List<LeadsFollow> leadsFollows) {
        Map<Integer, Set<Long>> bidStaffIdsMap = Maps.newHashMap();
        Map<Integer, Set<Long>> bidLeadsIdMap = Maps.newHashMap();

        //添加跟进的员工ids,线索ids
        leadsFollows.forEach(leadsFollow -> {
            Set<Long> staffIds = bidStaffIdsMap.getOrDefault(leadsFollow.getBid(), Sets.newHashSet());
            staffIds.add(leadsFollow.getStaffId());
            bidStaffIdsMap.putIfAbsent(leadsFollow.getBid(), staffIds);

            Set<Long> leadsIds = bidLeadsIdMap.getOrDefault(leadsFollow.getBid(), Sets.newHashSet());
            leadsIds.add(leadsFollow.getLeadsId());
            bidLeadsIdMap.putIfAbsent(leadsFollow.getBid(), leadsIds);
        });

        //线索详情
        List<Leads> leadsList = leadsManager.getByIds(bidLeadsIdMap.values().stream().flatMap(Collection::stream)
                .collect(Collectors.toSet()));

        //线索->客服Map
        Map<Long, Long> leadsIdPreStaffIdMap = leadsList.stream()
//                .filter(leads -> !ObjectUtils.isEmpty(leads.getPreFollowStaffId()) && leads.getPreFollowStaffId() != 0L)
                .peek((leads -> {
                    Set<Long> staffIds = bidStaffIdsMap.getOrDefault(leads.getBid(), Sets.newHashSet());
                    staffIds.add(leads.getPreFollowStaffId());
                    staffIds.add(leads.getDistributeStaffId());
                    bidStaffIdsMap.putIfAbsent(leads.getBid(), staffIds);
                })).collect(Collectors.toMap(Leads::getId, Leads::getPreFollowStaffId));
        Map<Long, Leads> leadsMap = leadsList.stream().collect(Collectors.toMap(Leads::getId, Function.identity()));

        leadsFollows.forEach(follow -> {
            if (Objects.isNull(follow.getStaffId()) || follow.getStaffId() == 0L) {
                Optional.ofNullable(leadsMap.get(follow.getLeadsId()))
                        .ifPresent(leads -> follow.setStaffId(leads.getDistributeStaffId()));
            }
        });

        //员工详情
        Map<Long, StaffDto> staffMap = getStaffMap(bidStaffIdsMap);

        StaffDto emptyStaff = new StaffDto();
        return leadsFollows.stream()
                .filter(leadsFollow -> filterLeadStatusIsNotCounted(leadsFollow.getLeadsId(), leadsMap))
                .map((leadsFollow -> {
                    Long leadsId = leadsFollow.getLeadsId();
                    Leads leads = leadsMap.get(leadsId);
                    if (ObjectUtils.isEmpty(leads)) {
                        log.error("未找到线索{}", leadsId);
                        return null;
                    }
                    LeadsEventEsDto leadsEventEsDto = LeadsFollowConverter.toLeadsFollowEsDto(leadsFollow);
                    leadsEventEsDto.setId(leadsFollow.getId());
                    leadsEventEsDto.setEventId(toEventId(leadsFollow.getLeadsStatus()));

                    //线索创建时间/分配时间
                    leadsEventEsDto.setLeadsCreateTime(getLeadsCreateTime(leadsId, leadsMap));
                    leadsEventEsDto.setFollowStaffId(leads.getDistributeStaffId());
                    leadsEventEsDto.setLeadsDistributeTime(getLeadsDistributeTime(leadsId, leadsMap));

                    //跟进人所在部门
                    leadsEventEsDto.setFollowDepartmentId(
                            staffMap.getOrDefault(leads.getDistributeStaffId(), emptyStaff).getDepartmentId());

                    //若线索被客服接待过添加客服部门ID
                    if (leadsIdPreStaffIdMap.containsKey(leadsId)) {
                        Long preStaffId = leadsIdPreStaffIdMap.get(leadsId);
                        leadsEventEsDto.setPreFollowStaffId(preStaffId);
                        leadsEventEsDto.setPreFollowDepartmentId(staffMap.getOrDefault(preStaffId, emptyStaff).getDepartmentId());
                    }

                    //填充父级部门
                    leadsEventEsDto.setFollowDepartmentIds(
                            leadsDepartmentCache.getParentDepartmentIds(leadsEventEsDto.getBid(),
                                    leadsEventEsDto.getFollowDepartmentId())
                    );
                    leadsEventEsDto.setPreFollowDepartmentIds(
                            leadsDepartmentCache.getParentDepartmentIds(leadsEventEsDto.getBid(),
                                    leadsEventEsDto.getPreFollowDepartmentId())
                    );

                    return leadsEventEsDto;
                })).filter(leadsEventEsDto -> !ObjectUtils.isEmpty(leadsEventEsDto)).collect(Collectors.toList());
    }

    private Map<Long, StaffDto> getStaffMap(Map<Integer, Set<Long>> bidStaffIdsMap) {

        Map<Long, StaffDto> allStaffMap = Maps.newHashMap();

        for (Integer bid : bidStaffIdsMap.keySet()) {
            allStaffMap.putAll(getStaffMap(bid, bidStaffIdsMap.get(bid)));
        }

        return allStaffMap;
    }

    private Map<Long, StaffDto> getStaffMap(Integer bid, Set<Long> staffIds) {
        return staffClientForLeads.getStaffByIds(bid, staffIds);
    }

    private Map<Long, List<StoreOrderDto>> getLeadsStoreOrderMap(Map<Long, Leads> leadsMap) {
        Map<Integer, List<Leads>> bidLeadsMap = leadsMap.values().stream().collect(Collectors.groupingBy(Leads::getBid));
        Map<Long, List<StoreOrderDto>> leadsOrderListMap = Maps.newHashMap();
        for (Integer bid : bidLeadsMap.keySet()) {
            Set<Long> leadsIds = Sets.newHashSet();
            Set<String> mobiles = Sets.newHashSet();
            bidLeadsMap.get(bid).forEach((leads -> {
                if (!StringUtils.isEmpty(leads.getMobile())) {
                    mobiles.add(leads.getMobile());
                }
                leadsIds.add(leads.getId());
            }));
            leadsOrderListMap.putAll(
                    storeOrderClientForLeads.getLeadsOrder(bid, leadsIds, mobiles)
                            .stream().collect(Collectors.groupingBy(StoreOrderDto::getLeadsId))
            );
        }

        return leadsOrderListMap;
    }

    private Integer toEventId(Integer status) {
        if (LEADS_STATUS_EVENT_LOG_MAP == null) {
            LEADS_STATUS_EVENT_LOG_MAP = leadsEventConfManager.list().stream()
                    .collect(Collectors.toMap(LeadsEventConf::getLeadsStatus, Function.identity()));
        }

        if (!LEADS_STATUS_EVENT_LOG_MAP.containsKey(status)) {
            return null;
        }

        return LEADS_STATUS_EVENT_LOG_MAP.get(status).getId();
    }

    private BigDecimal getLeadsPayAmount(
            Map<Long, List<StoreOrderDto>> leadsStoreOrderMap, Long leadsId,
            Map<String, List<StoreOrderDto>> mobileLeadsStoreOrderMap, String mobile) {
        List<StoreOrderDto> storeOrderList = leadsStoreOrderMap.containsKey(leadsId) ?
                Lists.newArrayList(leadsStoreOrderMap.get(leadsId)) : Lists.newArrayList();
        //通过手机号
        if (!StringUtils.isEmpty(mobile)) {
            List<StoreOrderDto> mobileStoreOrderList = mobileLeadsStoreOrderMap.getOrDefault(mobile, Lists.newArrayList());
            storeOrderList.addAll(mobileStoreOrderList);
        }

        return getLeadsPayAmount(storeOrderList);
    }

    private BigDecimal getLeadsPayAmount(List<StoreOrderDto> storeOrderList) {
        BigDecimal totalPayAmount = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(storeOrderList)) {
            for (StoreOrderDto storeOrderDto : storeOrderList) {
                if (!StringUtils.isEmpty(storeOrderDto.getOrderAmount())) {
                    BigDecimal deposit = new BigDecimal(storeOrderDto.getOrderAmount());
                    totalPayAmount = totalPayAmount.add(deposit);
                }
            }
        }

        return totalPayAmount;
    }

    private BigDecimal getLeadsDeposit(
            Map<Long, List<StoreOrderDto>> leadsStoreOrderMap, Long leadsId,
            Map<String, List<StoreOrderDto>> mobileLeadsStoreOrderMap, String mobile) {
        List<StoreOrderDto> storeOrderList = leadsStoreOrderMap.containsKey(leadsId) ?
                Lists.newArrayList(leadsStoreOrderMap.get(leadsId)) : Lists.newArrayList();
        //通过手机号
        if (!StringUtils.isEmpty(mobile)) {
            List<StoreOrderDto> mobileStoreOrderList = mobileLeadsStoreOrderMap.getOrDefault(mobile, Lists.newArrayList());
            storeOrderList.addAll(mobileStoreOrderList);
        }

        return getLeadsDeposit(storeOrderList);
    }

    private BigDecimal getLeadsDeposit(List<StoreOrderDto> storeOrderList) {
        BigDecimal totalDeposit = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(storeOrderList)) {
            for (StoreOrderDto storeOrderDto : storeOrderList) {
                if (!StringUtils.isEmpty(storeOrderDto.getOrderDeposit())) {
                    BigDecimal deposit = new BigDecimal(storeOrderDto.getOrderDeposit());
                    totalDeposit = totalDeposit.add(deposit);
                }
            }
        }

        return totalDeposit;
    }


    private List<Long> getParentDepartmentIds(Long departmentId, Integer bid) {
        if (ObjectUtils.isEmpty(departmentId)) {
            return Lists.newArrayList();
        }

        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setBid(bid == 0 ? 1 : bid);
        getDepartmentRequest.setDepartmentId(departmentId);
        return departmentClientForLeads.getDepartmentWithParents(getDepartmentRequest).stream()
                .map(DepartmentDto::getId).collect(Collectors.toList());
    }

    private Long getLeadsCreateTime(Long leadsId, Map<Long, Leads> leadsMap) {
        Leads leads = leadsMap.get(leadsId);
        if (ObjectUtils.isEmpty(leads)) {
            return null;
        }
        LocalDateTime createTime = leads.getCreateTime();
        if (ObjectUtils.isEmpty(createTime)) {
            return null;
        }

        return DateTimeUtils.getMilli(createTime);
    }

    private Long getLeadsDistributeTime(Long leadsId, Map<Long, Leads> leadsMap) {
        Leads leads = leadsMap.get(leadsId);
        if (ObjectUtils.isEmpty(leads)) {
            return null;
        }
        LocalDateTime distributeTime = leads.getDistributeTime();
        if (ObjectUtils.isEmpty(distributeTime)) {
            return null;
        }

        return DateTimeUtils.getMilli(distributeTime);
    }

    private IndexDocDto toIndexDocDto(LeadsEventEsDto leadsEventEsDto) {
        IndexDocDto indexDocDto = new IndexDocDto();
        indexDocDto.setDocId(leadsEventEsDto.getId().toString());
        indexDocDto.setDocJson(jsonService.toJson(leadsEventEsDto));

        return indexDocDto;
    }

    private Boolean filterLeadStatusIsNotCounted(Long leadsId, Map<Long, Leads> leadsMap) {
        if (ObjectUtils.isEmpty(leadsId)){
            return false;
        }
        if (Long.valueOf(0).equals(leadsId)){
            return false;
        }
        if (ObjectUtils.isEmpty(leadsMap.get(leadsId))) {
            return false;
        }

        return filterLeadStatusIsNotCounted(leadsMap.get(leadsId));
    }

    private Boolean filterLeadStatusIsNotCounted(Leads leads){
        return !NO_STATISTICS_LEADS_STATUS.contains(leads.getStatus());
    }

    private Leads getLeadsId(Integer bid, Long leadsId){
        QueryWrapper<Leads> wrapper = Wrappers.<Leads>query().eq(Leads.BID, bid).notIn(Leads.STATUS, NO_STATISTICS_LEADS_STATUS);

        if (!ObjectUtils.isEmpty(leadsId) && !Long.valueOf(0).equals(leadsId)){
            wrapper.eq(Leads.ID,leadsId);
        }else {
            return null;
        }
        wrapper.last("LIMIT 1");

        return leadsManager.getOne(wrapper);
    }

    private void fillInDepartmentInfo( LeadsEventEsDto leadsEventEsDto, Map<Long, StaffDto> staffMap,
                                       Long followStaffId, Long preFollowStaffId){
        StaffDto emptyStaff = new StaffDto();
        leadsEventEsDto.setFollowDepartmentId(
                staffMap.getOrDefault(followStaffId, emptyStaff).getDepartmentId());

        //若线索被客服接待过添加客服部门ID
        if (!ObjectUtils.isEmpty(preFollowStaffId)) {
            leadsEventEsDto.setPreFollowStaffId(preFollowStaffId);
            leadsEventEsDto.setPreFollowDepartmentId(staffMap.getOrDefault(preFollowStaffId, emptyStaff).getDepartmentId());
        }

        //填充父级部门
        leadsEventEsDto.setFollowDepartmentIds(
                getParentDepartmentIds(leadsEventEsDto.getFollowDepartmentId(), leadsEventEsDto.getBid()));
        leadsEventEsDto.setPreFollowDepartmentIds(
                getParentDepartmentIds(leadsEventEsDto.getPreFollowDepartmentId(), leadsEventEsDto.getBid()));
    }

    private void esIndexUpdate(String indexAliasName) {
        GetIndexRequest request = new GetIndexRequest(indexAliasName + "_*");
        GetIndexResponse response;
        try {
            response = client.indices().get(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("获取索引[{}]失败", indexAliasName, e);
            return;
        }
        List<String> indices = Lists.newArrayList(response.getIndices());
        indices.sort(Collections.reverseOrder());
        if (!CollectionUtils.isEmpty(indices) && indices.size() <= NEWEST_ES_INDEX_SIZE) {
            return;
        }
        List<String> deleteIndices = indices.subList(NEWEST_ES_INDEX_SIZE, indices.size());
        for (String deleteIndex : deleteIndices) {
            DeleteIndexRequest deleteRequest = new DeleteIndexRequest(deleteIndex);
            try {
                client.indices().delete(deleteRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.error("删除索引[{}]失败", deleteIndex, e);
            }
        }
    }

}

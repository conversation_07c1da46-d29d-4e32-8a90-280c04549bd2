package com.inngke.bp.leads.client;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.request.CreateBatchImportRequest;
import com.inngke.ip.common.dto.request.ImportTaskUpdateRequest;
import com.inngke.ip.common.dto.response.BatchImportTaskDTO;
import com.inngke.ip.common.service.BatchImportTaskService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/11/3
 **/
@Component
public class BatchImportTaskClientForLeads {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private BatchImportTaskService batchImportTaskService;

    public Long create(Integer bid, String url, String business, Long operatorId) {
        CreateBatchImportRequest createImportRequest = new CreateBatchImportRequest();
        createImportRequest.setUrl(url);
        createImportRequest.setBusiness(business);
        createImportRequest.setOperatorId(operatorId);
        createImportRequest.setBid(bid);
        BaseResponse<BatchImportTaskDTO> batchImportTaskDTOBaseResponse = batchImportTaskService.create(createImportRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(batchImportTaskDTOBaseResponse)) {
            throw new InngkeServiceException("创建批次失败");
        }

        return batchImportTaskDTOBaseResponse.getData().getId();
    }

    public void update(ImportTaskUpdateRequest request) {

        batchImportTaskService.update(request);
    }
}

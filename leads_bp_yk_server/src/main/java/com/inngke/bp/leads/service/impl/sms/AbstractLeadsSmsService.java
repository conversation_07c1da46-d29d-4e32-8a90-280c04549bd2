package com.inngke.bp.leads.service.impl.sms;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.MerchantClientForLeads;
import com.inngke.bp.leads.client.MqServiceForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.dto.request.LeadsGetRequest;
import com.inngke.bp.leads.dto.request.LeadsSmsSendRequest;
import com.inngke.bp.leads.dto.request.LeadsSmsVarDto;
import com.inngke.bp.leads.dto.request.UpdateLeadsStatusRecordRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.LeadsSmsService;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.organize.dto.request.merchant.MerchantDto;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.reach.dto.request.SendSmsRequest;
import com.inngke.ip.reach.dto.request.SmsTemplateRequest;
import com.inngke.ip.reach.dto.response.SmsTemplateResponse;
import com.inngke.ip.reach.service.SmsService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/6 10:41
 */
public abstract class AbstractLeadsSmsService implements LeadsSmsService {
    private static final Logger logger = LoggerFactory.getLogger(AbstractLeadsSmsService.class);

    private final static Integer SUCCESS = 3;

    private static final String MEASURE_SERVICE_SMS_CODE = "measure_service";

    private static final String REACH_SHOP_SMS_CODE = "reach_shop";

    private final static String STAFF_SEND_LEADS_SMS_TIMES = LeadsServiceConsts.APP_ID +
            ":staff_send_leads_sms_times:";

    private final static Integer MAX_SEND_TIMES = 100;

    @Autowired
    private LeadsService leadsService;

    @DubboReference(version = "1.0.0", url = "${inngke.dubbo.url.reach_ip_yk:}")
    private SmsService smsService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @DubboReference(version = "1.0.0")
    private CustomerGetService customerGetService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private MerchantClientForLeads merchantClientForLeads;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;


    @Override
    public BaseResponse<Boolean> send(LeadsSmsSendRequest request) {
        BaseResponse<Boolean> checkSendResponse = checkSend(request);
        if (!BaseResponse.responseSuccessWithNonNullData(checkSendResponse)) {
            return checkSendResponse;
        }
        //获取线索详情
        LeadsDto leadsInfo = getLeadsInfo(request.getBid(), request.getLeadsId());

        //检查线索
        BaseResponse<Boolean> checkResponse = checkLeads(leadsInfo);
        if (!BaseResponse.responseSuccess(checkResponse) || checkResponse.getData().equals(false)) {
            return checkResponse;
        }

        //发送短信
        BaseResponse<Boolean> response = sendSms(leadsInfo, request);
        incrSendTimes(request);
        if (!BaseResponse.responseSuccess(response) || !Boolean.TRUE.equals(response.getData())) {
            return response;
        }

        //创建跟进记录
        return createFollowAndChangeStatus(leadsInfo, request);
    }

    private String getCacheKey(LeadsSmsSendRequest request) {
        return STAFF_SEND_LEADS_SMS_TIMES + request.getBid() + "optId:" + request.getOperatorId();
    }

    private BaseResponse<Boolean> checkSend(LeadsSmsSendRequest request) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Integer sendTimes = Optional.ofNullable((Integer) valueOperations.get(getCacheKey(request))).orElse(0);
        if (sendTimes >= MAX_SEND_TIMES) {
            return BaseResponse.error("已达到每日发送最大限制");
        }
        return BaseResponse.success(true);
    }

    private void incrSendTimes(LeadsSmsSendRequest request) {
        String cacheKey = getCacheKey(request);
        redisTemplate.boundValueOps(cacheKey).increment();
        Long expire = redisTemplate.boundValueOps(cacheKey).getExpire();
        if (expire == null || expire <= 0) {
            redisTemplate.boundValueOps(cacheKey).expire(1L, TimeUnit.DAYS);
        }
    }

    /**
     * 创建跟进记录并且变更线索信息
     *
     * @return
     */
    protected BaseResponse<Boolean> createFollowAndChangeStatus(LeadsDto leads, LeadsSmsSendRequest request) {
        //线索的最新状态
        LeadsStatusEnum leadsNewStatusEnum = LeadsStatusEnum.parse(leads.getStatus());
        if (leadsNewStatusEnum.getStatus() < getNewStatusEnum().getStatus()) {
            leadsNewStatusEnum = getNewStatusEnum();
        }

        //创建跟进记录
        LeadsFollow leadsFollow = createLeadsFollow(leads, leadsNewStatusEnum, request);

        boolean save = leadsFollowManager.saveFollow(leadsFollow);
        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());

        if (!save) {
            return BaseResponse.error("保存跟进记录失败");
        }

        mqServiceForLeads.sendLeadsFollowMq(leadsFollow);

        //更新线索状态记录
        if (Objects.nonNull(leads)){
            UpdateLeadsStatusRecordRequest updateLeadsStatusRecordRequest = new UpdateLeadsStatusRecordRequest();
            updateLeadsStatusRecordRequest.setStatus(Objects.equals(request.getType(),1) ? LeadsStatusEnum.MEASURED.getStatus() : LeadsStatusEnum.STORED.getStatus());
            updateLeadsStatusRecordRequest.setLeadsId(leads.getId());
            updateLeadsStatusRecordRequest.setBid(request.getBid());
            leadsServiceV2.updateLeadsStatusRecord(updateLeadsStatusRecordRequest);
        }

        return BaseResponse.success(true);
    }

    /**
     * 获取短信模板
     *
     * @return
     */
    protected abstract String getSmsTemplateContent();

    /**
     * 获取跟进记录内容
     *
     * @param leads
     * @param request
     * @return
     */
    protected abstract String getFollowContent(LeadsDto leads, LeadsSmsSendRequest request);

    /**
     * 获取应该抵达的线索状态
     *
     * @return
     */
    protected abstract LeadsStatusEnum getNewStatusEnum();

    /**
     * 检查线索
     *
     * @param leadsDto
     * @return
     */
    private BaseResponse<Boolean> checkLeads(LeadsDto leadsDto) {
        if (ObjectUtils.isEmpty(leadsDto)) {
            return BaseResponse.error("获取线索详情失败");
        }

        if (StringUtils.isEmpty(leadsDto.getMobile())) {
            return BaseResponse.error("线索手机号为空");
        }

        LeadsStatusEnum leadsStatusEnum = LeadsStatusEnum.parse(leadsDto.getStatus());
        if (leadsStatusEnum == null) {
            return BaseResponse.error("线索状态异常");
        }

        return BaseResponse.success(true);
    }

    /**
     * 获取线索详情
     *
     * @param bid
     * @param leadsId
     * @return
     */
    private LeadsDto getLeadsInfo(Integer bid, Long leadsId) {
        LeadsGetRequest request = new LeadsGetRequest();
        request.setOperatorId(0L);
        request.setId(leadsId);
        request.setBid(bid);

        BaseResponse<LeadsDto> leadsResponse = leadsService.getLeads(request);
        if (!BaseResponse.responseSuccessWithNonNullData(leadsResponse)) {
            throw new InngkeServiceException("获取线索详情失败:" + leadsResponse.getMsg());
        }

        return leadsResponse.getData();
    }

    private void appendMerchantList(LeadsSmsSendRequest request) {
        Integer bid = request.getBid();
        //多加一个 brandName 变量
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        MerchantDto merchant = merchantClientForLeads.getMerchant(baseIdsRequest);
        LeadsSmsVarDto leadsSmsVarDto = new LeadsSmsVarDto();
        leadsSmsVarDto.setKey("brandName");
        leadsSmsVarDto.setValue(merchant.getName()); //获取企业简称
        request.getLeadsSmsVarList().add(leadsSmsVarDto);
    }

    /**
     * 发送短信
     *
     * @param leads
     * @param request
     * @return
     */
    private BaseResponse<Boolean> sendSms(LeadsDto leads, LeadsSmsSendRequest request) {

        //获取短信模板
        Integer bid = request.getBid();
        //发送的短信类型 1:量尺 2:到店
        Integer type = request.getType();

        SmsTemplateRequest smsTemplateRequest = new SmsTemplateRequest();
        smsTemplateRequest.setBid(bid);
        if (type.equals(1)) {
            smsTemplateRequest.setCode(MEASURE_SERVICE_SMS_CODE);
        } else {
            smsTemplateRequest.setCode(REACH_SHOP_SMS_CODE);
        }
        BaseResponse<SmsTemplateResponse> smsTemplateResp = smsService.getSmsTemplate(smsTemplateRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(smsTemplateResp)) {
            return BaseResponse.error("获取短信模板失败！");
        }
        SmsTemplateResponse template = smsTemplateResp.getData();
        Map<String, String> params = Maps.newHashMap();
        //TODO 兼容前端传来的变量，后面需要去除
        Map<String, String> varMaps = Maps.newHashMap();
        List<String> varConf = template.getVarConf();
        int index = 1;
        for (String var : varConf) {
            varMaps.put(String.valueOf(index),var);
            index++;
        }

        //装填变量
        request.getLeadsSmsVarList().forEach(
                leadsSmsVarDto -> {
                    String key = leadsSmsVarDto.getKey();
                    params.put(varMaps.get(key),leadsSmsVarDto.getValue());
                }
        );
        /*
         * 追加企业简称
         */
        appendMerchantList(request);


        SendSmsRequest sendSmsRequest = new SendSmsRequest();
        sendSmsRequest.setBid(bid);
        sendSmsRequest.setVarParam(params);
        sendSmsRequest.setTemplateCode(template.getTemplateCode());
        sendSmsRequest.setTemplateId(template.getTemplateId());
        sendSmsRequest.setMobiles(Lists.newArrayList(leads.getMobile()));
        sendSmsRequest.setPlatform(template.getPlatform());
        sendSmsRequest.setVarConf(template.getVarConf());

        BaseResponse<Boolean> response = smsService.sendSms(sendSmsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return BaseResponse.error(response.getMsg());
        }

        if (Boolean.TRUE.equals(response.getData())) {
            return BaseResponse.success(true);
        }

        return BaseResponse.error("短信发送失败" + response.getMsg());
    }

    /**
     * 创建线索跟进记录
     *
     * @param leads
     * @param leadsNewStatusEnum
     * @param request
     * @return
     */
    protected LeadsFollow createLeadsFollow(LeadsDto leads, LeadsStatusEnum leadsNewStatusEnum
            , LeadsSmsSendRequest request) {
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(request.getBid());
        leadsFollow.setLeadsId(leads.getId());
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setFollowContent(getFollowContent(leads, request));
        leadsFollow.setLeadsStatus(leadsNewStatusEnum.getStatus());
        leadsFollow.setCreateTime(LocalDateTime.now());
        StaffDto staffDto = staffClientForLeads.getStaffByCid(request.getBid(), request.getOperatorId());
        leadsFollow.setUserId(request.getOperatorId());
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setStaffId(staffDto.getId());
        }
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        return leadsFollow;
    }

    protected Long getStaffId(LeadsSmsSendRequest request) {
        StaffListRequest staffByCustomerId = new StaffListRequest();
        staffByCustomerId.setCustomerId(request.getOperatorId());
        staffByCustomerId.setBid(request.getBid());
        // 唯一性
        StaffDto staffDto = staffClientForLeads.getStaffList(staffByCustomerId).stream().filter(staff->staff.getStatus()>0).findFirst().orElse(null);
        if (staffDto == null){
            throw new InngkeServiceException("获取员工失败");
        }
        return staffDto.getId();
    }

    /**
     * 获取发送的短信详情
     *
     * @param leadsSmsVarDtoList
     * @return
     */
    protected String getSmsContentInfo(List<LeadsSmsVarDto> leadsSmsVarDtoList) {
        String smsTemplateContent = getSmsTemplateContent();

        for (LeadsSmsVarDto leadsSmsVarDto : leadsSmsVarDtoList) {
            String key = leadsSmsVarDto.getKey();
            String value = Optional.ofNullable(leadsSmsVarDto.getValue()).orElse(InngkeAppConst.EMPTY_STR);

            smsTemplateContent = smsTemplateContent.replace("{" + key + "}", value);
        }

        return smsTemplateContent;
    }

}

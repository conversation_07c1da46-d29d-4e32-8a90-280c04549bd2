package com.inngke.bp.leads.dto;

import com.inngke.common.utils.Excel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 线索导出Excel
 * <AUTHOR>
 */
public class LeadsStatisticsExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 经销商
     *
     * @demo 12345
     */
    @Excel(name = "经销商")
    private String agentName;

    /**
     * 员工
     *
     * @demo 张三
     */
    @Excel(name = "负责人")
    private String staffName;

    /**
     * 线索数量
     *
     * @demo 1234
     */
    @Excel(name = "线索数")
    private Integer leadsCount;

    /**
     * 订单数量
     *
     * @demo 231
     */
    @Excel(name = "下单数")
    private Integer orderCount;

    /**
     * 订单总金额
     *
     * @demo 2324.00
     */
    @Excel(name = "订单总金额(单位：元)")
    private BigDecimal orderTotalAmount;

    /**
     * 有效率，单位：%
     *
     * @demo 95
     */
    @Excel(name = "线索有效率(单位：%)")
    private BigDecimal availableLeadsRate;

    /**
     * 24小时联系率，单位：%
     *
     * @demo 97
     */
    @Excel(name = "24h联系率(单位：%)")
    private BigDecimal contact24hLeadsRate;

    /**
     * 总联系率，单位：%
     *
     * @demo 99
     */
    @Excel(name = "总联系率(单位：%)")
    private BigDecimal contactTotalLeadsRate;

    /**
     * 到店率，单位：%
     *
     * @demo 9
     */
    @Excel(name = "到店率(单位：%)")
    private BigDecimal storedLeadsRate;

    /**
     * 转化率，单位：%
     *
     * @demo 9
     */
    @Excel(name = "转化率(单位：%)")
    private BigDecimal tradedLeadsRate;


    public Integer getLeadsCount() {
        return leadsCount;
    }

    public void setLeadsCount(Integer leadsCount) {
        this.leadsCount = leadsCount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }

    public BigDecimal getAvailableLeadsRate() {
        return availableLeadsRate;
    }

    public void setAvailableLeadsRate(BigDecimal availableLeadsRate) {
        this.availableLeadsRate = availableLeadsRate;
    }

    public BigDecimal getContact24hLeadsRate() {
        return contact24hLeadsRate;
    }

    public void setContact24hLeadsRate(BigDecimal contact24hLeadsRate) {
        this.contact24hLeadsRate = contact24hLeadsRate;
    }

    public BigDecimal getContactTotalLeadsRate() {
        return contactTotalLeadsRate;
    }

    public void setContactTotalLeadsRate(BigDecimal contactTotalLeadsRate) {
        this.contactTotalLeadsRate = contactTotalLeadsRate;
    }

    public BigDecimal getStoredLeadsRate() {
        return storedLeadsRate;
    }

    public void setStoredLeadsRate(BigDecimal storedLeadsRate) {
        this.storedLeadsRate = storedLeadsRate;
    }

    public BigDecimal getTradedLeadsRate() {
        return tradedLeadsRate;
    }

    public void setTradedLeadsRate(BigDecimal tradedLeadsRate) {
        this.tradedLeadsRate = tradedLeadsRate;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }
}

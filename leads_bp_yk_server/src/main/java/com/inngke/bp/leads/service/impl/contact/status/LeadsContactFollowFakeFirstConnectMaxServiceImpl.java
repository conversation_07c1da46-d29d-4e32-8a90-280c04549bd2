package com.inngke.bp.leads.service.impl.contact.status;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.bp.leads.service.impl.contact.AbstractLeadsContactFollowService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 虚拟号 首次拨打 接通 10s以上
 * 跟进记录1：第一次拨打客户电话，系统将线索状态由【待联系】修改为【已联系】
 * 跟进记录2：第一次拨打客户电话，通话时长15秒，系统将线索状态由【已联系】修改为【已成功联系】
 *
 * <AUTHOR>
 * @date 2022/4/2 14:22
 */
@Service
public class LeadsContactFollowFakeFirstConnectMaxServiceImpl extends AbstractLeadsContactFollowService {

    @Resource
    private StaffClientForLeads staffClientForLeads;

    @Override
    public BaseResponse<Boolean> createContactFollow(Leads leads, PrivateVoiceRecordDTO dto) {
        //添加第一条条跟进记录 已联系
        LeadsFollow leadsFollow = createLeadsFollow(leads, generateFirstFollowContent());
        leadsFollow.setLeadsStatus(LeadsStatusEnum.CONTACTED.getStatus());
        leadsFollow.setCreateTime(dto.getVoiceStart());
        if (!Boolean.TRUE.equals(leadsFollowManager.createContactFollow(leadsFollow))) {
            return BaseResponse.error("创建跟进记录失败");
        }


        //添加第二条跟进记录 已成功联系
        LeadsFollow leadsTwiceFollow = createLeadsFollow(leads, generateTwiceFollowContent(dto));
        leadsTwiceFollow.setLeadsStatus(LeadsStatusEnum.SUCCESS_CONTACT.getStatus());
        leadsTwiceFollow.setCreateTime(dto.getVoiceEnd());
        Long guideId = dto.getGuideId();
        StaffDto staff = staffClientForLeads.getStaffByCid(dto.getBid(), guideId);
        if (Objects.nonNull(staff)) {
            leadsFollow.setStaffId(staff.getId());
        }

        Boolean contactFollow = leadsFollowManager.createContactFollow(leadsTwiceFollow);
        if (!Boolean.TRUE.equals(contactFollow)) {
            return BaseResponse.error("创建跟进记录失败");
        }
        setContactIn24Property(leads, dto);

        return BaseResponse.success(true);
    }

    @Override
    public LeadsContactStatusEnum getStatusEnum() {
        return LeadsContactStatusEnum.FAKE_FIRST_CONNECT_MAX;
    }

    @Override
    public String generateContactFollowContent(Leads leads, PrivateVoiceRecordDTO dto) {
        return null;
    }

    private String generateFirstFollowContent() {
        return "第一次拨打客户电话，系统将线索状态由【待联系】修改为【已联系】";
    }

    private String generateTwiceFollowContent(PrivateVoiceRecordDTO dto) {
        return "第一次拨打客户电话，通话时长" + getTalkTimeText(dto.getVoiceTime())
                + "，系统将线索状态由【已联系】修改为【已成功联系】";
    }

}

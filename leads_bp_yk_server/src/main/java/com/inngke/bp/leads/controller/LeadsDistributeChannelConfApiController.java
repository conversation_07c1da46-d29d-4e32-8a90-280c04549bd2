package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.AddDistributeChannelConfRequest;
import com.inngke.bp.leads.dto.request.UpdateDistributeChannelConfRequest;
import com.inngke.bp.leads.dto.response.DistributeChannelConfDto;
import com.inngke.bp.leads.service.LeadsDistributeChannelConfService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @chapter 接收人配置
 * @section 线索渠道配置
 * @since 2021/9/7 3:49 PM
 */
@RestController
@RequestMapping("/api/leads/distribute-conf-channel")
public class LeadsDistributeChannelConfApiController {

    @Resource
    private LeadsDistributeChannelConfService leadsDistributeChannelConfService;

    /**
     * 获取线索渠道配置列表
     */
    @GetMapping
    public BaseResponse<List<DistributeChannelConfDto>> getList(@RequestAttribute JwtPayload jwtPayload){
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(jwtPayload.getBid());
        return leadsDistributeChannelConfService.getList(request);
    }

    /**
     * 新增接收规则
     */
    @PostMapping
    public BaseResponse<Boolean> addDistributeChannelConf(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody AddDistributeChannelConfRequest request){
        request.setBid(jwtPayload.getBid());
        return leadsDistributeChannelConfService.addDistributeChannelConf(request);
    }

    /**
     * 更新接收规则
     */
    @PutMapping
    public BaseResponse<Boolean> updateDistributeChannelConf(@RequestBody UpdateDistributeChannelConfRequest request){
        return leadsDistributeChannelConfService.updateDistributeChannelConf(request);
    }
}

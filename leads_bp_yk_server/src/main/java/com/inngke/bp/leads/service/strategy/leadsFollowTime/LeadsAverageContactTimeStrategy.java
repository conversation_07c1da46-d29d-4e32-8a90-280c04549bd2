package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Created by emiya on 2022/6/15 17:30
 *
 * <AUTHOR>
 * @date 2022/6/15 17:30
 */
@Component(value = "LeadsAverageContactTimeStrategy")
public class LeadsAverageContactTimeStrategy implements LeadsFollowTimeStrategy {


    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.sum(
                        "firstCallIntervalTime"
                ).field("firstCallIntervalTime")
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedSum callSum = bucketAggregations.get("firstCallIntervalTime");
        double callTimeValue = callSum.getValue();
        dto.setFirstCallIntervalTime(Double.valueOf(callTimeValue).longValue());
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Long callTime = dtos.stream().map(LeadsFollowTimeByStaffResponse::getFirstCallIntervalTime).reduce(Long::sum).get();
        dto.setFirstCallIntervalTime(callTime);
    }
}

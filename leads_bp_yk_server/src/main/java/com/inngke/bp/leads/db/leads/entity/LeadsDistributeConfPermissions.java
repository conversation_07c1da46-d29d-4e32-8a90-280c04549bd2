/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 区域接收人配置的-权限配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsDistributeConfPermissions implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * bid
     */
    private Integer bid;

    /**
     * 区域id
     */
    private Integer regionId;

    /**
     * 员工ids
     */
    private String staffIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String REGION_ID = "region_id";

    public static final String STAFF_IDS = "staff_ids";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

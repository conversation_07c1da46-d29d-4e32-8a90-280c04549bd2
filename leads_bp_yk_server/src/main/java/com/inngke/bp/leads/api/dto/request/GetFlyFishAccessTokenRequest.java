package com.inngke.bp.leads.api.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:12
 */
@NoArgsConstructor
@Data
public class GetFlyFishAccessTokenRequest implements Serializable {
    /**
     * 必填 number 开发者申请的应用APP_ID，可通过“应用管理”界面查看
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 必填 string 开发者应用的私钥Secret，可通过“应用管理”界面查看（确保填入secret与app_id对应以免报错！）
     */
    private String secret;

    /**
     * 必填 string 授权类型。允许值: "auth_code"
     */
    @JsonProperty("grant_type")
    private String grantType;

}

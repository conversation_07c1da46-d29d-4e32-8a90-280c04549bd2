package com.inngke.bp.leads.service.message.context;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.service.message.MessageTypeEnum;

import java.io.Serializable;

public class MessageContext implements Serializable {

    private int bid;
    private Long deliverAt;
    private Integer deliverAfter;
    private MessageTypeEnum messageType;
    private Long targetSid;
    private String targetWxPubOpenId;
    private String targetQyUserId;
    private Leads leads;

    /**
     * 数量
     */
    private Integer count;
    /**
     * 企业简称
     */
    private String enterpriseName;

    private String mobile;

    public MessageContext(int bid, MessageTypeEnum messageType) {
        this.messageType = messageType;
        this.bid = bid;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public int getBid() {
        return bid;
    }

    public void setBid(int bid) {
        this.bid = bid;
    }

    public Long getDeliverAt() {
        return deliverAt;
    }

    public void setDeliverAt(Long deliverAt) {
        this.deliverAt = deliverAt;
    }

    public Integer getDeliverAfter() {
        return deliverAfter;
    }

    public void setDeliverAfter(Integer deliverAfter) {
        this.deliverAfter = deliverAfter;
    }

    public MessageTypeEnum getMessageType() {
        return messageType;
    }

    public void setMessageType(MessageTypeEnum messageType) {
        this.messageType = messageType;
    }

    public Long getTargetSid() {
        return targetSid;
    }

    public void setTargetSid(Long targetSid) {
        this.targetSid = targetSid;
    }

    public void setTargetWxPubOpenId(String targetWxPubOpenId) {
        this.targetWxPubOpenId = targetWxPubOpenId;
    }

    public String getTargetWxPubOpenId() {
        return targetWxPubOpenId;
    }

    public void setTargetQyUserId(String targetQyUserId) {
        this.targetQyUserId = targetQyUserId;
    }

    public String getTargetQyUserId() {
        return targetQyUserId;
    }

    public Leads getLeads() {
        return leads;
    }

    public void setLeads(Leads leads) {
        this.leads = leads;
    }

    public <T extends MessageContext> T transferContext() {
        return (T) this;
    }

}

package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.utils.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 定金订单转化率
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeDepositStrategy")
public class LeadsFollowTimeDepositStrategy implements LeadsFollowTimeStrategy {
    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateDeposit(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateDeposit",
                        //QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("depositAmount").gt(0))
                        QueryBuilders.boolQuery().should(QueryBuilders.rangeQuery("depositAmount").gt(0))
                                .should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.ORDERED.getStatus()))
                                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.ORDERED.getStatus()))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateDeposit = bucketAggregations.get("stateDeposit");
        long count = stateDeposit.getDocCount();
        dto.setStateDeposit((int)count);
        // 获取有效客户数
        ParsedFilter contactFilter = bucketAggregations.get("validCount");
        long validCount = contactFilter.getDocCount();
        if (validCount > 0L) {
            String rate = BigDecimal.valueOf(count).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(validCount),2, RoundingMode.HALF_DOWN).toString();
            dto.setDepositRate(rate+"%");
        }
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateDeposit).reduce(Integer::sum).get();
        dto.setStateDeposit(sum);
        if(dto.getStateAvail()==0){
            return;
        }
        String rate = BigDecimal.valueOf(sum).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getStateAvail()),2, RoundingMode.HALF_DOWN).toString();
        dto.setDepositRate(rate+"%");
    }
}

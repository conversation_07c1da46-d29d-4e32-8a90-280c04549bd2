package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.platform.CustomerServiceLeadsDto;
import com.inngke.bp.leads.dto.request.tp.FeiYuLeadsPushDto;
import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/2/28 10:42
 */
public interface LeadsTpConserveService {

    /**
     * 腾讯广告 线索转换
     *
     * @param tencentLeadsPushDto 腾讯广告推送dto
     * @return 标准信息线索dto
     */
    BaseResponse<Long> conserve(Integer bid,TencentLeadsPushDto tencentLeadsPushDto);

    /**
     * 飞鱼 线索转换
     *
     * @param feiYuLeadsPushDto 飞鱼推送dto
     * @return 标准信息线索dto
     */
    BaseResponse<Long> conserve(Integer bid,FeiYuLeadsPushDto feiYuLeadsPushDto);

    /**
     * AI客服 线索转换
     *
     * @param customerServiceLeadsDto Ai客服线索详情
     * @return 标准信息线索dto
     */
    BaseResponse<Long> conserve(Integer bid,CustomerServiceLeadsDto customerServiceLeadsDto);
}

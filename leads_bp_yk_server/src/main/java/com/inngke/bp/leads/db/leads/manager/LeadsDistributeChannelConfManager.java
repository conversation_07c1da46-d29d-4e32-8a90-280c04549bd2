/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsDistributeChannelConf;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 线索渠道区域接收人配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
public interface LeadsDistributeChannelConfManager extends IService<LeadsDistributeChannelConf> {

    Map<Integer, List<Long>> getChannelConfIdsMap(Integer bid);

}

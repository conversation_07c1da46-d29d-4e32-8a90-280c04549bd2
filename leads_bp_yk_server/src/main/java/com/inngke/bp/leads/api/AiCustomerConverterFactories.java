package com.inngke.bp.leads.api;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.common.collect.Lists;
import com.inngke.common.service.JsonService;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import retrofit2.Converter;
import retrofit2.Retrofit;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/5 15:13
 */
public class AiCustomerConverterFactories extends Converter.Factory {

    private final ObjectMapper mapper;

    @Autowired
    private JsonService jsonService;

    public static AiCustomerConverterFactories create() {
        return create(new ObjectMapper());
    }

    public AiCustomerConverterFactories(ObjectMapper mapper) {
        this.mapper = mapper;
    }

    @SuppressWarnings("ConstantConditions")
    public static AiCustomerConverterFactories create(ObjectMapper mapper) {
        if (mapper == null) {
            throw new NullPointerException("mapper == null");
        }
        return new AiCustomerConverterFactories(mapper);
    }

    @Override
    public Converter<ResponseBody, ?> responseBodyConverter(
            Type type, Annotation[] annotations, Retrofit retrofit) {
        JavaType javaType = mapper.getTypeFactory().constructType(type);
        ObjectReader reader = mapper.readerFor(javaType);
        return new SelfResponseBodyConverter<>(reader, javaType.getRawClass());
    }

    @Override
    public Converter<?, RequestBody> requestBodyConverter(
            Type type,
            Annotation[] parameterAnnotations,
            Annotation[] methodAnnotations,
            Retrofit retrofit) {
        JavaType javaType = mapper.getTypeFactory().constructType(type);
        ObjectWriter writer = mapper.writerFor(javaType);
        return new SelfRequestBodyConverter<>(writer);
    }

    private final class SelfResponseBodyConverter<T> implements Converter<ResponseBody, T> {
        private final ObjectReader adapter;
        private final Class<T> clazz;

        SelfResponseBodyConverter(ObjectReader adapter, Class<T> clazz) {
            this.adapter = adapter;
            this.clazz = clazz;
        }

        @Override
        public T convert(ResponseBody value) throws IOException {
            try {
                String jsonString = value.string();
                if (jsonString.contains("\"data\":[]")) {
                    return getEmptyResponse(jsonString);
                }
                return adapter.readValue(jsonString);
            } finally {
                value.close();
            }
        }

        private T getEmptyResponse(String jsonString) {
            try {
                Map map = jsonService.toObject(jsonString, Map.class);
                T response = clazz.getDeclaredConstructor().newInstance();
                ArrayList<String> fields = Lists.newArrayList();
                fields.add("code");
                fields.add("msg");

                for (String field : fields) {
                    Field fieldObj = response.getClass().getDeclaredField(field);
                    fieldObj.setAccessible(true);
                    fieldObj.set(response, map.get(field));
                }

                return response;
            } catch (Exception e) {
                return null;
            }
        }
    }

    private final class SelfRequestBodyConverter<T> implements Converter<T, RequestBody> {
        private final MediaType MEDIA_TYPE = MediaType.get("application/json; charset=UTF-8");

        private final ObjectWriter adapter;

        SelfRequestBodyConverter(ObjectWriter adapter) {
            this.adapter = adapter;
        }

        @Override
        public RequestBody convert(T value) throws IOException {
            byte[] bytes = adapter.writeValueAsBytes(value);
            return RequestBody.create(MEDIA_TYPE, bytes);
        }
    }

}

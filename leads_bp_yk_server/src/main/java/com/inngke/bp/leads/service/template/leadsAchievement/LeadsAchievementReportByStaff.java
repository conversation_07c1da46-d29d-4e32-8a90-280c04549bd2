package com.inngke.bp.leads.service.template.leadsAchievement;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.organize.dto.response.AgentDto;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工维度统计策略
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 16:43
 */
@Component
@Slf4j
public class LeadsAchievementReportByStaff extends AbstractLeadsAchievement {

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private AgentClientForLeads agentClientForLeads;

    @Autowired
    private StoreOrderClientForLeads storeOrderClientForLeads;

    @Autowired
    private StaffManageDepartmentClientForLeads staffManageDepartmentClientForLeads;

    @Override
    public String getDimensionCode() {
        return "staff";
    }

    @Override
    protected String getGroupByFiled(LeadsAchievementReportContext context) {
        context.setGroupByFiled("followStaffId");
        return "followStaffId";
    }

    @Override
    protected void afterQueryBuildPlugin(LeadsAchievementReportContext context) {
        BoolQueryBuilder qb = context.getBoolQueryBuilder();
        qb.mustNot(QueryBuilders.termQuery("followStaffId", 0L));
        if (Objects.nonNull(context.getSelectStaffId())) {
            qb.must(QueryBuilders.termQuery("followStaffId", context.getSelectStaffId()));
        }
        Set<Long> manageDepartment = getManageDepartment(context.getBid(), null, context.getCurrentStaffId());
        if (Objects.nonNull(context.getSelectDeptId())) {
            qb.must(QueryBuilders.termsQuery("followDepartmentIds", Lists.newArrayList(context.getSelectDeptId())));
        }
        qb.must(QueryBuilders.termsQuery("followDepartmentId", manageDepartment));
    }

    @Override
    protected void afterParseResponsePlugin(LeadsAchievementReportContext context, Terms.Bucket bucket, LeadsAchievementReportResponse dto) {
        dto.setStaffId(Long.valueOf(bucket.getKeyAsString()));
    }

    @Override
    protected void afterGetReportDataPlugin(LeadsAchievementReportContext context) {
        List<LeadsAchievementReportResponse> reportResult = context.getReportResult();
        if (CollectionUtils.isNotEmpty(reportResult)) {
            Set<Long> staffIds = reportResult.stream().map(LeadsAchievementReportResponse::getStaffId).collect(Collectors.toSet());
            Integer bid = context.getBid();
            Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(bid, staffIds);
            // 部门信息
            Set<Long> deptIds = staffMap.values().stream().map(StaffDto::getDepartmentId).collect(Collectors.toSet());
            Map<Long, DepartmentDto> departmentDtoMap = departmentClientForLeads.getDepartmentByIds(bid, Lists.newArrayList(deptIds))
                    .stream()
                    .collect(Collectors.toMap(DepartmentDto::getId, Function.identity(), (key1, key2) -> key2));
            // 经销商信息
            Set<Long> agentIds = departmentDtoMap.values().stream().map(DepartmentDto::getAgentId).collect(Collectors.toSet());
            Map<Long, AgentDto> agentDtoMap = agentClientForLeads.getAgentByIds(bid, agentIds).stream()
                    .collect(Collectors.toMap(AgentDto::getId, Function.identity(), (key1, key2) -> key2));

            // 组装关联数据
            reportResult.forEach(item -> {
                StaffDto staffDto = staffMap.get(item.getStaffId());
                if (Objects.nonNull(staffDto)) {
                    item.setStaffName(staffDto.getName());
                    item.setMobile(staffDto.getMobile());
                    item.setDeptId(staffDto.getDepartmentId());
                    DepartmentDto departmentDto = departmentDtoMap.get(staffDto.getDepartmentId());
                    item.setDeptName(Optional.ofNullable(departmentDto).map(DepartmentDto::getName).orElse("-"));
                    item.setAgentId(Optional.ofNullable(departmentDto).map(DepartmentDto::getAgentId).orElse(0L));
                    AgentDto agentDto = agentDtoMap.get(item.getAgentId());
                    item.setAgentName(Optional.ofNullable(agentDto).map(AgentDto::getName).orElse("-"));
                }
            });
        }
    }

    private Set<Long> getManageDepartment(Integer bid, Long deptId, Long staffId) {
        // 获取当前用户的管理部门
        StaffDto staffDto = staffClientForLeads.getStaffById(bid, staffId);
        if (Objects.isNull(staffDto)) {
            log.info("获取员工失败，staffId:{}", staffId);
            return Sets.newHashSet();
        }

        Set<Long> managerDepartmentIds = staffManageDepartmentClientForLeads.getStaffManageDepartment(bid, staffDto.getId());

        // 获取传入部门的子部门
        if (Objects.nonNull(deptId) && !deptId.equals(0L)) {
            // 获取子部门
            Set<Long> childrenDepartment = departmentClientForLeads.getChildrenDepartment(bid, deptId);
            childrenDepartment.add(deptId);

            managerDepartmentIds = Sets.newHashSet(CollUtil.intersection(childrenDepartment, managerDepartmentIds));
        }
        return managerDepartmentIds;
    }
}

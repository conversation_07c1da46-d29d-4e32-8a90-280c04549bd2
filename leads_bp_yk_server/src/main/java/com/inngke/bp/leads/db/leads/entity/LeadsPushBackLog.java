/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 线索退回记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsPushBackLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商户id
     */
    private Integer bid;

    /**
     * 线索id
     */
    private Long leadsId;

    /**
     * 线索退回前的快照
     */
    private String leadsSnapshot;

    /**
     * 退回原因id
     */
    private Long pushBackReasonId;

    /**
     * 退回原因描述
     */
    private String pushBackReason;

    /**
     * 退回凭证图片，json数组
     */
    private String pushBackReasonImages;

    /**
     * 是否被拒绝：0=否；1=是
     */
    private Integer isReject;

    /**
     * 拒绝退回人staffId
     */
    private Long rejectBy;

    /**
     * 拒绝时间
     */
    private LocalDateTime rejectTime;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 退回前的线索状态
     */
    private Integer leadsStatus;

    /**
     * 退回人staffId
     */
    private Long pushBackBy;

    /**
     * 退回时间
     */
    private LocalDateTime pushBackTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String LEADS_ID = "leads_id";

    public static final String LEADS_SNAPSHOT = "leads_snapshot";

    public static final String PUSH_BACK_REASON_ID = "push_back_reason_id";

    public static final String PUSH_BACK_REASON = "push_back_reason";

    public static final String PUSH_BACK_REASON_IMAGES = "push_back_reason_images";

    public static final String IS_REJECT = "is_reject";

    public static final String REJECT_BY = "reject_by";

    public static final String REJECT_TIME = "reject_time";

    public static final String REJECT_REASON = "reject_reason";

    public static final String LEADS_STATUS = "leads_status";

    public static final String PUSH_BACK_BY = "push_back_by";

    public static final String PUSH_BACK_TIME = "push_back_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

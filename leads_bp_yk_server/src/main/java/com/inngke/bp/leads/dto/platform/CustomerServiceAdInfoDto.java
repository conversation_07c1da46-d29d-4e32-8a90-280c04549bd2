package com.inngke.bp.leads.dto.platform;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户广告信息
 *
 * <AUTHOR>
 * @date 2022/2/26 14:32
 */
@Data
public class CustomerServiceAdInfoDto implements Serializable {

    /**
     * ": "wx0k3ohvm7f5qxi4", //记录ID
     */
    @JsonProperty("trace_id")
    private String traceId;

    /**
     * ": "oneD41e2J5Z4vbEYVwoX9le1zU98",
     */
    private String openid;

    /**
     * ": "3848376174", //广告ID
     */
    @JsonProperty("adgroup_id")
    private String adgroupId;

    /**
     * ": "单注册出价-优先跑量-", //广告名称
     */
    @JsonProperty("adgroup_name")
    private String adgroupName;

    /**
     * ": "3848376172", //计划ID
     */
    @JsonProperty("campaign_id")
    private String campaignId;

    /**
     * ": "819-广佛区-当月人群包-单注册出价-装差了-新中式-商品", //计划名称
     */
    @JsonProperty("campaign_name")
    private String campaignName;

    /**
     * ": "spid95e4a6be70", //服务商ID
     */
    @JsonProperty("agency_id")
    private String agencyId;

    /**
     * ": "********", //广告位ID
     */
    @JsonProperty("position_id")
    private String positionId;

    /**
     * ": "朋友圈信息流", //广告位名称
     */
    @JsonProperty("position_name")
    private String positionName;

    /**
     * ": "2021-10-24 10:25:35", //关注时间
     */
    @JsonProperty("followed_at")
    private String followedAt;

    /**
     * ": 9798242 //广告账号id
     */
    @JsonProperty("account_id")
    private String accountId;
}

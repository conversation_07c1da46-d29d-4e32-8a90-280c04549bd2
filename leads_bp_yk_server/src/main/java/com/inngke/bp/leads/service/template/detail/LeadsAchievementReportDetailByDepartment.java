package com.inngke.bp.leads.service.template.detail;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.client.StaffManageDepartmentClientForLeads;
import com.inngke.bp.leads.enums.LeadsTypeEnum;
import com.inngke.bp.leads.service.enums.LeadsReportListDetailTypeEnum;
import com.inngke.bp.leads.service.template.leadsAchievement.LeadsAchievementReportContext;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.exception.InngkeServiceException;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/23 14:00
 */
@Component
@Slf4j
public class LeadsAchievementReportDetailByDepartment extends LeadsAchievementDetail {

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private StaffManageDepartmentClientForLeads staffManageDepartmentClientForLeads;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Override
    public String getDimensionCode() {
        return "departmentDetail";
    }


    @Override
    protected void checkPara(LeadsAchievementReportContext context) {
        if (context.getSelectDeptId() == null) {
            throw new InngkeServiceException("selectDeptId不能为空");
        }
    }

    @Override
    protected void buildPageCountHandle(LeadsAchievementReportContext context, Integer type, Integer total) {
        if (!LeadsReportListDetailTypeEnum.DISTRIBUTE.getCode().equals(type) || total == null) {
            return;
        }
        Integer resultCount = context.getResultCount();
        if (resultCount.compareTo(total) < 0 && (total - resultCount) < 10) {
            log.info("业绩报表-部门下发数，将查询出的条数由：{}改为：{}", resultCount, total);
            context.setResultCount(total);
        }
    }

    @Override
    protected void afterQueryBuildPlugin(LeadsAchievementReportContext context) {
        BoolQueryBuilder qb = context.getBoolQueryBuilder();
        qb.mustNot(QueryBuilders.termQuery("followDepartmentId", 0L));
        Set<Long> manageDepartment = getManageDepartment(context.getBid(), context.getSelectDeptId(), context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("followDepartmentId", manageDepartment));
//        DepartmentDto department = getDepartment(context.getBid(), context.getSelectDeptId());
//        if (Objects.isNull(department.getChildren())) {
//            department.setChildren(Lists.newArrayList());
//        }
//        Set<Long> childrenIds = department.getChildren().stream().map(DepartmentDto::getId).collect(Collectors.toSet());
//        childrenIds.add(department.getId());
//        qb.must(QueryBuilders.termsQuery("followDepartmentId", CollUtil.intersection(childrenIds, manageDepartment)));

    }

    private DepartmentDto getDepartment(Integer bid, Long deptId) {
        GetDepartmentRequest request = new GetDepartmentRequest();
        request.setBid(bid);
        request.setDepartmentId(deptId);
        request.setChildrenLevel(1);
        return departmentClientForLeads.getDepartment(request);
    }

    protected Set<Long> getManageDepartment(Integer bid, Long deptId, Long staffId) {
        // 获取当前用户的管理部门
        StaffDto staffDto = staffClientForLeads.getStaffById(bid, staffId);
        if (Objects.isNull(staffDto)) {
            log.info("获取员工失败，staffId:{}", staffId);
            return Sets.newHashSet();
        }

        Set<Long> managerDepartmentIds = staffManageDepartmentClientForLeads.getStaffManageDepartment(bid, staffDto.getId());

        // 获取传入部门的子部门
        if (Objects.nonNull(deptId) && !deptId.equals(0L)) {
            // 获取子部门
            Set<Long> childrenDepartment = departmentClientForLeads.getChildrenDepartment(bid, deptId);
            childrenDepartment.add(deptId);

            managerDepartmentIds = Sets.newHashSet(CollUtil.intersection(childrenDepartment, managerDepartmentIds));
        }
        return managerDepartmentIds;
    }

    @Override
    protected void setTimeAndAmountBoolQueryBuilder(BoolQueryBuilder qb, LeadsAchievementReportContext context) {
        qb.mustNot(QueryBuilders.termQuery("followDepartmentId", 0L));
        Set<Long> manageDepartment = getManageDepartment(context.getBid(), context.getSelectDeptId(), context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("followDepartmentId", manageDepartment));
    }
}

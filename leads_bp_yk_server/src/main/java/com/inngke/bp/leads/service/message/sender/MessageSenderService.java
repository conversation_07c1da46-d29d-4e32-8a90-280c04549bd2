package com.inngke.bp.leads.service.message.sender;

import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;

public interface MessageSenderService {

    MessageTypeEnum getMessageType();

    void init(MessageContext ctx);

    TemplateMessageSendRequest sendMessage(MessageContext ctx);
}

package com.inngke.bp.leads.service.strategy.permissions.list;

import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * 退回
 */
@Component
public class ListPushBackPermissionsStrategy extends ListPermissionsStrategyAbstract {
    /**
     * 可以看到自己创建的、自己所管理部门的员工创建的、自己所管理部门员工退回的（员工退回列表才有此规则）
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    @Override
    protected void otherPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultOtherPermissionsHandle(request,queryBuilder);
    }

    /**
     * 可以看到自己创建的、自己所管理部门的员工创建的、自己所管理部门员工退回的（员工退回列表才有此规则）、对接第三方系统的线索数据
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    @Override
    protected void merchantManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultMerchantManagePermissionsHandle(request,queryBuilder);
    }

    @Override
    protected BoolQueryBuilder getDefaultPermissionsBuilder(SearchLeadsRequest request, BoolQueryBuilder queryBuilder){
        Set<Long> staffManageDepartmentIds = getStaffManageDepartmentIds(request.getBid(), request.getSid());

        //自己创建的
        BoolQueryBuilder sonQueryBuilder = QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getSid()));

        //自己所管理部门的员工创建的/退回的
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            sonQueryBuilder
                    .should(QueryBuilders.termsQuery(LeadsEsDto.CREATE_DEPARTMENT_ID, staffManageDepartmentIds))
                    .should(QueryBuilders.termsQuery(LeadsEsDto.PUSHBACK_DEPARTMENT_ID, staffManageDepartmentIds));
        }
        return sonQueryBuilder;
    }
}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.LeadsChannelUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import com.inngke.bp.leads.enums.LeadsPreFollowStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsSearchService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/23 17:38
 */
@Service
public class LeadsSearchServiceImpl implements LeadsSearchService {

    @Autowired
    private RestHighLevelClient esClient;


    @Autowired
    private JsonService jsonService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LeadsManager leadsManager;

    private LeadsBillingIndicatorsDto hitToLeadsBillingIndicatorsDto(SearchHit hit) {
        LeadsBillingIndicatorsDto leadsBillingIndicatorsDto = jsonService.toObject(hit.getSourceAsString(), LeadsBillingIndicatorsDto.class);
        Long zero = 0L;
        if (zero.compareTo(leadsBillingIndicatorsDto.getStateDeposit()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getStateDeposit()));
            leadsBillingIndicatorsDto.setStateDepositTimeStr(s);
        }
        if (zero.compareTo(leadsBillingIndicatorsDto.getStateOrderSuccess()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getStateOrderSuccess()));
            leadsBillingIndicatorsDto.setStateOrderSuccessTimeStr(s);
        }
        if (zero.compareTo(leadsBillingIndicatorsDto.getDistributeTime()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getDistributeTime()));
            leadsBillingIndicatorsDto.setDistributeTimeStr(s);
        }
        if (zero.compareTo(leadsBillingIndicatorsDto.getCreateTime()) < 0) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(leadsBillingIndicatorsDto.getCreateTime()));
            leadsBillingIndicatorsDto.setCreateTimeStr(s);
        }

        return leadsBillingIndicatorsDto;
    }

    @Override
    public BasePaginationResponse<LeadsBillingIndicatorsDto> getLeadsByEs(Integer bid, BoolQueryBuilder queryBuilder, int from, int size) throws IOException {

        BasePaginationResponse<LeadsBillingIndicatorsDto> result = new BasePaginationResponse<>();

        List<LeadsBillingIndicatorsDto> list = new ArrayList<>(size);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .size(size).from(from)
                .query(queryBuilder);

        // 线索排序
        searchSourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.DESC);

        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);

        SearchResponse search = esClient.search(searchRequest, RequestOptions.DEFAULT);

        SearchHits hits = search.getInternalResponse().hits();

        Set<Long> staffIds = new HashSet<>();

        for (SearchHit hit : hits.getHits()) {
            LeadsBillingIndicatorsDto leadsBillingIndicatorsDto = hitToLeadsBillingIndicatorsDto(hit);

            LeadsStatusEnum parse = LeadsStatusEnum.parse(leadsBillingIndicatorsDto.getStatus());
            if (LeadsStatusEnum.PRE_FOLLOW.equals(parse)) {
                LeadsPreFollowStatusEnum leadsPreFollowStatusEnum = LeadsPreFollowStatusEnum.parse(leadsBillingIndicatorsDto.getPreFollowStatus());
                leadsBillingIndicatorsDto.setStatusText(leadsPreFollowStatusEnum == null ? "" : leadsPreFollowStatusEnum.getName());
            } else {
                if (Objects.nonNull(leadsBillingIndicatorsDto.getClientId()) && leadsBillingIndicatorsDto.getClientId() > 0){
                    parse = LeadsStatusEnum.parse(leadsBillingIndicatorsDto.getClientStatus());
                }
                leadsBillingIndicatorsDto.setStatusText(parse == null ? "" : parse.getName());
            }

            String nameByChannel = LeadsChannelUtil.getName(bid, leadsBillingIndicatorsDto.getChannel());
            //String nameByChannel = LeadsChannelEnum.getNameByChannel(leadsBillingIndicatorsDto.getChannel());
            leadsBillingIndicatorsDto.setChannelText(nameByChannel);

            //设置渠道来源
            leadsBillingIndicatorsDto.setChannelSourceText(Objects.isNull(LeadsInputSourceEnum.parse(leadsBillingIndicatorsDto.getChannelSource()))
                    ? InngkeAppConst.EMPTY_STR : LeadsInputSourceEnum.parse(leadsBillingIndicatorsDto.getChannelSource()).getName());

            staffIds.add(leadsBillingIndicatorsDto.getPreFollowStaffId());
            staffIds.add(leadsBillingIndicatorsDto.getDistributeStaffId());
            staffIds.add(leadsBillingIndicatorsDto.getCreateStaffId());
            leadsBillingIndicatorsDto.setFullPayOrderCount(leadsBillingIndicatorsDto.getFullPayOrderCount() + leadsBillingIndicatorsDto.getDepositOrderCount());

            list.add(leadsBillingIndicatorsDto);
        }

        setStaffName(bid, list, staffIds);

        //线索跟进状态，客服跟进状态，订单留言
        setExtData(bid, list);

        list.sort(Comparator.comparingLong(LeadsBillingIndicatorsDto::getUpdateTime));

        Collections.reverse(list);

        result.setList(list);
        result.setTotal((int) hits.getTotalHits().value);

        return result;
    }

    private void setExtData(Integer bid, List<LeadsBillingIndicatorsDto> list) {
        List<Long> leadIds = list.stream().map(LeadsCustomerIndicatorsDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leadIds)) {
            return;
        }
        Map<Long, Leads> leadsMap = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, leadIds)
                        .select(Leads.ID, Leads.KF_FOLLOW_STATUSES, Leads.FOLLOW_STATUSES, Leads.ORDER_MESSAGE,Leads.REMARK)
        ).stream().collect(Collectors.toMap(Leads::getId, Function.identity()));
        list.forEach(
                item -> {
                    Leads leads = leadsMap.get(item.getId());
                    if (Objects.isNull(leads)) {
                        return;
                    }
                    if (!StringUtils.isEmpty(leads.getFollowStatuses())) {
                        item.setFollowStatuses(jsonService.toObjectList(leads.getFollowStatuses(), LeadsFollowStatusDto.class));
                    }
                    if (!StringUtils.isEmpty(leads.getKfFollowStatuses())) {
                        item.setKfFollowStatuses(jsonService.toObjectList(leads.getKfFollowStatuses(), LeadsFollowStatusDto.class));
                    }
                    if (!StringUtils.isEmpty(leads.getOrderMessage())) {
                        item.setOrderMessage(leads.getOrderMessage());
                    }
                    if (!StringUtils.isEmpty(leads.getRemark())){
                        item.setRemark(leads.getRemark());
                    }
                }
        );
    }

    private void setStaffName(Integer bid, List<LeadsBillingIndicatorsDto> list, Set<Long> staffIds) {
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffIds);
        list.forEach(item -> {
            StaffDto staffDto = staffByIds.getOrDefault(item.getPreFollowStaffId(), new StaffDto());
            item.setPreFollowName(staffDto.getName());
            staffDto = staffByIds.getOrDefault(item.getDistributeStaffId(), new StaffDto());
            item.setDistributeStaffName(staffDto.getName());
            staffDto = staffByIds.getOrDefault(item.getCreateStaffId(), new StaffDto());
            item.setCreateStaffName(staffDto.getName());
        });
    }


}

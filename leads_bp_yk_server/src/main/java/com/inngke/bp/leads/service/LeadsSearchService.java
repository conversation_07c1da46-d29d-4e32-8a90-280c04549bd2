package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/23 17:38
 */
public interface LeadsSearchService {


    BasePaginationResponse<LeadsBillingIndicatorsDto> getLeadsByEs(Integer bid,BoolQueryBuilder queryBuilder, int from , int size) throws IOException;
}

package com.inngke.bp.leads.api.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 16:05
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@Data
public class FlyFishLeadsDto implements Serializable {

    @JsonProperty("allocation_status")
    private Integer allocationStatus;

    @JsonProperty("advertiser_name")
    private String advertiserName;

    @JsonProperty("app_name")
    private String appName;

    @JsonProperty("site_id")
    private String siteId;

    @JsonProperty("site_name")
    private String siteName;

    @JsonProperty("creative_id")
    private String creativeId;

    @JsonProperty("intention_estimation")
    private String intentionEstimation;

    @JsonProperty("telephone")
    private String telephone;

    @JsonProperty("create_time_detail")
    private String createTimeDetail;

    @JsonProperty("date")
    private String date;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("city_name")
    private String cityName;

    @JsonProperty("convert_status")
    private String convertStatus;

    @JsonProperty("module_id")
    private String moduleId;

    @JsonProperty("clue_id")
    private String clueId;

    @JsonProperty("form_remark")
    private String formRemark;

    @JsonProperty("clue_owner_name")
    private String clueOwnerName;

    @JsonProperty("location")
    private String location;

    @JsonProperty("country_name")
    private String countryName;

    @JsonProperty("email")
    private String email;

    @JsonProperty("store")
    private FlyFishLeadsStoreDto store;

    @JsonProperty("clue_state")
    private Integer clueState;

    @JsonProperty("ad_id")
    private String adId;

    @JsonProperty("clue_source")
    private Integer clueSource;

    @JsonProperty("weixin")
    private String weixin;

    @JsonProperty("system_tags")
    private List<String> systemTags;

    @JsonProperty("follow_state_name")
    private String followStateName;

    @JsonProperty("advertiser_id")
    private String advertiserId;

    @JsonProperty("address")
    private String address;

    @JsonProperty("ad_name")
    private String adName;

    @JsonProperty("qq")
    private String qq;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("name")
    private String name;

    @JsonProperty("gender")
    private Integer gender;

    @JsonProperty("age")
    private Integer age;

    @JsonProperty("clue_state_name")
    private String clueStateName;

    @JsonProperty("req_id")
    private String reqId;

    @JsonProperty("clue_type")
    private Integer clueType;

    @JsonProperty("module_name")
    private String moduleName;

    @JsonProperty("province_name")
    private String provinceName;

    @JsonProperty("external_url")
    private String externalUrl;

    private Long createStaffId;

}

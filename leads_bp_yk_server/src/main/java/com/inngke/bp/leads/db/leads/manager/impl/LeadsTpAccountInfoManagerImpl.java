/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.dao.LeadsTpAccountInfoDao;
import com.inngke.bp.leads.db.leads.manager.LeadsTpAccountInfoManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告平台授权用户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Service
public class LeadsTpAccountInfoManagerImpl extends ServiceImpl<LeadsTpAccountInfoDao, LeadsTpAccountInfo> implements LeadsTpAccountInfoManager {

}

package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsTagAddRequest;
import com.inngke.bp.leads.dto.request.LeadsTagsDetailRequest;
import com.inngke.bp.leads.dto.response.LeadsTagsDetailDto;
import com.inngke.bp.user.dto.request.CustomerTagsTypeJudgeRequest;
import com.inngke.bp.user.dto.response.CustomerTagsDetailTagsDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/9
 **/
public interface LeadsTagsService {
    /**
     * 线索标签添加
     *
     * @param request 请求实体
     * @return 返回实体
     */
    BaseResponse<Boolean> tagsAdd(LeadsTagAddRequest request);

    /**
     * 查询线索标签详情
     *
     * @param request 请求实体
     * @return 返回实体
     */
    BaseResponse<LeadsTagsDetailDto> tagsDetail(LeadsTagsDetailRequest request);


    /**
     * 判断线索的类型是否是企业标签
     *
     * @param request 请求实体
     * @return 返回实体
     */
    List<CustomerTagsDetailTagsDto> customerTagsDetailTagsDto(CustomerTagsTypeJudgeRequest request);
}

package com.inngke.bp.leads.mq.process.organize;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConf;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfManager;
import com.inngke.bp.leads.dto.request.RegionConfClearRequest;
import com.inngke.bp.leads.dto.request.RegionConfSaveRequest;
import com.inngke.bp.leads.service.LeadsDistributeConfService;
import com.inngke.common.core.InngkeAppConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

public abstract class StaffChangeAbstract {


    @Autowired
    private LeadsDistributeConfService leadsDistributeConfService;

    @Autowired
    private LeadsDistributeConfManager leadsDistributeConfManager;

    /**
     * 删除员工已有配置
     *
     * @param bid     bid
     * @param staffId 员工ID
     */
    protected void deleteDistributeConf(int bid, Long staffId) {
        //获取此员工的配置
        List<LeadsDistributeConf> distributeConfList =
                getDistributeConfList(bid, staffId);

        //删除配置
        for (LeadsDistributeConf leadsDistributeConf : distributeConfList) {
            TreeSet<Long> newStaffIds = Arrays.stream(
                            leadsDistributeConf.getStaffIds().split(InngkeAppConst.COMMA_STR)).map(Long::valueOf)
                    .filter(distributeConfStaffId -> !staffId.equals(distributeConfStaffId)).distinct()
                    .collect(Collectors.toCollection(Sets::newTreeSet));
            if (CollectionUtils.isEmpty(newStaffIds)){
                RegionConfClearRequest clearRequest = new RegionConfClearRequest();
                clearRequest.setRegionId(leadsDistributeConf.getRegionId());
                clearRequest.setChannelId(leadsDistributeConf.getChannelId());
                clearRequest.setOperatorId(0L);
                clearRequest.setBid(bid);
                leadsDistributeConfService.clear(clearRequest);
            }else {
                RegionConfSaveRequest request = new RegionConfSaveRequest();
                request.setRegionIds(Sets.newHashSet(leadsDistributeConf.getRegionId()));
                request.setStaffIds(newStaffIds);
                request.setChannelId(leadsDistributeConf.getChannelId());
                request.setOperatorId(0L);
                request.setBid(bid);
                leadsDistributeConfService.save(request);
            }
        }

    }

    /**
     * 获取
     *
     * @param bid     bid
     * @param staffId 员工ID
     * @return List<LeadsDistributeConf>
     */
    private List<LeadsDistributeConf> getDistributeConfList(int bid, Long staffId) {
        List<LeadsDistributeConf> distributeConfList = leadsDistributeConfManager.list(Wrappers.<LeadsDistributeConf>query()
                .eq(LeadsDistributeConf.BID, bid)
                .like(LeadsDistributeConf.STAFF_IDS, staffId)
        );

        return distributeConfList.stream().filter((leadsDistributeConf -> {
            String staffIds = leadsDistributeConf.getStaffIds();
            if (StringUtils.isEmpty(staffIds)) {
                return false;
            }
            for (String distributeStaffId : staffIds.split(InngkeAppConst.COMMA_STR)) {
                if (Long.valueOf(distributeStaffId).equals(staffId)) {
                    return true;
                }
            }
            return false;
        })).collect(Collectors.toList());
    }
}

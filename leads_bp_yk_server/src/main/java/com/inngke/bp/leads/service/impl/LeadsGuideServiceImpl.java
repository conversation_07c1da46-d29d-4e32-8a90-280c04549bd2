package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.GuideCreateLeadsRequest;
import com.inngke.bp.leads.dto.request.LeadsInformationAddRequest;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.response.LeadsInformationDto;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.service.LeadsCheckService;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsGuideService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * LeadsGuideServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/15 14:42
 */
@DubboService(version = "1.0.0", timeout = 3000)
@Slf4j
public class LeadsGuideServiceImpl implements LeadsGuideService {

    @Resource
    private LeadsCheckService leadsCheckService;

    @Resource
    private LeadsServiceV2 leadsServiceV2;

    @Resource
    private JsonService jsonService;

    @Resource
    private LeadsManager leadsManager;

    @Resource
    private LeadsEsService leadsEsService;

    @Override
    public BaseResponse<Long> guideCreateLeads(@Validated GuideCreateLeadsRequest request) {
        Long leadsId = 0L;
        List<Leads> reportLeads = request.getCrateMode() == 1
                ? leadsCheckService.hasAnyMobileOrWechatLeadsOfDistributorStaff(request.getBid(), request.getOperatorStaffId(), request.getMobile(), request.getWeChat())
                : leadsCheckService.hasAnyMobileOrWechatLeadsOfReportStaff(request.getBid(), request.getOperatorStaffId(), request.getMobile(), request.getWeChat());
        if (!CollectionUtils.isEmpty(reportLeads)) {
            log.info("mode={} but find repeat leads, mobile={}, wechat={}", request.getCrateMode(), request.getMobile(), request.getWeChat());
            Long firstId = reportLeads.stream().filter(item -> item.getDistributeStaffId().equals(request.getOperatorStaffId())).findFirst().map(Leads::getId).orElse(null);
            return BaseResponse.error("线索已存在系统，请勿重复录入", firstId);
        }

        leadsCheckService.checkRepeatLeads(request.getBid(), request.getMobile(), request.getWeChat(), null);
        if (request.getCrateMode() == 1) {
            Leads leads = LeadsConverter.toLeads(request);
            if (Objects.isNull(leads)) {
                return BaseResponse.error("线索信息为空");
            }
            leads.setChannelType(LeadsDataSourceEnum.GUIDE_CREATE.getCode());
            leadsId = leadsManager.addLeads(leads, request.getOperatorId());
        } else if (request.getCrateMode() == 2) {
            LeadsInformationAddRequest leadsInformationAddRequest = LeadsConverter.toLeadsInformationAddRequest(request);
            if (Objects.isNull(leadsInformationAddRequest)) {
                return BaseResponse.error("线索信息为空");
            }
            leadsInformationAddRequest.setChannelType(LeadsDataSourceEnum.GUIDE_REPORT.getCode());
            BaseResponse<LeadsInformationDto> createLeadsResponse = leadsServiceV2.add(leadsInformationAddRequest);
            if (!BaseResponse.responseSuccessWithNonNullData(createLeadsResponse)) {
                log.info("导购上报总部线索失败：{}", jsonService.toJson(createLeadsResponse));
                return BaseResponse.error(createLeadsResponse.getMsg());
            }
            leadsId = createLeadsResponse.getData().getId();
        }

        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(Lists.newArrayList(leadsId));
        leadsEsService.updateDocs(leadsUpdateRequest);

        return BaseResponse.success(leadsId);
    }
}

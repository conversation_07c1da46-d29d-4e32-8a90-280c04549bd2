/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsBatch;
import com.inngke.bp.leads.db.leads.entity.LeadsDraft;
import com.inngke.bp.leads.db.leads.dao.LeadsDraftDao;
import com.inngke.bp.leads.db.leads.manager.LeadsBatchManager;
import com.inngke.bp.leads.db.leads.manager.LeadsDraftManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.bp.leads.enums.LeadsBatchFileTypeEnum;
import com.inngke.bp.leads.enums.LeadsBatchStatusEnum;
import com.inngke.common.ds.annotation.DS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsDraftManagerImpl extends ServiceImpl<LeadsDraftDao, LeadsDraft> implements LeadsDraftManager {
    private static final Logger logger = LoggerFactory.getLogger(LeadsDraftManagerImpl.class);


    @Autowired
    private LeadsBatchManager leadsBatchManager;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importLeadsDraft(Long batchId, List<LeadsDraft> drafts, int errorCount, String errorFileUrl) {
        //导入草稿表
        batchSaveLeadsDraft(drafts);

        //修改批次状态
        LeadsBatch leadsBatch = leadsBatchManager.getById(batchId);
        leadsBatch.setFileType(LeadsBatchFileTypeEnum.EXCEL.getStatus());
        leadsBatch.setProcessStatus(LeadsBatchStatusEnum.PROCESSED.getStatus());
        leadsBatch.setSuccessCount(drafts.size());
        leadsBatch.setErrorCount(errorCount);
        leadsBatch.setErrorFileUrl(errorFileUrl);
        leadsBatchManager.updateById(leadsBatch);
    }

    @Override
    public void batchSaveLeadsDraft(List<LeadsDraft> list) {
        this.saveBatch(list);
    }




}

package com.inngke.bp.leads.service.template.leadsAchievement;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.client.StaffManageDepartmentClientForLeads;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.dto.response.LeadsEventEsDto;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * LeadsAchievementReportByDepartment
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/13 18:31
 */
@Component
@Slf4j
public class LeadsAchievementReportByDepartment extends AbstractLeadsAchievement {


    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private RestHighLevelClient esClient;

    @Autowired
    private StaffManageDepartmentClientForLeads staffManageDepartmentClientForLeads;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private JsonService jsonService;

    @Override
    public String getDimensionCode() {
        return "department";
    }

    @Override
    protected String getGroupByFiled(LeadsAchievementReportContext context) {
        context.setGroupByFiled("followDepartmentId");
        return "followDepartmentId";
    }

    @Override
    protected void afterQueryBuildPlugin(LeadsAchievementReportContext context) {
        BoolQueryBuilder qb = context.getBoolQueryBuilder();
        qb.mustNot(QueryBuilders.termQuery("followDepartmentId", 0L));
        Set<Long> manageDepartment = getManageDepartment(context.getBid(), context.getSelectDeptId(), context.getCurrentStaffId());
        DepartmentDto department = getDepartment(context.getBid(), context.getSelectDeptId());
        if (Objects.isNull(department.getChildren())) {
            department.setChildren(Lists.newArrayList());
        }
        Set<Long> childrenIds = department.getChildren().stream().map(DepartmentDto::getId).collect(Collectors.toSet());
        childrenIds.add(department.getId());
                qb.must(QueryBuilders.termsQuery("followDepartmentId", CollUtil.intersection(childrenIds, manageDepartment)));
//        BoolQueryBuilder should = QueryBuilders.boolQuery()
//                .should(QueryBuilders.termsQuery("followDepartmentIds", CollUtil.intersection(childrenIds, manageDepartment)))
//                .should(QueryBuilders.termsQuery("followDepartmentId", CollUtil.intersection(childrenIds, manageDepartment)));
//        qb.must(should);
//        SearchSourceBuilder queryDataSource = context.getQueryDataSource();
//        queryDataSource.size(10000);
    }

    @Override
    protected void afterParseResponsePlugin(LeadsAchievementReportContext context, Terms.Bucket bucket, LeadsAchievementReportResponse dto) {
        dto.setDeptId(Long.valueOf(bucket.getKeyAsString()));
    }

    @Override
    protected void afterBuildResponsePlugin(LeadsAchievementReportContext context) {
        List<LeadsAchievementReportResponse> reportResult = context.getReportResult();
        DepartmentDto department = getDepartment(context.getBid(), context.getSelectDeptId());
        Set<Long> childrenIds = Optional.ofNullable(department).map(DepartmentDto::getChildren).orElse(Lists.newArrayList())
                .stream().map(DepartmentDto::getId).collect(Collectors.toSet());
        childrenIds.add(department.getId());

        SearchSourceBuilder queryBuilder = new SearchSourceBuilder();
        queryBuilder.size(1000000).trackTotalHits(true);
        BoolQueryBuilder qb = QueryBuilders.boolQuery();
        qb.must(QueryBuilders.termQuery("bid", context.getBid()));
        qb.must(QueryBuilders.rangeQuery("createTime").gte(DateTimeUtils.dateTimeStrToMilli(context.getStartEventTime())));
        qb.must(QueryBuilders.rangeQuery("createTime").lte(DateTimeUtils.dateTimeStrToMilli(context.getEndEventTime())));
        if (StringUtils.isNotEmpty(context.getStartCreateTime()) && StringUtils.isNotEmpty(context.getEndCreateTime())) {
            qb.must(QueryBuilders.rangeQuery("leadsCreateTime").gte(DateTimeUtils.dateTimeStrToMilli(context.getStartCreateTime())));
            qb.must(QueryBuilders.rangeQuery("leadsCreateTime").lte(DateTimeUtils.dateTimeStrToMilli(context.getEndCreateTime())));
        }
        if (StringUtils.isNotEmpty(context.getStartDistributeTime()) && StringUtils.isNotEmpty(context.getEndDistributeTime())) {
            qb.must(QueryBuilders.rangeQuery("leadsDistributeTime").gte(DateTimeUtils.dateTimeStrToMilli(context.getStartDistributeTime())));
            qb.must(QueryBuilders.rangeQuery("leadsDistributeTime").lte(DateTimeUtils.dateTimeStrToMilli(context.getEndDistributeTime())));
        }
        Set<Long> manageDepartment = getManageDepartment(context.getBid(), context.getSelectDeptId(), context.getCurrentStaffId());
        qb.must(QueryBuilders.termsQuery("followDepartmentId", manageDepartment));
        qb.must(QueryBuilders.termsQuery("followDepartmentIds", childrenIds));

        queryBuilder.query(qb);
        SearchRequest searchRequest = new SearchRequest()
                .indices("leads-event-log")
                .source(queryBuilder);
        SearchResponse response;

        try {
            response = esClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            List<LeadsEventEsDto> esDocList = Arrays.stream(hits).map(item -> jsonService.toObject(item.getSourceAsString(), LeadsEventEsDto.class))
                    .collect(Collectors.toList());
            // reportResult
            Map<Long, LeadsAchievementReportResponse> idMap = reportResult.stream().collect(Collectors.toMap(LeadsAchievementReportResponse::getDeptId, Function.identity()));
            List<LeadsAchievementReportResponse> finalReportResult = reportResult;
            childrenIds.forEach(deptId -> {
                LeadsAchievementReportResponse reportDto = idMap.get(deptId);
                if (Objects.isNull(reportDto)) {
                    reportDto = new LeadsAchievementReportResponse();
                    reportDto.setDeptId(deptId);
                    finalReportResult.add(reportDto);
                }
                LeadsAchievementReportResponse finalReportDto = reportDto;
                Map<Integer, List<LeadsEventEsDto>> eventMap = esDocList
                        .stream()
                        .filter(item -> item.getFollowDepartmentIds().contains(finalReportDto.getDeptId()))
                        .filter(item -> item.getEventId() != null)
                        .collect(Collectors.groupingBy(LeadsEventEsDto::getEventId));
//                reportDto.setTotal(Optional.ofNullable(eventMap.get(8)).map(List::size).orElse(0));

                reportDto.setTotal(Optional.ofNullable(eventMap.get(8)).orElse(Lists.newArrayList()).stream().map(LeadsEventEsDto::getLeadsId).collect(Collectors.toSet()).size());
                reportDto.setMeasureNum(Optional.ofNullable(eventMap.get(12)).orElse(Lists.newArrayList()).stream().map(LeadsEventEsDto::getLeadsId).collect(Collectors.toSet()).size());
                reportDto.setIntoStoreNum(Optional.ofNullable(eventMap.get(13)).orElse(Lists.newArrayList()).stream().map(LeadsEventEsDto::getLeadsId).collect(Collectors.toSet()).size());
                reportDto.setDepositNum(Optional.ofNullable(eventMap.get(15)).orElse(Lists.newArrayList()).stream().map(LeadsEventEsDto::getLeadsId).collect(Collectors.toSet()).size());
                reportDto.setTradingNum(Optional.ofNullable(eventMap.get(18)).orElse(Lists.newArrayList()).stream().map(LeadsEventEsDto::getLeadsId).collect(Collectors.toSet()).size());
                reportDto.setLossNum(Optional.ofNullable(eventMap.get(1)).orElse(Lists.newArrayList()).stream().map(LeadsEventEsDto::getLeadsId).collect(Collectors.toSet()).size());

                List<LeadsEventEsDto> docList = Optional.ofNullable(eventMap.get(20)).orElse(Lists.newArrayList());
                BigDecimal deposit = docList.stream().filter(item -> item.getDeposit() != null).map(LeadsEventEsDto::getDeposit).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal payAmount = docList.stream().filter(item -> item.getPayAmount() != null).map(LeadsEventEsDto::getPayAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                reportDto.setDepositAmount(deposit);
                reportDto.setTradingAmount(payAmount);
            });
            reportResult = reportResult.stream().filter(item -> item.getTotal() > 0 || item.getMeasureNum() > 0 || item.getDepositNum() > 0 || item.getTradingNum() > 0)
                    .sorted(Comparator.comparing(LeadsAchievementReportResponse::getTotal).reversed().thenComparing(LeadsAchievementReportResponse::getDeptId))
                    .collect(Collectors.toList());
            context.setReportResult(reportResult);


        } catch (IOException e) {
            log.error("查询业绩数据报表部门报表异常：", e);
        }
    }

    @Override
    protected void afterGetReportDataPlugin(LeadsAchievementReportContext context) {
        List<LeadsAchievementReportResponse> reportResult = context.getReportResult();
        if (CollectionUtils.isNotEmpty(reportResult)) {
            Set<Long> deptIds = reportResult.stream().map(LeadsAchievementReportResponse::getDeptId).collect(Collectors.toSet());
            Map<Long, DepartmentDto> departmentDtoMap = departmentClientForLeads.getDepartmentByIds(context.getBid(), Lists.newArrayList(deptIds))
                    .stream()
                    .collect(Collectors.toMap(DepartmentDto::getId, Function.identity(), (key1, key2) -> key2));
            reportResult.forEach(item -> {
                DepartmentDto departmentDto = departmentDtoMap.get(item.getDeptId());
                item.setDeptName(Optional.ofNullable(departmentDto).map(DepartmentDto::getName).orElse("-"));
            });
        }
    }

    private DepartmentDto getDepartment(Integer bid, Long deptId) {
        GetDepartmentRequest request = new GetDepartmentRequest();
        request.setBid(bid);
        request.setDepartmentId(deptId);
        request.setChildrenLevel(1);
        return departmentClientForLeads.getDepartment(request);
    }

    private Set<Long> getManageDepartment(Integer bid, Long deptId, Long staffId) {
        // 获取当前用户的管理部门
        StaffDto staffDto = staffClientForLeads.getStaffById(bid, staffId);
        if (Objects.isNull(staffDto)) {
            log.info("获取员工失败，staffId:{}", staffId);
            return Sets.newHashSet();
        }

        Set<Long> managerDepartmentIds = staffManageDepartmentClientForLeads.getStaffManageDepartment(bid, staffDto.getId());

        // 获取传入部门的子部门
        if (Objects.nonNull(deptId) && !deptId.equals(0L)) {
            // 获取子部门
            Set<Long> childrenDepartment = departmentClientForLeads.getChildrenDepartment(bid, deptId);
            childrenDepartment.add(deptId);

            managerDepartmentIds = Sets.newHashSet(CollUtil.intersection(childrenDepartment, managerDepartmentIds));
        }
        return managerDepartmentIds;
    }
}

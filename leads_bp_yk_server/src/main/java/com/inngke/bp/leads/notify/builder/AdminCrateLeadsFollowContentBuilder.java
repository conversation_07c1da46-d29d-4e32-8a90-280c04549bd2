package com.inngke.bp.leads.notify.builder;

import com.inngke.bp.leads.notify.context.AdminCreateLeadsFollowMessageContext;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * AdminCrateLeadsFollowContentBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/21 17:55
 */
@Component(value = "admin_add_leads_follow_notify")
@Slf4j
public class AdminCrateLeadsFollowContentBuilder extends TemplateMessageContentBuilder<AdminCreateLeadsFollowMessageContext> {
    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(AdminCreateLeadsFollowMessageContext ctx) {
        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);

        return builder
                .setTitle("客户跟进提醒")
                .setVar("name", ctx.getName())
                .setVar("mobile", StringUtils.isBlank(ctx.getMobile()) ? "-" : ctx.getMobile())
                .setVar("followType", "跟进提醒")
                .setVar("followContent", ctx.getContent())
                .setVar("content", ctx.getContent())
                .setVar("remark", "点击前往小程序立即查看");
    }
}

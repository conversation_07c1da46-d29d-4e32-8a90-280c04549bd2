/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.dto.request.LeadsAchievementReportRequest;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.service.leadsInvalidReasonInitService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@RestController
@RequestMapping("/leadsInvalidReason")
public class LeadsInvalidReasonController {

    @Autowired
    private leadsInvalidReasonInitService leadsInvalidReasonInitService;

    /**
     * 系统默认的初始化无效原因：
     * ● 客户明确表示无需求
     * ● 客户地址超出门店服务范围
     * ● 多次联系不上/空号
     * ● 添加微信好友不成功
     * ● 其他
     *
     * @return
     */
    @PostMapping("/init")
    public BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> init(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload
    ) {

        return leadsInvalidReasonInitService.init();
    }
}


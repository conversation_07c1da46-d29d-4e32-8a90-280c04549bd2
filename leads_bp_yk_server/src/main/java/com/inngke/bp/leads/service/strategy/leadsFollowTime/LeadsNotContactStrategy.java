package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.service.JsonService;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by emiya on 2022/6/13 14:41
 *
 * <AUTHOR>
 * @date 2022/6/13 14:41
 */
@Component(value = "leadsNotContactCountStrategy")
public class LeadsNotContactStrategy implements LeadsFollowTimeStrategy{

    @Autowired
    private JsonService jsonService;

    /**
     * 统计待联系线索数
     * @param	bucketAggregations
     * @param	dto
     */
    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        Aggregation statusCount = bucketAggregations.get("statusCount");
        List<? extends Terms.Bucket> buckets = ((ParsedLongTerms) statusCount).getBuckets();
        for (Terms.Bucket bucket : buckets) {
            bucket.getKey();
            if (bucket.getKey().toString().equals("1")) {
                dto.setNotContactCount((int) bucket.getDocCount());
            }
        }
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getNotContactCount).reduce(Integer::sum).get();
        dto.setNotContactCount(sum);
    }
}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.client.dto.response.common.ClientLevelItemDto;
import com.inngke.bp.leads.client.ClientLevelClientForLeads;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.client.StaffManageDepartmentClientForLeads;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.core.utils.StaffToAgentUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsConf;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsConfManager;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsQuery;
import com.inngke.bp.leads.dto.request.LeadsStatusRequest;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsDocService;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.LeadsStatisticsService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.bp.user.dto.request.staff.StaffGetRequest;
import com.inngke.bp.user.service.UserStaffService;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.request.RegionGetRequest;
import com.inngke.ip.common.dto.response.RegionDto;
import com.inngke.ip.common.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.ParsedSingleBucketAggregation;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.termQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

/**
 * LeadsDocServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/11 19:24
 */
@DubboService(version = "1.0.0", timeout = 3000)
@Slf4j
public class LeadsDocServiceImpl implements LeadsDocService {

    public static final Map<Integer, List<Integer>> CHILD_STATUS_MAP = Maps.newHashMap();

    static {
        CHILD_STATUS_MAP.put(121, Lists.newArrayList(LeadsStatusEnum.UNSURE_INTENT.getStatus()));
        CHILD_STATUS_MAP.put(122, Lists.newArrayList(LeadsStatusEnum.ORDERED.getStatus()));
        CHILD_STATUS_MAP.put(123, Lists.newArrayList(LeadsStatusEnum.MEASURED.getStatus()));
        CHILD_STATUS_MAP.put(124, Lists.newArrayList(LeadsStatusEnum.TRADED.getStatus()));
        CHILD_STATUS_MAP.put(125, Lists.newArrayList(LeadsStatusEnum.TO_INSTALL.getStatus()));
        CHILD_STATUS_MAP.put(126, Lists.newArrayList(LeadsStatusEnum.CONTACTED.getStatus(), LeadsStatusEnum.SUCCESS_CONTACT.getStatus(), LeadsStatusEnum.UNSURE_INTENT.getStatus()));
        CHILD_STATUS_MAP.put(127, Lists.newArrayList(LeadsStatusEnum.INTENT.getStatus()));
        CHILD_STATUS_MAP.put(128, Lists.newArrayList(LeadsStatusEnum.STORED.getStatus()));
        CHILD_STATUS_MAP.put(153, Lists.newArrayList(LeadsStatusEnum.INSTALLED.getStatus()));
    }

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private RegionService regionService;

    @Resource
    private DepartmentClientForLeads departmentClientForLeads;

    @Resource
    private StaffClientForLeads staffClientForLeads;

    @Resource
    private StaffManageDepartmentClientForLeads staffManageDepartmentClientForLeads;

    @Resource
    private LeadsStatisticsService leadsStatisticsService;

    @Resource
    private LeadsManager leadsManager;

    @Resource
    private LeadsFollowManager leadsFollowManager;

    @Resource
    private ClientLevelClientForLeads clientLevelClientForLeads;

    @Resource
    private LeadsServiceV2 leadsServiceV2;

    @Resource
    private StaffToAgentUtil staffToAgentUtil;

    @Resource
    private LeadsService leadsService;

    @Resource
    private UserStaffService userStaffService;

    @Resource
    private LeadsConfManager leadsConfManager;

    @Override
    public BaseResponse<LeadsListVo> searchLeads(LeadsQuery request) {
        LeadsListVo vo = new LeadsListVo();
        BaseResponse<LeadsListVo> response = BaseResponse.success(vo);
        Integer bid = request.getBid();
        Boolean pcOperate = Optional.ofNullable(request.getPcOperate()).orElse(true);

        SearchRequest searchRequest = new SearchRequest().indices("leads");

        BoolQueryBuilder queryBuilder = buildQueryBuilder(request);

        SearchSourceBuilder sourceBuilder = parseSortField(request.getSortType());
        sourceBuilder.query(queryBuilder);

        int skip = (request.getPageNo() - 1) * request.getPageSize() - Optional.ofNullable(request.getSkipSize()).orElse(0);
        skip = Math.max(skip, 0);
        sourceBuilder.from(skip);
        sourceBuilder.size(request.getPageSize());
        searchRequest.source(sourceBuilder);

        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }

        vo.setTotal(Long.valueOf(searchResponse.getHits().getTotalHits().value).intValue());

        List<LeadsGroupDto> leadsGroupList = installIsGroupInfoForList(request);
        if (CollectionUtils.isNotEmpty(leadsGroupList)) {
            vo.setGroups(leadsGroupList);
        }

        List<LeadsEsDto> leadsEsList = parseHits(searchResponse);
        List<Long> leadsIds = leadsEsList.stream().map(LeadsEsDto::getLeadsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leadsIds)) {
            vo.setList(Lists.newArrayList());
            return response;
        }

        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .in(Leads.ID, leadsIds)
        );
        Map<Long, Leads> leadsIdMap = leadsList.stream().collect(Collectors.toMap(Leads::getId, Function.identity()));

        // 这里做排序，使数据库查出来的数据顺序与ES一致
        leadsList = leadsEsList.stream()
                .map(item1 -> leadsIdMap.get(item1.getLeadsId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 跟进数量
        List<LeadsFollow> leadsFollowList = Lists.newArrayList();
        if (!org.springframework.util.CollectionUtils.isEmpty(leadsIds)) {
            leadsFollowList = leadsFollowManager.list(
                    Wrappers.<LeadsFollow>query()
                            .eq(LeadsFollow.BID, request.getBid())
                            .in(LeadsFollow.LEADS_ID, leadsIds)
                            .select(LeadsFollow.ID, LeadsFollow.LEADS_ID)
            );
        }

        Map<Long, List<LeadsFollow>> followCountMap = leadsFollowList.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));

        Map<Integer, ClientLevelItemDto> levelMap = clientLevelClientForLeads.getClientLevelList(bid).stream().collect(Collectors.toMap(ClientLevelItemDto::getId, v -> v));

        ArrayList<LeadsListItemDto> leadsListItemDtos = Lists.newArrayList();
        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(leadsList, bid);

        leadsList.forEach(item -> {
            LeadsVo leadsVo = LeadsConverter.toLeadsListItem(item, staffAndAgent);
            if (leadsVo == null) {
                return;
            }
            ClientLevelItemDto clientLevelItemDto = levelMap.get(item.getLevelId());
            if (clientLevelItemDto != null) {
                leadsVo.setLevelDes(clientLevelItemDto.getExplainInfo());
                leadsVo.setLevelText(clientLevelItemDto.getTitle());
            }
            leadsListItemDtos.add(leadsVo);
        });
        for (LeadsListItemDto item : leadsListItemDtos) {
            Integer type = leadsServiceV2.getLeadsDetailTypeHandle(item.getType(), item.getChannel());
            item.setType(type);
            List<LeadsFollow> follows = followCountMap.get(item.getId());
            if (!org.springframework.util.CollectionUtils.isEmpty(follows)) {
                item.setFollowCount(follows.size());
            } else {
                item.setFollowCount(0);
            }
        }
        vo.setList(leadsListItemDtos);
        //组装跟进数据
        installFollowInfo(request, leadsListItemDtos, pcOperate);

        installStaffNameAndAgentName(leadsListItemDtos, request.getBid(), request.getStatus());

        //已分配状态添加部门信息
        if (Objects.nonNull(request.getStatusGroup()) && request.getStatusGroup().equals(5)) {
            installStaffDepartmentInfo(request.getBid(), leadsListItemDtos);
        }
        //组装preFollowStaffName
        buildPreFollowStaffName(request.getBid(), leadsListItemDtos);
        return response;
    }

    @Override
    public BaseResponse<LeadsStatusDto> dimensionStatisticsCount(LeadsStatusRequest request) {
        // query
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(termQuery(LeadsEsDto.BID, request.getBid()))
                .mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, Lists.newArrayList(LeadsStatusEnum.RECOVERY.getStatus(),
                        LeadsStatusEnum.DELETED.getStatus(),
                        LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                        LeadsStatusEnum.PUSH_BACK.getStatus(),
                        LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                        LeadsStatusEnum.PRE_FOLLOW.getStatus())))
                ;
        if (Objects.nonNull(request.getStartDistributeTime()) && Objects.nonNull(request.getEndDistributeTime())) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.DISTRIBUTE_TIME).gte(DateTimeUtils.getMilli(request.getStartDistributeTime())).lte(DateTimeUtils.getMilli(request.getEndDistributeTime())));
        }

        if (Objects.nonNull(request.getStartLastFollowTime()) && Objects.nonNull(request.getEndLastFollowTime())) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.LAST_FOLLOW_TIME).gte(DateTimeUtils.getMilli(request.getStartLastFollowTime())).lte(DateTimeUtils.getMilli(request.getEndLastFollowTime())));
        }

        if (Objects.isNull(request.getDepartmentId()) && Objects.nonNull(request.getStaffId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, request.getStaffId()));
        }
        if (Objects.nonNull(request.getDepartmentId())) {
            Set<Long> staffManagerTopDepartmentIds = staffManageDepartmentClientForLeads.getStaffManagerTopDepartmentIds(request.getBid(), request.getStaffId());
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, staffManagerTopDepartmentIds)).must(QueryBuilders.termQuery(LeadsEsDto.DEPT_IDS, request.getDepartmentId()));
        }
        // aggs
        TermsAggregationBuilder statusGroup = AggregationBuilders.terms("status_group").field(LeadsEsDto.STATUS).size(100);
        FilterAggregationBuilder placeOrder = AggregationBuilders.filter("place_order", termQuery(LeadsEsDto.HAS_STORE_ORDER, 1))
                .subAggregation(AggregationBuilders.filter("sign_bill", termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.TRADED.getStatus())));

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(queryBuilder);
        sourceBuilder.aggregation(statusGroup);
        sourceBuilder.aggregation(placeOrder);
        sourceBuilder.size(0);
        SearchRequest searchRequest = new SearchRequest().indices("leads");
        searchRequest.source(sourceBuilder);

        SearchResponse searchResponse;
        try {
            log.info("dimensionStatisticsCount -> {}", sourceBuilder.toString());
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }

        LeadsStatusDto leadsStatusDto = parseDimensionStatisticsCount(searchResponse);

        return BaseResponse.success(leadsStatusDto);
    }

    @Override
    public BaseResponse<Map<Integer, Integer>> count(LeadsQuery query) {
        List<Integer> statusList = query.getStatusList();
        if (org.springframework.util.ObjectUtils.isEmpty(statusList)) {
            return BaseResponse.success(Maps.newHashMap());
        }
        Map<Integer, Integer> countMap = Maps.newHashMap();

        statusList.forEach(status -> {
            query.setStatusList(LeadsStatusEnum.toLeadsStatus(status));
            CountRequest searchRequest = new CountRequest().indices("leads");
            BoolQueryBuilder queryBuilder = buildQueryBuilder(query);
            SearchSourceBuilder sourceBuilder = parseSortField(query.getSortType());
            sourceBuilder.size(100000);
            sourceBuilder.fetchSource("id", "");
            sourceBuilder.query(queryBuilder);
            searchRequest.source(sourceBuilder);

            CountResponse response = null;
            try {
                response = restHighLevelClient.count(searchRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                throw new InngkeServiceException(e);
            }
            long count = response.getCount();

            countMap.put(status, Long.valueOf(count).intValue());
        });
        return BaseResponse.success(countMap);
    }

    private LeadsStatusDto parseDimensionStatisticsCount(SearchResponse searchResponse) {
        Map<Integer, Long> statusCountMap = Maps.newHashMap();
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedLongTerms statusGroup = (ParsedLongTerms)aggregations.get("status_group");
        List<? extends Terms.Bucket> buckets = statusGroup.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            Number status = bucket.getKeyAsNumber();
            long docCount = bucket.getDocCount();
            statusCountMap.put(status.intValue(), docCount);
        }
        LeadsConf leadsConf = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, BidUtils.getBid()));
        boolean leadsEnable = false;
        if (!ObjectUtils.isNull(leadsConf)) {
            leadsEnable = leadsConf.getEnable() != null && leadsConf.getEnable();
        }
        // 全部
        Long allCount = statusCountMap.values().stream().reduce(Long::sum).orElse(0L);

        // 待联系
        Long notContactedCount = statusCountMap.getOrDefault(LeadsStatusEnum.DISTRIBUTED.getStatus(), 0L);

        // 跟进中
        Long followingUpCount = allCount - (statusCountMap.getOrDefault(LeadsStatusEnum.DISTRIBUTED.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.INSTALLED.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.LOST.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.INVALID.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.TRADED.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.TO_INSTALL.getStatus(), 0L));
        // 已签单
        ParsedFilter placeOrderFilter = (ParsedFilter) aggregations.get("place_order");
        Long storeOrderCount = Optional.ofNullable(placeOrderFilter).map(ParsedSingleBucketAggregation::getDocCount).orElse(0L);
        Long tradedCount = 0L;
        if (Objects.nonNull(placeOrderFilter)) {
            Aggregations subAggregation = placeOrderFilter.getAggregations();
            ParsedFilter signBillFilter = (ParsedFilter) subAggregation.get("sign_bill");
            tradedCount = signBillFilter.getDocCount();
        }

        // 已交付
        Long installedCount = statusCountMap.getOrDefault(LeadsStatusEnum.INSTALLED.getStatus(), 0L);
        // 待安装
        Long toInstallCount = statusCountMap.getOrDefault(LeadsStatusEnum.TO_INSTALL.getStatus(), 0L);
        // 已流失
        Long lostCount = statusCountMap.getOrDefault(LeadsStatusEnum.LOST.getStatus(), 0L);
        // 待再次联系
        Long unsureIntentCount = statusCountMap.getOrDefault(LeadsStatusEnum.UNSURE_INTENT.getStatus(), 0L);
        // 已下定
        Long orderedCount = statusCountMap.getOrDefault(LeadsStatusEnum.ORDERED.getStatus(), 0L);
        // 已量尺
        Long measuredCount = statusCountMap.getOrDefault(LeadsStatusEnum.MEASURED.getStatus(), 0L);
        // 已联系
        Long contractedCount = statusCountMap.getOrDefault(LeadsStatusEnum.CONTACTED.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.SUCCESS_CONTACT.getStatus(), 0L)
                + statusCountMap.getOrDefault(LeadsStatusEnum.UNSURE_INTENT.getStatus(), 0L);
        // 有意向
        Long intentCount = statusCountMap.getOrDefault(LeadsStatusEnum.INTENT.getStatus(), 0L);
        // 已到店
        Long storedCount = statusCountMap.getOrDefault(LeadsStatusEnum.STORED.getStatus(), 0L);


        LeadsStatusDto leadsStatusDto = new LeadsStatusDto();
        leadsStatusDto.setAllLeadsCount(allCount.intValue());
        leadsStatusDto.setLeadsEnable(leadsEnable);
        leadsStatusDto.setNotContactedCount(notContactedCount.intValue());
        leadsStatusDto.setFollowingUpCount(followingUpCount.intValue());
        leadsStatusDto.setInstalledCount(installedCount.intValue());
        leadsStatusDto.setLostCount(lostCount.intValue());
        leadsStatusDto.setUnsureIntentCount(unsureIntentCount.intValue());
        leadsStatusDto.setOrderedCount(orderedCount.intValue());
        leadsStatusDto.setMeasuredCount(measuredCount.intValue());
        leadsStatusDto.setTradedCount(tradedCount.intValue());
        leadsStatusDto.setToInstallCount(toInstallCount.intValue());
        leadsStatusDto.setContractedCount(contractedCount.intValue());
        leadsStatusDto.setIntentCount(intentCount.intValue());
        leadsStatusDto.setStoredCount(storedCount.intValue());
        leadsStatusDto.setStoreOrderCount(storeOrderCount.intValue());
        return leadsStatusDto;
    }

    private void installStaffDepartmentInfo(Integer bid, List<LeadsListItemDto> leadsListItemDtos) {
        Set<Long> staffIds = leadsListItemDtos.stream().map(LeadsListItemDto::getDistributeStaffId).collect(Collectors.toSet());
        Map<Long, String> staffIdDeptNameMap = getStaffDto(bid, staffIds).stream().collect(Collectors.toMap(UserStaffDto::getId, UserStaffDto::getDepartmentName));

        if (org.springframework.util.CollectionUtils.isEmpty(staffIdDeptNameMap)) {
            return;
        }
        leadsListItemDtos.forEach(
                item -> {
                    String deptName = staffIdDeptNameMap.get(item.getDistributeStaffId());
                    if (org.springframework.util.StringUtils.isEmpty(deptName)) {
                        return;
                    }
                    item.setDistributeStaffDepartment(deptName);
                }
        );
    }

    private List<UserStaffDto> getStaffDto(Integer bid, Set<Long> staffIds) {
        StaffGetRequest staffGetRequest = new StaffGetRequest();
        staffGetRequest.setBid(bid);
        staffGetRequest.setIds(staffIds);
        staffGetRequest.setPageSize(staffIds.size());
        BaseResponse<List<UserStaffDto>> response = userStaffService.getList(staffGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response) || org.springframework.util.CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        return response.getData();
    }

    private void buildPreFollowStaffName(Integer bid, List<LeadsListItemDto> leadsListItemDtos) {
        Map<Long, StaffDto> staffMap = getStaffMap(bid, getStaffIds(leadsListItemDtos));
        leadsListItemDtos.forEach(leadsListItemDto -> {
            StaffDto staffDto = staffMap.get(leadsListItemDto.getPreFollowStaffId());
            if (Objects.nonNull(staffDto)) {
                leadsListItemDto.setPreFollowStaffName(staffDto.getName());
            }
        });
    }

    private List<Long> getStaffIds(List<LeadsListItemDto> leadsListItemDtos) {
        return leadsListItemDtos.stream().map(LeadsListItemDto::getPreFollowStaffId).collect(Collectors.toList());
    }

    private Map<Long, StaffDto> getStaffMap(Integer bid, List<Long> staffIds) {
        if (org.springframework.util.CollectionUtils.isEmpty(staffIds)) {
            return Maps.newHashMap();
        }

        return staffClientForLeads.getStaffByIds(bid, Sets.newHashSet(staffIds));
    }

    private List<LeadsEsDto> parseHits(SearchResponse searchResponse) {
        if (Objects.isNull(searchResponse)) {
            return Lists.newArrayList();
        }

        List<LeadsEsDto> result = Lists.newArrayList();

        SearchHits hits = searchResponse.getInternalResponse().hits();
        hits.forEach(hit -> {
            String sourceAsString = hit.getSourceAsString();
            LeadsEsDto leadsEsDto = JsonUtil.jsonToObject(sourceAsString, LeadsEsDto.class);
            result.add(leadsEsDto);
        });

        return result;
    }

    private BoolQueryBuilder buildQueryBuilder(LeadsQuery request) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery().must(termQuery(LeadsEsDto.BID, request.getBid()));
        buildBaseQuery(queryBuilder, request);
        buildPolymerizationQuery(queryBuilder, request);

        return queryBuilder;
    }

    private BoolQueryBuilder buildBaseQuery(BoolQueryBuilder queryBuilder, LeadsQuery request) {
        Integer regionId = request.getRegionId();
        Integer level = 0;
        if (regionId != null) {
            RegionGetRequest regionGetRequest = new RegionGetRequest();
            regionGetRequest.setId(regionId);
            // Region RPC调用
            BaseResponse<RegionDto> regionDtoBaseResponse = regionService.getRegion(regionGetRequest);
            if (BaseResponse.responseSuccessWithNonNullData(regionDtoBaseResponse)) {
                level = regionDtoBaseResponse.getData().getLevel();
            }
        }

        String name = request.getName();
        String mobile = request.getMobile();
        Integer status = request.getStatus();
        Integer statusGroup = request.getStatusGroup();
        Long agentId = request.getAgentId();
        Long staffId = request.getStaffId();
        Long targetDepartmentId = request.getTargetDepartmentId();
        String importTimeStart = request.getImportTimeStart();
        String importTimeEnd = request.getImportTimeEnd();
        String distributeTimeStart = request.getDistributeTimeStart();
        String distributeTimeEnd = request.getDistributeTimeEnd();
        Integer channel = request.getChannel();
        Integer excludesChannel = request.getExcludesChannel();
        Integer channelType = request.getChannelType();
        Long channelSource = request.getChannelSource();
        Integer sortType = request.getSortType();
        String orderNum = request.getOrderNum();
        Long shopChannelId = request.getShopChannelId();
        Long preFollowStaffId = request.getFollowStaffId();
        Boolean showFollowStaff = request.getShowFollowStaff();
        Long preFollowStatus = request.getPreFollowStatus();
        List<Integer> statusList = request.getStatusList();
        Boolean hasTransferClient = request.getHasTransferClient();
        String keyword = request.getKeyword();
        Long lastUpdateTime = request.getLastUpdateTime();

        if (Objects.nonNull(lastUpdateTime) && lastUpdateTime > 0L) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.UPDATE_TIME).gt(lastUpdateTime));
        }
        if (!StringUtils.isEmpty(name)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.NAME, name));
        }
        if (!StringUtils.isEmpty(mobile)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.MOBILE, mobile));
        }

        if (Objects.nonNull(status)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, status));
        }

        if (Objects.nonNull(preFollowStatus)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STATUS, preFollowStaffId));
        }

        if (!StringUtils.isEmpty(orderNum)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.ORDER_SN, orderNum));
        }

        if (Objects.nonNull(agentId) && agentId > 0L) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.AGENT_ID, agentId));
        }

        if (Objects.isNull(targetDepartmentId) && Objects.nonNull(staffId) && staffId > 0L) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, staffId));
        }

        if (Objects.isNull(request.getReportStaffId()) && Objects.nonNull(targetDepartmentId)) {
            Set<Long> staffManageDepartment = staffManageDepartmentClientForLeads.getStaffManageDepartment(request.getBid(), staffId);
            // 取查询部门和员工有权限部门的交集
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, Lists.newArrayList(targetDepartmentId)));
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, staffManageDepartment));
        }

        if (Objects.nonNull(request.getReportStaffId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.REPORT_STAFF_ID, request.getReportStaffId()));
        }

        if (!StringUtils.isEmpty(importTimeStart) && !StringUtils.isEmpty(importTimeEnd)) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).gte(importTimeEnd).lte(importTimeEnd));
        }

        if (!StringUtils.isEmpty(distributeTimeStart) && !StringUtils.isEmpty(distributeTimeEnd)) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.DISTRIBUTE_TIME).gte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeStart())).lte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeEnd())));
        }

        if (Objects.nonNull(channel)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL, channel));
        }

        if (Objects.nonNull(excludesChannel)) {
            queryBuilder.mustNot(QueryBuilders.termQuery(LeadsEsDto.CHANNEL, excludesChannel));
        }

        if (Objects.nonNull(channelType)) {
            queryBuilder.mustNot(QueryBuilders.termQuery(LeadsEsDto.CHANNEL_TYPE, channelType));
        }

        if (Objects.nonNull(channelSource)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL_SOURCE, channelSource));
        }

        if (Objects.nonNull(shopChannelId) && shopChannelId >= 0) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL_ID, shopChannelId));
        }

        if (Objects.nonNull(preFollowStatus) && preFollowStaffId >= 0L) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, preFollowStaffId));
        }

        if (Boolean.TRUE.equals(showFollowStaff)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, 0));
        }

        if (StringUtils.isNotBlank(request.getTag())) {
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.TAGS + ".keyword", "*" + request.getTag() + "*"));
        }

        if (StringUtils.isNotBlank(request.getLevel())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.LEVEL, request.getLevelId()));
        }

        if (Objects.nonNull(request.getCreateStaffId()) && request.getCreateStaffId() > 0L) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getCreateStaffId()));
        }

        if (Objects.nonNull(request.getProvinceId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PROVINCE_ID, request.getProvinceId()));
        }

        if (Objects.nonNull(request.getCityId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CITY_ID, request.getCityId()));
        }

        if (Objects.nonNull(request.getAreaId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.AREA_ID, request.getAreaId()));
        }

        if (Objects.nonNull(request.getPushBackStaffId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PUSH_BACK_STAFF_ID, request.getPushBackStaffId()));;
        }

        if (StringUtils.isNotBlank(request.getPushBackContent())) {
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.ERROR_MSG + ".keyword", "*" + request.getPushBackContent() + "*"));
        }

        if (Objects.nonNull(request.getLevelId()) && request.getLevelId() > 0L) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.LEVEL_ID, request.getLevelId()));
        }

        if (!CollectionUtils.isEmpty(request.getChannels())) {
            queryBuilder.must(termsQuery(LeadsEsDto.CHANNEL, request.getChannels()));
        }

        if (CollectionUtils.isNotEmpty(statusList)) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, statusList));
        }
        if (Boolean.TRUE.equals(hasTransferClient)) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.CLIENT_ID).gt(0));
        }
        if (Boolean.FALSE.equals(hasTransferClient)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CLIENT_ID, 0));
        }
        if (StringUtils.isNotBlank(keyword)) {
            queryBuilder.must(
                    QueryBuilders.boolQuery().should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.NAME + ".keyword", "*" + keyword + "*")
                    ).should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.PROVINCE_NAME + ".keyword", "*" + keyword + "*")
                    ).should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.CITY_NAME + ".keyword", "*" + keyword + "*")
                    ).should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.AREA_NAME + ".keyword", "*" + keyword + "*")
                    ).should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.ADDRESS + ".keyword", "*" + keyword + "*")
                    ).should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.MOBILE + ".keyword", "*" + keyword + "*")
                    ).should(
                            QueryBuilders.wildcardQuery(LeadsEsDto.WE_CHAT_ID + ".keyword", "*" + keyword + "*")
                    )
            );
        }
        if (Objects.nonNull(request.getLeadsId()) && request.getLeadsId() > 0L) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.LEADS_ID, request.getLeadsId()));
        }

        // 时间类
        RangeQueryBuilder lastFollowTimeRangeQuery = QueryBuilders.rangeQuery(LeadsEsDto.LAST_FOLLOW_TIME);
        if (Objects.nonNull(request.getStartLastFollowTime()) && request.getStartLastFollowTime() > 0L) {
            lastFollowTimeRangeQuery.gte(request.getStartLastFollowTime());
        }
        if (Objects.nonNull(request.getEndLastFollowTime()) && request.getEndLastFollowTime() > 0L) {
            lastFollowTimeRangeQuery.lt(request.getEndLastFollowTime());
        }
        queryBuilder.must(lastFollowTimeRangeQuery);

        RangeQueryBuilder pushBackTimeRangeQuery = QueryBuilders.rangeQuery(LeadsEsDto.PUSH_BACK_TIME);
        if (StringUtils.isNotBlank(request.getPushBackStartTime())) {
            pushBackTimeRangeQuery.gte(request.getPushBackStartTime());
        }
        if (StringUtils.isNotBlank(request.getPushBackEndTime())) {
            pushBackTimeRangeQuery.lt(request.getPushBackEndTime());
        }
        queryBuilder.must(pushBackTimeRangeQuery);
        return queryBuilder;
    }

    private BoolQueryBuilder buildPolymerizationQuery(BoolQueryBuilder queryBuilder, LeadsQuery request) {
        Integer regionId = request.getRegionId();
        Integer level = 0;
        if (regionId != null) {
            RegionGetRequest regionGetRequest = new RegionGetRequest();
            regionGetRequest.setId(regionId);
            // Region RPC调用
            BaseResponse<RegionDto> regionDtoBaseResponse = regionService.getRegion(regionGetRequest);
            if (BaseResponse.responseSuccessWithNonNullData(regionDtoBaseResponse)) {
                level = regionDtoBaseResponse.getData().getLevel();
            }
        }

        Integer statusGroup = request.getStatusGroup();
        if (statusGroup != null) {
            switch (statusGroup) {
                case 0:
                case 5:
                    List<Integer> excludeStatus = LeadsStatusEnum.getNonAllocatedLeadsStatus();
                    if (Objects.nonNull(request.getReportStaffId()) && request.getReportStaffId() > 0L) {
                        excludeStatus = Lists.newArrayList(LeadsStatusEnum.DELETED.getStatus(), LeadsStatusEnum.RECOVERY.getStatus());
                    }
                    queryBuilder.mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, excludeStatus));
                    break;
                case 1:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus()));
                    break;
                case 2:
                    queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.STATUS).gte(LeadsStatusEnum.CONTACTED.getStatus()).lte(LeadsStatusEnum.INSTALLED.getStatus()));
                    break;
                case 3:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.HAS_STORE_ORDER, 1));
                    break;
                case 4:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.TO_DISTRIBUTE.getStatus()));
                    break;

                case 6:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.INVALID.getStatus()));
                    break;
                case 12:
                    queryBuilder.mustNot(
                            QueryBuilders.termsQuery(
                                    LeadsEsDto.STATUS,
                                    Lists.newArrayList(
                                            LeadsStatusEnum.DISTRIBUTED.getStatus(),
                                            LeadsStatusEnum.INSTALLED.getStatus(),
                                            LeadsStatusEnum.LOST.getStatus(),
                                            LeadsStatusEnum.INVALID.getStatus(),
                                            LeadsStatusEnum.RECOVERY.getStatus(),
                                            LeadsStatusEnum.DELETED.getStatus(),
                                            LeadsStatusEnum.PUSH_BACK.getStatus(),
                                            LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                                            LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                                            LeadsStatusEnum.TRADED.getStatus(),
                                            LeadsStatusEnum.TO_INSTALL.getStatus()
                                    )
                            )
                    );
                    break;
                case 13:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.INSTALLED.getStatus()));
                    break;
                case 14:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.LOST.getStatus()));
                    break;
                case 15:
                    queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.HAS_STORE_ORDER, 1))
                            .mustNot(QueryBuilders.termsQuery(
                                    LeadsEsDto.STATUS,
                                    Lists.newArrayList(LeadsStatusEnum.LOST.getStatus(), LeadsStatusEnum.RECOVERY.getStatus(), LeadsStatusEnum.DELETED.getStatus(), LeadsStatusEnum.INVALID.getStatus()))
                            );
                    break;
                default:
                    break;

            }
        }

        Integer childStatusGroup = request.getChildStatusGroup();
        List<Integer> childStatues = CHILD_STATUS_MAP.get(childStatusGroup);
        if (CollectionUtils.isNotEmpty(childStatues)) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, childStatues));
        }

        switch (level) {
            case 1:
                queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PROVINCE_ID, regionId));
                break;
            case 2:
                queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CITY_ID, regionId));
                break;
            case 3:
                queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.AREA_ID, regionId));
                break;
            default:
                break;
        }
        return queryBuilder;
    }

    private SearchSourceBuilder parseSortField(Integer sortType) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        if (Objects.nonNull(sortType)) {
            switch (sortType) {
                case 0:
                    sourceBuilder.sort(LeadsEsDto.CREATE_TIME, SortOrder.DESC);
                    break;
                case 1:
                    sourceBuilder.sort(LeadsEsDto.DISTRIBUTE_TIME, SortOrder.DESC);
                    break;
                case 2:
                    sourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.DESC);
                    break;
                case 3:
                    sourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.ASC);
                    break;
                case 4:
                    sourceBuilder.sort(LeadsEsDto.LAST_FOLLOW_TIME, SortOrder.DESC);
                    sourceBuilder.sort(LeadsEsDto.DISTRIBUTE_TIME, SortOrder.DESC);
                    break;
                default:
                    sourceBuilder.sort(LeadsEsDto.LEADS_ID, SortOrder.DESC);
            }
        }
        return sourceBuilder;
    }

    private List<LeadsGroupDto> installIsGroupInfoForList(LeadsQuery request) {
        //根据是否需要返回分组聚合数据标识判断并在为true时组装聚合数据，1为待联系，2为跟进中，3为已成交
        Boolean withGroups = request.getWithGroups();
        if (withGroups != null && request.getWithGroups()) {
            LeadsStatusRequest leadsStatusRequest = new LeadsStatusRequest();
            leadsStatusRequest.setBid(request.getBid());
            leadsStatusRequest.setOperatorId(request.getOperatorId());
            leadsStatusRequest.setStaffId(request.getStaffId());
            leadsStatusRequest.setStartDistributeTime(DateTimeUtils.toLocalDateTime(request.getDistributeTimeStart()));
            leadsStatusRequest.setEndDistributeTime(DateTimeUtils.toLocalDateTime(request.getDistributeTimeEnd()));
            leadsStatusRequest.setStartLastFollowTime(DateTimeUtils.MillisToLocalDateTime(request.getStartLastFollowTime()));
            leadsStatusRequest.setEndLastFollowTime(DateTimeUtils.MillisToLocalDateTime(request.getEndLastFollowTime()));
            leadsStatusRequest.setDepartmentId(request.getTargetDepartmentId());
            BaseResponse<LeadsStatusDto> leadsStatusStatistics = this.dimensionStatisticsCount(leadsStatusRequest);
            if (leadsStatusStatistics != null && leadsStatusStatistics.getData() != null) {
                LeadsStatusDto leadsStatusDto = leadsStatusStatistics.getData();

                LeadsGroupDto all = new LeadsGroupDto(0, "全部", leadsStatusDto.getAllLeadsCount());
                LeadsGroupDto notContractedCount = new LeadsGroupDto(1, "待联系", leadsStatusDto.getNotContactedCount());

                LeadsGroupDto followingUpCount = new LeadsGroupDto(12, "跟进中", leadsStatusDto.getFollowingUpCount());
                LeadsGroupDto followingUpCountAll = new LeadsGroupDto(12, "全部", followingUpCount.getLeadsCount());
                // LeadsGroupDto followingUpCountUnsure = new LeadsGroupDto(121, "待再次联系", leadsStatusDto.getUnsureIntentCount());
                LeadsGroupDto followingUpCountOrdered = new LeadsGroupDto(122, "已下定", leadsStatusDto.getOrderedCount());
                LeadsGroupDto followingUpCountMeasured = new LeadsGroupDto(123, "已量尺", leadsStatusDto.getMeasuredCount());
                LeadsGroupDto contract = new LeadsGroupDto(126, "已联系", leadsStatusDto.getContractedCount());
                LeadsGroupDto intent = new LeadsGroupDto(127, "有意向", leadsStatusDto.getIntentCount());
                LeadsGroupDto store = new LeadsGroupDto(128, "已到店", leadsStatusDto.getStoredCount());
                followingUpCount.setChildren(Lists.newArrayList(followingUpCountAll, contract, intent, followingUpCountOrdered, followingUpCountMeasured, store));

                LeadsGroupDto lost = new LeadsGroupDto(14, "已流失", leadsStatusDto.getLostCount());
                LeadsGroupDto deal = new LeadsGroupDto(15, "已成交", Optional.ofNullable(leadsStatusDto.getStoreOrderCount()).orElse(0));
                LeadsGroupDto dealAll = new LeadsGroupDto(15, "全部", deal.getLeadsCount());
                LeadsGroupDto traded = new LeadsGroupDto(124, "已签单", leadsStatusDto.getTradedCount());
                LeadsGroupDto toInstall = new LeadsGroupDto(125, "待安装", leadsStatusDto.getToInstallCount());
                LeadsGroupDto installed = new LeadsGroupDto(153, "已交付", leadsStatusDto.getInstalledCount());
                deal.setChildren(Lists.newArrayList(dealAll, traded, toInstall, installed));


                return Lists.newArrayList(all, notContractedCount, followingUpCount, deal, lost);
            }
        }
        return Lists.newArrayList();
    }

    private void installFollowInfo(LeadsQuery request, List<LeadsListItemDto> leadsListItemDtos, Boolean pcOperate) {
        if (org.springframework.util.CollectionUtils.isEmpty(leadsListItemDtos)) {
            return;
        }
        //如果是否包含跟进记录入参为true则将该线索最新的一条跟进记录返回,数据供小程序线索列表使用
        Boolean withFollow = request.getWithFollow();

        if (withFollow == null || !withFollow) {
            return;
        }

        List<Long> leadsIds = leadsListItemDtos.stream().map(LeadsListItemDto::getId).collect(Collectors.toList());
        Map<Long, LeadsFollow> followMap = getLastLeadsFollowMap(request, pcOperate, leadsIds);

        //为每一个线索组装最新的跟进记录数据
        leadsListItemDtos.forEach(item -> {
            LeadsFollow leadsFollow = followMap.get(item.getId());
            if (leadsFollow == null) {
                return;
            }
            LeadsFollowSimpleDto simpleDto = new LeadsFollowSimpleDto();
            simpleDto.setCreateTime(leadsFollow.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            simpleDto.setFollowContent(leadsFollow.getFollowContent());
            simpleDto.setId(leadsFollow.getId());
            item.setFollow(simpleDto);

        });

    }

    private Map<Long, LeadsFollow> getLastLeadsFollowMap(LeadsQuery request, Boolean pcOperate, List<Long> leadsIds) {
        Map<Long, List<LeadsFollow>> tmpMap = Maps.newHashMap();
        Map<Long, LeadsFollow> followMap = Maps.newHashMap();

        List<LeadsFollow> list = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, request.getBid())
                        .ne(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .apply(!pcOperate, "follow_content NOT REGEXP '处回收|转交'")
        );

        list.forEach(
                e -> tmpMap.computeIfAbsent(e.getLeadsId(), leadsId -> Lists.newArrayList()).add(e)
        );
        tmpMap.forEach(
                (k, v) -> {
                    if (org.springframework.util.CollectionUtils.isEmpty(v)) {
                        return;
                    }
                    followMap.put(k, v.stream().sorted(Comparator.comparing(LeadsFollow::getCreateTime).reversed()).collect(Collectors.toList()).get(0));
                }
        );
        return followMap;
    }

    private void installStaffNameAndAgentName(List<LeadsListItemDto> leadsList, Integer bid, Integer status) {
        //distributorStaffIds
        Set<Long> ids = leadsList.stream().map(LeadsListItemDto::getDistributeStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> leadsId = leadsList.stream().map(LeadsListItemDto::getId).collect(Collectors.toList());
        if (Objects.nonNull(status) && status.equals(LeadsStatusEnum.PUSH_BACK.getStatus())) {//被退回的线索
            leadsService.installPushBackLeadsStaffAndAgentName(bid, leadsId, leadsList);
        } else {
            //组装非退回状态线索
            installNotPushBackLeadsStaffAndAgentName(bid, ids, leadsList);
        }
    }

    private void installNotPushBackLeadsStaffAndAgentName(Integer bid, Set<Long> ids, List<LeadsListItemDto> leadsList) {
        if (org.springframework.util.CollectionUtils.isEmpty(ids) || org.springframework.util.CollectionUtils.isEmpty(leadsList)) {
            return;
        }
        //非线索退回状态
        Map<Long, StaffIdAndAgentIdDto> staffIdMap = getLeadsStaffAndAgentName(bid, ids);

        if (org.springframework.util.CollectionUtils.isEmpty(staffIdMap)) {
            return;
        }

        leadsList.forEach(item -> {
            StaffIdAndAgentIdDto staffIdAndAgentIdDto = staffIdMap.get(item.getDistributeStaffId());
            if (Objects.isNull(staffIdAndAgentIdDto)) {
                return;
            }
            item.setDistributeStaffName(staffIdAndAgentIdDto.getStaffName());
            item.setDistributeAgentName(staffIdAndAgentIdDto.getAgentName());
        });
    }

    private Map<Long, StaffIdAndAgentIdDto> getLeadsStaffAndAgentName(Integer bid, Set<Long> staffIds) {
        if (org.springframework.util.CollectionUtils.isEmpty(staffIds)) {
            return Maps.newHashMap();
        }

        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(Lists.newArrayList(staffIds));
        List<StaffIdAndAgentIdDto> staffIdAndAgentIdDto = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        return staffIdAndAgentIdDto.stream().collect(Collectors.toMap(StaffIdAndAgentIdDto::getId, t -> t));
    }
}

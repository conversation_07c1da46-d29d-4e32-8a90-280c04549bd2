package com.inngke.bp.leads.mq.process.order;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.inngke.bp.leads.dto.request.LeadsEsBatchRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.mq.message.order.OrderChangeMessageDto;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.es.dto.IndexDocDto;
import com.inngke.common.es.service.EsDocService;
import com.inngke.ip.common.dto.response.EsDocsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/8/31 3:42 PM
 */
@Service
public class LeadsStoreOrderProcess extends BaseOrderProcess {

    private static final Logger logger = LoggerFactory.getLogger(LeadsStoreOrderProcess.class);

    private static final String LEADS_INDEX_NAME = "leads";


    @Resource
    private LeadsEsService leadsEsService;

    @Resource
    private EsDocService esDocService;

    /**
     * 需要处理的订单类型
     *
     * @return 订单类型
     */
    @Override
    public Integer getProcessOrderType() {
        return 2;
    }

    /**
     * 是否需要处理
     *
     * @param message 消息体
     * @return 是否需要继续处理
     */
    @Override
    public boolean match(OrderChangeMessageDto message) {
        return message.getEvent() == 3 || message.getEvent() == 4 || message.getEvent() == -7;
    }

    /**
     * 处理逻辑
     *
     * @param message 消息体
     */
    @Override
    public void process(OrderChangeMessageDto message) {
        // 防止再小程序后台-开单开出无线索Id的单
        if (ObjectUtils.isEmpty(message.getLeadsId()) || message.getLeadsId().equals(0L)) {
            logger.info("无leadsId开单不改变线索状态,orderId:{},mobile:{}", message.getOrderId(), message.getMobile());
            return;
        }

        this.changeLeadsStatus(message);

        if (message.getLeadsId() != null && message.getLeadsId() > 0L) {
            LeadsEsBatchRequest esRequest = new LeadsEsBatchRequest();
            esRequest.setPageSize(1);
            esRequest.setBid(message.getBid());
            esRequest.setLeadsId(message.getLeadsId());
            BaseResponse<EsDocsResponse<LeadsEsDto>> response = leadsEsService.getBatchDoc(esRequest);
            List<LeadsEsDto> leadsEsDtos = Optional.ofNullable(response).map(BaseResponse::getData).map(EsDocsResponse::getList).orElse(null);
            if (!CollectionUtils.isEmpty(leadsEsDtos)) {
                ArrayList<IndexDocDto> dataList = new ArrayList<>();
                for (LeadsEsDto leadsEsDto : leadsEsDtos) {
                    IndexDocDto data = new IndexDocDto();
                    data.setDocId(leadsEsDto.getBid() + InngkeAppConst.UNDERLINE_STR + leadsEsDto.getId());
                    data.setDocJson(jsonService.toJson(leadsEsDto));
                    dataList.add(data);
                }
                logger.info("门店订单更新线索ES:{}", jsonService.toJson(dataList));
                esDocService.updateDocs(LEADS_INDEX_NAME, dataList);
            }
        }
    }

}

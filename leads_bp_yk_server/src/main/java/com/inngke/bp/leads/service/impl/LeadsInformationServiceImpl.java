package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.manager.LeadsExtInformationManager;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsInformationDto;
import com.inngke.bp.leads.dto.response.LeadsInformationVo;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.enums.LeadsTypeEnum;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsInformationService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/3/8 15:28
 */
@Service
@DubboService(version = "1.0.0")
public class LeadsInformationServiceImpl implements LeadsInformationService {

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsEsService leadsEsService;

    @Override
    public BaseResponse<LeadsInformationDto> getInfo(LeadsGetRequest request) {
        LeadsExtInformation leadsExtInformation = getById(request.getBid(), request.getId());

        if (ObjectUtils.isEmpty(leadsExtInformation)) {
            return BaseResponse.success(null);
        }

        return BaseResponse.success(toDto(leadsExtInformation));
    }


    @Override
    public BaseResponse<LeadsInformationVo> getLeadsDetail(LeadsGetRequest request) {
        LeadsExtInformation leadsExtInformation = getById(request.getBid(), request.getId());

        if (ObjectUtils.isEmpty(leadsExtInformation)) {
            return BaseResponse.success(null);
        }

        return BaseResponse.success(toVo(leadsExtInformation));
    }

    @Override
    public BaseResponse<Boolean> update(LeadsInformationUpdateRequest request) {
        LeadsExtInformation leadsExtInformation = toEntity(request);

        LeadsExtInformation dbLeadsExtInformation = leadsExtInformationManager.getOne(Wrappers.<LeadsExtInformation>query()
                .eq(LeadsExtInformation.BID, request.getBid())
                .eq(LeadsExtInformation.ID, request.getId())
        );
        if (Objects.nonNull(dbLeadsExtInformation)) {
            leadsExtInformationManager.updateById(leadsExtInformation);
        } else {
            leadsExtInformation.setCreateTime(LocalDateTime.now());
            leadsExtInformationManager.save(leadsExtInformation);
        }

        //更新es
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setIds(Lists.newArrayList(request.getId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<LeadsInformationDto> add(LeadsInformationAddRequest request) {
        LeadsExtInformation leadsExtInformation = toEntity(request);
        //if (LeadsChannelEnum.isOrder(request.getChannel())){
        // 同步天猫，订单类也要留资时间
        if (request.getSubmitTime() == null) {
            return BaseResponse.success(toDto(leadsExtInformation));
        }
        boolean save = leadsExtInformationManager.save(leadsExtInformation);
        if (!save) {
            return BaseResponse.error("fail");
        }
        //更新es
        AsyncUtils.runAsync(()->{
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setIds(Lists.newArrayList(request.getId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return BaseResponse.success(toDto(leadsExtInformation));
    }

    private LeadsExtInformation getById(Integer bid, Long id) {
        return leadsExtInformationManager.getOne(
                Wrappers.<LeadsExtInformation>query()
                        .eq(LeadsExtInformation.BID, bid)
                        .eq(LeadsExtInformation.ID, id)
        );
    }

    private LeadsInformationDto toDto(LeadsExtInformation leadsExtInformation) {
        LeadsInformationDto leadsInformationDto = new LeadsInformationDto();
        leadsInformationDto.setGender(leadsExtInformation.getGender());
        leadsInformationDto.setAccountId(leadsExtInformation.getAccountId());
        leadsInformationDto.setAccountName(leadsExtInformation.getAccountName());
        leadsInformationDto.setExternalId(leadsExtInformation.getExternalId());
        leadsInformationDto.setCampaignId(leadsExtInformation.getCampaignId());
        leadsInformationDto.setCampaignName(leadsExtInformation.getCampaignName());
        leadsInformationDto.setSubmitTime(DateTimeUtils.getMilli(leadsExtInformation.getSubmitTime()));
        leadsInformationDto.setId(leadsExtInformation.getId());
        leadsInformationDto.setBid(leadsExtInformation.getBid());

        return leadsInformationDto;
    }

    private LeadsInformationVo toVo(LeadsExtInformation leadsExtInformation) {
        LeadsInformationVo leadsInformationVo = new LeadsInformationVo();
        leadsInformationVo.setGender(leadsExtInformation.getGender());
        leadsInformationVo.setAccountId(leadsExtInformation.getAccountId());
        leadsInformationVo.setAccountName(leadsExtInformation.getAccountName());
        leadsInformationVo.setExternalId(leadsExtInformation.getExternalId());
        leadsInformationVo.setCampaignId(leadsExtInformation.getCampaignId());
        leadsInformationVo.setCampaignName(leadsExtInformation.getCampaignName());
        if (leadsExtInformation.getSubmitTime() != null) {
            leadsInformationVo.setSubmitTime(DateTimeUtils.getMilli(leadsExtInformation.getSubmitTime()));
        }
        leadsInformationVo.setId(leadsExtInformation.getId());
        leadsInformationVo.setBid(leadsExtInformation.getBid());

        return leadsInformationVo;
    }

    private LeadsExtInformation toEntity(LeadsInformationUpdateRequest request) {
        LeadsExtInformation leadsExtInformation = new LeadsExtInformation();
        leadsExtInformation.setId(request.getId());
        leadsExtInformation.setBid(request.getBid());
        leadsExtInformation.setGender(request.getGender());
        leadsExtInformation.setCampaignId(request.getCampaignId());
        leadsExtInformation.setCampaignName(request.getCampaignName());
        leadsExtInformation.setAccountId(request.getAccountId());
        leadsExtInformation.setAccountName(request.getAccountName());
        leadsExtInformation.setOpenId(InngkeAppConst.EMPTY_STR);
        if (request.getSubmitTime() != null) {
            leadsExtInformation.setSubmitTime(null);
        }

        return leadsExtInformation;
    }


    private LeadsExtInformation toEntity(LeadsInformationAddRequest request) {
        LeadsExtInformation leadsExtInformation = new LeadsExtInformation();
        leadsExtInformation.setId(request.getId());
        leadsExtInformation.setBid(request.getBid());
        leadsExtInformation.setExternalId(request.getExternalId());
        leadsExtInformation.setGender(request.getGender());
        leadsExtInformation.setCampaignId(request.getCampaignId());
        leadsExtInformation.setCampaignName(request.getCampaignName());
        leadsExtInformation.setAccountId(request.getAccountId());
        leadsExtInformation.setAccountName(request.getAccountName());
        leadsExtInformation.setCreateTime(LocalDateTime.now());
        if (request.getSubmitTime() != null) {
            leadsExtInformation.setSubmitTime(DateTimeUtils.MillisToLocalDateTime(request.getSubmitTime()));
        }
        return leadsExtInformation;

    }
}

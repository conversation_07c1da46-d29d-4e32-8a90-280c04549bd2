package com.inngke.bp.leads.api.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:14
 */
@NoArgsConstructor
@Data
public class BaseFlyFishResponse<T> implements Serializable {

    private Integer code;

    private String message;

    private T data;

    @JsonProperty("request_id")
    private String requestId;
}

package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsFollowDto;
import com.inngke.bp.leads.service.LeadsFollowService;
import com.inngke.bp.leads.service.LeadsSmsService;
import com.inngke.bp.leads.service.impl.sms.LeadsSmsFactoryService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索-跟进
 * @since 2021/9/8 5:51 PM
 */
@RestController
@RequestMapping("/api/leads-follow")
public class LeadsFollowApiController {
    @Autowired
    private LeadsFollowService leadsFollowService;

    @Autowired
    private LeadsSmsFactoryService leadsSmsFactoryService;

    /**
     * 创建线索跟进
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    跟进请求
     * @return 是否创建成功
     */
    @PostMapping
    public BaseResponse<Boolean> createFollow(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated LeadsFollowCreateRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setBid(bid);
        request.setOperatorId(jwtPayload.getCid());
        request.setStaffId(jwtPayload.getSid());
        return leadsFollowService.createFollow(request);
    }

    /**
     * 后台创建跟进记录
     *
     * @param bid 商户
     * @param operatorId 操作员编号
     * @param request 跟进信息
     * @return 添加跟进成功/失败
     */
    @PostMapping("/admin")
    public BaseResponse<Boolean> createFollowForOpt(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsFollowCreateRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ){
        request.setBid(bid);
        request.setOperatorId(jwtPayload.getCid());
        request.setStaffId(jwtPayload.getSid());
        return leadsFollowService.createFollowForOpt(request);
    }

    /**
     * 查询某个线索的跟进记录
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      筛选条件
     * @return 跟进记录列表
     */
    @GetMapping("/list")
    public BaseResponse<List<LeadsFollowDto>> list(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsFollowQuery query,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        query.setBid(bid);
        query.setOperatorId(operatorId);
        query.setOperatorStaffId(jwtPayload.getSid());
        return leadsFollowService.list(query);
    }

    /**
     * 获取某个线索根基记录数
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    请求
     * @return 线索根基记录数
     */
    @GetMapping("/count")
    public BaseResponse<Integer> getFollowCount(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsGetRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsFollowService.getLeadsFollowCount(request);
    }

    /**
     * pc端跟进列表
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    请求
     * @return 跟进列表
     */
    @GetMapping("/query")
    public BaseResponse<BasePaginationResponse<LeadsFollowDto>> getFollowList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsFollowListRequest request,
            @RequestAttribute JwtPayload jwtPayload

    ) {
        request.setBid(bid);
        request.setOperatorId(jwtPayload.getCid());
        return leadsFollowService.getLeadsFollowList(request);
    }

    /**
     * 发送跟进短信
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return
     */
    @PostMapping("/sms")
    public BaseResponse<Boolean> sendFollowSms(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated LeadsSmsSendRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ){
        request.setBid(bid);
        request.setOperatorId(jwtPayload.getCid());

        LeadsSmsService leadsSmsService = leadsSmsFactoryService.getInstance(request.getType());

        return leadsSmsService.send(request);
    }
}

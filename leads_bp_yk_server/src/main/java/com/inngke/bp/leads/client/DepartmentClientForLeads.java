package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.organize.dto.request.department.GetDepIdsByAgentIdRequest;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.request.department.GetDepartmentsRequest;
import com.inngke.bp.organize.dto.request.department.GetStaffDepartmentRequest;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.service.DepartmentService;
import com.inngke.bp.user.dto.request.staff.StaffDeptRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeErrorException;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/25 9:16
 */
@Component
@Slf4j
public class DepartmentClientForLeads {

    private static final Logger LOG = LoggerFactory.getLogger(DepartmentClientForLeads.class);

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private DepartmentService departmentService;

    @Autowired
    private JsonService jsonService;

    public List<DepartmentDto> getDepartmentList(Integer bid, List<Long> ids){
        return getDepartmentList(bid,ids,10);
    }

    public List<DepartmentDto> getDepartmentList(Integer bid, List<Long> ids,Integer childrenLevel){
        GetDepartmentsRequest request = new GetDepartmentsRequest();
        request.setChildrenLevel(childrenLevel);
        request.setIds(ids);
        request.setBid(bid);

        BaseResponse<List<DepartmentDto>> response = departmentService.getDepartments(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.warn("获取部门信息失败！，err={}"+jsonService.toJson(response));
            return Lists.newArrayList();
        }
        return response.getData();
    }


    /**
     * 获取员工部门关系
     * @param staffDeptRequest 请求类
     * @return key-staffId value- 部门信息
     */
    public Map<Long, DepartmentDto> getStaffDepartmentByStaffIds(StaffDeptRequest staffDeptRequest) {
        Integer bid = staffDeptRequest.getBid();
        Set<Long> staffIds = staffDeptRequest.getStaffIds();

        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(bid);
        staffListRequest.setIds(Lists.newArrayList(staffIds));
        // 如果异常staffClient会抛异常
        List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);

        GetDepartmentsRequest getDepartmentsRequest = new GetDepartmentsRequest();
        // 只要本身
        getDepartmentsRequest.setChildrenLevel(0);
        getDepartmentsRequest.setBid(staffDeptRequest.getBid());
        // todo hxw 去重看看有没有更好的方法
        getDepartmentsRequest.setIds(Lists.newArrayList(staffList.stream().map(StaffDto::getDepartmentId).collect(Collectors.toSet())));
        return getDepartments(getDepartmentsRequest).stream().collect(
                Collectors.toMap((key) -> {
                    for (StaffDto staffDto : staffList) {
                        if (key.getId().equals(staffDto.getDepartmentId())) {
                            return staffDto.getId();
                        }
                    }
                    // 按逻辑来说是能够找到的 除非逻辑跑到一半有人改了员工和部门的映射关系
                    return null;
                }, (value) -> value));
    }


    public Map<Long,Set<Long>> getChildrenMapByIds(Integer bid, List<Long> deptIds){
        BaseIdsRequest request = new BaseIdsRequest();
        request.setBid(bid);
        request.setIds(deptIds);
        BaseResponse<Map<Long, Set<Long>>> childrenMapByIdsResp = departmentService.getChildrenMapByIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(childrenMapByIdsResp)){
            LOG.warn("通过部门id，获取子部门id失败！，request={}",request);
            return null;
        }
        return childrenMapByIdsResp.getData();
    }

    public List<DepartmentDto> getDepartmentByIds(Integer bid,List<Long> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>(0);
        }
        GetDepartmentsRequest getDepartmentsRequest=new GetDepartmentsRequest();
        getDepartmentsRequest.setBid(bid);
        getDepartmentsRequest.setIds(ids);
        getDepartmentsRequest.setChildrenLevel(1);
        return getDepartments(getDepartmentsRequest);
    }

    /**
     * 获取不同起点多个部门树
     */
    public List<DepartmentDto> getDepartments(GetDepartmentsRequest getDepartmentsRequest) {
        BaseResponse<List<DepartmentDto>> response = departmentService.getDepartments(getDepartmentsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.warn("获取部门信息失败！，err={}"+jsonService.toJson(response));
            return Lists.newArrayList();
        }
        return response.getData();
    }

    public Set<Long> getDeptParentsById(Integer bid, Long deptId) {
        BaseIdsRequest departmentRequest = new BaseIdsRequest();
        departmentRequest.setIds(Lists.newArrayList(deptId));
        departmentRequest.setBid(bid);
        BaseResponse<Map<Long, Set<Long>>> departmentTree = departmentService.getParentsAndOwnMapByIds(departmentRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(departmentTree) || CollectionUtils.isEmpty(departmentTree.getData())) {
            return Sets.newHashSet();
        }
        return departmentTree.getData().get(deptId);
    }

    public Map<Long, Set<Long>> getDeptParentsMapByIds(Integer bid, List<Long> deptIds) {
        BaseIdsRequest departmentRequest = new BaseIdsRequest();
        departmentRequest.setIds(Lists.newArrayList(deptIds));
        departmentRequest.setBid(bid);
        BaseResponse<Map<Long, Set<Long>>> departmentTree = departmentService.getParentsAndOwnMapByIds(departmentRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(departmentTree) || CollectionUtils.isEmpty(departmentTree.getData())) {
            return Maps.newHashMap();
        }
        return departmentTree.getData();
    }

    public DepartmentDto getParentWithChildrenById(GetDepartmentRequest request) {
        BaseResponse<DepartmentDto> response = departmentService.getParentWithChildrenById(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.error(response.getMsg());
            throw new InngkeServiceException("获取父部门包含本身异常");
        }
        return response.getData();
    }

    public DepartmentDto getDepartment(GetDepartmentRequest request) {
        BaseResponse<DepartmentDto> response = departmentService.getDepartment(request);
        if (!BaseResponse.responseSuccess(response)) {
            LOG.error("获取部门数据失败！request={}，response={}", jsonService.toJson(request), jsonService.toJson(response));
            throw new InngkeServiceException("获取部门数据失败！");
        }
        return response.getData();
    }

    //获取当前部门的下级部门
    public List<DepartmentDto> getDepartmentChild(GetDepartmentRequest request) {
        BaseResponse<DepartmentDto> response = departmentService.getDepartment(request);
        if (!BaseResponse.responseSuccess(response)) {
            LOG.error("获取部门数据失败！request={}，response={}", jsonService.toJson(request), jsonService.toJson(response));
            throw new InngkeServiceException("获取部门数据失败！");
        }
        return Optional.ofNullable(response.getData().getChildren()).orElse(Lists.newArrayList());
    }

    public List<DepartmentDto> getDepartmentWithParents(Integer bid, Long id){
        GetDepartmentRequest getDepartmentRequest=new GetDepartmentRequest();
        getDepartmentRequest.setBid(bid);
        getDepartmentRequest.setDepartmentId(id);
        return getDepartmentWithParents(getDepartmentRequest);
    }

    public List<DepartmentDto> getDepartmentWithParents(GetDepartmentRequest request) {
        BaseResponse<List<DepartmentDto>> response = departmentService.getDepartmentWithParents(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.error("获取父类部门异常 error request bid: {} departmentid: {}", request.getBid(), request.getDepartmentId());
            throw new InngkeServiceException("获取父类部门异常");
        }
        return response.getData();
    }

    public List<DepartmentDto> getDepartmentsWithParents(GetDepartmentsRequest request) {
        BaseResponse<List<DepartmentDto>> response = departmentService.getDepartmentsWithParents(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.error("获取父类部门异常 error request bid: {} departmentIds: {}", request.getBid(), request.getIds());
            throw new InngkeServiceException("获取父类部门异常");
        }
        return response.getData();
    }

    public Set<Long> getDepartmentChildrenByIds(BaseIdsRequest request) {
        BaseResponse<Map<Long, Set<Long>>> response = departmentService.getChildrenMapByIds(request);
        Map<Long, Set<Long>> childrenMap = response.getData();
        if (!BaseResponse.responseSuccessWithNonNullData(response) || CollectionUtils.isEmpty(childrenMap)) {
            return Sets.newHashSet();
        }
        Set<Long> allChildren = Sets.newHashSet();
        for (Map.Entry<Long, Set<Long>> entry:childrenMap.entrySet()) {
            Set<Long> child = entry.getValue();
            allChildren.addAll(child);
        }
        return allChildren;
    }

    public Map<Long, Set<Long>> getChildrenMapByDeptIds(BaseIdsRequest request) {
        if (CollectionUtils.isEmpty(request.getIds())) {
            return Maps.newHashMap();
        }
        BaseResponse<Map<Long, Set<Long>>> response = departmentService.getChildrenMapByIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }


    public Map<Long,Set<Long>> getAllChildrenMapByIds(BaseIdsRequest request){
        BaseResponse<Map<Long, Set<Long>>> allChildrenMapByIdsResp = departmentService.getAllChildrenMapByIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(allChildrenMapByIdsResp)){
            LOG.warn("获取部门子集失败，request={}",jsonService.toJson(request));
            return null;
        }
        return allChildrenMapByIdsResp.getData();
    }

    /**
     * 获取所有子部门ID
     *
     * @param bid
     * @param departmentId
     * @return
     */
    public Set<Long> getAllChildrenIdsById(Integer bid,Long departmentId){
        BaseIdsRequest request = new BaseIdsRequest();
        request.setIds(Lists.newArrayList());
        request.setOperatorId(0L);
        request.setBid(0);

        Map<Long, Set<Long>> childrenMapByIds = getAllChildrenMapByIds(request);
        if (CollectionUtils.isEmpty(childrenMapByIds)){
            return Sets.newHashSet();
        }

        return childrenMapByIds.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public DepartmentDto getDepartmentById(Integer bid,Long id){
        GetDepartmentRequest getDepartmentRequest=new GetDepartmentRequest();
        getDepartmentRequest.setBid(bid);
        getDepartmentRequest.setDepartmentId(id);
        getDepartmentRequest.setChildrenLevel(1);
        BaseResponse<DepartmentDto> baseResponse = departmentService.getDepartment(getDepartmentRequest);
        if(!BaseResponse.responseSuccessWithNonNullData(baseResponse)){
            throw new InngkeErrorException("获取部门数据失败！");
        }
        return baseResponse.getData();
    }

    public Set<Long> getDepIdsByAgentId(Integer bid,Long agentId){
        GetDepIdsByAgentIdRequest getDepartmentRequest=new GetDepIdsByAgentIdRequest();
        getDepartmentRequest.setBid(bid);
        getDepartmentRequest.setAgentId(agentId);
        BaseResponse<Set<Long>> response = departmentService.getDepIdsByAgentId(getDepartmentRequest);
        if(BaseResponse.responseErrorOrNullData(response)){
            throw new InngkeErrorException("获取部门数据失败！");
        }
        return response.getData();
    }

    public Set<Long> getChildrenDepartment(Integer bid, Long deptId) {
        BaseIdsRequest request = new BaseIdsRequest();
        request.setBid(bid);
        request.setIds(Lists.newArrayList(deptId));
        LOG.info("获取部门的子部门请求：{}", jsonService.toJson(request));
        BaseResponse<Map<Long, Set<Long>>> response = departmentService.getChildrenMapByIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.error("获取部门的子部门异常：{}", jsonService.toJson(response));
            return Sets.newHashSet();
        }

        return response.getData().get(deptId);
    }

    public DepartmentDto getDepartmentByStaffId(Integer bid , Long staffId) {
        GetStaffDepartmentRequest getStaffDepartmentRequest = new GetStaffDepartmentRequest();
        getStaffDepartmentRequest.setBid(bid);
        getStaffDepartmentRequest.setStaffId(staffId);
        BaseResponse<DepartmentDto> response = departmentService.getDepartmentByStaffId(getStaffDepartmentRequest);
        if (BaseResponse.responseErrorOrNullData(response)) {
            LOG.error("获取部门异常：{}", jsonService.toJson(response));
            throw new InngkeErrorException("获取部门数据失败！");
        }
        return response.getData();
    }

    public List<DepartmentDto> getParentDepartmentIds(Integer bid, Long departmentId) {
        GetDepartmentRequest request = new GetDepartmentRequest();
        request.setDepartmentId(departmentId);
        request.setBid(bid);

        BaseResponse<List<DepartmentDto>> response = departmentService.getDepartmentWithParents(request);
        if (BaseResponse.responseErrorOrNullData(response)) {
            return Lists.newArrayList();
        }

        return response.getData();
    }

    public Set<Long> filterDepartmentTopIds(Integer bid, List<Long> departmentIds) {
        BaseIdsRequest request = new BaseIdsRequest();
        request.setIds(departmentIds);
        request.setOperatorId(0L);
        request.setBid(bid);

        BaseResponse<Set<Long>> response = departmentService.filterDepartmentTopIds(request);
        if (BaseResponse.responseErrorOrNullData(response)) {
            return Sets.newHashSet();
        }

        return response.getData();
    }

    public Map<Long, Set<Long>> batchGetChildrenDepartment(Integer bid, List<Long> departmentIds) {
        BaseIdsRequest request = new BaseIdsRequest();
        request.setBid(bid);
        request.setIds(departmentIds);
        BaseResponse<Map<Long, Set<Long>>> response = departmentService.getAllChildrenMapByIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("批量获取部门下所有子部门请求异常：{}", jsonService.toJson(response));
            return Maps.newHashMap();
        }

        return response.getData();
    }

    public DepartmentDto getRootDepartment(Integer bid) {
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        BaseResponse<List<DepartmentDto>> response = departmentService.getRootDepartments(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("com.inngke.bp.organize.service.DepartmentService.getRootDepartments发生异常：{}", jsonService.toJson(response));
            return null;
        }

        return response.getData().get(0);
    }

    public Set<Long> filterTopDepartment(Integer bid, Set<Long> staffManagerDepartmentIds) {
        BaseIdsRequest request = new BaseIdsRequest();
        request.setBid(bid);
        request.setIds(Lists.newArrayList(staffManagerDepartmentIds));
        BaseResponse<Set<Long>> response = departmentService.filterDepartmentTopIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("com.inngke.bp.leads.client.DepartmentClientForLeads.filterTopDepartment{}", jsonService.toJson(response));
            return Sets.newHashSet();
        }

        return response.getData();
    }

    public List<DepartmentDto> findDepartmentByIds(int bid, Set<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }

        GetDepartmentsRequest request = new GetDepartmentsRequest();
        request.setBid(bid);
        request.setIds(Lists.newArrayList(departmentIds));
        BaseResponse<List<DepartmentDto>> departments = departmentService.getDepartments(request);
        if (!BaseResponse.responseSuccessWithNonNullData(departments)) {
            log.info("com.inngke.api.mp.client.organize.DepartmentClientForMp.findDepartmentByIds:{}", jsonService.toJson(departments));
            return Lists.newArrayList();
        }

        return departments.getData();

    }
}

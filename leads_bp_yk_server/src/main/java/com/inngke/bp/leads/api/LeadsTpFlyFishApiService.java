package com.inngke.bp.leads.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.bp.leads.api.dto.request.GetFlyFishAdvertiserRequest;
import com.inngke.bp.leads.api.dto.request.GetFlyFishTokenByCodeRequest;
import com.inngke.bp.leads.api.dto.request.RefreshFlyFishTokenRequest;
import com.inngke.bp.leads.api.dto.response.*;
import retrofit2.http.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:05
 */
@RetrofitClient(
        baseUrl = "https://ad.oceanengine.com/open_api/",
        callTimeoutMs = 100000,
        connectTimeoutMs = 991000,
        readTimeoutMs = 991000,
        converterFactories = AiCustomerConverterFactories.class,
        logLevel = LogLevel.INFO,
        logStrategy = LogStrategy.BODY
)
public interface LeadsTpFlyFishApiService {

    /**
     * 获取Access Token
     * https://open.oceanengine.com/labels/7/docs/1696710505596940
     *
     * @return
     */
    @POST("oauth2/access_token/")
    BaseFlyFishResponse<FlyFishAccessTokenDto> getAccessToken(@Body GetFlyFishTokenByCodeRequest request);

    /**
     * 刷新Refresh Token
     * https://open.oceanengine.com/labels/7/docs/1696710506097679
     *
     * @param request
     * @return
     */
    @POST("oauth2/refresh_token/")
    BaseFlyFishResponse<FlyFishAccessTokenDto> refreshAccessToken(@Body RefreshFlyFishTokenRequest request);

    /**
     * 获取已授权账户
     * https://open.oceanengine.com/labels/7/docs/1696710506574848
     *
     * @param accessToken
     * @param advertiserId
     * @return
     */
    @GET("2/majordomo/advertiser/select")
    BaseFlyFishResponse<BaseFlyFishListResponse<FlyFishAdvertiserDto>> getAdvertiser(
            @Header("Access-Token") String accessToken, @Query("advertiser_id") String advertiserId);

    /**
     * 获取线索列表
     * https://open.oceanengine.com/labels/7/docs/1696710631271436
     *
     * @param advertiserIds
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    @GET("2/tools/clue/get/")
    BaseFlyFishResponse<BaseFlyFishListResponse<FlyFishLeadsDto>> getLeads(
            @Header("Access-Token") String accessToken,
            @Query("advertiser_ids") String advertiserIds,
            @Query("start_time") String startTime,
            @Query("end_time") String endTime,
            @Query("page") Integer page,
            @Query("page_size") Integer pageSize
    );
}

package com.inngke.bp.leads.service.impl.contact.status;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.bp.leads.service.impl.contact.AbstractLeadsContactFollowService;
import org.springframework.stereotype.Service;

/**
 * 真实号码 第二次拨打
 * 拨打客户电话
 *
 * <AUTHOR>
 * @date 2022/4/2 16:47
 */
@Service
public class LeadsContactFollowRealTwiceServiceImpl extends AbstractLeadsContactFollowService {
    @Override
    public LeadsContactStatusEnum getStatusEnum() {
        return LeadsContactStatusEnum.REAL_TWICE;
    }

    @Override
    protected String generateContactFollowContent(Leads leads, PrivateVoiceRecordDTO dto) {
        return "拨打客户电话";
    }
}

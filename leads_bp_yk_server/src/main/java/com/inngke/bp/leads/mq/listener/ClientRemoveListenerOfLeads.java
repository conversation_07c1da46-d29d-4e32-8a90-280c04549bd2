package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClientRemoveListenerOfLeads
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/9/25 17:53
 */
//@Service
//@MqConsumer(value = "client_change", consumerName = "leads_bp_yk_client_change", messageType = EntityChangeMessageDto.class)
@Slf4j
public class ClientRemoveListenerOfLeads implements InngkeMqListener<EntityChangeMessageDto> {

    @Resource
    private LeadsManager leadsManager;

    @Resource
    private LeadsEsService leadsEsService;


    @Override
    public void process(EntityChangeMessageDto entityChangeMessageDto) {
        if (Objects.isNull(entityChangeMessageDto) || !entityChangeMessageDto.getEvent().equals(3)) {
            log.info("ClientRemoveListenerOfLeads.process: entityChangeMessageDto is null or event is not 3; messageInfo:{}", JsonUtil.toJsonString(entityChangeMessageDto));
            return;
        }

        Long clientId = entityChangeMessageDto.getId();

        List<Leads> leadsList = leadsManager.findLeadsByClient(entityChangeMessageDto.getBid(), clientId);
        if (CollectionUtils.isEmpty(leadsList)) {
            log.info("clientId:{} not has leads", clientId);
            return;
        }
        leadsList.forEach(leads -> leads.setStatus(LeadsStatusEnum.DELETED.getStatus()));
        boolean saveBatch = leadsManager.saveOrUpdateBatch(leadsList);
        if (saveBatch) {
            //更新es索引
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(entityChangeMessageDto.getBid());
            leadsUpdateRequest.setIds(leadsList.stream().map(Leads::getId).collect(Collectors.toList()));
            leadsUpdateRequest.setRefreshEs(true);
            leadsEsService.updateDocs(leadsUpdateRequest);
        }
    }
}

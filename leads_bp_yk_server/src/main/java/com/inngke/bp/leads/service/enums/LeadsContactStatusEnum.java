package com.inngke.bp.leads.service.enums;

import com.inngke.common.utils.StringUtils;

/**
 * <AUTHOR>
 */

public enum LeadsContactStatusEnum {

    /**
     * 虚拟号 首次拨打 接通 >10s
     */
    FAKE_FIRST_CONNECT_MAX("1111"),

    /**
     * 虚拟号 首次拨打 接通 <10s
     */
    FAKE_FIRST_CONNECT_MIN("1110"),

    /**
     * 虚拟号 首次拨打 未接通 <10s
     */
    FAKE_FIRST_CLOSE("1100"),

    /**
     * 虚拟号 非首次拨打 接通 >10s
     */
    FAKE_TWICE_CONNECT_MAX("1011"),

    /**
     * 虚拟号 非首次拨打 接通 <10s
     */
    FAKE_TWICE_CONNECT_MIN("1010"),

    /**
     * 虚拟号 非首次拨打 未接通 <10s
     */
    FAKE_TWICE_CLOSE("1000"),

    /**
     * 真实号码首次拨打
     */
    REAL_FIRST("0100"),

    /**
     * 真实号码非首次拨打
     */
    REAL_TWICE("0100");


    private final String code;

    LeadsContactStatusEnum(String code) {
        this.code = code;
    }

    static LeadsContactStatusEnum parse(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (LeadsContactStatusEnum leadsContactStatusEnum : LeadsContactStatusEnum.values()) {
            if (leadsContactStatusEnum.code.equals(code)) {
                return leadsContactStatusEnum;
            }
        }
        return null;
    }

    static public LeadsContactStatusEnum parse(Boolean isFakeMobile, Boolean isFirstTime, Boolean isConnect, Boolean isGreater) {
        String code = toNumber(isFakeMobile) + toNumber(isFirstTime) + toNumber(isConnect) + toNumber(isGreater);

        return parse(code);
    }

    static private String toNumber(Boolean flg) {
        return flg ? "1" : "0";
    }

    public String getCode() {
        return code;
    }
}

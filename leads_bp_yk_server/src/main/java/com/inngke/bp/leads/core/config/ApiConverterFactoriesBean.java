package com.inngke.bp.leads.core.config;

import com.inngke.bp.leads.api.AiCustomerConverterFactories;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/3/6 20:57
 */
@Configuration
public class ApiConverterFactoriesBean {

    @Bean
    public AiCustomerConverterFactories aiCustomerConverterFactories() {
        return AiCustomerConverterFactories.create();
    }
}

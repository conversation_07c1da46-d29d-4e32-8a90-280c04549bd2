/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 线索退回记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsPushBackLogDao extends BaseMapper<LeadsPushBackLog> {

}

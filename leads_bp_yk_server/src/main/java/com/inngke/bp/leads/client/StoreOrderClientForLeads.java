package com.inngke.bp.leads.client;

import com.google.common.collect.Maps;
import com.inngke.bp.store.dto.request.GetLeadsStoreOrderRequest;
import com.inngke.bp.store.dto.request.StoreOrderMobileQuery;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class StoreOrderClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store_bp_yk:}")
    private StoreOrderService storeOrderService;

    @Autowired
    private JsonService jsonService;

    public List<StoreOrderDto> getLeadsOrder(Integer bid, Set<Long> ids, Set<String> mobiles){

        StoreOrderMobileQuery request = new StoreOrderMobileQuery();
        request.setMobiles(mobiles);
        request.setLeadsIds(ids);
        request.setBid(bid);

        BaseResponse<List<StoreOrderDto>> listBaseResponse = storeOrderService.getStoreOrderListByMobile(request);
        if (!BaseResponse.responseSuccessWithNonNullData(listBaseResponse)){
            return Lists.newArrayList();
        }

        return listBaseResponse.getData();
    }

    public Map<Long, List<StoreOrderDto>> getLeadsOrder(Integer bid, Set<Long> leadsIds, LocalDateTime startTime, LocalDateTime endTime) {
        GetLeadsStoreOrderRequest request = new GetLeadsStoreOrderRequest();
        request.setBid(bid);
        request.setLeadsIds(leadsIds);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        BaseResponse<Map<Long, List<StoreOrderDto>>> response = null;
        try {
            response  = storeOrderService.getStoreOrderListByLeads(request);

        } catch (Exception e) {
            log.error("", e);
            return Maps.newHashMap();
        }
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("获取门店订单异常：{}", jsonService.toJson(response));
            return Maps.newHashMap();
        }

        return response.getData();
    }
}

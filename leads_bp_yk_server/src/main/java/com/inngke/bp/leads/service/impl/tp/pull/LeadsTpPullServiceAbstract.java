package com.inngke.bp.leads.service.impl.tp.pull;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.db.leads.entity.LeadsTpPullCondition;
import com.inngke.bp.leads.db.leads.manager.LeadsTpAccountInfoManager;
import com.inngke.bp.leads.db.leads.manager.LeadsTpLogManager;
import com.inngke.bp.leads.db.leads.manager.LeadsTpOauthManager;
import com.inngke.bp.leads.db.leads.manager.LeadsTpPullConditionManager;
import com.inngke.bp.leads.service.LeadsPullService;
import com.inngke.bp.leads.service.LeadsTpConserveService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.StringUtils;
import com.qcloud.cos.utils.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/25 10:05
 */
public abstract class LeadsTpPullServiceAbstract<T, R> implements LeadsPullService {
    private static final Map<Class<?>, Map<String, Field>> FIELD_MAP = Maps.newHashMap();

    /**
     * 同步15天前的数据
     */
    private static final Integer START_TIME_MINUS_DAYS = 15;

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpPullServiceAbstract.class);

    private static final String LEADS_CHANGE_KEY = LeadsServiceConsts.APP_ID + InngkeAppConst.CLN_STR + "tp:" + "leads";

    @Autowired
    protected LeadsTpAccountInfoManager leadsTpAccountInfoManager;

    @Autowired
    protected LeadsTpOauthManager leadsTpOauthManager;

    @Autowired
    protected LeadsTpPullConditionManager leadsTpPullConditionManager;

    @Autowired
    protected LeadsTpLogManager leadsTpLogManager;

    @Autowired
    protected JsonService jsonService;

    @Autowired
    protected LeadsTpConserveService leadsTpConserveService;

    @Autowired
    protected RedisTemplate redisTemplate;

    @Override
    public void pull(BaseBidOptRequest request) {
        pull(InngkeAppConst.EMPTY_STR, request);
    }

    @Override
    public void pull(String startTime, BaseBidOptRequest request) {
        List<T> tpLeadsList = getTpLeadsList(startTime, request);
        if (CollectionUtils.isEmpty(tpLeadsList)) {
            return;
        }
        logger.info("bid:{}获取到的线索数量:{}", request.getBid(), tpLeadsList.size());

        saveLog(request.getBid(), tpLeadsList);

        List<LeadsTpPullCondition> conditionList = getCondition(request.getBid());

        List<T> filterLeadsList = filter(conditionList, tpLeadsList);

        List<R> pushLeadsList = filterLeadsList.stream().map(this::toLeadsPushDto).collect(Collectors.toList());

        saveLeads(request.getBid(), pushLeadsList);
    }

    /**
     * 判断线索是否有变更
     *
     * @param leads 线索
     * @return true有 false 没有
     */
    protected boolean isChangeData(T leads) {
        String leadsJsonString = toJson(leads);

        String leadsMd5 = Md5Utils.md5Hex(leadsJsonString);

        String changeMemberValue = getLeadsId(leads) + InngkeAppConst.CLN_STR + leadsMd5;

        SetOperations operations = redisTemplate.opsForSet();

        if (!operations.isMember(LEADS_CHANGE_KEY, changeMemberValue)) {
            operations.add(LEADS_CHANGE_KEY, changeMemberValue);
            return true;
        }

        return false;
    }

    /**
     * 获取线索拉取条件
     *
     * @param bid
     * @return
     */
    private List<LeadsTpPullCondition> getCondition(Integer bid) {
        List<LeadsTpPullCondition> list = leadsTpPullConditionManager.list(Wrappers.<LeadsTpPullCondition>query()
                .eq(LeadsTpPullCondition.BID, bid)
                .eq(LeadsTpPullCondition.TYPE, getType().getCode())
        );

        return !CollectionUtils.isEmpty(list) ? list : Lists.newArrayList();
    }

    /**
     * 过滤有效线索
     *
     * @param tpLeadsList
     * @return
     */
    protected List<T> filter(List<LeadsTpPullCondition> conditionList, List<T> tpLeadsList) {
        for (LeadsTpPullCondition leadsTpPullCondition : conditionList) {
            String fieldName = leadsTpPullCondition.getFields();

            Boolean isEqual = leadsTpPullCondition.getEqual();
            String valueList = leadsTpPullCondition.getValueList();

            if (StringUtils.isEmpty(valueList)) {
                continue;
            }
            List<String> legitimateValueList = Lists.newArrayList(valueList.split(InngkeAppConst.COMMA_STR));

            Field fileFromClazz = FIELD_MAP.computeIfAbsent(getTpLeadsDtoClazz(), clazz -> Maps.newHashMap())
                    .computeIfAbsent(fieldName, fn -> getFieldFromClazz(fieldName));
            if (fileFromClazz == null) {
                continue;
            }

            tpLeadsList = tpLeadsList.stream().filter((tpLeads) -> {
                try {
                    Object value = fileFromClazz.get(tpLeads);
                    String valueString = String.valueOf(value);
                    return isEqual == legitimateValueList.contains(valueString);
                } catch (IllegalAccessException e) {
                    return true;
                }
            }).collect(Collectors.toList());
        }
        return tpLeadsList;
    }

    /**
     * 获取所有已授权的三方平台账号token
     */
    protected List<LeadsTpOauth> getAllLeadsTpOauth(BaseBidOptRequest request) {
        return leadsTpOauthManager.list(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.BID, request.getBid())
                .eq(LeadsTpOauth.TYPE, getType().getCode())
                .select(LeadsTpOauth.ACCOUNT_ID, LeadsTpOauth.ACCESS_TOKEN, LeadsTpOauth.CREATE_TIME)
        );
    }

    /**
     * 获取账号下的所有广告主
     *
     * @param bid
     * @param parentAccountId
     * @return
     */
    protected List<String> getAllAccountId(Integer bid, String parentAccountId) {
        return leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, bid)
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .eq(LeadsTpAccountInfo.TYPE, getType().getCode())
                .eq(LeadsTpAccountInfo.PARENT_ACCOUNT_ID, parentAccountId)
                .select(LeadsTpAccountInfo.ACCOUNT_ID)
        ).stream().map(LeadsTpAccountInfo::getAccountId).collect(Collectors.toList());
    }

    protected List<String> getAllAccountId(Integer bid) {
        return leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, bid)
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .eq(LeadsTpAccountInfo.TYPE, getType().getCode())
                .select(LeadsTpAccountInfo.ACCOUNT_ID)
        ).stream().map(LeadsTpAccountInfo::getAccountId).collect(Collectors.toList());
    }

    protected List<LeadsTpAccountInfo> getAllAccount(Integer bid, String parentAccountId) {
        return leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, bid)
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .eq(LeadsTpAccountInfo.TYPE, getType().getCode())
                .eq(LeadsTpAccountInfo.PARENT_ACCOUNT_ID, parentAccountId)

        );
    }

    protected List<LeadsTpAccountInfo> getAllAccount(int bid) {
        return leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, bid)
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .eq(LeadsTpAccountInfo.TYPE, getType().getCode())
        );
    }

    /**
     * 获取条件对应的字段
     *
     * @param fieldName 字段名称
     * @return Field
     */
    private Field getFieldFromClazz(String fieldName) {
        Class<T> clazz = getTpLeadsDtoClazz();
        for (Field declaredField : clazz.getDeclaredFields()) {
            JsonProperty annotation = declaredField.getAnnotation(JsonProperty.class);
            if (annotation != null && !StringUtils.isEmpty(annotation.value()) && annotation.value().equals(fieldName)) {
                declaredField.setAccessible(true);
                return declaredField;
            }
            if (declaredField.getName().equals(fieldName)) {
                declaredField.setAccessible(true);
                return declaredField;
            }
            SerializedName serializedNameAnnotation = declaredField.getAnnotation(SerializedName.class);
            if (serializedNameAnnotation != null &&
                    !StringUtils.isEmpty(serializedNameAnnotation.value()) &&
                    serializedNameAnnotation.value().equals(fieldName)) {
                declaredField.setAccessible(true);
                return declaredField;
            }
        }
        return null;
    }

    /**
     * 获取开始时间
     *
     * @return
     */
    protected LocalDateTime getStartTime() {
        return LocalDateTime.now().minusDays(START_TIME_MINUS_DAYS);
    }


    /**
     * 从第三方平台获取所有线索数据
     *
     * @param request
     * @return
     */
    protected abstract List<T> getTpLeadsList(String startTime, BaseBidOptRequest request);

    /**
     * 保存所有线索日志
     *
     * @param tpLeadsList
     */
    protected abstract void saveLog(Integer bid, List<T> tpLeadsList);

    /**
     * 获取线索类
     *
     * @return
     */
    protected abstract Class<T> getTpLeadsDtoClazz();

    /**
     * 保存线索
     *
     * @param filterLeadsList
     */
    protected abstract void saveLeads(Integer bid, List<R> filterLeadsList);

    /**
     * 转换线索DTO
     *
     * @param pullLeads
     * @return
     */
    protected abstract R toLeadsPushDto(T pullLeads);


    protected abstract String toJson(T obj);

    protected  abstract String getLeadsId(T leads);
}

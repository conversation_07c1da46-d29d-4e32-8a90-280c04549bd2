package com.inngke.bp.leads.dto.response;

import com.inngke.bp.user.dto.response.CustomerTagsDetailDto;
import com.inngke.bp.user.dto.response.CustomerTagsDetailTagsDto;
import com.inngke.bp.user.dto.response.CustomerTagsListDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/9
 **/
public class LeadsTagsDetailDto {

    public LeadsTagsDetailDto() {
    }

    public LeadsTagsDetailDto(CustomerTagsDetailDto data) {
        this.tags = data.getTags();
        this.enterpriseTags = data.getEnterpriseTags();
        this.userTags = data.getUserTags();
    }


    /**
     * 客户标签列表
     */
    private List<CustomerTagsDetailTagsDto> tags;
    /**
     * 企业标签列表
     */
    private List<CustomerTagsListDto> enterpriseTags;
    /**
     * 用户标签列表
     */
    private List<CustomerTagsListDto> userTags;


    public List<CustomerTagsDetailTagsDto> getTags() {
        return tags;
    }

    public void setTags(List<CustomerTagsDetailTagsDto> tags) {
        this.tags = tags;
    }

    public List<CustomerTagsListDto> getEnterpriseTags() {
        return enterpriseTags;
    }

    public void setEnterpriseTags(List<CustomerTagsListDto> enterpriseTags) {
        this.enterpriseTags = enterpriseTags;
    }

    public List<CustomerTagsListDto> getUserTags() {
        return userTags;
    }

    public void setUserTags(List<CustomerTagsListDto> userTags) {
        this.userTags = userTags;
    }
}

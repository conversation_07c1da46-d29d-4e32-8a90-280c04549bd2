package com.inngke.bp.leads.service.impl.contact;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.LeadsFollowContentUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsContactFollowService;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/1 16:42
 */
@Service
public abstract class AbstractLeadsContactFollowService implements LeadsContactFollowService {

    @Autowired
    protected LeadsFollowManager leadsFollowManager;

    @Autowired
    protected LeadsEsService leadsEsService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Override
    public BaseResponse<Boolean> createContactFollow(Leads leads, PrivateVoiceRecordDTO dto) {
        //生成跟进内容
        String content = generateContactFollowContent(leads, dto);

        //创建跟进记录
        LeadsFollow leadsFollow = createLeadsFollow(leads, content);
        leadsFollow.setCreateTime(dto.getVoiceStart());
        Long guideId = dto.getGuideId();
        StaffDto staff = staffClientForLeads.getStaffByCid(dto.getBid(), guideId);
        if (Objects.nonNull(staff)) {
            leadsFollow.setStaffId(staff.getId());
        }

        //保存跟进记录
        Boolean contactFollow = leadsFollowManager.createContactFollow(leadsFollow);
        this.setContactIn24Property(leads, dto);
        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(leads.getBid());
        leadsUpdateRequest.setIds(Lists.newArrayList(leads.getId()));
        leadsUpdateRequest.setRefreshEs(true);
        leadsEsService.updateDocs(leadsUpdateRequest);

        return BaseResponse.success(contactFollow);
    }

    protected void setContactIn24Property(Leads leads, PrivateVoiceRecordDTO dto) {

        //计算24小时内联系
        LocalDateTime voiceStart = dto.getVoiceStart();
        LocalDateTime distributeTime = leads.getDistributeTime();
        long between = ChronoUnit.HOURS.between(voiceStart, distributeTime);

        leadsManager.update(
                Wrappers.<Leads>update()
                        .eq(Leads.BID, leads.getBid())
                        .eq(Leads.ID, leads.getId())
                        .set(Leads.LAST_CONTACT_TIME, voiceStart)
                        .set(leads.getFirstContactTime() != null, Leads.FIRST_CONTACT_TIME, voiceStart)
                        .set(between <= 24L, Leads.CONTACT_IN_24, 1)
        );
    }



    /**
     * 创建跟进记录
     *
     * @param leads Leads
     * @return leadsFollow
     */
    protected LeadsFollow createLeadsFollow(Leads leads, String content) {

        LeadsStatusEnum newStatus = getNewStatus(leads);
        //拼装跟进记录
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setLeadsId(leads.getId());
        leadsFollow.setBid(leads.getBid());
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setFollowContent(content);
        leadsFollow.setLeadsStatus(newStatus.getStatus());
        leadsFollow.setStaffId(0L);
        leadsFollow.setUserId(0L);
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        leadsFollow.setCreateTime(LocalDateTime.now());

        return leadsFollow;
    }

    /**
     * 获取最新状态
     *
     * @param leads
     * @return
     */
    protected LeadsStatusEnum getNewStatus(Leads leads) {
        return LeadsFollowContentUtil.getNewStatus(leads);
    }

    /**
     * 生成跟进记录内容
     *
     * @return String
     */
    protected abstract String generateContactFollowContent(Leads leads, PrivateVoiceRecordDTO dto);

    protected String getMinuteVoiceTime(Integer voiceTime) {
        int minute = voiceTime / 60;
        return minute > 0 ? minute + "分" : "";
    }

    protected String getSecondVoiceTime(Integer voiceTime) {
        int second = voiceTime % 60;
        return second > 0 ? second + "秒" : "";
    }

    protected String getTalkTimeText(Integer voiceTime) {
        return getMinuteVoiceTime(voiceTime) + getSecondVoiceTime(voiceTime);
    }
}

package com.inngke.bp.leads.service.message;

import java.util.Map;

public enum MessageTypeEnum {

    REMINDERS_TO_FOLLOWED("线索待跟进提醒", "reminders_to_followed", "Clue/Clue/myClue/myClue"),
    FOLLOW_TEMPLATE("跟进计划提醒", "leads_follow_plan","Clue/Clue/Cluetit/Cluetit" ),
    LEADS_DAILY_NOTIFY("线索每日提醒", "leads_daily_notify","Clue/Clue/myClue/myClue" ),
    LEADS_FORWARD_ROLLBACK("线索回收提醒", "clue_retrieval", "Clue/Clue/myClue/myClue");

    private final String name;

    private final String templateCode;

    private final String path;

    MessageTypeEnum(String name, String templateCode, String path) {
        this.name = name;
        this.templateCode = templateCode;
        this.path = path;
    }

    public String getName() {
        return name;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public String getPath(Map<String, String> params) {
        StringBuilder path = new StringBuilder(this.path);
        path.append("?");
        for (String key : params.keySet()) {
            path.append(key).append("=").append(params.get(key)).append("&");
        }

        return path.toString();
    }

    public String getPath() {
        return this.path;
    }
}

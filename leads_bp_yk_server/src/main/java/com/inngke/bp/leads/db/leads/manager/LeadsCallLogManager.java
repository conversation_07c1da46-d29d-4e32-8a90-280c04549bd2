package com.inngke.bp.leads.db.leads.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.db.leads.entity.LeadsCallLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface LeadsCallLogManager extends IService<LeadsCallLog> {

    Map<Long, LocalDateTime> getLeadsFirstCallTime(Integer bid, Set<Long> leadsIds);

   LocalDateTime getLeadsFirstCallTime(Integer bid, Long leadsId);

    List<LeadsCallLog> getLeadsCallLog(Integer bid, Long id,Integer type);
}

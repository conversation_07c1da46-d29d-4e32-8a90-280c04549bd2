package com.inngke.bp.leads.core.utils;

import com.inngke.bp.leads.dto.response.LeadsChannelValueDto;
import com.inngke.bp.leads.service.impl.LeadsChannelCacheFactory;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

public class LeadsChannelUtil {


    public static String getName(Integer bid, Integer value) {
        LeadsChannelCacheFactory.LeadsChannelValueCache cache = LeadsChannelCacheFactory.cacheFactory.getCache(bid);
        return cache.getNameByValue(value);
    }
}

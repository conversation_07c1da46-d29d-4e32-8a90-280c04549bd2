package com.inngke.bp.leads.service.message.sender;

import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.service.JsonService;
import com.inngke.ip.reach.service.TemplateMessageSendService;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Service
public abstract class MessageSenderServiceAbs implements MessageSenderService{
    private static final Logger logger = LoggerFactory.getLogger(MessageSenderServiceAbs.class);

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private CustomerGetServiceClientForLeads customerGetServiceClientForLeads;

    @Autowired
    protected JsonService jsonService;


    protected boolean checkAndInitSendTargetMemberData(MessageContext ctx) {
        Long targetSid = ctx.getTargetSid();
        Integer bid = ctx.getBid();

        StaffDto staff = staffClientForLeads.getStaffById(bid, targetSid);
        if (Objects.isNull(staff)) {
            logger.info("获取员工信息失败staffId:{}", targetSid);
            return false;
        }

        CustomerDto customer = new CustomerDto();

        if (Objects.nonNull(staff.getCustomerId()) && staff.getCustomerId() > 0L) {
            customer = customerGetServiceClientForLeads.getCustomerById(bid, staff.getCustomerId());
            ctx.setTargetWxPubOpenId(customer.getWxPubOpenId());
        }

        if (!StringUtils.isEmpty(staff.getQyUserId())) {
            ctx.setTargetQyUserId(staff.getQyUserId());
        }

        if (StringUtils.isEmpty(ctx.getTargetQyUserId()) && StringUtils.isEmpty(ctx.getTargetWxPubOpenId())) {
            logger.info("获取目标对象信息失败 customer:{},staff:{}",  jsonService.toJson(customer), jsonService.toJson(staff));
            return false;
        }

        return true;
    }

    /**
     * 准备发送给线索分配员工的消息
     *
     * @param ctx
     * @return
     */
    protected boolean prepareSendToLeadsDistributeStaff(MessageContext ctx){
        Leads leads = ctx.getLeads();
        if (Objects.isNull(leads) || Objects.isNull(leads.getDistributeStaffId()) || leads.getDistributeStaffId() == 0L){
            return false;
        }

        ctx.setTargetSid(leads.getDistributeStaffId());

        return checkAndInitSendTargetMemberData(ctx);
    }

    protected TemplateMessageSendRequestBuilder getTemplateRequestBuilder(MessageContext ctx){
        TemplateMessageSendRequestBuilder builder = TemplateMessageSendRequestBuilder
                .newBuilder(ctx.getBid(), getMessageType().getTemplateCode());

        if (!StringUtils.isEmpty(ctx.getTargetQyUserId())){
            builder.setWxQyUserId(ctx.getTargetQyUserId());
        }

        if (!StringUtils.isEmpty(ctx.getTargetWxPubOpenId())){
            builder.setWxPubOpenId(ctx.getTargetWxPubOpenId());
        }

        return builder;
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.annotation.LeadsIdValidator;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.notify.builder.LeadsDistributeContentBuilder;
import com.inngke.bp.leads.service.*;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 客户线索
 * @since 2021/9/7 3:49 PM
 */
@RestController
@RequestMapping("/api/leads")
@Validated
public class LeadsApiController {
    @Autowired
    private LeadsService leadsService;

    @Autowired
    private LeadsGetService leadsGetService;

    @Autowired
    private LeadsChangeService leadsChangeService;

    @Autowired
    private LeadsStatisticsService leadsStatisticsService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsDistributeContentBuilder leadsDistributeContentBuilder;

    @Autowired
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    @Resource
    private LeadsPushBackLogService leadsPushBackLogService;

    @Resource
    private LeadsDocService leadsDocService;
    @Resource
    private AmountMaskingService amountMaskingService;


    /**
     * 线索搜索
     *
     * @param bid   商户ID
     * @param query 筛选条件
     * @return 分页的线索列表
     */
    @GetMapping("/list")
    public BaseResponse<LeadsListVo> search(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsQuery query,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        query.setOperatorId(operatorId);
        query.setBid(bid);
        if(Objects.isNull(jwtPayload) || Objects.isNull(jwtPayload.getSid())){
            query.setStaffId(-1L);
        }
//        return leadsService.search(query);
        return leadsDocService.searchLeads(query);
    }


    /**
     * 线索搜索(PC后台)
     *
     * @param bid   商户ID
     * @param query 筛选条件
     * @return 分页的线索列表
     */
    @GetMapping("/manage-list")
    public BaseResponse<LeadsListVo> query(
            @RequestHeader int bid,
            SearchLeadsRequest query,
            @RequestAttribute JwtPayload jwtPayload) {
        query.setBid(bid);
        query.setSid(jwtPayload.getSid());

        return leadsGetService.search(query);
    }

    /**
     * 历史线索搜索
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      筛选条件
     * @return 分页的线索列表
     */
    @GetMapping("/history-list")
    public BaseResponse<LeadsListVo> historyList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsQuery query
    ) {
        query.setOperatorId(operatorId);
        query.setBid(bid);
        return leadsService.historyList(query);
    }

    /**
     * 历史线索数据
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param query      筛选条件
     * @return 分页的线索列表
     */
    @GetMapping("/history-count")
    public BaseResponse<Integer> historyCount(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsQuery query
    ) {
        query.setOperatorId(operatorId);
        query.setBid(bid);
        return leadsService.historyCount(query);
    }

    /**
     * 导出查询数据
     *
     * @param query 筛选条件
     * @return 导出的数据文件地址
     */
    @GetMapping("/export")
    public BaseResponse<String> export(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsQuery query,
            @RequestAttribute JwtPayload jwtPayload
    ) throws IOException {
        query.setOperatorId(operatorId);
        query.setPcOperator(jwtPayload.getCid());
        query.setBid(bid);
        return leadsService.export(query);
    }

    /**
     * 获取线索详情（后端修改）
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param id         线索ID
     * @return 线索详情
     */
    @GetMapping("/{id:\\d+}")
    public BaseResponse<LeadsDto> getLeads(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long id
    ) {
        LeadsGetRequest request = new LeadsGetRequest();
        request.setId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsService.getLeads(request);
    }

    /**
     * 获取线索详情（供前端）
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param id         线索ID
     * @return 线索详情
     */
    @GetMapping("/{id:\\d+}/detail")
    public BaseResponse<LeadsVo> getLeadsDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long id
    ) {
        LeadsGetRequest request = new LeadsGetRequest();
        request.setId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsService.getLeadsDetail(request);
    }

    /**
     * 获取线索有效经销商列表
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return 经销商列表
     */
    @GetMapping("/agents")
    public BaseResponse<List<LeadsAgentSimpleInfoDto>> getAgentList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        BaseBidOptRequest request = new BaseBidOptRequest();
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsService.getAgentList(request);
    }


    /**
     * 获取线索有效负责人列表
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return 经销商列表
     */
    @GetMapping("/staffs")
    public BaseResponse<List<LeadsStaffSimpleInfoDto>> getStaffName(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsStaffNameGetRequest request
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsService.getStaffName(request);
    }


    /**
     * 添加线索
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    线索信息
     * @return 保存成功后的线索信息
     */
    @PostMapping
    public BaseResponse<LeadsDto> add(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsAddRequest request
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsChangeService.add(request);
    }

    /**
     * 修改线索信息
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    修改请求
     * @return 修改成功后的线索信息
     */
    @PutMapping("/{id:\\d+}")
    public BaseResponse<LeadsDto> save(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long id,
            @RequestBody LeadsUpdateRequest request
    ) {
        request.setId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsChangeService.save(request);
    }

    /**
     * 修改线索状态
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    修改请求
     * @return 修改成功后的线索信息
     */
    @PutMapping("/updateStatus")
    public BaseResponse<Boolean> updateStatus(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsStatusUpdateRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(jwtPayload.getCid());
        request.setBid(bid);
        return leadsChangeService.updateStatus(request);
    }

    /**
     * 修改线索信息v2
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return
     */
    @PutMapping("")
    public BaseResponse<Boolean> updateLeads(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsUpdateRequest request,
            @RequestAttribute JwtPayload jwtPayload) {
        request.setOperatorId(jwtPayload.getCid());
        request.setBid(bid);
        request.setOperatorStaffId(jwtPayload.getSid());

        return leadsChangeService.updateLeads(request);
    }

    /**
     * 批量分配线索
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    分配请求
     * @return 是否分配成功
     */
    @PostMapping("/distribute")
    public BaseResponse<LeadsDistributeResultDto> distribute(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsDistributeRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setSid(jwtPayload.getSid());
        return leadsChangeService.distributeWithFollowStaff(request);
    }


    /**
     * 线索分配预览
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    分配预览请求
     * @return 分配预览结果集
     */
    @PostMapping("/distributePreview")
    public BaseResponse<List<LeadsDistributePreviewDto>> distributePreview(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsDistributeRequest request
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsChangeService.distributePreview(request);
    }


    /**
     * 退回线索
     *
     * @param bid     商户ID
     * @param request 退回请求
     * @return 是否退回成功
     */
    @PostMapping("/push-back")
    public BaseResponse<Boolean> pushBack(
            @RequestHeader int bid,
            @RequestBody @Validated LeadsPushBackRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(jwtPayload.getSid());
        request.setOperatorUserId(jwtPayload.getCid());
        request.setBid(bid);
        return leadsChangeService.pushBack(request);
    }

    /**
     * 驳回线索退回
     *
     * @param jwtPayload 会话信息
     * @param pushBackId 退回记录id
     * @return 操作结果
     */
    @PutMapping("/push-back/{pushBackId:\\d+}/reject")
    public BaseResponse<Long> rejectPushBack(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long pushBackId,
            @RequestBody RejectLeadsPushBackRequest request

    ) {
        request.setPushBackLogId(pushBackId);
        request.setBid(jwtPayload.getBid());
        request.setOperatorId(jwtPayload.getCid());
        request.setOperatorStaffId(jwtPayload.getSid());


        return leadsPushBackLogService.rejectLeadsPushBack(request);
    }

    /**
     * 删除线索
     * 仅允许删除分配失败和退回状态的线索
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    删除请求
     * @return 是否删除成功
     */
    @DeleteMapping
    public BaseResponse<Boolean> delete(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsBatchRequest request
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsChangeService.delete(request);
    }

    /**
     * 转移线索
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    转移请求
     * @return 是否转移成功
     */
    @PostMapping("/forward")
    public BaseResponse<Boolean> forward(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated LeadsForwardRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(operatorId);
        request.setOperatorStaffId(jwtPayload.getSid());
        request.setBid(bid);
        return leadsChangeService.forward(request);
    }

    /**
     * 合伙人线索转移
     *
     * @param bid        商户编号
     * @param operatorId 操作者ID
     * @return 是/否
     */
    @PostMapping(value = "/transfer")
    public BaseResponse<Boolean> transfer(
            @RequestHeader(InngkeApiConst.STR_BID) int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @Validated @RequestBody LeadTransferRequest request) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsChangeService.transfer(request);
    }

    /**
     * 线索回收
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    线索回收请求
     * @return 是否回收成功
     */
    @PostMapping("/recovery")
    public BaseResponse<Boolean> recoveryLeadsList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsRecoveryRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(jwtPayload.getCid());
        request.setBid(bid);
        request.setStaffId(jwtPayload.getSid());
        return leadsChangeService.recoveryLeads(request);
    }

    /**
     * 查询有线索管理权限的员工对应的线索统计列表（包括部门线索统计和员工线索统计）数据
     *
     * @param bid        商户编号
     * @param operatorId 操作员编号
     * @param request    入参
     * @return 回参
     */
    @Deprecated
    @PostMapping("/getLeadsStatisticsInfos")
    public BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsInfos(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsDepStaffStatisticsQuery request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setCustomerId(jwtPayload.getCid());
        return leadsService.getLeadsStatisticsList(request);
    }


    @PostMapping("/getLeadsStatisticsInfos2")
    public BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsInfos2(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsDepStaffStatisticsQuery request,
            @RequestAttribute JwtPayload jwtPayload
    ) throws IOException {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setCustomerId(jwtPayload.getCid());
        request.setOperatorStaffId(jwtPayload.getSid());

        BaseResponse<LeadsDepartmentStaffStatisticsDto> response = leadsService.getLeadsStatisticsList2(request);
        LeadsDepartmentStaffStatisticsDto data = response.getData();

        data.setDepartments(amountMaskingService.departmentDoMasking(jwtPayload.getSid(), data.getDepartments()));
        data.setStaffs(amountMaskingService.staffDoMasking(jwtPayload.getSid(), data.getStaffs()));
        return BaseResponse.success(data);
    }


    /**
     * 查询有线索管理权限的员工对应的线索统计列表（包括部门线索统计和员工线索统计）数据
     *
     * @param bid        商户编号
     * @param operatorId 操作员编号
     * @param request    入参
     * @return 回参
     */
    @PostMapping("/getLeadsStatisticsInfosV2")
    public BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsInfosV2(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsDepStaffStatisticsQuery request,
            @RequestAttribute JwtPayload jwtPayload
    ) throws IOException {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setCustomerId(jwtPayload.getCid());
        request.setOperatorStaffId(jwtPayload.getSid());
        BaseResponse<LeadsDepartmentStaffStatisticsDto> leadsStatisticsInfosV2 = leadsService.getLeadsStatisticsInfosV2(request);
        LeadsDepartmentStaffStatisticsDto data = leadsStatisticsInfosV2.getData();
        data.setDepartments(amountMaskingService.departmentDoMasking(jwtPayload.getSid(), data.getDepartments()));
        data.setStaffs(amountMaskingService.staffDoMasking(jwtPayload.getSid(), data.getStaffs()));
        
        return BaseResponse.success(data);
    }

    /**
     * 批量删除线索信息
     *
     * @param bid        商户id
     * @param operatorId 操作者ID
     * @param request    请求体
     * @return 是否删除成功
     */
    @DeleteMapping("/delete-batch")
    public BaseResponse<Boolean> deleteBatch(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsDeleteBatchRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsService.deleteBatch(request);
    }


    /**
     * 线索搜索模糊
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return 全局模糊搜索线索
     */
    @GetMapping("/search")
    public BaseResponse<LeadsListVo> searchLeads(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload,
            LeadsQuery request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setSid(jwtPayload.getSid());

        return leadsService.searchLeads(request);
    }

    /**
     * 线索转交员工信息列表
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return 管理者对应部门以及部门下员工信息
     */
    @GetMapping("/department-staff-list")
    public BaseResponse<LeadsTransmitStaffInfoListDto> LeadsTransmitStaffList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsDepEsGetRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsService.leadsTransmitStaffList(request);
    }

    /**
     * 线索转交员工信息列表
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return 管理者对应部门以及部门下员工信息
     */
    @GetMapping("/department-guide-list")
    public BaseResponse<LeadsTransmitStaffInfoListDto> LeadsTransmitGuideList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsDepEsGetRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsService.leadsTransmitGuideList(request);
    }

    /**
     * 获取线索渠道列表
     *
     * @return
     */
    @GetMapping("/channel-list")
    public BaseResponse<List<LeadsChannelVo>> getChannelList() {
        return leadsService.getChannelList();
    }


    /**
     * 获取线索数据来源
     *
     * @return
     */
    @GetMapping("/channel-type-list")
    public BaseResponse<List<LeadsChannelVo>> getChannelTypeList() {
        return leadsService.getChannelTypeList();
    }

    /**
     * 获取线索来源
     *
     * @return
     */
    @GetMapping("/channel-source-list")
    public BaseResponse<List<LeadsChannelVo>> getChannelSourceList() {
        return leadsService.getChannelSourceList();
    }


    @GetMapping("/testMq")
    public BaseResponse<Boolean> test(
            @RequestHeader int bid
    ) {
        LeadsAddIncludeStaffRequest leadsAddIncludeStaffRequest = new LeadsAddIncludeStaffRequest();
        leadsAddIncludeStaffRequest.setBid(bid);
        leadsAddIncludeStaffRequest.setAreaName("小店区");
        leadsAddIncludeStaffRequest.setOperatorId(0L);
        leadsAddIncludeStaffRequest.setProvinceName("山西省");
        leadsAddIncludeStaffRequest.setCityName("太原市");
        leadsAddIncludeStaffRequest.setDistributeStaffId(123L);
        leadsAddIncludeStaffRequest.setCustomerUid(0L);
        leadsAddIncludeStaffRequest.setCustomerId(0L);
        leadsAddIncludeStaffRequest.setDistributeStaffId(418L);
        leadsAddIncludeStaffRequest.setDistributeAgentId(0L);
        leadsAddIncludeStaffRequest.setName("测试");
        leadsAddIncludeStaffRequest.setMobile("13826618845");
        BaseResponse<LeadsDto> leadsDtoBaseResponse = leadsChangeService.add(leadsAddIncludeStaffRequest);
        System.out.println(leadsDtoBaseResponse.toString());
        return BaseResponse.success(true);
    }

    /**
     * 线索绑定导购-线索隐号关系
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return
     */
    @PostMapping("/bind/mobile")
    public BaseResponse<BindMobileDto> bind(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsPrivateNumberBindRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setBid(bid);
        return leadsService.bindMobile(request);
    }

    /**
     * 模糊匹配线索客户名
     *
     * @param bid
     * @param operatorId
     * @param name
     * @return
     */
    @GetMapping("/customer")
    public BaseResponse<List<LeadsNameDto>> getLeadsName(
            String keyword,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        LeadsAddRequest request = new LeadsAddRequest();
        request.setOperatorId(jwtPayload.getCid());
        request.setBid(jwtPayload.getBid());
        request.setCustomerId(jwtPayload.getCid());
        request.setStaffId(jwtPayload.getSid());
        request.setName(keyword);
        return leadsService.getLeadsName(request);
    }

    /**
     * 获取线索是否删除/已流失
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param id         线索ID
     * @return true-线索正常
     */
    @GetMapping("leadsIdValidator/{id:\\d+}")
    public BaseResponse<Boolean> LeadsIdValidator(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable @LeadsIdValidator long id
    ) {
        return BaseResponse.success(true);
    }

    /**
     * 批量修改线索创建人
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    修改请求
     * @return 修改成功后的线索信息
     */
    @PutMapping("/updateCreateBy")
    public BaseResponse<Boolean> updateCreateBy(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsCreateUpdateRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(jwtPayload.getCid());
        request.setBid(bid);
        return leadsChangeService.updateStaffId(request);
    }

}


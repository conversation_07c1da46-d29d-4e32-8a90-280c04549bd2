package com.inngke.bp.leads.service.message.sender;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.bp.leads.service.message.context.RemindersToFollowedContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Deprecated(since = "需求迭代，需要支持自定线索跟进时间提醒")
public class RemindersToFollowedSenderService extends MessageSenderServiceAbs {
    private static final Logger logger = LoggerFactory.getLogger(RemindersToFollowedSenderService.class);

    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.REMINDERS_TO_FOLLOWED;
    }

    @Override
    public void init(MessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(MessageContext context) {
        RemindersToFollowedContext ctx = context.transferContext();
        if (CollectionUtils.isEmpty(ctx.getLeadsList())){
            logger.info("准备发送线索跟进提醒失败:线索数据为空!");
            return null;
        }

        if (!checkAndInitSendTargetMemberData(ctx)) {
            logger.info("准备发送线索跟进提醒失败");
            return null;
        }

        String timeOut = ctx.getTimeOut();

        Map<String, String> params = Maps.newHashMap();
        params.put("umaEvent", "clueRemind");
        params.put("staffId", String.valueOf(ctx.getTargetSid()));

        if (ctx.getLeadsList().size() == 1) {
            params.put("leadsId", String.valueOf(ctx.getLeadsList().get(0).getId()));
            params.put("id", String.valueOf(ctx.getLeadsList().get(0).getId()));
        }

        List<String> nameList = ctx.getLeadsList().stream().map(Leads::getName).filter(StringUtils::isNotBlank)
                .limit(2).collect(Collectors.toList());
        String names = nameList.size() > 1 ? Joiner.on("，").join(nameList) + "..." :
                nameList.stream().findFirst().orElse(InngkeAppConst.EMPTY_STR);

        List<String> mobileList = ctx.getLeadsList().stream().map(Leads::getMobile).filter(StringUtils::isNotBlank)
                .limit(2).map(this::cellPhoneNumberDesensitization).collect(Collectors.toList());
        String mobiles = mobileList.size() > 1 ? Joiner.on("，").join(mobileList) + "..." :
                mobileList.stream().findFirst().orElse("未知");

        String createTime = ctx.getLeadsList().stream().filter(leads -> Objects.nonNull(leads.getDistributeTime()))
                .min(Comparator.comparing(Leads::getDistributeTime))
                .map(Leads::getDistributeTime)
                .map(distributeTime -> DateTimeUtils.format(distributeTime, DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                .orElse(InngkeAppConst.EMPTY_STR);

        return getTemplateRequestBuilder(ctx)
                .setTitle("您有" + ctx.getLeadsList().size() + "条线索超时未跟进！")
                .setVar("title", "您有" + ctx.getLeadsList().size() + "条线索超时未跟进！")
                .setVar("name", names)
                .setVar("mobile", StringUtils.isEmpty(ctx.getTargetQyUserId()) ? (mobileList.get(0) + "..") :mobiles)
                .setVar("followType", "总部派发")
                .setVar("followContent", "请及时跟进处理")
                .setVar("createTime", createTime)
                .setVar("content", "请及时跟进处理以免错过最佳跟进时间！")
                .setMpPagePath(getMessageType().getPath(params))
                .build();
    }

    /**
     * 手机号脱敏
     */
    private String cellPhoneNumberDesensitization(String mobile){
        if (StringUtils.isBlank(mobile)){
            return mobile;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}

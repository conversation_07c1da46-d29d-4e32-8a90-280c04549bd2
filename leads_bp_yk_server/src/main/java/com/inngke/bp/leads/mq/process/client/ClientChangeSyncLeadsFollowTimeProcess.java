package com.inngke.bp.leads.mq.process.client;

import com.inngke.bp.client.dto.response.client.ClientFollowDto;
import com.inngke.bp.client.service.ClientFollowOpenService;
import com.inngke.bp.leads.client.ClientGetClientFollowForLeads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class ClientChangeSyncLeadsFollowTimeProcess implements ClientChangeProcess{

    @Autowired
    private ClientGetClientFollowForLeads clientGetClientFollowForLeads;

    @Autowired
    private LeadsManager leadsManager;

    @Override
    public void process(ClientTransferMessage message) {
        log.info("message content: {}", message);

        Integer bid = message.getBid();
        Long id = message.getId();

        ClientFollowDto lastFollow = clientGetClientFollowForLeads.getLastByClientId(bid, id);
        if (Objects.isNull(lastFollow)){
            return;
        }

        leadsManager.updateLeadsFollowTimeByClientId(bid, lastFollow.getClientId(), DateTimeUtils.MillisToLocalDateTime(lastFollow.getCreateTime()));
    }
}

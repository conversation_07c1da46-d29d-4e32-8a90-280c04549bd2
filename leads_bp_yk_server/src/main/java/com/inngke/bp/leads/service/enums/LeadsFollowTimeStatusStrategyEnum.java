package com.inngke.bp.leads.service.enums;

/**
 * @author: moqing<PERSON>
 * @chapter
 * @section
 * @since 2022/4/7 11:41
 */
public enum LeadsFollowTimeStatusStrategyEnum {
    //线索状态：-5=已回收 -4=删除 -3=无效线索 -2=分配失败 -1=员工退回 0=待分配 1=未联系
    // 2=24h内联系 3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交
    NoAvail(-3, "leadsFollowTimeNoAvailStrategy"),
    Contact(2, "leadsFollowTimeContactStrategy"),
    ContactSuccess(3, "leadsFollowTimeContactSuccessStrategy"),
    Measuring(5, "leadsFollowTimeMeasuringStrategy"),
    ArrivalStore(6, "leadsFollowTimeArrivalStoreStrategy"),
    OfferPrice(7, "leadsFollowTimeOfferPriceStrategy"),
    Deposit(8, "leadsFollowTimeDepositStrategy"),
    OrderSuccess(11, "leadsFollowTimeOrderSuccessStrategy"),
    Install(10, "leadsFollowTimeInstallStrategy"),
    ;
    private int status;

    private String bean;

    LeadsFollowTimeStatusStrategyEnum(int status, String bean) {
        this.status = status;
        this.bean = bean;
    }

    public int getStatus() {
        return status;
    }

    public String getBean() {
        return bean;
    }

    public static String parse(Integer status) {
        if (status == null) {
            return null;
        }

        for (LeadsFollowTimeStatusStrategyEnum item : LeadsFollowTimeStatusStrategyEnum.values()) {
            if (item.status == status) {
                return item.getBean();
            }
        }
        return null;
    }
}

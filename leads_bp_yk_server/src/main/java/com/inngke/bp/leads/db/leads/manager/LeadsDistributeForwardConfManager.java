/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsDistributeForwardConf;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 员工线索自动转发配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
public interface LeadsDistributeForwardConfManager extends IService<LeadsDistributeForwardConf> {

    boolean saveOrUpdateByStaffId(LeadsDistributeForwardConf entity);

    boolean existForward(Integer bid, Long forwardStaffId);

    boolean isForwarder(Integer bid, Long staffId);

    Long getByStaffId(Integer bid, Long staffId);

}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeChannelConf;
import com.inngke.bp.leads.db.leads.dao.LeadsDistributeChannelConfDao;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeChannelConfManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 线索渠道区域接收人配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsDistributeChannelConfManagerImpl extends ServiceImpl<LeadsDistributeChannelConfDao, LeadsDistributeChannelConf> implements LeadsDistributeChannelConfManager {

    @Override
    public Map<Integer, List<Long>> getChannelConfIdsMap(Integer bid) {
        Map<Integer, List<Long>> channelConfIdsMap = Maps.newHashMap();
        List<LeadsDistributeChannelConf> list = list(Wrappers.<LeadsDistributeChannelConf>query().eq(LeadsDistributeChannelConf.BID, bid));
        list.forEach(conf-> Splitter.on(InngkeAppConst.COMMA_STR).split(conf.getChannelIds())
                        .forEach(channelId-> channelConfIdsMap.computeIfAbsent(Integer.valueOf(channelId), key-> Lists.newArrayList()).add(conf.getId())));

        return channelConfIdsMap;
    }
}

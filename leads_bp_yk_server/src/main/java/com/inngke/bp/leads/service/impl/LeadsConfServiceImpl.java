package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.core.converter.LeadsConfConverter;
import com.inngke.bp.leads.db.leads.entity.LeadsConf;
import com.inngke.bp.leads.db.leads.manager.LeadsConfManager;
import com.inngke.bp.leads.dto.request.LeadsConfSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsConfDto;
import com.inngke.bp.leads.service.LeadsConfService;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/8 3:56 PM
 */
@DubboService(version = "1.0.0")
@Service
public class LeadsConfServiceImpl implements LeadsConfService {
    private static final Logger logger = LoggerFactory.getLogger(LeadsConfServiceImpl.class);

    @Autowired
    private LeadsConfManager leadsConfManager;

    /**
     * 开启线索功能
     *
     * @param request 开启请求
     * @return 是否开启成功
     */
    @Override
    public BaseResponse<Boolean> enableLeads(BaseBidOptRequest request) {
        LeadsConf leads = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, request.getBid()));

        //判断线索是否存在，存在：判断是否开启，未开启直接开启，已开启返回失败. 不存在，直接开启，并设置默认值
        if (ObjectUtils.isNull(leads)) {
            leads = new LeadsConf();
            //设置线索对象默认值
            leads.setId(request.getBid());
            leads.setEnable(true);
            leads.setDistributeType(0);
            leads.setForwardEnable(true);
            leads.setCreateTime(LocalDateTime.now());
        } else {
            if (leads.getEnable()) {
                logger.warn("尝试添加线索:{},但线索已开启", leads.getId());
                return BaseResponse.error("线索已开启", false);
            }
            leads.setEnable(true);
        }
        leadsConfManager.saveOrUpdate(leads);
        return BaseResponse.success(true);

    }

    /**
     * 关闭线索功能
     *
     * @param request 关闭请求
     * @return 是否关闭成功
     */
    @Override
    public BaseResponse<Boolean> disableLeads(BaseBidOptRequest request) {
        LeadsConf leads = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, request.getBid()));
        if (ObjectUtils.isNull(leads)) {
            logger.warn("尝试关闭线索:{},但是未找到线索", request.getBid());
            return BaseResponse.error("未找到线索" + request.getBid(), false);
        }
        Boolean enable = leads.getEnable();
        if (enable) {
            leads.setEnable(false);
            leadsConfManager.updateById(leads);
        } else {
            return BaseResponse.error("线索已关闭, 请勿重复关闭", false);
        }
        return BaseResponse.success(true);
    }

    /**
     * 设置线索配置
     *
     * @param request 配置请求
     * @return 保存成功后的线索配置
     */
    @Override
    public BaseResponse<LeadsConfDto> setLeadsConf(LeadsConfSaveRequest request) {
        LeadsConf leads = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, request.getBid()));
        if (ObjectUtils.isNull(leads)) {
            logger.warn("尝试配置线索，未找到线索:{}", request.getBid());
            return BaseResponse.error("未找到线索配置" + request.getBid());
        }
        //如果存在，将请求参数设置回对象保存
        leads.setEnable(request.getEnable());
        leads.setDistributeType(request.getDistributeType());
        leads.setForwardEnable(request.getForwardEnable());
        leads.setPushbackEnable(request.getPushbackEnable());
        leads.setRepeatDistributionWay(request.getRepeatDistributionWay());
        leads.setPreFollowEnable(Optional.ofNullable(request.getPreFollowEnable()).orElse(leads.getPreFollowEnable()));
        leads.setOpenRepeatRemove(Optional.ofNullable(request.getOpenRepeatRemove()).orElse(leads.getOpenRepeatRemove()));
        leads.setRepeatRemoveDay(Optional.ofNullable(request.getRepeatRemoveDay()).orElse(leads.getRepeatRemoveDay()));
        leads.setPushbackReason(request.getPushbackReason());
        leads.setPushbackImage(request.getPushbackImage());
        leads.setLeadsInvalidReason(request.getLeadsInvalidReason());
        leads.setDisplayRepeat(request.getDisplayRepeat());
        leads.setTextMessageNotify(request.getTextMessageNotify());
        if(!CollectionUtils.isEmpty(request.getLeadsFollowNotifyList())){
            leads.setLeadsFollowNotify(String.join(",",request.getLeadsFollowNotifyList().stream().map(String::valueOf).collect(Collectors.toList())));
        }
        leads.setUpdateTime(LocalDateTime.now());
        leadsConfManager.updateById(leads);

        LeadsConfDto leadsConfDto = LeadsConfConverter.toLeadsConfDto(leads);
        return BaseResponse.success(leadsConfDto);
    }

    /**
     * 获取线索配置
     *
     * @param request 获取请求
     * @return 线索配置
     */
    @Override
    public BaseResponse<LeadsConfDto> getLeadsConf(BaseBidOptRequest request) {
        LeadsConf leads = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, request.getBid()));
        if (ObjectUtils.isNull(leads)) {
            // 插入数据
            LeadsConf leadsConf = new LeadsConf();
            leadsConf.setId(request.getBid());
            leadsConf.setCreateTime(LocalDateTime.now());
            leadsConfManager.save(leadsConf);

            leads = leadsConfManager.getById(request.getBid());

            if (ObjectUtils.isNull(leads)) {
                throw new InngkeServiceException("获取配置失败");
            }
        }
        LeadsConfDto leadsConfDto = LeadsConfConverter.toLeadsConfDto(leads);
        return BaseResponse.success(leadsConfDto);
    }
}

package com.inngke.bp.leads.notify.builder;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.notify.context.LeadsDistributeMessageContext;
import com.inngke.bp.leads.notify.context.LeadsReportMessageContext;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * LeadsDistributeContentBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/16 17:48
 */
@Component(value = "leads_follow_notification")
@Slf4j
public class LeadsReportContentBuilder extends TemplateMessageContentBuilder<LeadsReportMessageContext> {
    @Autowired
    private StaffClientForLeads staffClientForLeads;
    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(LeadsReportMessageContext ctx) {
        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);

        return builder
                .setTitle("有一条新的线索分配给您")
                .setVar("name", ctx.getName())
                .setVar("clientName", ctx.getName())
                .setVar("phoneNumber", ctx.getMobile())
                .setVar("phone",ctx.getMobile())
                .setVar("sources",ctx.getLeadsType()== 1 ? "报备客户" : "总部下发")
                .setVar("followType", ctx.getLeadsType()== 1 ? "报备客户" : "总部下发")
                .setVar("time", ctx.getFormatTime())
                .setVar("followSituation","请在15分钟内跟进处理")
                .setVar("suggest", "请在15分钟内跟进处理以免错过最佳跟进时间!");
    }

}

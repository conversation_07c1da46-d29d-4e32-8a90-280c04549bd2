package com.inngke.bp.leads.core.converter;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.dto.response.LeadsEventEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowDto;
import com.inngke.bp.leads.dto.response.LeadsFollowSimpleDto;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

public class LeadsFollowConverter {

    public static LeadsEventEsDto toLeadsFollowEsDto(LeadsFollow leadsFollow) {
        LeadsEventEsDto leadsEventEsDto = new LeadsEventEsDto();
        leadsEventEsDto.setBid(leadsFollow.getBid());
        leadsEventEsDto.setEventType(1);
        leadsEventEsDto.setLeadsId(leadsFollow.getLeadsId());
        leadsEventEsDto.setCreateTime(DateTimeUtils.getMilli(leadsFollow.getCreateTime()));
        leadsEventEsDto.setFollowStaffId(leadsFollow.getStaffId());
        leadsEventEsDto.setPreFollowStaffId(0L);
        leadsEventEsDto.setDeposit(BigDecimal.ZERO);
        leadsEventEsDto.setPayAmount(BigDecimal.ZERO);

        return leadsEventEsDto;
    }

    public static LeadsEventEsDto toLeadsEventEsDto(StoreOrderDto storeOrderDto){
        LeadsEventEsDto leadsEventEsDto = new LeadsEventEsDto();
        leadsEventEsDto.setEventType(2);
        leadsEventEsDto.setLeadsId(storeOrderDto.getLeadsId());
        leadsEventEsDto.setEventId(20);
        leadsEventEsDto.setCreateTime(storeOrderDto.getCreateTime());
        leadsEventEsDto.setPreFollowStaffId(0L);
        leadsEventEsDto.setBid(storeOrderDto.getBid());
        leadsEventEsDto.setDeposit(new BigDecimal(storeOrderDto.getOrderDeposit()));
        leadsEventEsDto.setPayAmount(new BigDecimal(storeOrderDto.getOrderAmount()));
        leadsEventEsDto.setFollowStaffId(storeOrderDto.getStaffId());

        return leadsEventEsDto;
    }

    public static LeadsFollowSimpleDto toLeadsFollowSimpleDto(LeadsFollow leadsFollow) {
        LeadsFollowSimpleDto leadsFollowSimpleDto = new LeadsFollowSimpleDto();
        leadsFollowSimpleDto.setId(leadsFollow.getId());
        leadsFollowSimpleDto.setFollowContent(leadsFollow.getFollowContent());
        leadsFollowSimpleDto.setCreateTime(DateTimeUtils.getMilli(leadsFollow.getCreateTime()));
        leadsFollowSimpleDto.setLeadsId(leadsFollow.getLeadsId());

        return leadsFollowSimpleDto;
    }

    public static LeadsFollowDto toLeadsFollowDto(LeadsFollow leadsFollow) {
        if (Objects.isNull(leadsFollow)) {
            return null;
        }
        LeadsFollowDto leadsFollowDto = new LeadsFollowDto();
        leadsFollowDto.setId(leadsFollow.getId());
        leadsFollowDto.setBid(leadsFollow.getBid());
        leadsFollowDto.setLeadsId(leadsFollow.getLeadsId());
        leadsFollowDto.setStaffId(leadsFollow.getStaffId());
        leadsFollowDto.setFollowType(leadsFollow.getFollowType());
        leadsFollowDto.setFollowContent(leadsFollow.getFollowContent());
        leadsFollowDto.setFollowImages(StringUtils.isNotBlank(leadsFollow.getFollowImages()) ? Lists.newArrayList(StringUtils.split(leadsFollow.getFollowImages(), InngkeAppConst.COMMA_STR)) : Lists.newArrayList());
        leadsFollowDto.setLeadsStatus(leadsFollow.getLeadsStatus());
        leadsFollowDto.setCreateTime(DateTimeUtils.getMilli(leadsFollow.getCreateTime()));
        leadsFollowDto.setUserId(leadsFollow.getUserId());
        leadsFollowDto.setReason(leadsFollow.getReason());
        leadsFollowDto.setReasonId(leadsFollow.getReasonId());
        return leadsFollowDto;
    }

    public static LeadsFollow toLeadsFollow(LeadsFollowDto leadsFollowDto) {
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setId(leadsFollowDto.getId());
        leadsFollow.setBid(leadsFollowDto.getBid());
        leadsFollow.setLeadsId(leadsFollowDto.getLeadsId());
        leadsFollow.setStaffId(leadsFollowDto.getStaffId());
        leadsFollow.setUserId(leadsFollowDto.getUserId());
        leadsFollow.setFollowType(leadsFollowDto.getFollowType());
        leadsFollow.setFollowContent(leadsFollowDto.getFollowContent());
        leadsFollow.setFollowImages(Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(leadsFollowDto.getFollowImages()));
        leadsFollow.setLeadsStatus(leadsFollowDto.getLeadsStatus());
        leadsFollow.setReasonId(leadsFollowDto.getReasonId());
        leadsFollow.setReason(leadsFollowDto.getReason());
        return leadsFollow;

    }
}

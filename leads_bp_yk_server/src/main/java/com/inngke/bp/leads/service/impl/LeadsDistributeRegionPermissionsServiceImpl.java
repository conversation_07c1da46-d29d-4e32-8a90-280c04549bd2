package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConfPermissions;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfPermissionsManager;
import com.inngke.bp.leads.dto.request.SaveRegionPermissionsRequest;
import com.inngke.bp.leads.dto.response.RegionConfDto;
import com.inngke.bp.leads.dto.response.RegionPermissionsDto;
import com.inngke.bp.leads.dto.response.StaffSimpleInfoDto;
import com.inngke.bp.leads.service.LeadsDistributeRegionPermissionsService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.request.RegionGetRequest;
import com.inngke.ip.common.dto.response.RegionDto;
import com.inngke.ip.common.service.RegionService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LeadsDistributeRegionPermissionsServiceImpl implements LeadsDistributeRegionPermissionsService {

    @Resource
    private LeadsDistributeConfPermissionsManager leadsDistributeConfPermissionsManager;
    @Resource
    private StaffClientForLeads staffClientForLeads;
    @DubboReference(version = "1.0.0")
    private RegionService regionService;


    @Override
    public BaseResponse<List<RegionConfDto>> getRegionPermissions(BaseBidRequest request) {

        Map<Integer, RegionPermissionsDto> regionConfMap = leadsDistributeConfPermissionsManager.list(
                Wrappers.<LeadsDistributeConfPermissions>query().eq(LeadsDistributeConfPermissions.BID, request.getBid())
        ).stream().collect(Collectors.toMap(LeadsDistributeConfPermissions::getRegionId, this::toRegionPermissionsDto));

        List<Long> staffIds = regionConfMap.values().stream().map(RegionPermissionsDto::getStaffIds).flatMap(Collection::stream).collect(Collectors.toList());

        Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(request.getBid(), Sets.newHashSet(staffIds));

        List<RegionDto> provinceList = getAllProvince();
        if (CollectionUtils.isEmpty(provinceList)){
            return BaseResponse.success();
        }

        return BaseResponse.success(provinceList.stream().map(province -> {
            RegionPermissionsDto regionPermissions = regionConfMap.get(province.getId());

            RegionConfDto regionConfDto = new RegionConfDto();
            regionConfDto.setId(province.getId());
            regionConfDto.setName(province.getName());
            regionConfDto.setStaffList(
                    Optional.ofNullable(regionPermissions).map(RegionPermissionsDto::getStaffIds).orElse(Lists.newArrayList())
                            .stream().map(staffMap::get).filter(Objects::nonNull).map(this::toStaffSimpleDto).collect(Collectors.toList())
            );
            return regionConfDto;
        }).collect(Collectors.toList()));
    }


    @Override
    public BaseResponse<Boolean> saveRegionPermissions(SaveRegionPermissionsRequest request) {
        if (CollectionUtils.isEmpty(request.getStaffIds())){
            return BaseResponse.error("员工不能为空");
        }
        LeadsDistributeConfPermissions leadsDistributeConfPermissions = new LeadsDistributeConfPermissions();
        leadsDistributeConfPermissions.setBid(request.getBid());
        leadsDistributeConfPermissions.setRegionId(request.getRegionId());
        leadsDistributeConfPermissions.setStaffIds(
                request.getStaffIds().stream().map(String::valueOf).collect(Collectors.joining(InngkeAppConst.COMMA_STR))
        );

        if (leadsDistributeConfPermissionsManager.saveOrUpdateByRegionId(leadsDistributeConfPermissions)) {
            return BaseResponse.success(true);
        }
        return BaseResponse.error("保存失败");
    }

    @Override
    public BaseResponse<Boolean> removeRegionPermissions(BaseIdRequest request) {
        return BaseResponse.success(
                leadsDistributeConfPermissionsManager.remove(Wrappers.<LeadsDistributeConfPermissions>query()
                        .eq(LeadsDistributeConfPermissions.BID, request.getBid())
                        .eq(LeadsDistributeConfPermissions.REGION_ID, request.getId()))
        );
    }

    private RegionPermissionsDto toRegionPermissionsDto(LeadsDistributeConfPermissions leadsDistributeConfPermissions) {
        RegionPermissionsDto regionPermissionsDto = new RegionPermissionsDto();
        regionPermissionsDto.setId(leadsDistributeConfPermissions.getRegionId());
        regionPermissionsDto.setStaffIds(
                Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(leadsDistributeConfPermissions.getStaffIds()))
                        .stream().map(Long::valueOf).collect(Collectors.toList())
        );
        return regionPermissionsDto;
    }

    private List<RegionDto> getAllProvince(){
        RegionGetRequest request = new RegionGetRequest();
        request.setId(1);
        request.setWithChildren(true);

        BaseResponse<RegionDto> response = regionService.getRegion(request);

        return Optional.ofNullable(response).map(BaseResponse::getData).map(RegionDto::getChildren).orElse(Lists.newArrayList());
    }

    private StaffSimpleInfoDto toStaffSimpleDto(StaffDto staffDto) {
        StaffSimpleInfoDto staffSimpleInfoDto = new StaffSimpleInfoDto();
        staffSimpleInfoDto.setId(staffDto.getId());
        staffSimpleInfoDto.setName(staffDto.getName());
        return staffSimpleInfoDto;
    }

}

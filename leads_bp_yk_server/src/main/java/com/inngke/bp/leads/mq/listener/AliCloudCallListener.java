package com.inngke.bp.leads.mq.listener;


import com.inngke.bp.leads.mq.message.reach.AliCloudCallDto;
import com.inngke.bp.leads.service.AliCloudCallService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
@MqConsumer(value = "ali_cloud_call", consumerName = "leads_bp_yk_ali_cloud_call", messageType = AliCloudCallDto.class)
public class AliCloudCallListener implements InngkeMqListener<AliCloudCallDto> {

    @Autowired
    private AliCloudCallService aliCloudCallService;

    @Autowired
    private JsonService jsonService;

    @Override
    public void process(AliCloudCallDto aliCloudCallDto) {
        log.info("阿里云云呼叫,接收更新线索MQ", jsonService.toJson(aliCloudCallDto));
        if (Objects.isNull(aliCloudCallDto)){
            log.error("获取更新线索Mq,阿里云云呼叫实体为空");
            return;
        }

        BaseResponse baseResponse = aliCloudCallService.updateClueInfoByAliCloudCallCenterMessage(aliCloudCallDto);
        if (!BaseResponse.responseSuccess(baseResponse)){
            log.error("使用阿里云云呼叫信息内容更新线索失败!",baseResponse.getMsg());
        }
    }
}

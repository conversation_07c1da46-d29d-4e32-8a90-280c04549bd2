package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;

import java.time.LocalDateTime;
import java.util.List;

public interface LeadsFollowTimeStrategy {

    /**
     *leadsFollowTime bean处理
     * @param	leadsFollowTime
     * @param	time
     * @return com.inngke.bp.leads.db.leads.entity.leadsFollowTime
    */
    default LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time){
        return leadsFollowTime;
    }

    /**
     *TermsAggregationBuilder bean处理
     * @param	aggregation
     * @return void
    */
    default void process(TermsAggregationBuilder aggregation){
    }

    /**
     * LeadsFollowTimeByStaffResponse bean处理
     * @param	bucketAggregations
     * @param	dto
     * @return void
     */
    default void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto){
    }

    /**
     *
     * @param	dtos
     * @return void
    */
    default void process(List<LeadsFollowTimeByStaffResponse> dtos,LeadsFollowTimeByStaffResponse dto){
    }
}

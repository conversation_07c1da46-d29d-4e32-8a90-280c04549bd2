package com.inngke.bp.leads.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.nacos.common.utils.NumberUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.client.dto.request.client.GetClientEventStatisticsDataRequest;
import com.inngke.bp.client.dto.response.client.ClientEventStatisticsDto;
import com.inngke.bp.client.dto.response.client.ClientStatisticsItem;
import com.inngke.bp.client.dto.response.common.ClientLevelItemDto;
import com.inngke.bp.client.dto.response.level.ClientLevelListDto;
import com.inngke.bp.client.service.ClientEventDocStatisticsService;
import com.inngke.bp.client.service.ClientLeadsDocStatisticsService;
import com.inngke.bp.common.db.card.manager.ShopOrderManager;
import com.inngke.bp.content.dto.request.ContentGetListRequest;
import com.inngke.bp.content.dto.request.ProductGetListRequest;
import com.inngke.bp.content.dto.response.ContentDto;
import com.inngke.bp.content.dto.response.product.ProductDto;
import com.inngke.bp.content.enums.ContentTypeEnum;
import com.inngke.bp.content.service.ContentGetService;
import com.inngke.bp.content.service.ProductService;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.converter.AgentDtoConverter;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.core.utils.LeadsChannelUtil;
import com.inngke.bp.leads.core.utils.LeadsCommonUtil;
import com.inngke.bp.leads.core.utils.StaffToAgentUtil;
import com.inngke.bp.leads.db.leads.entity.*;
import com.inngke.bp.leads.db.leads.manager.*;
import com.inngke.bp.leads.dto.LeadsExcelDto;
import com.inngke.bp.leads.dto.LeadsExtDataDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.*;
import com.inngke.bp.leads.events.leads.LeadsBindClientEvent;
import com.inngke.bp.leads.notify.context.AdminNotifyNotContactGuidesContext;
import com.inngke.bp.leads.service.*;
import com.inngke.bp.leads.service.message.MessageManagerService;
import com.inngke.bp.organize.dto.request.GetAgentRequest;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.request.department.GetDepartmentsRequest;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.*;
import com.inngke.bp.organize.dto.response.card.CardDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.organize.service.StaffDepartmentService;
import com.inngke.bp.shop.dto.request.GetShopOrderCountByCustomerIdRequest;
import com.inngke.bp.shop.service.OrderGetService;
import com.inngke.bp.store.dto.request.GetLeadsStoreOrderRequest;
import com.inngke.bp.store.dto.request.QueryStoreOrderCountRequest;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.bp.user.dto.request.department.GetDepartmentChildIdsRequest;
import com.inngke.bp.user.dto.request.staff.StaffDeptRequest;
import com.inngke.bp.user.dto.request.staff.StaffGetByIdsRequest;
import com.inngke.bp.user.dto.request.staff.StaffGetRequest;
import com.inngke.bp.user.dto.response.AgentIdAndNameDto;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.bp.user.service.UserStaffService;
import com.inngke.common.attachment.service.InngkeUploaderService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.core.utils.StaticResourceUtils;
import com.inngke.common.ds.annotation.DS;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.enums.ErrorCode;
import com.inngke.common.es.service.EsDocService;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.ExcelUtils;
import com.inngke.ip.common.dto.request.RegionGetRequest;
import com.inngke.ip.common.dto.response.RegionDto;
import com.inngke.ip.common.service.RegionService;
import com.inngke.ip.reach.dto.request.VoiceBindRequest;
import com.inngke.ip.reach.service.voice.PrivateBindService;
import com.inngke.ip.reach.service.voice.PrivateVoiceRecordService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.core.converter.LeadsConverter.fillDistributeAgentId;
import static com.inngke.bp.leads.enums.LeadsStatusEnum.*;

/**
 * <AUTHOR>
 * @since 2021/9/7 4:37 PM
 */
@DubboService(version = "1.0.0")
@Service
public class LeadsServiceImpl implements LeadsService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsServiceImpl.class);

    private static final String DEPARTMENT_STAFF_GROUP = "departmentStaffGroup";

    private static final Executor executor;

    static {
        executor = new ThreadPoolExecutor(
                12,
                50,
                30,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(50),
                new ThreadFactory() {
                    private final AtomicInteger seq = new AtomicInteger(0);

                    @Override
                    public Thread newThread(@NotNull Runnable r) {
                        Thread t = new Thread(r);
                        t.setName("leads-bp" + seq.getAndAdd(1));
                        return t;
                    }
                },
                new ThreadPoolExecutor.DiscardPolicy()
        );
    }

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsFollowTimeManager leadsFollowTimeManager;

    @Autowired
    private ShopOrderManager shopOrderManager;

    @Autowired
    private InngkeUploaderService inngkeUploaderService;

    @Autowired
    private LeadsStatisticsService leadsStatisticsService;

    @Autowired
    private LeadsEsInfoGetService leadsEsInfoGetService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private LeadsLogManager leadsLogManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsCallLogManager leadsCallLogManager;

    @Autowired
    private LeadsConfManager leadsConfManager;

    @Autowired
    private StaffToAgentUtil staffToAgentUtil;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LoginClientForLeads loginClientForLeads;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private DistributorCustomerServiceClientForLeads distributorCustomerServiceClientForLeads;

    @Autowired
    private ClientApiForMp clientApiForMp;

    @Autowired
    private MessageManagerService messageManagerService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientLeadsDocStatisticsService clientLeadsDocStatisticsService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientEventDocStatisticsService clientEventDocStatisticsService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private RegionService regionService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store_bp_yk:}")
    private StoreOrderService storeOrderService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerGetService customerGetService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    private PrivateBindService privateBindService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    private PrivateVoiceRecordService privateVoiceRecordService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.shop_bp_yk:}")
    private OrderGetService orderGetService;

    @DubboReference(version = "1.0.0", timeout = 6000, url = "${inngke.dubbo.url.content_bp_yk:}")
    private ContentGetService contentGetService;

    @Autowired
    private EsDocService esDocService;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private CardClientForLeads cardClientForLeads;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private AgentClientForLeads agentClientForLeads;

    @Autowired
    private CustomerGetServiceClientForLeads customerGetServiceClientForLeads;

    @Autowired
    private StaffManageDepartmentClientForLeads staffManageDepartmentClientForLeads;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @Autowired
    private LeadsEventService leadsEventService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private UserStaffService userStaffService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private StaffDepartmentService staffDepartmentService;

    @Autowired
    private ClientLevelClientForLeads clientLevelClientForLeads;

    @Autowired
    private LeadsGetService leadsGetService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.content_bp_yk:}")
    private ProductService productService;


    public static final String LEADS_NOTIFY_INC = LeadsServiceConsts.APP_ID + ":leadsNotifyInc:";

    private static final String SHEET_NAME = "leadsInfos";

    private static final String LEADS_INDEX = "leads";

    private static final String DEP_STAFF_GROUP = "depStaffGroup";

    private static final String LEADS_CONTACT_FAIL_KEY = LeadsServiceConsts.APP_ID + InngkeAppConst.CLN_STR + "contact_fail" + InngkeAppConst.CLN_STR;

    private List<Leads> filterLeads(Map<Long, Long> staffAndAgent, List<Leads> leadsList, Long agentId) {
        // 过滤掉 非指定经销商的线索
        return leadsList.stream().filter(leads -> {
            Long staffAgentId = staffAndAgent.get(leads.getDistributeStaffId());
            return agentId.equals(staffAgentId);
        }).collect(Collectors.toList());
    }

    /**
     * 线索搜索
     *
     * @param request 筛选条件
     * @return 分页的线索列表
     */
    @Override
    public BaseResponse<LeadsListVo> search(LeadsQuery request) {
        LeadsListVo dto = new LeadsListVo();
        Integer bid = request.getBid();
        Boolean pcOperate = Optional.ofNullable(request.getPcOperate()).orElse(true);
        BaseResponse<LeadsListVo> response = BaseResponse.success(dto);

        //构建查询条件
        QueryWrapper<Leads> query = buildQuery(request, false);
        dto.setTotal(leadsManager.count(query));
        //分页
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        int skip = (pageNo - 1) * pageSize - Optional.ofNullable(request.getSkipSize()).orElse(0);
        skip = Math.max(skip, 0);
        query = buildQuery(request, true);
        query.last("limit " + skip + "," + pageSize);
        List<Leads> leadsList = leadsManager.list(query);

        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(leadsList, bid);

        //组装聚合数据
        List<LeadsGroupDto> leadsGroupDtos = installIsGroupInfoForList(request);
        if (!CollectionUtils.isEmpty(leadsGroupDtos)) {
            dto.setGroups(leadsGroupDtos);
        }
        if (leadsList.isEmpty()) {
            dto.setList(Lists.newArrayList());
            return response;
        }
        // 已成交定义：有成交单；此处对leadsList做特殊处理
        // 已签单
        if (Objects.nonNull(request.getStatusGroup()) && request.getStatusGroup() == 15) {
            query = buildQuery(request, true);
            query.notIn(Leads.STATUS,
                    getNonAllocatedLeadsStatus()
            );
            leadsList = leadsManager.list(query);
            GetLeadsStoreOrderRequest orderRequest = new GetLeadsStoreOrderRequest();
            orderRequest.setBid(request.getBid());
            orderRequest.setLeadsIds(leadsList.stream().map(Leads::getId).collect(Collectors.toSet()));
            Map<Long, List<StoreOrderDto>> orderResponse = storeOrderService.getStoreOrderListByLeads(orderRequest).getData();
            if (Objects.isNull(orderResponse)) {
                leadsList = Lists.newArrayList();
                dto.setTotal(leadsList.size());
            } else {
                List<Long> orderLeadsId = orderResponse.values().stream().flatMap(Collection::stream).filter(item -> item.getType() == 2).map(StoreOrderDto::getLeadsId).collect(Collectors.toList());
                leadsList = leadsList.stream().filter(item -> orderLeadsId.contains(item.getId())).collect(Collectors.toList());
                dto.setTotal(leadsList.size());
            }
        }


        Set<Long> leadsIds = leadsList.stream().map(Leads::getId).collect(Collectors.toSet());
        // 跟进数量
        List<LeadsFollow> leadsFollowList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(leadsIds)) {
            leadsFollowList = leadsFollowManager.list(
                    Wrappers.<LeadsFollow>query()
                            .eq(LeadsFollow.BID, request.getBid())
                            .in(LeadsFollow.LEADS_ID, leadsIds)
                            .select(LeadsFollow.ID, LeadsFollow.LEADS_ID)
            );
        }

        Map<Long, List<LeadsFollow>> followCountMap = leadsFollowList.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));

        Map<Integer, ClientLevelItemDto> levelMap = clientLevelClientForLeads.getClientLevelList(bid).stream().collect(Collectors.toMap(ClientLevelItemDto::getId, v -> v));

        ArrayList<LeadsListItemDto> leadsListItemDtos = Lists.newArrayList();
        leadsList.forEach(item -> {
            LeadsVo leadsVo = LeadsConverter.toLeadsListItem(item, staffAndAgent);
            if (leadsVo == null) {
                return;
            }
            ClientLevelItemDto clientLevelItemDto = levelMap.get(item.getLevelId());
            if (clientLevelItemDto != null) {
                leadsVo.setLevelDes(clientLevelItemDto.getExplainInfo());
                leadsVo.setLevelText(clientLevelItemDto.getTitle());
            }
            leadsListItemDtos.add(leadsVo);
        });
        for (LeadsListItemDto item : leadsListItemDtos) {
            Integer type = leadsServiceV2.getLeadsDetailTypeHandle(item.getType(), item.getChannel());
            item.setType(type);
            List<LeadsFollow> follows = followCountMap.get(item.getId());
            if (!CollectionUtils.isEmpty(follows)) {
                item.setFollowCount(follows.size());
            } else {
                item.setFollowCount(0);
            }
        }
        dto.setList(leadsListItemDtos);
        //组装跟进数据
        installFollowInfo(request, leadsListItemDtos, pcOperate);

        installStaffNameAndAgentName(leadsListItemDtos, request.getBid(), request.getStatus());

        //已分配状态添加部门信息
        if (Objects.nonNull(request.getStatusGroup()) && request.getStatusGroup().equals(5)) {
            installStaffDepartmentInfo(request.getBid(), leadsListItemDtos);
        }
        //组装preFollowStaffName
        buildPreFollowStaffName(request.getBid(), leadsListItemDtos);
        return response;
    }


    private void buildPreFollowStaffName(Integer bid, List<LeadsListItemDto> leadsListItemDtos) {
        Map<Long, StaffDto> staffMap = getStaffMap(bid, getStaffIds(leadsListItemDtos));
        leadsListItemDtos.forEach(leadsListItemDto -> {
            StaffDto staffDto = staffMap.get(leadsListItemDto.getPreFollowStaffId());
            if (Objects.nonNull(staffDto)) {
                leadsListItemDto.setPreFollowStaffName(staffDto.getName());
            }
        });
    }

    private List<Long> getStaffIds(List<LeadsListItemDto> leadsListItemDtos) {
        return leadsListItemDtos.stream().map(LeadsListItemDto::getPreFollowStaffId).collect(Collectors.toList());
    }

    /**
     * 组装员工部门
     *
     * @param bid
     * @param
     * @param leadsListItemDtos
     * @param
     */
    private void installStaffDepartmentInfo(Integer bid, List<LeadsListItemDto> leadsListItemDtos) {
        Set<Long> staffIds = leadsListItemDtos.stream().map(LeadsListItemDto::getDistributeStaffId).collect(Collectors.toSet());
        Map<Long, String> staffIdDeptNameMap = getStaffDto(bid, staffIds).stream().collect(Collectors.toMap(UserStaffDto::getId, UserStaffDto::getDepartmentName));

        if (CollectionUtils.isEmpty(staffIdDeptNameMap)) {
            return;
        }
        leadsListItemDtos.forEach(
                item -> {
                    String deptName = staffIdDeptNameMap.get(item.getDistributeStaffId());
                    if (StringUtils.isEmpty(deptName)) {
                        return;
                    }
                    item.setDistributeStaffDepartment(deptName);
                }
        );
    }

    private List<UserStaffDto> getStaffDto(Integer bid, Set<Long> staffIds) {
        StaffGetRequest staffGetRequest = new StaffGetRequest();
        staffGetRequest.setBid(bid);
        staffGetRequest.setIds(staffIds);
        staffGetRequest.setPageSize(staffIds.size());
        BaseResponse<List<UserStaffDto>> response = userStaffService.getList(staffGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response) || CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        return response.getData();
    }


    private Map<Long, DepartmentDto> getStaffDepartmentInfosByStaffIds(Integer bid, Set<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Maps.newHashMap();
        }
        StaffDeptRequest staffDeptRequest = new StaffDeptRequest();
        staffDeptRequest.setStaffIds(staffIds);
        staffDeptRequest.setBid(bid);

        // 员工编号+bid ---> 部门信息
        Map<Long, DepartmentDto> departmentByStaffIdsResp;
        try {
            departmentByStaffIdsResp = departmentClientForLeads.getStaffDepartmentByStaffIds(staffDeptRequest);
        } catch (Exception e) {
            logger.warn("员工部门关系查询不到", e);
            // 按照原来的逻辑 即使没有数据也要返回空的 不影响调用方
            return Maps.newHashMap();
        }
        return departmentByStaffIdsResp;
    }


    private void installStaffNameAndAgentName(List<LeadsListItemDto> leadsList, Integer bid, Integer status) {
        //distributorStaffIds
        Set<Long> ids = leadsList.stream().map(LeadsListItemDto::getDistributeStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> leadsId = leadsList.stream().map(LeadsListItemDto::getId).collect(Collectors.toList());
        if (Objects.nonNull(status) && status.equals(LeadsStatusEnum.PUSH_BACK.getStatus())) {//被退回的线索
            installPushBackLeadsStaffAndAgentName(bid, leadsId, leadsList);
        } else {
            //组装非退回状态线索
            installNotPushBackLeadsStaffAndAgentName(bid, ids, leadsList);
        }
    }

    private void installNotPushBackLeadsStaffAndAgentName(Integer bid, Set<Long> ids, List<LeadsListItemDto> leadsList) {
        if (CollectionUtils.isEmpty(ids) || CollectionUtils.isEmpty(leadsList)) {
            return;
        }
        //非线索退回状态
        Map<Long, StaffIdAndAgentIdDto> staffIdMap = getLeadsStaffAndAgentName(bid, ids);

        if (CollectionUtils.isEmpty(staffIdMap)) {
            return;
        }

        leadsList.forEach(item -> {
            StaffIdAndAgentIdDto staffIdAndAgentIdDto = staffIdMap.get(item.getDistributeStaffId());
            if (Objects.isNull(staffIdAndAgentIdDto)) {
                return;
            }
            item.setDistributeStaffName(staffIdAndAgentIdDto.getStaffName());
            item.setDistributeAgentName(staffIdAndAgentIdDto.getAgentName());
        });
    }

    @Override
    public void installPushBackLeadsStaffAndAgentName(Integer bid, List<Long> leadsId, List<LeadsListItemDto> leadsList) {
        if (CollectionUtils.isEmpty(leadsId) || CollectionUtils.isEmpty(leadsList)) {
            return;
        }
        List<LeadsFollow> list = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, bid)
                        .in(LeadsFollow.LEADS_ID, leadsId)
                        .orderByDesc(LeadsFollow.CREATE_TIME)
                        .select(LeadsFollow.ID, LeadsFollow.LEADS_ID, LeadsFollow.STAFF_ID, LeadsFollow.CREATE_TIME)
        );


        //获取分配时间
        Map<Long, LocalDateTime> distributeTimeMap = getLeadsDistributeTime(bid, leadsId);

        Set<Long> staffIds = list.stream().map(LeadsFollow::getStaffId).collect(Collectors.toSet());

        //去除list中重复的值
        Map<Long, Long> leadsStaffIdMap = list.stream().collect(Collectors.toMap(LeadsFollow::getLeadsId, LeadsFollow::getStaffId, (e1, e2) -> e1));

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<Long, StaffIdAndAgentIdDto> staffIdMap = getLeadsStaffAndAgentName(bid, staffIds);

        if (CollectionUtils.isEmpty(staffIdMap)) {
            return;
        }

        if (CollectionUtils.isEmpty(leadsStaffIdMap)) {
            return;
        }

        leadsList.forEach(item -> {
            //设置回退时间
            LocalDateTime distributeTime = distributeTimeMap.get(item.getId());
            item.setDistributeTime(Objects.nonNull(distributeTime) ? DateTimeUtils.getMilli(distributeTime) : null);

            Long staffId = leadsStaffIdMap.get(item.getId());
            if (staffId == null || staffId <= 0) {
                return;
            }
            StaffIdAndAgentIdDto staffIdAndAgentIdDto = staffIdMap.get(staffId);
            if (Objects.isNull(staffIdAndAgentIdDto)) {
                return;
            }
            item.setDistributeStaffName(staffIdAndAgentIdDto.getStaffName());
            item.setDistributeAgentName(staffIdAndAgentIdDto.getAgentName());

        });
    }

    @Override
    public BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsInfosV2(LeadsDepStaffStatisticsQuery request) {
        if (Objects.isNull(request.getDepartmentId())) {
            Set<Long> staffManagerDepartmentIds = staffManageDepartmentClientForLeads.getStaffManagerDepartmentIds(request.getBid(), request.getOperatorStaffId());
            Set<Long> topDepartmentIds = departmentClientForLeads.filterTopDepartment(request.getBid(), staffManagerDepartmentIds);
            if (!CollectionUtils.isEmpty(topDepartmentIds)) {
                List<DepartmentDto> topDepartmentList = departmentClientForLeads.findDepartmentByIds(request.getBid(), topDepartmentIds);
                if (!CollectionUtils.isEmpty(topDepartmentList)) {
                    DepartmentDto minLevelDepartment = topDepartmentList.stream().min(Comparator.comparing(DepartmentDto::getLevel)).stream().findFirst().orElse(null);
                    Long parentId = minLevelDepartment.getParentId();
                    if (Objects.nonNull(parentId) && 0L < parentId) {
                        DepartmentDto rootDepartment = departmentClientForLeads.getRootDepartment(request.getBid());
                        if (rootDepartment.getId().equals(parentId)) {
                            request.setDepartmentId(rootDepartment.getId());
                        } else {
                            request.setDepartmentId(parentId);
                        }
                    }
                }
            }
        }

        //设置查询的部门路径
        LeadsDepartmentStaffStatisticsDto leadsDepartmentStaffStatisticsDto = initPathForLeadsDepartmentStaffStatisticsDto(request);

        //获取员工的顶级管理部门（权限）
        Set<Long> staffManageDepartment = staffManageDepartmentClientForLeads.getStaffManageDepartment(request.getBid(), request.getStaffId());
        Set<Long> staffManagerTopDepartmentIds = staffManageDepartmentClientForLeads.getStaffManagerTopDepartmentIds(request.getBid(), request.getStaffId());

        //获取当前展示的员工
        List<LeadsStatisticsStaffItemDto> statisticsStaffItemDtoList = getShowStaffDto(request, staffManageDepartment);
        leadsDepartmentStaffStatisticsDto.setStaffs(statisticsStaffItemDtoList);

        //员工的数据统计
        leadsStatisticsOfStaff(request.getBid(), leadsDepartmentStaffStatisticsDto, staffManageDepartment, request.getStartDistributeTime(), request.getEndDistributeTime());

        //员工精确查询
        if (!ObjectUtils.isEmpty(request.getKeyword())) {
            GetClientEventStatisticsDataRequest clientLeadsRequest = new GetClientEventStatisticsDataRequest();
            clientLeadsRequest.setBid(request.getBid());
            clientLeadsRequest.setSid(request.getStaffId());
            clientLeadsRequest.setDepartmentId(request.getDepartmentId());
            clientLeadsRequest.setStaffName(request.getKeyword());
            clientLeadsRequest.setStartCreateTime(request.getStartDistributeTime());
            clientLeadsRequest.setEndCreateTime(request.getEndDistributeTime());
            clientLeadsRequest.setRelationLeads(Boolean.TRUE);
            BaseResponse<ClientEventStatisticsDto> clientLeadsResponse = clientEventDocStatisticsService.getClientLeadsEventStatisticsData(clientLeadsRequest);
            ClientEventStatisticsDto clientLeadsData = clientLeadsResponse.getData();
            logger.info("client-leads-data:{}", jsonService.toJson(clientLeadsData));
            mergeClientLeadsDataOfStaff(leadsDepartmentStaffStatisticsDto.getStaffs(), clientLeadsData.getStaffStatisticsList());
            return BaseResponse.success(leadsDepartmentStaffStatisticsDto);
        }

        //获取当前展示的部门
        List<LeadsStatisticsDepItemDto> leadsStatisticsDepItemDtoList = getShowDepartmentDto(request, staffManageDepartment, staffManagerTopDepartmentIds);
        leadsDepartmentStaffStatisticsDto.setDepartments(leadsStatisticsDepItemDtoList);

        //部门的数据统计
        leadsStatisticsOfDept(request.getBid(), leadsDepartmentStaffStatisticsDto, staffManageDepartment, request.getStartDistributeTime(), request.getEndDistributeTime());
        // 将数据聚合到root部门下
        if (Objects.isNull(request.getDepartmentId()) || request.getDepartmentId() == 0L) {
            DepartmentDto rootDepartment = departmentClientForLeads.getRootDepartment(request.getBid());
            List<LeadsStatisticsDepItemDto> departments = leadsDepartmentStaffStatisticsDto.getDepartments();
            if (!CollectionUtils.isEmpty(departments) && !departments.get(0).getDepartmentId().equals(rootDepartment.getId())) {
                Map<Long, Set<Long>> childrenDepartmentMap = departmentClientForLeads.batchGetChildrenDepartment(request.getBid(), Lists.newArrayList(rootDepartment.getId()));
                List<StaffDto> staffList = staffClientForLeads.getStaffByBidOfCache(request.getBid());
                Set<Long> childrenDepartmentIds = childrenDepartmentMap.get(rootDepartment.getId());


                LeadsStatisticsDepItemDto leadsStatisticsDepItemDto = departments.get(0);
                leadsStatisticsDepItemDto.setDepartmentId(rootDepartment.getId());
                leadsStatisticsDepItemDto.setDepartmentName(rootDepartment.getName());
                leadsStatisticsDepItemDto.setStaffNum(Long.valueOf(staffList.stream().filter(staff -> childrenDepartmentIds.contains(staff.getDepartmentId())).count()).intValue());
            }
        }

        // 查询client-leads-doc索引获取量尺数、定金数、签单数、签单金额

        GetClientEventStatisticsDataRequest clientLeadsRequest = new GetClientEventStatisticsDataRequest();
        clientLeadsRequest.setBid(request.getBid());
        clientLeadsRequest.setSid(request.getStaffId());
        clientLeadsRequest.setDepartmentId(request.getDepartmentId());
        clientLeadsRequest.setStaffName(request.getKeyword());
        clientLeadsRequest.setStartCreateTime(request.getStartDistributeTime());
        clientLeadsRequest.setEndCreateTime(request.getEndDistributeTime());
        clientLeadsRequest.setRelationLeads(Boolean.TRUE);
        BaseResponse<ClientEventStatisticsDto> clientLeadsResponse = clientEventDocStatisticsService.getClientLeadsEventStatisticsData(clientLeadsRequest);
        ClientEventStatisticsDto clientLeadsData = clientLeadsResponse.getData();
        logger.info("client-leads-data:{}", jsonService.toJson(clientLeadsData));

        // merge leads、client-leads数据
        mergeClientLeadsData(leadsDepartmentStaffStatisticsDto.getDepartments(), clientLeadsData.getDepartmentStatisticsList());
        mergeClientLeadsDataOfStaff(leadsDepartmentStaffStatisticsDto.getStaffs(), clientLeadsData.getStaffStatisticsList());

        List<Long> departmentIds = leadsDepartmentStaffStatisticsDto.getDepartments().stream().map(LeadsStatisticsDepItemDto::getDepartmentId).collect(Collectors.toList());
        Map<Long, Set<Long>> childrenDepartmentMap = departmentClientForLeads.batchGetChildrenDepartment(request.getBid(), departmentIds);
        List<StaffDto> staffList = staffClientForLeads.getStaffByBidOfCache(request.getBid());
        // 设置部门员工数量
        Optional.ofNullable(leadsDepartmentStaffStatisticsDto.getDepartments()).ifPresent(departmentList -> departmentList.forEach(item -> {
            Set<Long> childrenDepartmentIds = childrenDepartmentMap.get(item.getDepartmentId());
            childrenDepartmentIds.add(item.getDepartmentId());
            if (!CollectionUtils.isEmpty(childrenDepartmentIds)) {
                item.setStaffNum(Long.valueOf(staffList.stream().filter(staff -> childrenDepartmentIds.contains(staff.getDepartmentId())).count()).intValue());
            }
        }));


        return BaseResponse.success(leadsDepartmentStaffStatisticsDto);
    }

    private void mergeClientLeadsDataOfStaff(List<LeadsStatisticsStaffItemDto> leadsStatisticsList, List<ClientStatisticsItem> clientLeadsStatisticsList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(clientLeadsStatisticsList)) {
            return;
        }

        //如果clientLeadsStatisticsList.id在leadsStatisticsList.id中存在，则合并数据,如果不存在，将ClientStatisticsItem转化为LeadsStatisticsDepItemDto，append到leadsStatisticsList中
        clientLeadsStatisticsList.forEach(clientLeadsStatistics -> {
            Optional<LeadsStatisticsStaffItemDto> leadsStatisticsDepItemDtoOptional = leadsStatisticsList.stream()
                    .filter(leadsStatisticsDepItemDto -> leadsStatisticsDepItemDto.getStaffId().equals(clientLeadsStatistics.getId())).findFirst();

            if (leadsStatisticsDepItemDtoOptional.isPresent()) {
                LeadsStatisticsStaffItemDto leadsStatisticsDepItemDto = leadsStatisticsDepItemDtoOptional.get();
                leadsStatisticsDepItemDto.setMeasurementCount(clientLeadsStatistics.getMeasurementCount());
                leadsStatisticsDepItemDto.setSignBillCount(clientLeadsStatistics.getSignContractCount());
                leadsStatisticsDepItemDto.setDepositCount(clientLeadsStatistics.getDepositCount());
                leadsStatisticsDepItemDto.setSignBillAmount(Objects.isNull(clientLeadsStatistics.getSignContractAmount()) ? "0" : String.valueOf(clientLeadsStatistics.getSignContractAmount()));
                leadsStatisticsDepItemDto.setClientCount(clientLeadsStatistics.getDemandCount());
            } else {
                LeadsStatisticsStaffItemDto leadsStatisticsDepItemDto = new LeadsStatisticsStaffItemDto();
                leadsStatisticsDepItemDto.setMeasurementCount(clientLeadsStatistics.getMeasurementCount());
                leadsStatisticsDepItemDto.setSignBillCount(clientLeadsStatistics.getSignContractCount());
                leadsStatisticsDepItemDto.setDepositCount(clientLeadsStatistics.getDepositCount());
                leadsStatisticsDepItemDto.setSignBillAmount(Objects.isNull(clientLeadsStatistics.getSignContractAmount()) ? "0" : String.valueOf(clientLeadsStatistics.getSignContractAmount()));

                leadsStatisticsDepItemDto.setStaffId(clientLeadsStatistics.getId());
                leadsStatisticsDepItemDto.setStaffName(clientLeadsStatistics.getName());
                leadsStatisticsDepItemDto.setClientCount(clientLeadsStatistics.getDemandCount());
                leadsStatisticsList.add(leadsStatisticsDepItemDto);
            }
        });
    }

    private void mergeClientLeadsData(List<LeadsStatisticsDepItemDto> leadsStatisticsList, List<ClientStatisticsItem> clientLeadsStatisticsList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(clientLeadsStatisticsList)) {
            return;
        }

        //如果clientLeadsStatisticsList.id在leadsStatisticsList.id中存在，则合并数据,如果不存在，将ClientStatisticsItem转化为LeadsStatisticsDepItemDto，append到leadsStatisticsList中
        clientLeadsStatisticsList.forEach(clientLeadsStatistics -> {
            Optional<LeadsStatisticsDepItemDto> leadsStatisticsDepItemDtoOptional = leadsStatisticsList.stream().filter(leadsStatisticsDepItemDto -> leadsStatisticsDepItemDto.getDepartmentId().equals(clientLeadsStatistics.getId())).findFirst();
            if (leadsStatisticsDepItemDtoOptional.isPresent()) {
                LeadsStatisticsDepItemDto leadsStatisticsDepItemDto = leadsStatisticsDepItemDtoOptional.get();
                leadsStatisticsDepItemDto.setMeasurementCount(clientLeadsStatistics.getMeasurementCount());
                leadsStatisticsDepItemDto.setSignBillCount(clientLeadsStatistics.getSignContractCount());
                leadsStatisticsDepItemDto.setDepositCount(clientLeadsStatistics.getDepositCount());
                leadsStatisticsDepItemDto.setSignBillAmount(Objects.isNull(clientLeadsStatistics.getSignContractAmount()) ? "0" : String.valueOf(clientLeadsStatistics.getSignContractAmount()));
                leadsStatisticsDepItemDto.setClientCount(clientLeadsStatistics.getDemandCount());
            } else {
                LeadsStatisticsDepItemDto leadsStatisticsDepItemDto = new LeadsStatisticsDepItemDto();
                leadsStatisticsDepItemDto.setMeasurementCount(clientLeadsStatistics.getMeasurementCount());
                leadsStatisticsDepItemDto.setSignBillCount(clientLeadsStatistics.getSignContractCount());
                leadsStatisticsDepItemDto.setDepositCount(clientLeadsStatistics.getDepositCount());
                leadsStatisticsDepItemDto.setSignBillAmount(Objects.isNull(clientLeadsStatistics.getSignContractAmount()) ? "0" : String.valueOf(clientLeadsStatistics.getSignContractAmount()));
                leadsStatisticsDepItemDto.setDepartmentId(clientLeadsStatistics.getId());
                leadsStatisticsDepItemDto.setDepartmentName(clientLeadsStatistics.getName());
                leadsStatisticsDepItemDto.setClientCount(clientLeadsStatistics.getDemandCount());
                leadsStatisticsList.add(leadsStatisticsDepItemDto);
            }
        });
    }

    private void leadsStatisticsOfDept(Integer bid, LeadsDepartmentStaffStatisticsDto leadsDepartmentStaffStatisticsDto, Set<Long> staffManageDepartment, Long startDistributeTime, Long endDistributeTime) {
        List<LeadsStatisticsDepItemDto> leadsStatisticsDepItemDtoList = leadsDepartmentStaffStatisticsDto.getDepartments();
        List<Long> depIds = leadsStatisticsDepItemDtoList.stream().map(LeadsStatisticsDepItemDto::getDepartmentId).collect(Collectors.toList());
        Map<Long, LeadsStatisticsBaseInfoDto> deptStatisticsDtoMap = leadsEsInfoGetService.getDepLeadsListIncludeSubsByDepId(bid, depIds, Lists.newArrayList(staffManageDepartment), startDistributeTime, endDistributeTime).getData();
        // 获取各个子部门的员工人数
        Map<Long, Integer> staffCountOfDept = getStaffCountOfDept(bid, staffManageDepartment, depIds);
        leadsStatisticsDepItemDtoList.forEach(leadsStatisticsDepItemDto -> {
            LeadsStatisticsBaseInfoDto statisticsBaseInfoDto = deptStatisticsDtoMap.get(leadsStatisticsDepItemDto.getDepartmentId());
            if (!ObjectUtils.isEmpty(statisticsBaseInfoDto)) {
                installBaseInfo(statisticsBaseInfoDto, leadsStatisticsDepItemDto);
            }
            Integer count = staffCountOfDept.get(leadsStatisticsDepItemDto.getDepartmentId());
            if (!ObjectUtils.isEmpty(count)) {
                leadsStatisticsDepItemDto.setStaffNum(count);
            }
        });
        //重新排序
        if (!CollectionUtils.isEmpty(leadsStatisticsDepItemDtoList)) {
            leadsStatisticsDepItemDtoList = leadsStatisticsDepItemDtoList.stream().filter(i -> !Objects.equals(i.getTotalCount(), 0)).sorted(Comparator.comparing(LeadsStatisticsBaseInfoDto::getTotalCount).reversed()).collect(Collectors.toList());
            leadsDepartmentStaffStatisticsDto.setDepartments(leadsStatisticsDepItemDtoList);
        }
    }

    private Map<Long, Integer> getStaffCountOfDept(Integer bid, Set<Long> staffManageDepartment, List<Long> depIds) {
        if (ObjectUtils.isEmpty(depIds)) {
            return Maps.newHashMap();
        }
        GetDepartmentChildIdsRequest getDepartmentChildIdsRequest = new GetDepartmentChildIdsRequest();
        getDepartmentChildIdsRequest.setDepartmentIds(depIds);
        getDepartmentChildIdsRequest.setBid(bid);
        getDepartmentChildIdsRequest.setPressionList(Lists.newArrayList(staffManageDepartment));
        return getDepartmentStaffCount(getDepartmentChildIdsRequest);
    }

    private void leadsStatisticsOfStaff(Integer bid, LeadsDepartmentStaffStatisticsDto leadsDepartmentStaffStatisticsDto, Set<Long> staffManageDepartment, Long startDistributeTime, Long endDistributeTime) {
        List<LeadsStatisticsStaffItemDto> statisticsStaffItemDtoList = leadsDepartmentStaffStatisticsDto.getStaffs();
        List<Long> staffIds = statisticsStaffItemDtoList.stream().map(LeadsStatisticsStaffItemDto::getStaffId).collect(Collectors.toList());
        Map<Long, CardDto> cardDtoMap = cardClientForLeads.getCardDtoListByStaffIds(bid, staffIds);
        Map<Long, LeadsStatisticsBaseInfoDto> staffStatisticsDtoMap = leadsEsInfoGetService.getLeadsListByStaffIds(bid, staffIds, Lists.newArrayList(staffManageDepartment), startDistributeTime, endDistributeTime).getData();
        statisticsStaffItemDtoList.forEach(statisticsStaffItemDto -> {
            LeadsStatisticsBaseInfoDto statisticsBaseInfoDto = staffStatisticsDtoMap.get(statisticsStaffItemDto.getStaffId());
            if (!ObjectUtils.isEmpty(statisticsBaseInfoDto)) {
                installBaseInfo(statisticsBaseInfoDto, statisticsStaffItemDto);
            }
            CardDto cardDto = cardDtoMap.get(statisticsStaffItemDto.getStaffId());
            if (cardDto != null) {
                statisticsStaffItemDto.setAvatar(cardDto.getAvatarUrl());
            }
        });

        //重新排序
        if (!CollectionUtils.isEmpty(statisticsStaffItemDtoList)) {
            statisticsStaffItemDtoList = statisticsStaffItemDtoList.stream().filter(i -> !Objects.equals(i.getTotalCount(), 0)).sorted(Comparator.comparing(LeadsStatisticsBaseInfoDto::getTotalCount).reversed()).collect(Collectors.toList());
            leadsDepartmentStaffStatisticsDto.setStaffs(statisticsStaffItemDtoList);
        }
    }

    private List<LeadsStatisticsStaffItemDto> getShowStaffDto(LeadsDepStaffStatisticsQuery request, Set<Long> staffManageDepartment) {
        StaffListRequest staffRequest = new StaffListRequest();
        staffRequest.setBid(request.getBid());
        staffRequest.setName(request.getKeyword());
        if (!StringUtils.isEmpty(request.getKeyword())) {
//            staffRequest.setDepartmentId(Objects.nonNull(request.getDepartmentId()) ? request.getDepartmentId() : null);
        } else {
            staffRequest.setDepartmentId(Objects.nonNull(request.getDepartmentId()) ? request.getDepartmentId() : -1L);
        }
        List<StaffDto> staffList = staffClientForLeads.getStaffList(staffRequest);
        staffList = staffList.stream().filter(i -> staffManageDepartment.contains(i.getDepartmentId())).collect(Collectors.toList());
        List<LeadsStatisticsStaffItemDto> statisticsStaffItemDtoList = staffList.stream().map(this::toLeadsStatisticsStaffItemDto).collect(Collectors.toList());
        return statisticsStaffItemDtoList;
    }

    private List<LeadsStatisticsDepItemDto> getShowDepartmentDto(LeadsDepStaffStatisticsQuery request, Set<Long> staffManageDepartment, Set<Long> staffManagerTopDepartmentIds) {
        List<DepartmentDto> departmentDtoList;
        if (!ObjectUtils.isEmpty(request.getDepartmentId())) {
            GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
            getDepartmentRequest.setBid(request.getBid());
            getDepartmentRequest.setChildrenLevel(1);
            getDepartmentRequest.setDepartmentId(request.getDepartmentId());
            departmentDtoList = departmentClientForLeads.getDepartmentChild(getDepartmentRequest).stream().collect(Collectors.toList());
        } else {
            departmentDtoList = departmentClientForLeads.getDepartmentByIds(request.getBid(), Lists.newArrayList(staffManagerTopDepartmentIds));
            //汇聚到有权限的顶级部门
            if (!CollectionUtils.isEmpty(departmentDtoList) && departmentDtoList.size() > 1) {
                departmentDtoList = gatherToRootDepartment(request.getBid(), departmentDtoList);
            }
        }
        List<LeadsStatisticsDepItemDto> leadsStatisticsDepItemDtoList = departmentDtoList.stream().map(this::toLeadsStatisticsDepItemDto).collect(Collectors.toList());
        return leadsStatisticsDepItemDtoList;
    }

    private List<DepartmentDto> gatherToRootDepartment(Integer bid, List<DepartmentDto> showDepartmentList) {
        while (showDepartmentList.size() > 1) {
            //如果有根部在列表中 则直接返回根部门
            for (DepartmentDto departmentDto : showDepartmentList) {
                if (departmentDto.getParentId() == 0L) {
                    return Lists.newArrayList(departmentDto);
                }
            }
            Set<Long> parentDepartmentIds = showDepartmentList.stream()
                    .map(DepartmentDto::getParentId).collect(Collectors.toSet());

            List<DepartmentDto> parentDepartment = departmentClientForLeads
                    .getDepartmentList(bid, Lists.newArrayList(parentDepartmentIds));

            showDepartmentList = parentDepartment.stream()
                    .filter(department -> !parentDepartmentIds.contains(department.getParentId()))
                    .collect(Collectors.toList());
        }

        return showDepartmentList;
    }

    private LeadsStatisticsDepItemDto toLeadsStatisticsDepItemDto(DepartmentDto departmentDto) {
        LeadsStatisticsDepItemDto leadsStatisticsDepItemDto = new LeadsStatisticsDepItemDto();
        leadsStatisticsDepItemDto.setDepartmentId(departmentDto.getId());
        leadsStatisticsDepItemDto.setDepartmentName(departmentDto.getName());
        leadsStatisticsDepItemDto.setStaffNum(0);
        leadsStatisticsDepItemDto.setTotalCount(0);
        leadsStatisticsDepItemDto.setContactTimelyCount(0);
        leadsStatisticsDepItemDto.setToBeContactedCount(0);
        leadsStatisticsDepItemDto.setInContactCount(0);
        leadsStatisticsDepItemDto.setDealCount(0);
        leadsStatisticsDepItemDto.setClientCount(0);

        return leadsStatisticsDepItemDto;
    }

    private LeadsStatisticsStaffItemDto toLeadsStatisticsStaffItemDto(UserStaffDto staffDto) {
        LeadsStatisticsStaffItemDto leadsStatisticsStaffItemDto = new LeadsStatisticsStaffItemDto();
        leadsStatisticsStaffItemDto.setStaffId(staffDto.getId());
        leadsStatisticsStaffItemDto.setStaffName(staffDto.getName());
        leadsStatisticsStaffItemDto.setPosition(staffDto.getPosition());
        leadsStatisticsStaffItemDto.setAvatar("");
        leadsStatisticsStaffItemDto.setTotalCount(0);
        leadsStatisticsStaffItemDto.setContactTimelyCount(0);
        leadsStatisticsStaffItemDto.setToBeContactedCount(0);
        leadsStatisticsStaffItemDto.setInContactCount(0);
        leadsStatisticsStaffItemDto.setDealCount(0);
        leadsStatisticsStaffItemDto.setClientCount(0);

        return leadsStatisticsStaffItemDto;
    }

    private LeadsStatisticsStaffItemDto toLeadsStatisticsStaffItemDto(StaffDto staffDto) {
        LeadsStatisticsStaffItemDto leadsStatisticsStaffItemDto = new LeadsStatisticsStaffItemDto();
        leadsStatisticsStaffItemDto.setStaffId(staffDto.getId());
        leadsStatisticsStaffItemDto.setStaffName(staffDto.getName());
        leadsStatisticsStaffItemDto.setPosition(staffDto.getPosition());
        leadsStatisticsStaffItemDto.setAvatar("");
        leadsStatisticsStaffItemDto.setTotalCount(0);
        leadsStatisticsStaffItemDto.setContactTimelyCount(0);
        leadsStatisticsStaffItemDto.setToBeContactedCount(0);
        leadsStatisticsStaffItemDto.setInContactCount(0);
        leadsStatisticsStaffItemDto.setDealCount(0);
        leadsStatisticsStaffItemDto.setClientCount(0);

        return leadsStatisticsStaffItemDto;
    }

    private LeadsDepartmentStaffStatisticsDto initPathForLeadsDepartmentStaffStatisticsDto(LeadsDepStaffStatisticsQuery request) {
        LeadsDepartmentStaffStatisticsDto leadsDepartmentStaffStatisticsDto = new LeadsDepartmentStaffStatisticsDto();
        leadsDepartmentStaffStatisticsDto.setDepSimpleInfoList(setDepartmentPath(request));

        return leadsDepartmentStaffStatisticsDto;
    }

    /**
     * 设置查询部门路径
     */
    private List<LeadsDepSimpleDto> setDepartmentPath(LeadsDepStaffStatisticsQuery request) {
        LeadsDepSimpleDto all = new LeadsDepSimpleDto();
        all.setDepartmentName("全部");
        List<LeadsDepSimpleDto> departmentIdNameList = Lists.newArrayList(all);
        if (!ObjectUtils.isEmpty(request.getDepartmentId())) {
            List<DepartmentDto> parentDepartmentIds = departmentClientForLeads.getParentDepartmentIds(request.getBid(), request.getDepartmentId());
            departmentIdNameList.addAll(parentDepartmentIds.stream()
                    .map(departmentDto -> {
                        LeadsDepSimpleDto departmentIdNameDto = new LeadsDepSimpleDto();
                        departmentIdNameDto.setDepartmentName(departmentDto.getName());
                        departmentIdNameDto.setDepartmentId(departmentDto.getId());
                        return departmentIdNameDto;
                    }).collect(Collectors.toList()));

        }
        return departmentIdNameList;
    }


    private Map<Long, StaffIdAndAgentIdDto> getLeadsStaffAndAgentName(Integer bid, Set<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Maps.newHashMap();
        }

        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(Lists.newArrayList(staffIds));
        List<StaffIdAndAgentIdDto> staffIdAndAgentIdDto = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
        return staffIdAndAgentIdDto.stream().collect(Collectors.toMap(StaffIdAndAgentIdDto::getId, t -> t));
    }


    private Map<Long, LocalDateTime> getLeadsDistributeTime(Integer bid, List<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return Maps.newHashMap();
        }
        return leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, leadsIds)
                        .select(Leads.ID, Leads.DISTRIBUTE_TIME)
        ).stream().collect(Collectors.toMap(Leads::getId, Leads::getDistributeTime));
    }

    private void installFollowInfo(LeadsQuery request, List<LeadsListItemDto> leadsListItemDtos, Boolean pcOperate) {
        if (CollectionUtils.isEmpty(leadsListItemDtos)) {
            return;
        }
        //如果是否包含跟进记录入参为true则将该线索最新的一条跟进记录返回,数据供小程序线索列表使用
        Boolean withFollow = request.getWithFollow();

        if (withFollow == null || !withFollow) {
            return;
        }

        List<Long> leadsIds = leadsListItemDtos.stream().map(LeadsListItemDto::getId).collect(Collectors.toList());
//        Map<Long, LeadsFollow> followMap = leadsFollowManager.getLastFollowRecordList(leadsIds, pcOperate).stream()
//                .collect(Collectors.toMap(LeadsFollow::getLeadsId, t -> t));
        Map<Long, LeadsFollow> followMap = getLastLeadsFollowMap(request, pcOperate, leadsIds);

        //为每一个线索组装最新的跟进记录数据
        leadsListItemDtos.forEach(item -> {
            LeadsFollow leadsFollow = followMap.get(item.getId());
            if (leadsFollow == null) {
                return;
            }
            LeadsFollowSimpleDto simpleDto = new LeadsFollowSimpleDto();
            simpleDto.setCreateTime(leadsFollow.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            simpleDto.setFollowContent(leadsFollow.getFollowContent());
            simpleDto.setId(leadsFollow.getId());
            item.setFollow(simpleDto);

        });

    }

    private Map<Long, LeadsFollow> getLastLeadsFollowMap(LeadsQuery request, Boolean pcOperate, List<Long> leadsIds) {
        Map<Long, List<LeadsFollow>> tmpMap = Maps.newHashMap();
        Map<Long, LeadsFollow> followMap = Maps.newHashMap();
//        Map<Long, Set<Long>> recoverIdsMap = leadsManager.list(
//                Wrappers.<Leads>query()
//                        .eq(Leads.BID, request.getBid())
//                        .in(Leads.ID, leadsIds)
//                        .select(Leads.RECOVERY_FROM_IDS)
//        ).stream().collect(Collectors.toMap(Leads::getId, e -> leadsManager.strToIds(e.getId(), e.getRecoveryFromIds())));
//        recoverIdsMap.forEach((leadsId, recoveryIds) -> leadsIds.addAll(recoveryIds));
        List<LeadsFollow> list = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, request.getBid())
                        .ne(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .apply(!pcOperate, "follow_content NOT REGEXP '处回收|转交'")
        );

        list.forEach(
                e -> tmpMap.computeIfAbsent(e.getLeadsId(), leadsId -> Lists.newArrayList()).add(e)
        );
        tmpMap.forEach(
                (k, v) -> {
                    if (CollectionUtils.isEmpty(v)) {
                        return;
                    }
                    followMap.put(k, v.stream().sorted(Comparator.comparing(LeadsFollow::getCreateTime).reversed()).collect(Collectors.toList()).get(0));
                }
        );
        return followMap;
    }

    private List<LeadsGroupDto> installIsGroupInfo(LeadsQuery request) {
        //根据是否需要返回分组聚合数据标识判断并在为true时组装聚合数据，1为待联系，2为跟进中，3为已成交
        Boolean withGroups = request.getWithGroups();
        if (withGroups != null && request.getWithGroups()) {
            LeadsStatusRequest leadsStatusRequest = new LeadsStatusRequest();
            leadsStatusRequest.setBid(request.getBid());
            leadsStatusRequest.setOperatorId(request.getOperatorId());
            leadsStatusRequest.setStaffId(request.getStaffId());
            BaseResponse<LeadsStatusDto> leadsStatusStatistics = leadsStatisticsService.getLeadsStatusStatistics(leadsStatusRequest);
            if (leadsStatusStatistics != null && leadsStatusStatistics.getData() != null) {
                LeadsStatusDto leadsStatusDto = leadsStatusStatistics.getData();
                LeadsGroupDto all = new LeadsGroupDto();
                all.setGroupId(0);
                all.setLeadsCount(leadsStatusDto.getAllLeadsCount());
                LeadsGroupDto notContact = new LeadsGroupDto();
                notContact.setGroupId(1);
                notContact.setLeadsCount(leadsStatusDto.getNotContactedCount());
                LeadsGroupDto inContact = new LeadsGroupDto();
                inContact.setGroupId(2);
                inContact.setLeadsCount(leadsStatusDto.getFollowedCount());
                LeadsGroupDto contacted = new LeadsGroupDto();
                contacted.setGroupId(3);
                contacted.setLeadsCount(leadsStatusDto.getCompletedCount());
                return Lists.newArrayList(all, notContact, inContact, contacted);
            }
        }
        return Lists.newArrayList();
    }


    private List<LeadsGroupDto> installIsGroupInfoForList(LeadsQuery request) {
        //根据是否需要返回分组聚合数据标识判断并在为true时组装聚合数据，1为待联系，2为跟进中，3为已成交
        Boolean withGroups = request.getWithGroups();
        if (withGroups != null && request.getWithGroups()) {
            LeadsStatusRequest leadsStatusRequest = new LeadsStatusRequest();
            leadsStatusRequest.setBid(request.getBid());
            leadsStatusRequest.setOperatorId(request.getOperatorId());
            leadsStatusRequest.setStaffId(request.getStaffId());
            leadsStatusRequest.setStartDistributeTime(DateTimeUtils.toLocalDateTime(request.getDistributeTimeStart()));
            leadsStatusRequest.setEndDistributeTime(DateTimeUtils.toLocalDateTime(request.getDistributeTimeEnd()));
            leadsStatusRequest.setStartLastFollowTime(DateTimeUtils.MillisToLocalDateTime(request.getStartLastFollowTime()));
            leadsStatusRequest.setEndLastFollowTime(DateTimeUtils.MillisToLocalDateTime(request.getEndLastFollowTime()));
            BaseResponse<LeadsStatusDto> leadsStatusStatistics = leadsStatisticsService.getLeadsStatusStatisticsForList(leadsStatusRequest);
            if (leadsStatusStatistics != null && leadsStatusStatistics.getData() != null) {
                LeadsStatusDto leadsStatusDto = leadsStatusStatistics.getData();

                LeadsGroupDto all = new LeadsGroupDto(0, "全部", leadsStatusDto.getAllLeadsCount());
                LeadsGroupDto notContractedCount = new LeadsGroupDto(1, "待联系", leadsStatusDto.getNotContactedCount());

                LeadsGroupDto followingUpCount = new LeadsGroupDto(12, "跟进中", leadsStatusDto.getFollowingUpCount());
                LeadsGroupDto followingUpCountAll = new LeadsGroupDto(12, "全部", followingUpCount.getLeadsCount());
                // LeadsGroupDto followingUpCountUnsure = new LeadsGroupDto(121, "待再次联系", leadsStatusDto.getUnsureIntentCount());
                LeadsGroupDto followingUpCountOrdered = new LeadsGroupDto(122, "已下定", leadsStatusDto.getOrderedCount());
                LeadsGroupDto followingUpCountMeasured = new LeadsGroupDto(123, "已量尺", leadsStatusDto.getMeasuredCount());
                LeadsGroupDto contract = new LeadsGroupDto(126, "已联系", leadsStatusDto.getContractedCount());
                LeadsGroupDto intent = new LeadsGroupDto(127, "有意向", leadsStatusDto.getIntentCount());
                LeadsGroupDto store = new LeadsGroupDto(128, "已到店", leadsStatusDto.getStoredCount());
                followingUpCount.setChildren(Lists.newArrayList(followingUpCountAll, contract, intent, followingUpCountOrdered, followingUpCountMeasured, store));

                LeadsGroupDto lost = new LeadsGroupDto(14, "已流失", leadsStatusDto.getLostCount());
                LeadsGroupDto deal = new LeadsGroupDto(15, "已成交", Optional.ofNullable(leadsStatusDto.getStoreOrderCount()).orElse(0));
                LeadsGroupDto dealAll = new LeadsGroupDto(15, "全部", deal.getLeadsCount());
                LeadsGroupDto traded = new LeadsGroupDto(124, "已签单", leadsStatusDto.getTradedCount());
                LeadsGroupDto toInstall = new LeadsGroupDto(125, "待安装", leadsStatusDto.getToInstallCount());
                LeadsGroupDto installed = new LeadsGroupDto(153, "已交付", leadsStatusDto.getInstalledCount());
                deal.setChildren(Lists.newArrayList(dealAll, traded, toInstall, installed));


                return Lists.newArrayList(all, notContractedCount, followingUpCount, deal, lost);
            }
        }
        return Lists.newArrayList();
    }

    private QueryWrapper<Leads> buildQuery(LeadsQuery request, Boolean isSort) {
        Integer regionId = request.getRegionId();
        Integer level = 0;
        if (regionId != null) {
            RegionGetRequest regionGetRequest = new RegionGetRequest();
            regionGetRequest.setId(regionId);
            // Region RPC调用
            BaseResponse<RegionDto> regionDtoBaseResponse = regionService.getRegion(regionGetRequest);
            if (BaseResponse.responseSuccessWithNonNullData(regionDtoBaseResponse)) {
                level = regionDtoBaseResponse.getData().getLevel();
            }
        }

        //组装查询数据
        QueryWrapper<Leads> query = Wrappers.query();
        String name = request.getName();
        String mobile = request.getMobile();
        Integer status = request.getStatus();
        Integer statusGroup = request.getStatusGroup();
        Long agentId = request.getAgentId();
        Long staffId = request.getStaffId();
        String importTimeStart = request.getImportTimeStart();
        String importTimeEnd = request.getImportTimeEnd();
        String distributeTimeStart = request.getDistributeTimeStart();
        String distributeTimeEnd = request.getDistributeTimeEnd();
        Integer channel = request.getChannel();
        Integer excludesChannel = request.getExcludesChannel();
        Integer channelType = request.getChannelType();
        Long channelSource = request.getChannelSource();
        Integer sortType = request.getSortType();
        String orderNum = request.getOrderNum();
        Long shopChannelId = request.getShopChannelId();
        Long preFollowStaffId = request.getFollowStaffId();
        Boolean showFollowStaff = request.getShowFollowStaff();
        Long preFollowStatus = request.getPreFollowStatus();
        List<Integer> statusList = request.getStatusList();
        Boolean hasTransferClient = request.getHasTransferClient();
        String keyword = request.getKeyword();

        Long lastUpdateTime = request.getLastUpdateTime();
        if (lastUpdateTime != null && lastUpdateTime > 0) {
            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(lastUpdateTime / 1000, 0, ZoneOffset.ofHours(8));
            query = query.gt(Leads.UPDATE_TIME, dateTime);
        }

        if (Objects.nonNull(request.getStartUpdateTime())) {
            query.ge(Leads.UPDATE_TIME, DateTimeUtils.MillisToLocalDateTime(request.getStartUpdateTime()));
        }
        if (Objects.nonNull(request.getEndUpdateTime())) {
            query.le(Leads.UPDATE_TIME, DateTimeUtils.MillisToLocalDateTime(request.getEndUpdateTime()));
        }

        query.eq(Leads.BID, request.getBid())
                .like(!StringUtils.isEmpty(name), Leads.NAME, name)
                .eq(!StringUtils.isEmpty(mobile), Leads.MOBILE, mobile)
                .eq(status != null, Leads.STATUS, status)
                .eq(preFollowStatus != null, Leads.PRE_FOLLOW_STATUS, preFollowStatus);

        if (statusGroup != null) {
            switch (statusGroup) {
                case 0:
                case 5:
                    // -4=删除 -2=分配失败 -1=员工退回 0=待分配
                    query.notIn(Leads.STATUS,
                            getNonAllocatedLeadsStatus()
                    );
                    break;
                case 1:
                    query.eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus());
                    break;
                case 2:
                    query.between(Leads.STATUS, LeadsStatusEnum.CONTACTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus());
                    break;
//                case 3:
//                    query.eq(Leads.STATUS, LeadsStatusEnum.TRADED.getStatus());
//                    break;
                case 4:
                    query.eq(Leads.STATUS, LeadsStatusEnum.TO_DISTRIBUTE.getStatus());
                    break;
                case 6:
                    query.between(Leads.STATUS, LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.INVALID.getStatus());
                    break;
                case 12:
                    query.notIn(Leads.STATUS, Sets.newHashSet(LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus(),
                            LeadsStatusEnum.LOST.getStatus(), LeadsStatusEnum.INVALID.getStatus(),
                            LeadsStatusEnum.RECOVERY.getStatus(), LeadsStatusEnum.DELETED.getStatus(), LeadsStatusEnum.PUSH_BACK.getStatus(),
                            LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(), LeadsStatusEnum.TO_DISTRIBUTE.getStatus()
                            , LeadsStatusEnum.TRADED.getStatus(), LeadsStatusEnum.TO_INSTALL.getStatus()));
                    break;
                case 13:
                    query.eq(Leads.STATUS, LeadsStatusEnum.INSTALLED.getStatus());
                    break;
                case 14:
                    query.eq(Leads.STATUS, LeadsStatusEnum.LOST.getStatus());
                    break;
//                case 15:
//                    query.in(Leads.STATUS, Sets.newHashSet(LeadsStatusEnum.TRADED.getStatus(), LeadsStatusEnum.TO_INSTALL.getStatus(), LeadsStatusEnum.INSTALLED.getStatus()));
//                    break;
                default:
                    break;
            }
        }
        Integer childStatusGroup = request.getChildStatusGroup();
        if (childStatusGroup != null) {
            switch (childStatusGroup) {
                case 121:
                    query.eq(Leads.STATUS, LeadsStatusEnum.UNSURE_INTENT.getStatus());
                    break;
                case 122:
                    query.eq(Leads.STATUS, LeadsStatusEnum.ORDERED.getStatus());
                    break;
                case 123:
                    query.eq(Leads.STATUS, LeadsStatusEnum.MEASURED.getStatus());
                    break;
                case 124:
                    query.eq(Leads.STATUS, LeadsStatusEnum.TRADED.getStatus());
                    break;
                case 125:
                    query.eq(Leads.STATUS, LeadsStatusEnum.TO_INSTALL.getStatus());
                    break;
                case 126:
                    query.in(Leads.STATUS, Sets.newHashSet(LeadsStatusEnum.CONTACTED.getStatus(), LeadsStatusEnum.SUCCESS_CONTACT.getStatus(), LeadsStatusEnum.UNSURE_INTENT.getStatus()));
                    break;
                case 127:
                    query.eq(Leads.STATUS, LeadsStatusEnum.INTENT.getStatus());
                    break;
                case 128:
                    query.eq(Leads.STATUS, LeadsStatusEnum.STORED.getStatus());
                    break;
                case 153:
                    query.eq(Leads.STATUS, LeadsStatusEnum.INSTALLED.getStatus());
                    break;
            }
        }
        if (!StringUtils.isEmpty(orderNum)) {
            query.eq(Leads.ORDER_SN, orderNum);
        }
        if (agentId != null) {
            //todo 兼容旧逻辑,下个需求修改
            Set<Long> deptIds = departmentClientForLeads.getDepIdsByAgentId(request.getBid(), agentId);
            List<UserStaffDto> userStaffDtoList = staffClientForLeads.getStaffByDeptIds(request.getBid(), deptIds);
            List<Long> staffIds = userStaffDtoList.stream().map(UserStaffDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(staffIds)) {
                //临时处理异常
                query.in(Leads.DISTRIBUTE_STAFF_ID, Lists.newArrayList(-1));
            } else {
                query.in(Leads.DISTRIBUTE_STAFF_ID, staffIds);
            }
        }

        if (staffId != null) {
            query.eq(Leads.DISTRIBUTE_STAFF_ID, staffId);
        }
        if (!StringUtils.isEmpty(importTimeStart) && !StringUtils.isEmpty(importTimeEnd)) {
            query.between(Leads.CREATE_TIME, importTimeStart, importTimeEnd);
        }
        if (!StringUtils.isEmpty(distributeTimeStart) && !StringUtils.isEmpty(distributeTimeEnd)) {
            query.between(Leads.DISTRIBUTE_TIME, distributeTimeStart, distributeTimeEnd);
        }
        if (channel != null) {
            query.eq(Leads.CHANNEL, channel);
        }
        if (excludesChannel != null) {
            query.ne(Leads.CHANNEL, excludesChannel);
        }
        if (channelType != null) {
            query.eq(Leads.CHANNEL_TYPE, channelType);
        }
        if (channelSource != null) {
            query.eq(Leads.CHANNEL_SOURCE, channelSource);
        }


        switch (level) {
            case 1:
                query.eq(Leads.PROVINCE_ID, regionId);
                break;
            case 2:
                query.eq(Leads.CITY_ID, regionId);
                break;
            case 3:
                query.eq(Leads.AREA_ID, regionId);
                break;
            default:
                break;
        }

        //根据排序类型对结果进行排序,0=按导入时间倒序 1=按分配时间倒序 2=更新时间，即最新的修改的线索时间排序
        if (sortType != null && isSort) {
            switch (sortType) {
                case 0:
                    query.orderByDesc(Leads.CREATE_TIME);
                    break;
                case 1:
                    query.orderByDesc(Leads.DISTRIBUTE_TIME);
                    break;
                case 2:
                    query.orderByDesc(Leads.UPDATE_TIME);
                    break;
                case 3:
                    query.orderByAsc(Leads.UPDATE_TIME);
                    break;
                case 4:
                    query.orderByDesc(Leads.LAST_FOLLOW_TIME, Leads.DISTRIBUTE_TIME);
                    break;
                default:
                    return query.orderByDesc(Leads.ID);
            }
        }

        if (shopChannelId != null && shopChannelId >= 0) {
            query.eq(Leads.CHANNEL_ID, shopChannelId);
        }

        if (preFollowStaffId != null && preFollowStaffId >= 0) {
            query.eq(Leads.PRE_FOLLOW_STAFF_ID, preFollowStaffId);
        }

        if (Objects.equals(showFollowStaff, Boolean.TRUE)) {
            query.gt(Leads.PRE_FOLLOW_STAFF_ID, 0);
        }

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(request.getTag())) {
            query.like(Leads.TAGS, request.getTag());
        }

        if (!StringUtils.isEmpty(request.getLevel())) {
            query.eq(Leads.LEVEL, request.getLevel());
        }

        query.eq(!ObjectUtils.isEmpty(request.getCreateStaffId()) && request.getCreateStaffId() != 0,
                        Leads.CREATED_STAFF_ID, request.getCreateStaffId())
                .eq(!ObjectUtils.isEmpty(request.getProvinceId()),
                        Leads.PROVINCE_ID, request.getProvinceId())
                .eq(!ObjectUtils.isEmpty(request.getCityId()),
                        Leads.CITY_ID, request.getCityId())
                .eq(!ObjectUtils.isEmpty(request.getAreaId()),
                        Leads.AREA_ID, request.getAreaId())
                .eq(!ObjectUtils.isEmpty(request.getPushBackStaffId()),
                        Leads.PUSH_BACK_STAFF_ID, request.getPushBackStaffId())
                .like(!ObjectUtils.isEmpty(request.getPushBackContent()),
                        Leads.ERROR_MSG, request.getPushBackContent())
                .gt(Objects.nonNull(request.getStartLastFollowTime()) && request.getStartLastFollowTime() > 0L, Leads.LAST_FOLLOW_TIME, DateTimeUtils.MillisToLocalDateTime(request.getStartLastFollowTime()))
                .lt(Objects.nonNull(request.getEndLastFollowTime()) && request.getEndLastFollowTime() > 0L, Leads.LAST_FOLLOW_TIME, DateTimeUtils.MillisToLocalDateTime(request.getEndLastFollowTime()))
                .eq(Objects.nonNull(request.getLevelId()) && request.getLevelId() > 0L, Leads.LEVEL_ID, request.getLevelId())
                .in(!CollectionUtils.isEmpty(request.getChannels()), Leads.CHANNEL, request.getChannels())
        ;


        if (!StringUtils.isEmpty(request.getPushBackEndTime()) && !StringUtils.isEmpty(request.getPushBackStartTime())) {
            query.between(Leads.PUSH_BACK_TIME, request.getPushBackStartTime(), request.getPushBackEndTime());
        }

        if (!ObjectUtils.isEmpty(statusList)) {
            query.in(Leads.STATUS, statusList);
        }

        if (!ObjectUtils.isEmpty(hasTransferClient)) {
            if (hasTransferClient) {
                query.gt(Leads.CLIENT_ID, 0);
            }
            if (!hasTransferClient) {
                query.eq(Leads.CLIENT_ID, 0);
            }
        }

        boolean digits = NumberUtils.isDigits(keyword);
        Long num;
        if (digits) {
            num = Long.parseLong(keyword);
        } else {
            num = null;
        }

        query.and(!ObjectUtils.isEmpty(keyword),
                leadsQueryWrapper -> leadsQueryWrapper
                        .like(Leads.NAME, keyword).or()
                        .like(digits, Leads.MOBILE, num).or()
                        .like(Leads.ADDRESS, keyword).or()
                        .like(Leads.CITY_NAME, keyword).or()
                        .like(Leads.AREA_NAME, keyword).or()
                        .like(Leads.PROVINCE_NAME, keyword).or()
                        .like(Leads.WE_CHAT, keyword)
        );
        if (Objects.nonNull(request.getLeadsId()) && request.getLeadsId() > 0) {
            query.eq(Leads.ID, request.getLeadsId());
        }

        return query;
    }

    /**
     * 导出查询数据
     *
     * @param query 筛选条件
     * @return 导出的数据文件地址
     */
    @Override
    public BaseResponse<String> export(LeadsQuery query) {
        Integer bid = query.getBid();

        BoolQueryBuilder qb = getQueryBuilder(query);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(qb)
                .from(query.getPageNo()).size(query.getPageSize());

        buildSort(query.getSortType(), searchSourceBuilder);


        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);

        List<LeadsExcelDto> excelDtoList = Lists.newArrayList();
        try {
            SearchResponse resp = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (resp.getHits() == null && resp.getHits().getTotalHits().value != 0) {
                //处理数据
                List<LeadsEsDto> esDtoList = Arrays.stream(resp.getHits().getHits()).map(
                        e -> jsonService.toObject(e.getSourceAsString(), LeadsEsDto.class)).collect(Collectors.toList());
                excelDtoList = esDtoList.stream().map(esDto -> {
                    LeadsExcelDto leadsExcelDto = new LeadsExcelDto();
                    leadsExcelDto.setBid(bid);
                    leadsExcelDto.setName(esDto.getName());
                    leadsExcelDto.setMobile(esDto.getMobile());
                    if (Objects.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus(), esDto.getStatus())) {
                        leadsExcelDto.setStatus(LeadsStatusEnum.parse(esDto.getPreFollowStatus()).getName());
                    } else {
                        leadsExcelDto.setStatus(LeadsStatusEnum.parse(esDto.getStatus()).getName());
                    }
                    leadsExcelDto.setTags(esDto.getTags());
                    leadsExcelDto.setStaffName(esDto.getStaffName());
                    leadsExcelDto.setStaffDepartment(esDto.getDepartmentName());
                    leadsExcelDto.setAgentName(esDto.getAgentName());
                    leadsExcelDto.setProvinceId(esDto.getProvinceId());
                    leadsExcelDto.setProvinceName(esDto.getProvinceName());
                    leadsExcelDto.setCityId(esDto.getCityId());
                    leadsExcelDto.setCityName(esDto.getCityName());
                    leadsExcelDto.setAreaId(esDto.getAreaId());
                    leadsExcelDto.setAreaName(esDto.getAreaName());
                    leadsExcelDto.setAddress(esDto.getAddress());
                    leadsExcelDto.setGoodsName(esDto.getGoodsName());
                    leadsExcelDto.setOrderSn(esDto.getOrderSn());
                    leadsExcelDto.setOrderAccount(esDto.getOrderAccount());
                    String extData = esDto.getExtData();
                    if (!StringUtils.isEmpty(extData)) {
                        LeadsExtDataDto leadsExtDataDto = jsonService.toObject(extData, LeadsExtDataDto.class);
                        Integer isRefund = leadsExtDataDto.getIsRefund();
                        leadsExcelDto.setIsRefund(isRefund != null ? isRefund == 1 ? "是" : "否" : null);
                        leadsExcelDto.setRefundRemark(leadsExtDataDto.getRefundRemark());
                    }
                    leadsExcelDto.setPrice(esDto.getPayAmount());
                    leadsExcelDto.setChannelName(LeadsChannelUtil.getName(bid, esDto.getChannel()));
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime createTime = LocalDateTimeUtil.of(esDto.getCreateTime());
                    if (createTime != null) {
                        leadsExcelDto.setCreateTime(dtf.format(createTime));
                    }
                    LocalDateTime distributeTime = LocalDateTimeUtil.of(esDto.getDistributeTime());
                    if (distributeTime != null) {
                        leadsExcelDto.setDistributeTime(dtf.format(distributeTime));
                    }
                    return leadsExcelDto;
                }).collect(Collectors.toList());

            }
        } catch (IOException e) {
            logger.error("ES获取guide-cudtomer数据异常");
        }

        if (CollectionUtils.isEmpty(excelDtoList)) {
            throw new InngkeServiceException("无导出数据");
        }

        // 输出文件
        ExcelUtils<LeadsExcelDto> excelUtils = new ExcelUtils<>(LeadsExcelDto.class);
        File file = excelUtils.writeExcelFile(excelDtoList, SHEET_NAME, "已分配线索列表.xlsx");
        String url;
        try {
            url = inngkeUploaderService.builder(bid, query.getOperatorId(), null)
                    .setModelName(bid + "/leads")
                    .build()
                    .uploadFile(file);
        } catch (Exception e) {
            throw new InngkeServiceException("OSS文件上传失败");
        } finally {
            if (file != null) {
                file.delete();
            }
        }
        return BaseResponse.success(StaticResourceUtils.getFullUrl(url));
    }

    /**
     * 处理退款信息
     *
     * @param leadsExcelDto excel
     * @param leads         线索
     */
    private void handleReimburseInfo(LeadsExcelDto leadsExcelDto, Leads leads) {
        String extData = leads.getExtData();
        if (!StringUtils.isEmpty(extData)) {
            LeadsExtDataDto leadsExtDataDto = jsonService.toObject(extData, LeadsExtDataDto.class);
            Integer isRefund = leadsExtDataDto.getIsRefund();
            leadsExcelDto.setIsRefund(isRefund != null ? isRefund == 1 ? "是" : "否" : null);
            leadsExcelDto.setRefundRemark(leadsExtDataDto.getRefundRemark());
        }
    }

    /**
     * 获取线索详情
     *
     * @param request 线索请求
     * @return 线索详情
     */
    @Override
    public BaseResponse<LeadsDto> getLeads(LeadsGetRequest request) {
        logger.info("getLeads request bid {} id {}", request.getBid(), request.getBid());
        Integer bid = request.getBid();
        Leads leads = leadsManager.getOne(Wrappers.<Leads>query().eq(Leads.BID, bid).eq(Leads.ID, request.getId()));
        if (leads==null){
            return BaseResponse.error("找不到有效的线索");
        }
        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(Lists.newArrayList(leads), bid);

        LeadsVo leadsVo = LeadsConverter.toLeadsListItem(leads, staffAndAgent);
        if (ObjectUtils.isEmpty(leadsVo)) {
            return BaseResponse.success(leadsVo);
        }

        //2022-10-24 添加字段
        StaffDto staff = staffClientForLeads.getStaffById(bid, leadsVo.getCreateStaffId());
        if (!ObjectUtils.isEmpty(staff)) {
            leadsVo.setCreateStaffName(staff.getName());
        }
        if (!StringUtils.isEmpty(leads.getFollowStatuses())) {
            leadsVo.setFollowStatuses(jsonService.toObjectList(leads.getFollowStatuses(), LeadsFollowStatusDto.class));
        }
        if (!StringUtils.isEmpty(leads.getKfFollowStatuses())) {
            leadsVo.setKfFollowStatuses(jsonService.toObjectList(leads.getKfFollowStatuses(), LeadsFollowStatusDto.class));
        }

        return BaseResponse.success(leadsVo);
    }

    /**
     * 获取线索有效经销商列表
     *
     * @param request 请求
     * @return 经销商列表
     */
    @Override
    public BaseResponse<List<LeadsAgentSimpleInfoDto>> getAgentList(BaseBidOptRequest request) {
        Integer bid = request.getBid();
        List<Leads> list = leadsManager.list(Wrappers.<Leads>query().eq(Leads.BID, bid).notIn(Leads.STATUS, Lists.newArrayList(0, -4, -5)));
        /*
         * 对结果集进行分组，分组后为一个map，map的key为经销商Id,value为该经销商下的线索数量
         * 1. 通过线索找到分配人员，在通过这个找到经销商
         */
        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(list, bid);
        Map<Long, Integer> agentIdAndLeadsCountMap = Maps.newHashMap();
        list.forEach(leads -> {
            Long distributeStaffId = leads.getDistributeStaffId();
            Long agentId = staffAndAgent.get(distributeStaffId);
            Integer leadsCount = agentIdAndLeadsCountMap.get(agentId);
            if (Objects.isNull(leadsCount)) {
                agentIdAndLeadsCountMap.put(agentId, 1);
            } else {
                Integer nowLeadsCount = agentIdAndLeadsCountMap.get(agentId);
                agentIdAndLeadsCountMap.put(agentId, nowLeadsCount + 1);
            }
        });
        //组装dubbo接口入参调用用户服务获取经销商名称信息集合
        Set<Long> ids = agentIdAndLeadsCountMap.keySet();
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.success(Lists.newArrayList());
        }
        List<AgentIdAndNameDto> agentIdAndNameDtos = getAgentInfos(bid, ids);
        //获取经销商名称集合以后组装本接口回参（经销商编号，经销商名称，经销商下的线索个数）
        List<LeadsAgentSimpleInfoDto> leadsAgentSimpleInfos = agentIdAndNameDtos.stream().map(item -> {
            LeadsAgentSimpleInfoDto agentSimpleInfoDto = new LeadsAgentSimpleInfoDto();
            Long id = item.getId();
            agentSimpleInfoDto.setId(item.getId());
            agentSimpleInfoDto.setName(item.getName());
            agentSimpleInfoDto.setLeadsCount(agentIdAndLeadsCountMap.get(id));
            return agentSimpleInfoDto;
        }).collect(Collectors.toList());
        return BaseResponse.success(leadsAgentSimpleInfos);
    }

    /**
     * 根据id获取线索简要信息
     *
     * @param request 线索获取检索数据
     * @return 线索简要信息
     */
    @Override
    public BaseResponse<LeadsSimpleInfoDto> getLeadsSimpleInfoById(LeadsGetRequest request) {
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.ID, request.getId())
                        .select(Leads.ID, Leads.MOBILE, Leads.CUSTOMER_ID, Leads.DISTRIBUTE_TIME)
        );
        if (leads == null) {
            return BaseResponse.error("无法找到id为->" + request.getId() + "的线索信息");
        }
        LeadsSimpleInfoDto leadsSimpleInfoDto = new LeadsSimpleInfoDto();
        leadsSimpleInfoDto.setId(leads.getId());
        leadsSimpleInfoDto.setCustomerId(leads.getCustomerId());
        leadsSimpleInfoDto.setMobile(leads.getMobile());
        leadsSimpleInfoDto.setDistributeTime(leads.getDistributeTime());
        return BaseResponse.success(leadsSimpleInfoDto);
    }

    /**
     * @param request 看之前的 员工必填，且员工必须有部门权限
     */
    @Override
    public BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsList(LeadsDepStaffStatisticsQuery request) {

        Long operatorId = request.getOperatorId();
        Long staffId = request.getStaffId();
        Integer bid = request.getBid();
        List<Long> allPermission = Lists.newArrayList();

        //查询出当前操作员可以管理的所有部门
        LoginDto loginDto = loginClientForLeads.getLoginDtoByStaffId(bid, staffId);
//        Set<Long> manageDepartmentIds = staffManageDepartmentClientForLeads.getStaffManagerTopDepartmentIds(bid, staffId);
        Set<Long> manageDepartmentIds = loginDto.getManageDepartmentIds();

        //管理的部门包括子部门
        if (CollectionUtils.isEmpty(manageDepartmentIds)) {
            return BaseResponse.error("当前操作员无部门权限");
        }
        List<Long> permissions = Lists.newArrayList(manageDepartmentIds);
        allPermission.addAll(manageDepartmentIds);
        logger.info("getLeadsStatisticsList manageDepartmentIds size: {}", manageDepartmentIds.size());

        List<DepartmentDto> childDepartmentDtoList = Lists.newArrayList();
        Long departmentId = request.getDepartmentId();
        boolean emptyDep = departmentId == null || request.getDepartmentId() == 0;
        //当前请求的部门编号信息为空时需要把当前的部门设定为可管理部门的第一个部门的上级部门
        DepartmentDto rootDepartmentDto = departmentClientForLeads.getDepartmentById(bid, 0L);
        if (emptyDep) {
            departmentId = rootDepartmentDto.getId();
            if (permissions.contains(departmentId)) {
                childDepartmentDtoList = rootDepartmentDto.getChildren();
            } else {
                childDepartmentDtoList = departmentClientForLeads.getDepartmentByIds(bid, permissions);
            }
        } else {
            if (departmentId.equals(rootDepartmentDto.getId())) {
                childDepartmentDtoList = rootDepartmentDto.getChildren();
            } else {
                List<DepartmentDto> departmentDtoList = departmentClientForLeads.getDepartmentByIds(bid, Lists.newArrayList(departmentId));
                childDepartmentDtoList = departmentDtoList.get(0).getChildren();
            }
        }

        LeadsDepartmentStaffStatisticsDto result = new LeadsDepartmentStaffStatisticsDto();
        Map<Long, LeadsStatisticsBaseInfoDto> staffLeadsEsDtoList = Maps.newHashMap();
        String keyword = request.getKeyword();

        List<DepartmentDto> departmentWithParents = departmentClientForLeads.getDepartmentWithParents(bid, departmentId);
        List<LeadsDepSimpleDto> resultDepSimpleList = departmentWithParents.stream().map(item -> {
            LeadsDepSimpleDto leadsDepSimpleDto = new LeadsDepSimpleDto();
            leadsDepSimpleDto.setDepartmentId(item.getId());
            leadsDepSimpleDto.setDepartmentName(item.getName());
            return leadsDepSimpleDto;
        }).collect(Collectors.toList());
        result.setDepSimpleInfoList(resultDepSimpleList);

        // 查找子部门的员工
        if (!StringUtils.isEmpty(keyword)) {
            // 查询员工信息
            StaffListRequest staffListRequestByName = new StaffListRequest();
            staffListRequestByName.setBid(bid);
            staffListRequestByName.setName(keyword);
            staffListRequestByName.setGtStatus(StaffStatusEnum.DELETE.getCode());
            List<StaffDto> staffListByNameList = staffClientForLeads.getStaffList(staffListRequestByName);
            // 查询该员工所管理的所有部门下的员工
            Set<Long> managerStaffIds = staffListByNameList.stream().filter(item -> permissions.contains(item.getDepartmentId())).map(StaffDto::getId).collect(Collectors.toSet());
            logger.info("getLeadsStatisticsList managerStaffIds :{}", managerStaffIds);
            BaseResponse<Map<Long, LeadsStatisticsBaseInfoDto>> leadsListByStaffIdsResponse = leadsEsInfoGetService.getLeadsListByStaffIds(bid, Lists.newArrayList(managerStaffIds), permissions, request.getStartDistributeTime(), request.getEndDistributeTime());
            if (!BaseResponse.responseSuccessWithNonNullData(leadsListByStaffIdsResponse)) {
                logger.info("商户编号:{},部门编号：{},部门下的姓名为{}的员工无线索数据", bid, departmentId, keyword);
            } else {
                staffLeadsEsDtoList = leadsListByStaffIdsResponse.getData();
            }
        } else {
            StaffListRequest staffListRequestByDepartmentId = new StaffListRequest();
            staffListRequestByDepartmentId.setDepartmentId(departmentId);
            staffListRequestByDepartmentId.setBid(bid);
            staffListRequestByDepartmentId.setGtStatus(StaffStatusEnum.DELETE.getCode());
            Set<Long> staffIds = staffClientForLeads.getStaffList(staffListRequestByDepartmentId).stream().map(StaffDto::getId).collect(Collectors.toSet());
            LeadsStaffIdsEsGetRequest staffIdsReq = new LeadsStaffIdsEsGetRequest();
            staffIdsReq.setBid(bid);
            staffIdsReq.setOperatorId(operatorId);
            staffIdsReq.setStaffIds(staffIds);
            logger.info("当前部门{} 下的员工 {}", departmentId, staffIds);
            BaseResponse<Map<Long, LeadsStatisticsBaseInfoDto>> leadsListByStaffIdsResponse = leadsEsInfoGetService.getLeadsListByStaffIds(bid, new ArrayList<>(staffIds), permissions, request.getStartDistributeTime(), request.getEndDistributeTime());
            if (!BaseResponse.responseSuccessWithNonNullData(leadsListByStaffIdsResponse)) {
                logger.info("商户编号:{},部门编号：{} staffIds: {},部门下的员工无线索数据", bid, departmentId, staffIds);
            } else {
                staffLeadsEsDtoList = leadsListByStaffIdsResponse.getData();
            }

            //处理部门信息
            List<DepartmentDto> children = childDepartmentDtoList;
            if (!CollectionUtils.isEmpty(children)) {
                logger.info("getLeadsStatisticsList children size: {}", children.size());
                // 过滤掉不属于该员工管理的部门
                children = children.stream().filter(item -> permissions.contains(item.getId())).collect(Collectors.toList());
                logger.info("getLeadsStatisticsList children filter size: {}", children.size());
                // 抽取出部门编号
                List<Long> depIds = children.stream().map(DepartmentDto::getId).collect(Collectors.toList());

                // 获取各个子部门的员工人数
                GetDepartmentChildIdsRequest getDepartmentChildIdsRequest = new GetDepartmentChildIdsRequest();
                if (!CollectionUtils.isEmpty(depIds)) {
                    getDepartmentChildIdsRequest.setDepartmentIds(depIds);
                }
                getDepartmentChildIdsRequest.setBid(request.getBid());
                getDepartmentChildIdsRequest.setPressionList(permissions);
                // key-departmentId
                Map<Long, Integer> departmentStaffCountMap = getDepartmentStaffCount(getDepartmentChildIdsRequest);
                //组装当前部门下子级部门的聚合线索数据
                Map<Long, LeadsStatisticsBaseInfoDto> finalDepPolymerizeMap = leadsEsInfoGetService.getDepLeadsListIncludeSubsByDepId(bid, depIds, permissions, request.getStartDistributeTime(), request.getEndDistributeTime()).getData();
                logger.info("getLeadsStatisticsList finalDepPolymerizeMap size: {}", finalDepPolymerizeMap.size());
                logger.info("getLeadsStatisticsList finalDepPolymerizeMap: {}", finalDepPolymerizeMap);

                List<LeadsStatisticsDepItemDto> departments = Lists.newArrayList();
                result.setDepartments(departments);
                children.forEach(item -> {
                    Long childrenDeptId = item.getId();
                    String departmentName = item.getName();
                    LeadsStatisticsBaseInfoDto leadsStatisticsBaseInfoDto = finalDepPolymerizeMap.get(childrenDeptId);
                    LeadsStatisticsDepItemDto leadsStatisticsDepItemDto = new LeadsStatisticsDepItemDto();
                    leadsStatisticsDepItemDto.setDepartmentId(childrenDeptId);
                    leadsStatisticsDepItemDto.setDepartmentName(departmentName);
                    if (leadsStatisticsBaseInfoDto != null) {
                        installBaseInfo(leadsStatisticsBaseInfoDto, leadsStatisticsDepItemDto);
                    } else {
                        leadsStatisticsDepItemDto.setDealCount(0);
                        leadsStatisticsDepItemDto.setContactTimelyCount(0);
                        leadsStatisticsDepItemDto.setTotalCount(0);
                        leadsStatisticsDepItemDto.setInContactCount(0);
                        leadsStatisticsDepItemDto.setToBeContactedCount(0);
                    }

                    if (!CollectionUtils.isEmpty(departmentStaffCountMap)) {
                        Integer departmentStaffCount = departmentStaffCountMap.get(item.getId());
                        if (Objects.nonNull(departmentStaffCount)) {
                            leadsStatisticsDepItemDto.setStaffNum(departmentStaffCount);
                        } else {
                            leadsStatisticsDepItemDto.setStaffNum(0);
                        }
                    } else {
                        leadsStatisticsDepItemDto.setStaffNum(0);
                    }

                    departments.add(leadsStatisticsDepItemDto);
                });
            }
        }

        //组装员工信息
        if (!CollectionUtils.isEmpty(staffLeadsEsDtoList)) {
            Map<Long, StaffDto> qyWxStaffSimpleInfosMap = getStaffMap(bid, Lists.newArrayList(staffLeadsEsDtoList.keySet()));
            List<Long> staffIds = new ArrayList<>(staffLeadsEsDtoList.keySet());
            StaffListRequest staffListRequestFromEsRequest = new StaffListRequest();
            staffListRequestFromEsRequest.setIds(staffIds);
            staffListRequestFromEsRequest.setBid(bid);
            List<StaffDto> staffFromEsList = staffClientForLeads.getStaffList(staffListRequestFromEsRequest);
            List<LeadsStatisticsStaffItemDto> staffResultList = Lists.newArrayList();
            result.setStaffs(staffResultList);

            Map<Long, CardDto> cardDtoMap = cardClientForLeads.getCardDtoListByStaffIds(bid, staffIds);

            staffLeadsEsDtoList.forEach((key, list) -> {
                LeadsStatisticsStaffItemDto staffItemDto = new LeadsStatisticsStaffItemDto();
                StaffDto staffDtoItem = qyWxStaffSimpleInfosMap.get(key);
                staffItemDto.setStaffId(key);
                staffItemDto.setStaffName(staffDtoItem.getName());
                staffItemDto.setPosition(staffDtoItem.getPosition());
                CardDto cardDto = cardDtoMap.get(key);
                if (cardDto != null) {
                    staffItemDto.setAvatar(cardDto.getAvatarUrl());
                }
                installBaseInfo(list, staffItemDto);
                staffResultList.add(staffItemDto);
            });
        }
        //重新排序
        List<LeadsStatisticsDepItemDto> departments = result.getDepartments();
        if (!CollectionUtils.isEmpty(departments)) {
            result.setDepartments(departments.stream().sorted(Comparator.comparing(LeadsStatisticsBaseInfoDto::getTotalCount).reversed()).collect(Collectors.toList()));
        }
        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsList2(LeadsDepStaffStatisticsQuery query) {

        if (Objects.isNull(query.getDepartmentId())) {
            Set<Long> staffManagerDepartmentIds = staffManageDepartmentClientForLeads.getStaffManagerDepartmentIds(query.getBid(), query.getOperatorStaffId());
            Set<Long> topDepartmentIds = departmentClientForLeads.filterTopDepartment(query.getBid(), staffManagerDepartmentIds);
            if (!CollectionUtils.isEmpty(topDepartmentIds)) {
                List<DepartmentDto> topDepartmentList = departmentClientForLeads.findDepartmentByIds(query.getBid(), topDepartmentIds);
                if (!CollectionUtils.isEmpty(topDepartmentList)) {
                    DepartmentDto minLevelDepartment = topDepartmentList.stream().min(Comparator.comparing(DepartmentDto::getLevel)).stream().findFirst().orElse(null);
                    Long parentId = minLevelDepartment.getParentId();
                    if (Objects.nonNull(parentId) && 0L < parentId) {
                        DepartmentDto rootDepartment = departmentClientForLeads.getRootDepartment(query.getBid());
                        if (rootDepartment.getId().equals(parentId)) {
                            query.setDepartmentId(rootDepartment.getId());
                        } else {
                            query.setDepartmentId(parentId);
                        }
                    }
                }
            }
        }

        LeadsDepartmentStaffStatisticsDto result = new LeadsDepartmentStaffStatisticsDto();
        result.setDepSimpleInfoList(setDepartmentPath(query));
        result.setDepartments(Lists.newArrayList());
        result.setStaffs(Lists.newArrayList());

        // 计算部门权限，如果未传入部门，则使用管理权限的顶级部门，如传入，则以传入部门作为顶级部门，获取有权限的下级部门
        Set<Long> topManagerDepartmentIds = staffManageDepartmentClientForLeads
                .getStaffManagerTopDepartmentIds(query.getBid(), query.getOperatorStaffId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(topManagerDepartmentIds)) {
            logger.info("未获取到员工[{}]管理权限", query.getOperatorStaffId());
            return BaseResponse.success(result);
        }
        query.setManagerDepartmentIds(topManagerDepartmentIds);

        Set<Long> departmentChildren = Sets.newHashSet();
        if (Objects.nonNull(query.getDepartmentId()) && query.getDepartmentId() > 0L) {
            GetDepartmentRequest departmentRequest = new GetDepartmentRequest();
            departmentRequest.setDepartmentId(query.getDepartmentId());
            departmentRequest.setBid(query.getBid());
            departmentRequest.setChildrenLevel(1);
            List<DepartmentDto> departmentChild = departmentClientForLeads.getDepartmentChild(departmentRequest);
            departmentChildren = departmentChild.stream().map(DepartmentDto::getId).collect(Collectors.toSet());
            departmentChildren.remove(query.getDepartmentId());
        } else {
            DepartmentDto rootDepartment = departmentClientForLeads.getRootDepartment(query.getBid());
            departmentChildren = Sets.newHashSet(rootDepartment.getId());
        }
        query.setFilterDepartmentIds(departmentChildren);

        StaffListRequest staffRequest = new StaffListRequest();
        staffRequest.setBid(query.getBid());
        staffRequest.setName(query.getKeyword());
        staffRequest.setGtStatus(StaffStatusEnum.DELETE.getCode());
        if (!StringUtils.isEmpty(query.getKeyword())) {
            List<StaffDto> staffList = staffClientForLeads.getStaffList(staffRequest);
            query.setFilterStaffIds(staffList.stream().map(StaffDto::getId).collect(Collectors.toSet()));
            query.setFilterDepartmentIds(Sets.newHashSet());
        } else if (StringUtils.isEmpty(query.getKeyword()) && Objects.nonNull(query.getDepartmentId())) {
            Long departmentId = query.getDepartmentId();
            staffRequest.setDepartmentId(departmentId);
            List<StaffDto> staffList = staffClientForLeads.getStaffList(staffRequest);
            query.setFilterStaffIds(staffList.stream().map(StaffDto::getId).collect(Collectors.toSet()));
        }
        // 计算聚合根数据
        result = computeAggregateData(query, result);
        filterDepartmentChildren(result, departmentChildren);

        return BaseResponse.success(result);
    }

    private void filterDepartmentChildren(LeadsDepartmentStaffStatisticsDto leadsDepartmentStaffStatisticsDto, Set<Long> departmentChildren) {
        List<LeadsStatisticsDepItemDto> departmentDataList = leadsDepartmentStaffStatisticsDto.getDepartments();

        leadsDepartmentStaffStatisticsDto.setDepartments(departmentDataList.stream().filter(item -> departmentChildren.contains(item.getDepartmentId())).collect(Collectors.toList()));
    }

    private LeadsDepartmentStaffStatisticsDto computeAggregateData(LeadsDepStaffStatisticsQuery query, LeadsDepartmentStaffStatisticsDto result) {
        Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> aggregationData = getAggregationData(query);
        Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> eventData = getEventData(query);
        // merge department data
        Map<Long, LeadsStatisticsBaseInfoDto> mergeDepartmentStatisticsDataMap = mergeAggregationAndEventData(aggregationData.get("dept_group"), eventData.get("dept_group"));
        List<LeadsStatisticsDepItemDto> leadsStatisticsDepItemList = converterLeadsStatisticsDepItemDto(mergeDepartmentStatisticsDataMap);
        result.setDepartments(setDepartmentPropertiesOfLeadsStatisticsDepartmentItemDto(leadsStatisticsDepItemList).stream().sorted(Comparator.comparing(LeadsStatisticsDepItemDto::getTotalCount).reversed()).collect(Collectors.toList()));

        // merge staff data
        Map<Long, LeadsStatisticsBaseInfoDto> mergeStaffStatisticsDataMap = mergeAggregationAndEventData(aggregationData.get("staff_group"), eventData.get("staff_group"));
        List<LeadsStatisticsStaffItemDto> leadsStatisticsStaffItemList = converterLeadsStatisticsStaffItemDto(mergeStaffStatisticsDataMap);
        result.setStaffs(setStaffPropertiesOfLeadsStatisticsStaffItemDto(leadsStatisticsStaffItemList).stream().sorted(Comparator.comparing(LeadsStatisticsStaffItemDto::getTotalCount).reversed()).collect(Collectors.toList()));
        return result;
    }

    private List<LeadsStatisticsDepItemDto> setDepartmentPropertiesOfLeadsStatisticsDepartmentItemDto(List<LeadsStatisticsDepItemDto> leadsStatisticsDepItemList) {
        if (CollectionUtils.isEmpty(leadsStatisticsDepItemList)) {
            return Lists.newArrayList();
        }
        List<Long> departmentIds = leadsStatisticsDepItemList.stream().map(LeadsStatisticsDepItemDto::getDepartmentId).collect(Collectors.toList());

        // 获取部门名
        Map<Long, DepartmentDto> departmentIdMap = departmentClientForLeads.getDepartmentByIds(BidUtils.getBid(), departmentIds).stream().collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));

        // 获取部门下开通账号员工数量
        Map<Long, Set<Long>> childrenDepartmentMap = departmentClientForLeads.batchGetChildrenDepartment(BidUtils.getBid(), departmentIds);
        List<StaffDto> staffList = staffClientForLeads.getStaffByBidOfCache(BidUtils.getBid());
        // 设置部门员工数量
        leadsStatisticsDepItemList.forEach(item -> {
            Set<Long> childrenDepartmentIds = childrenDepartmentMap.get(item.getDepartmentId());
            childrenDepartmentIds.add(item.getDepartmentId());
            if (!CollectionUtils.isEmpty(childrenDepartmentIds)) {
                item.setStaffNum(Long.valueOf(staffList.stream().filter(staff -> childrenDepartmentIds.contains(staff.getDepartmentId())).count()).intValue());
            }
            DepartmentDto departmentDto = departmentIdMap.get(item.getDepartmentId());
            if (Objects.nonNull(departmentDto)) {
                item.setDepartmentName(departmentDto.getName());
            }
        });
        return leadsStatisticsDepItemList;
    }

    private List<LeadsStatisticsDepItemDto> converterLeadsStatisticsDepItemDto(Map<Long, LeadsStatisticsBaseInfoDto> leadsStatisticsData) {
        if (CollectionUtils.isEmpty(leadsStatisticsData)) {
            return Lists.newArrayList();
        }
        List<LeadsStatisticsDepItemDto> result = Lists.newArrayList();
        leadsStatisticsData.forEach((deptId, baseInfo) -> {
            LeadsStatisticsDepItemDto leadsStatisticsDepItemDto = new LeadsStatisticsDepItemDto();
            leadsStatisticsDepItemDto.setDepartmentId(deptId);
            leadsStatisticsDepItemDto.setTotalCount(baseInfo.getTotalCount());
            leadsStatisticsDepItemDto.setToBeContactedCount(baseInfo.getToBeContactedCount());
            leadsStatisticsDepItemDto.setContactTimelyCount(baseInfo.getContactTimelyCount());
            leadsStatisticsDepItemDto.setDepositCount(baseInfo.getDepositCount());
            leadsStatisticsDepItemDto.setDealCount(baseInfo.getDealCount());
            leadsStatisticsDepItemDto.setContactedCount(baseInfo.getContactedCount());
            leadsStatisticsDepItemDto.setMeasurementCount(baseInfo.getMeasurementCount());
            leadsStatisticsDepItemDto.setSignBillAmount(baseInfo.getSignBillAmount());
            result.add(leadsStatisticsDepItemDto);
        });
        return result;
    }

    private List<LeadsStatisticsStaffItemDto> converterLeadsStatisticsStaffItemDto(Map<Long, LeadsStatisticsBaseInfoDto> leadsStatisticsData) {
        if (CollectionUtils.isEmpty(leadsStatisticsData)) {
            return Lists.newArrayList();
        }
        List<LeadsStatisticsStaffItemDto> result = Lists.newArrayList();
        leadsStatisticsData.forEach((staffId, baseInfo) -> {
            LeadsStatisticsStaffItemDto leadsStatisticsStaffItem = new LeadsStatisticsStaffItemDto();
            leadsStatisticsStaffItem.setStaffId(staffId);
            leadsStatisticsStaffItem.setTotalCount(baseInfo.getTotalCount());
            leadsStatisticsStaffItem.setToBeContactedCount(baseInfo.getToBeContactedCount());
            leadsStatisticsStaffItem.setContactTimelyCount(baseInfo.getContactTimelyCount());
            leadsStatisticsStaffItem.setDepositCount(baseInfo.getDepositCount());
            leadsStatisticsStaffItem.setDealCount(baseInfo.getDealCount());
            leadsStatisticsStaffItem.setContactedCount(baseInfo.getContactedCount());
            leadsStatisticsStaffItem.setMeasurementCount(baseInfo.getMeasurementCount());
            leadsStatisticsStaffItem.setSignBillAmount(Objects.isNull(baseInfo.getSignBillAmount()) ? "0" : String.valueOf(baseInfo.getSignBillAmount()));
            result.add(leadsStatisticsStaffItem);
        });
        return result;
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> mergeAggregationAndEventData(Map<Long, LeadsStatisticsBaseInfoDto> aggregationData, Map<Long, LeadsStatisticsBaseInfoDto> eventData) {
        if (CollectionUtils.isEmpty(aggregationData)) {
            return eventData;
        }
        if (CollectionUtils.isEmpty(eventData)) {
            return aggregationData;
        }
        Map<Long, LeadsStatisticsBaseInfoDto> mergerData = Maps.newHashMap();
        for (Map.Entry<Long, LeadsStatisticsBaseInfoDto> entry : aggregationData.entrySet()) {
            LeadsStatisticsBaseInfoDto mergerNode = entry.getValue();
            LeadsStatisticsBaseInfoDto polymerEventNode = eventData.get(entry.getKey());
            mergerNode.setDepositCount(Optional.ofNullable(polymerEventNode).map(LeadsStatisticsBaseInfoDto::getDepositCount).orElse(0));
            mergerData.put(entry.getKey(), mergerNode);
        }

        return mergerData;
    }

    private Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> getEventData(LeadsDepStaffStatisticsQuery query) {
        SearchResponse searchResponse = searchEventData(query);
        return parsedAggregationEventData(query, searchResponse);
    }

    private SearchResponse searchEventData(LeadsDepStaffStatisticsQuery query) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("bid", query.getBid()))
                .must(QueryBuilders.rangeQuery("createTime").gte(query.getStartDistributeTime()).lte(query.getStartDistributeTime()))
                .must(QueryBuilders.termsQuery("followDepartmentIds", query.getManagerDepartmentIds()));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(queryBuilder).size(0);


        if (!CollectionUtils.isEmpty(query.getFilterDepartmentIds())) {
            long[] includeDepartmentIds = query.getFilterDepartmentIds().stream().mapToLong(Long::longValue).toArray();
            IncludeExclude includeExcludeDepartment = new IncludeExclude(includeDepartmentIds, null);
            TermsAggregationBuilder departmentAggregation = AggregationBuilders.terms("dept_group").field("followDepartmentIds").size(10000).includeExclude(includeExcludeDepartment)
                    .subAggregation(
                            AggregationBuilders.filter("deposit_filter", QueryBuilders.termQuery("eventId", 15))
                                    .subAggregation(AggregationBuilders.cardinality("distinct_count").field("leadsId"))
                    );
            searchSourceBuilder.aggregation(departmentAggregation);
        }

        if (!CollectionUtils.isEmpty(query.getFilterStaffIds())) {
            long[] includeStaffIds = query.getFilterStaffIds().stream().mapToLong(Long::longValue).toArray();
            IncludeExclude includeExcludeStaff = new IncludeExclude(includeStaffIds, null);
            TermsAggregationBuilder staffAggregation = AggregationBuilders.terms("staff_group").field("followStaffId").size(10000).includeExclude(includeExcludeStaff)
                    .subAggregation(
                            AggregationBuilders.filter("deposit_filter", QueryBuilders.termQuery("eventId", 15))
                                    .subAggregation(AggregationBuilders.cardinality("distinct_count").field("leadsId"))
                    );
            searchSourceBuilder.aggregation(staffAggregation);
        }

        SearchRequest searchRequest = new SearchRequest()
                .indices("leads-event-log")
                .source(searchSourceBuilder);
        logger.info("getEventPolymerizeData {}", searchRequest.source());
        SearchResponse searchResponse;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }

        return searchResponse;
    }

    private Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> parsedAggregationEventData(LeadsDepStaffStatisticsQuery query, SearchResponse searchResponse) {
        Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> resultMap = Maps.newHashMap();
        if (Objects.isNull(searchResponse)) {
            return resultMap;
        }
        Aggregations aggregations = searchResponse.getAggregations();

        Map<Long, LeadsStatisticsBaseInfoDto> deptGroup = parsedEventDataBucketKey(aggregations, "dept_group");
        resultMap.put("dept_group", deptGroup);

        Map<Long, LeadsStatisticsBaseInfoDto> staffGroup = parsedEventDataBucketKey(aggregations, "staff_group");
        resultMap.put("staff_group", staffGroup);

        return resultMap;
    }

    private Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> getAggregationData(LeadsDepStaffStatisticsQuery query) {
        BoolQueryBuilder queryBuilder = buildAggregationDataQuery(query);
        List<TermsAggregationBuilder> termsAggregationBuilders = buildAggregationDataAggregation(query);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder).size(0).trackTotalHits(true);

        termsAggregationBuilders.forEach(searchSourceBuilder::aggregation);

        SearchRequest searchRequest = new SearchRequest().indices("leads")
                .source(searchSourceBuilder);

        SearchResponse searchResponse;
        try {
            logger.info("search leads es:{}", searchRequest.source());
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return parsedAggregationData(query, searchResponse);
    }

    private BoolQueryBuilder buildAggregationDataQuery(LeadsDepStaffStatisticsQuery query) {
        BoolQueryBuilder queryBuilder = QueryBuilders
                .boolQuery()
                .must(QueryBuilders.termQuery(LeadsEsDto.BID, query.getBid()))
                .must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, query.getManagerDepartmentIds()));
        if (!StringUtils.isEmpty(query.getKeyword())) {
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.DISTRIBUTE_STAFF_NAME_KEYWORD, "*" + query.getKeyword() + "*"));
        }

        if (Objects.nonNull(query.getStartDistributeTime()) && Objects.nonNull(query.getEndDistributeTime())) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.DISTRIBUTE_TIME).gte(query.getStartDistributeTime()).lte(query.getEndDistributeTime()));
        }
        return queryBuilder;
    }

    private List<TermsAggregationBuilder> buildAggregationDataAggregation(LeadsDepStaffStatisticsQuery query) {

        List<TermsAggregationBuilder> aggregations = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(query.getFilterDepartmentIds())) {
            long[] includeDepartment = query.getFilterDepartmentIds().stream().mapToLong(Long::longValue).toArray();
            IncludeExclude includeExcludeDepartment = new IncludeExclude(includeDepartment, null);
            TermsAggregationBuilder departmentAggregation = AggregationBuilders.terms("dept_group").field(LeadsEsDto.DEPT_IDS).size(10000).includeExclude(includeExcludeDepartment)
                    .subAggregation(
                            // 总数
                            AggregationBuilders.filter("totalCount", QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getAllocatedLeadsStatus())))
                    )
                    .subAggregation(
                            // 待联系
                            AggregationBuilders.filter("distributedCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())))
                    )
                    .subAggregation(
                            // 24小时内联系
                            AggregationBuilders.filter("contactCount", QueryBuilders.boolQuery()
                                    .must(QueryBuilders.rangeQuery("firstCallIntervalTime").lte(24 * 60 * 60))
                                    .must(QueryBuilders.rangeQuery(LeadsEsDto.STATUS).gt(0)))
                    )
                    .subAggregation(
                            // 已联系
                            AggregationBuilders.filter(
                                    "contactedCount", QueryBuilders.rangeQuery(LeadsEsDto.STATUS).gte(LeadsStatusEnum.CONTACTED.getStatus())
                            )
                    )
                    .subAggregation(
                            // 已量尺
                            AggregationBuilders.filter(
                                    "measurementCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.HAS_MEASURE, 1)).mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getNotStatisticsLeadsStatus()))
                            )
                    )
                    .subAggregation(
                            // 已成交
                            AggregationBuilders.filter("dealCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.HAS_STORE_ORDER, 1)).mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getNotStatisticsLeadsStatus())))
                    )
                    .subAggregation(
                            // 签单金额
                            AggregationBuilders.filter("statusFilter", QueryBuilders.boolQuery().mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getNotStatisticsLeadsStatus())))
                                    .subAggregation(AggregationBuilders.sum("orderAmount").field("orderAmount"))
                    );

            aggregations.add(departmentAggregation);
        }
        if (!CollectionUtils.isEmpty(query.getFilterStaffIds())) {
            long[] includeStaff = query.getFilterStaffIds().stream().mapToLong(Long::longValue).toArray();
            IncludeExclude includeExcludeStaff = new IncludeExclude(includeStaff, null);
            TermsAggregationBuilder staffAggregation = AggregationBuilders.terms("staff_group").field(LeadsEsDto.DISTRIBUTE_STAFF_ID).size(10000).includeExclude(includeExcludeStaff)
                    .subAggregation(
                            // 总数
                            AggregationBuilders.filter("totalCount", QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getAllocatedLeadsStatus())))
                    )
                    .subAggregation(
                            // 待联系
                            AggregationBuilders.filter("distributedCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())))
                    )
                    .subAggregation(
                            // 已联系
                            AggregationBuilders.filter(
                                    "contactedCount", QueryBuilders.rangeQuery(LeadsEsDto.STATUS).gte(LeadsStatusEnum.CONTACTED.getStatus())
                            )
                    )
                    .subAggregation(
                            // 已量尺
                            AggregationBuilders.filter(
                                    "measurementCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.HAS_MEASURE, 1)).mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getNotStatisticsLeadsStatus()))
                            )
                    )
                    .subAggregation(
                            // 24小时内联系
                            AggregationBuilders.filter("contactCount", QueryBuilders.boolQuery()
                                    .must(QueryBuilders.rangeQuery("firstCallIntervalTime").lte(24 * 60 * 60))
                                    .must(QueryBuilders.rangeQuery(LeadsEsDto.STATUS).gt(0)))
                    )
                    .subAggregation(
                            // 已成交
                            AggregationBuilders.filter("dealCount", QueryBuilders.boolQuery().must(QueryBuilders.termQuery(LeadsEsDto.HAS_STORE_ORDER, 1)).mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getNotStatisticsLeadsStatus())))
                    )
                    .subAggregation(
                            // 签单金额
                            AggregationBuilders.filter("statusFilter", QueryBuilders.boolQuery().mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, getNotStatisticsLeadsStatus())))
                                    .subAggregation(AggregationBuilders.sum("orderAmount").field("orderAmount"))
                    );
            aggregations.add(staffAggregation);
        }
        return aggregations;
    }

    private List<LeadsStatisticsStaffItemDto> setStaffPropertiesOfLeadsStatisticsStaffItemDto(List<LeadsStatisticsStaffItemDto> leadsStatisticsStaffItem) {
        if (CollectionUtils.isEmpty(leadsStatisticsStaffItem)) {
            return Lists.newArrayList();
        }
        List<Long> staffIds = leadsStatisticsStaffItem.stream().map(LeadsStatisticsStaffItemDto::getStaffId).collect(Collectors.toList());
        Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(BidUtils.getBid(), Sets.newHashSet(staffIds));

        Map<Long, CardDto> cardMap = cardClientForLeads.getCardDtoListByStaffIds(BidUtils.getBid(), staffIds);

        leadsStatisticsStaffItem.forEach(item -> {
            StaffDto staffDto = staffMap.get(item.getStaffId());
            if (Objects.nonNull(staffDto)) {
                item.setStaffName(staffDto.getName());
                item.setPosition(staffDto.getPosition());
            }
            CardDto cardDto = cardMap.get(item.getStaffId());
            if (Objects.nonNull(cardDto)) {
                item.setAvatar(cardDto.getAvatarUrl());
            }
        });
        return leadsStatisticsStaffItem;
    }

    private Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> parsedAggregationData(LeadsDepStaffStatisticsQuery query, SearchResponse searchResponse) {

        Map<String, Map<Long, LeadsStatisticsBaseInfoDto>> resultMap = Maps.newHashMap();
        if (Objects.isNull(searchResponse)) {
            return resultMap;
        }

        Aggregations aggregations = searchResponse.getAggregations();
        if (Objects.isNull(aggregations)) {
            return resultMap;
        }

        // 传入了keyword不解析dept_group
        if (StringUtils.isEmpty(query.getKeyword())) {
            Map<Long, LeadsStatisticsBaseInfoDto> deptGroup = parsedBucketKey(aggregations, "dept_group");
            resultMap.put("dept_group", deptGroup);
        }
        // 未传入departmentId 不解析staff_group
        if (Objects.nonNull(query.getDepartmentId()) || !StringUtils.isEmpty(query.getKeyword())) {
            Map<Long, LeadsStatisticsBaseInfoDto> staffGroup = parsedBucketKey(aggregations, "staff_group");
            resultMap.put("staff_group", staffGroup);
        }

        return resultMap;
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> parsedEventDataBucketKey(Aggregations aggregations, String bucketKey) {
        Map<Long, LeadsStatisticsBaseInfoDto> resultMap = Maps.newHashMap();

        if (Objects.isNull(aggregations)) {
            return resultMap;
        }

        Terms terms = (Terms) aggregations.get(bucketKey);
        if (Objects.isNull(terms)) {
            return resultMap;
        }
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            Aggregations subBuckets = bucket.getAggregations();
            if (Objects.isNull(subBuckets)) {
                continue;
            }
            ParsedFilter depositFilter = (ParsedFilter) subBuckets.get("deposit_filter");
            if (Objects.isNull(depositFilter)) {
                continue;
            }

            Aggregations depositSubAggregations = depositFilter.getAggregations();
            if (Objects.isNull(depositSubAggregations)) {
                continue;
            }
            ParsedCardinality depositDistinctCardinality = (ParsedCardinality) depositSubAggregations.get("distinct_count");
            if (Objects.isNull(depositDistinctCardinality)) {
                continue;
            }
            long value = depositDistinctCardinality.getValue();
            if (value == 0L) {
                continue;
            }

            LeadsStatisticsBaseInfoDto baseInfo = new LeadsStatisticsBaseInfoDto();
            baseInfo.setDepositCount(Long.valueOf(depositDistinctCardinality.getValue()).intValue());
            resultMap.putIfAbsent(bucket.getKeyAsNumber().longValue(), baseInfo);
        }

        return resultMap;
    }

    private Map<Long, LeadsStatisticsBaseInfoDto> parsedBucketKey(Aggregations aggregations, String bucketKey) {
        Map<Long, LeadsStatisticsBaseInfoDto> resultMap = Maps.newHashMap();
        if (Objects.isNull(aggregations)) {
            return resultMap;
        }
        ParsedLongTerms terms = (ParsedLongTerms) aggregations.get(bucketKey);
        if (Objects.isNull(terms)) {
            return resultMap;
        }
        List<? extends Terms.Bucket> buckets = terms.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            Aggregations aggregation = bucket.getAggregations();
            if (Objects.isNull(aggregation)) {
                continue;
            }

            ParsedFilter totalCountFilter = (ParsedFilter) aggregation.get("totalCount");
            ParsedFilter distributedCountFilter = (ParsedFilter) aggregation.get("distributedCount");
            ParsedFilter contact24Filter = (ParsedFilter) aggregation.get("contactCount");
            ParsedFilter dealCountFilter = (ParsedFilter) aggregation.get("dealCount");
            ParsedFilter contactedFilter = (ParsedFilter) aggregation.get("contactedCount");
            ParsedFilter measurementFilter = (ParsedFilter) aggregation.get("measurementCount");
            ParsedFilter statusFilter = (ParsedFilter) aggregation.get("statusFilter");


            Long totalCount = Optional.ofNullable(totalCountFilter).map(ParsedFilter::getDocCount).orElse(0L);
            Long distributedCount = Optional.ofNullable(distributedCountFilter).map(ParsedFilter::getDocCount).orElse(0L);
            Long contact24Count = Optional.ofNullable(contact24Filter).map(ParsedFilter::getDocCount).orElse(0L);
            Long dealCount = Optional.ofNullable(dealCountFilter).map(ParsedFilter::getDocCount).orElse(0L);
            Long contactedCount = Optional.ofNullable(contactedFilter).map(ParsedFilter::getDocCount).orElse(0L);
            Long measurementCount = Optional.ofNullable(measurementFilter).map(ParsedFilter::getDocCount).orElse(0L);
            String orderAmountStr = "0";
            if (Objects.nonNull(statusFilter)) {
                ParsedSum orderAmount = (ParsedSum) statusFilter.getAggregations().get("orderAmount");
                orderAmountStr = (String) Optional.ofNullable(orderAmount).map(ParsedSum::getValueAsString).orElse("0");

            }
//            String orderAmountStr = Optional.ofNullable(orderAmount).map(ParsedSum::getValueAsString).orElse("0");

            if (totalCount + distributedCount + contact24Count + dealCount == 0L) {
                continue;
            }
            LeadsStatisticsBaseInfoDto baseInfo = new LeadsStatisticsBaseInfoDto();
            baseInfo.setTotalCount(totalCount.intValue());
            baseInfo.setToBeContactedCount(distributedCount.intValue());
            baseInfo.setContactTimelyCount(contact24Count.intValue());
            baseInfo.setDealCount(dealCount.intValue());
            baseInfo.setContactedCount(contactedCount.intValue());
            baseInfo.setMeasurementCount(measurementCount.intValue());
            baseInfo.setSignBillAmount(orderAmountStr);

            resultMap.putIfAbsent(bucket.getKeyAsNumber().longValue(), baseInfo);
        }

        return resultMap;
    }


    private Map<Long, StaffDto> getStaffMap(Integer bid, List<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Maps.newHashMap();
        }

        return staffClientForLeads.getStaffByIds(bid, Sets.newHashSet(staffIds));
    }

    /**
     * 获取部门下的员工人数
     *
     * @return
     */
    private Map<Long, Integer> getDepartmentStaffCount(GetDepartmentChildIdsRequest request) {
        //获取部门及子部门id
        List<Long> departmentIds = request.getDepartmentIds();
        Integer bid = request.getBid();
        Map<Long, Integer> depStaffCountMap = Maps.newHashMap();
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(departmentIds);
        Map<Long, Set<Long>> childrenMapByIds = departmentClientForLeads.getAllChildrenMapByIds(baseIdsRequest);
        if (CollectionUtils.isEmpty(childrenMapByIds)) {
            return depStaffCountMap;
        }
        Set<Long> allDepIds = Sets.newHashSet();
        childrenMapByIds.forEach((k, v) -> {
            allDepIds.add(k);
            allDepIds.addAll(v);
        });

        BaseIdsRequest departmentStaffCountRequest = new BaseIdsRequest();
        departmentStaffCountRequest.setBid(bid);
        departmentStaffCountRequest.setIds(Lists.newArrayList(allDepIds));
        Map<Long, Integer> countMapByDeptIds = staffClientForLeads.getCountMapByDeptIds(departmentStaffCountRequest);
        childrenMapByIds.forEach((depId, childIds) -> {
            int count = 0;
            for (Long childId : childIds) {
                if (countMapByDeptIds.containsKey(childId)) {
                    count += countMapByDeptIds.getOrDefault(childId, 0);
                }
            }
            if (countMapByDeptIds.containsKey(depId)) {
                count = countMapByDeptIds.getOrDefault(depId, 0) + count;
                depStaffCountMap.put(depId, count);
            } else {
                depStaffCountMap.put(depId, count);
            }

        });
        return depStaffCountMap;
    }

    private Integer getDepartmentStaffCountOnEs(Integer bid, Set<Long> depIds) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("bid", bid));
        qb.must(QueryBuilders.termsQuery("belongingDepartmentIds", depIds));

        AggregationBuilder termsAggregationBuilder = AggregationBuilders.terms(DEPARTMENT_STAFF_GROUP).field("distributeStaffId").size(10000).
                subAggregation(AggregationBuilders.topHits("my_top_hist").size(1));

        searchSourceBuilder.query(qb)
                .aggregation(termsAggregationBuilder)
                .size(10000)
        ;
        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);

        try {
            SearchResponse resp = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (resp.getHits() == null || resp.getHits().getTotalHits().value == 0) {
                return null;
            }
            Aggregations aggregations = resp.getAggregations();
            Aggregation aggregation = aggregations.get(DEPARTMENT_STAFF_GROUP);
            List<? extends Terms.Bucket> buckets = ((Terms) aggregation).getBuckets();
            if (!CollectionUtils.isEmpty(buckets)) {
                return buckets.size();
            }
        } catch (IOException e) {
            logger.error("ES获取leads数据异常");
        }
        return null;
    }


    private <T extends LeadsStatisticsBaseInfoDto> void installBaseInfo(LeadsStatisticsBaseInfoDto leadsStatisticsDepItemDto, T t) {
        Optional<LeadsStatisticsBaseInfoDto> infoOpt = Optional.ofNullable(leadsStatisticsDepItemDto);
        Integer dealCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getDealCount).orElse(0);
        Integer contactTimelyCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getContactTimelyCount).orElse(0);
        Integer totalCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getTotalCount).orElse(0);
        Integer inContactCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getInContactCount).orElse(0);
        Integer toBeContactedCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getToBeContactedCount).orElse(0);
        Integer clientCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getClientCount).orElse(0);
        Integer depositCount = infoOpt.map(LeadsStatisticsBaseInfoDto::getDepositCount).orElse(0);

        t.setDealCount(dealCount);
        t.setContactTimelyCount(contactTimelyCount);
        t.setTotalCount(totalCount);
        t.setInContactCount(inContactCount);
        t.setToBeContactedCount(toBeContactedCount);
        t.setClientCount(clientCount);
        t.setDepositCount(depositCount);
    }

    private List<AgentIdAndNameDto> getAgentInfos(Integer bid, Set<Long> ids) {
        GetAgentRequest getAgentRequest = new GetAgentRequest();
        getAgentRequest.setBid(bid);
        getAgentRequest.setIds(Lists.newArrayList(ids));
        List<AgentDto> agentDtoList = agentClientForLeads.getAgentById(getAgentRequest);
        return agentDtoList.stream().map(AgentDtoConverter::toAgentIdAndNameDto).collect(Collectors.toList());
    }

    @Override
    public BaseResponse<List<LeadsStaffSimpleInfoDto>> getStaffName(LeadsStaffNameGetRequest request) {
        Integer bid = request.getBid();

        QueryWrapper<Leads> queryWrapper = Wrappers.<Leads>query().eq(Leads.BID, bid);
        final List<LeadsStaffSimpleInfoDto> resultList = Lists.newArrayList();
        List<Leads> list = leadsManager.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            Set<Long> ids = list.stream().map(Leads::getDistributeStaffId).collect(Collectors.toSet());
            BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
            baseIdsRequest.setBid(bid);
            baseIdsRequest.setIds(Lists.newArrayList(ids));
            List<StaffIdAndAgentIdDto> staffIdAndAgentIdDtoList = staffClientForLeads.getStaffIdAndAgentIdDto(baseIdsRequest);
            if (!CollectionUtils.isEmpty(staffIdAndAgentIdDtoList)) {
                staffIdAndAgentIdDtoList.forEach(item -> {
                    LeadsStaffSimpleInfoDto leadsStaffSimpleInfoDto = new LeadsStaffSimpleInfoDto();
                    leadsStaffSimpleInfoDto.setId(item.getId());
                    leadsStaffSimpleInfoDto.setName(item.getStaffName());
                    resultList.add(leadsStaffSimpleInfoDto);
                });
            }
        }
        return BaseResponse.success(resultList);
    }

    /**
     * 获取线索详情（前端）
     *
     * @param request 线索请求
     * @return 线索详情
     */
    @Override
    public BaseResponse<LeadsVo> getLeadsDetail(LeadsGetRequest request) {
        Integer bid = request.getBid();
        Leads leads = leadsManager.getOne(Wrappers.<Leads>query().eq(Leads.BID, bid).eq(Leads.ID, request.getId()));
        if (leads == null) {
            throw new InngkeServiceException("线索不存在");
        }
        //异步获取其他信息
        CompletableFuture<LeadsConf> leadsConfFuture = AsyncUtils.supplyTraceAsync(
                () -> leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, bid)), executor
        );
        CompletableFuture<Map<Long, Long>> staffAndAgentFuture = AsyncUtils.supplyTraceAsync(
                () -> staffToAgentUtil.getStaffAndAgent(Lists.newArrayList(leads), bid), executor
        );
        CompletableFuture<LocalDateTime> firstContactTimeFuture = AsyncUtils.supplyTraceAsync(
                () -> leadsFollowManager.getLeadsFirstCallTime(bid, leads.getId()), executor
        );
        CompletableFuture<List<ClientLevelListDto>> levelListFuture = AsyncUtils.supplyTraceAsync(
                () -> clientLevelClientForLeads.getByIds(bid, Lists.newArrayList(leads.getLevelId())), executor
        );
        CompletableFuture<Integer> leadsFollowCountFuture = AsyncUtils.supplyTraceAsync(
                () -> getFollowSize(bid, leads.getId(), leads.getRecoveryFromIds()), executor
        );
        CompletableFuture<Integer> shopOrderFuture = Optional.ofNullable(leads.getCustomerId()).map(customerId ->
                        AsyncUtils.supplyTraceAsync(() -> getShopOrderCount(customerId, bid), executor))
                .orElse(CompletableFuture.completedFuture(0));
        CompletableFuture<Integer> mobileShopOrderCountFuture = !StringUtils.isEmpty(leads.getMobile()) ?
                AsyncUtils.supplyTraceAsync(() -> getStoreOrderCount(leads.getMobile(), bid), executor) :
                CompletableFuture.completedFuture(0);
        CompletableFuture<List<LeadsCallLog>> callLogFuture = AsyncUtils.supplyTraceAsync(
                () -> leadsCallLogManager.getLeadsCallLog(bid, leads.getId(), 1), executor
        );
        CompletableFuture<List<LeadsCallLog>> closePrivateCallLogListFuture = AsyncUtils.supplyTraceAsync(
                () -> leadsCallLogManager.getLeadsCallLog(bid, leads.getId(), 2), executor
        );
        CompletableFuture<List<LeadsFollow>> leadsFollowListFuture = AsyncUtils.supplyTraceAsync(
                () -> leadsFollowManager.getByLeadsIds(bid, Lists.newArrayList(leads.getId(), leads.getRecoveryFrom())), executor
        );

        List<Long> productIds = strIdsToList(leads.getProductIds());

        Map<Long, Long> staffAndAgent = AsyncUtils.getFutureData(staffAndAgentFuture);
        leads.setFirstContactTime(AsyncUtils.getFutureData(firstContactTimeFuture));

        LeadsVo leadsDto = toLeadsListItem(leads, staffAndAgent);
        List<LeadsFollow> leadsFollows = AsyncUtils.getFutureData(leadsFollowListFuture);
        sortTheStates(leads, leadsDto, leadsFollows);

        setClientLevel(bid, leadsDto, AsyncUtils.getFutureData(levelListFuture));
        Integer shopOrderCount = AsyncUtils.getFutureData(shopOrderFuture);
        Integer mobileShopOrderCount = AsyncUtils.getFutureData(mobileShopOrderCountFuture);

        // 设置product信息
        if (!CollectionUtils.isEmpty(productIds)) {
            ProductGetListRequest productRequest = new ProductGetListRequest();
            productRequest.setBid(bid);
            productRequest.setProductIds(productIds);
            BaseResponse<List<ProductDto>> productResponse = productService.list(productRequest);
            if (BaseResponse.responseSuccessWithNonNullData(productResponse) && !CollectionUtils.isEmpty(productResponse.getData())) {
                List<ProductDto> productList = productResponse.getData();
                leadsDto.setProducts(productList.stream().map(this::toProductDto).collect(Collectors.toList()));
            }
        }


        int orderCount = 0;
        if (leadsDto != null && leadsDto.getCustomerId() != null && shopOrderCount != null) {
            //开单订单数直接查库
            orderCount += Optional.ofNullable(shopOrderCount).orElse(0);
        }
        if (leadsDto != null && !StringUtils.isEmpty(leadsDto.getMobile())) {
            //小程序订单查询门店服务
            orderCount += Optional.ofNullable(mobileShopOrderCount).orElse(0);
        }
        leadsDto.setTransactionOrderCount(orderCount);

        // 计算出【线索跟踪】记录数量
        Integer leadsFollowSize = AsyncUtils.getFutureData(leadsFollowCountFuture);
        leadsDto.setLeadsFollowCount(leadsFollowSize);
        //组装是否可转发和是否可退回的标识字段,线索状态为“待联系”且未在当前导购下产生跟进记录，不可以退回
        LeadsConf leadsConf = AsyncUtils.getFutureData(leadsConfFuture);
        int canRollback = 1;
        int canForward = 1;
        if (leadsConf != null) {
            Boolean pushbackEnable = leadsConf.getPushbackEnable();
            Boolean forwardEnable = leadsConf.getForwardEnable();
            canRollback = Boolean.TRUE.equals(pushbackEnable) ? 1 : 0;
            canForward = Boolean.TRUE.equals(forwardEnable) ? 1 : 0;
        }

        leadsDto.setCanRollback(canRollback);
        //线索状态为“已成交”的不允许转交
        leadsDto.setCanForward(canForward);
        //组装名字
        installStaffNameAndAgentName(Arrays.asList(leadsDto), bid, null);
        //拼装通话记录
        List<LeadsCallLog> callLogList = AsyncUtils.getFutureData(callLogFuture);
        List<LeadsCallLog> closePrivateCallLogList = AsyncUtils.getFutureData(closePrivateCallLogListFuture);
        leadsDto.setClosePrivatePhoneCallLogList(closePrivateCallLogList.stream().map(LeadsConverter::leadsCallLogDto).collect(Collectors.toList()));
        leadsDto.setCallLogList(callLogList.stream().map(LeadsConverter::leadsCallLogDto).collect(Collectors.toList()));

        buildDeptName(leadsDto);

        //2022-10-24 添加字段
        leadsDetailBuildLeadsName(bid, leadsDto);

        return BaseResponse.success(leadsDto);
    }

    private com.inngke.bp.leads.dto.response.ProductDto toProductDto(ProductDto productDto) {
        com.inngke.bp.leads.dto.response.ProductDto leadsProduct = new com.inngke.bp.leads.dto.response.ProductDto();
        leadsProduct.setId(productDto.getId());
        leadsProduct.setBid(productDto.getBid());
        leadsProduct.setTitle(productDto.getTitle());
        leadsProduct.setDescription(productDto.getDescription());
        leadsProduct.setSpecDescription(productDto.getSpecDescription());
        leadsProduct.setCoverUrl(productDto.getCoverUrl());
        leadsProduct.setCoverImages(productDto.getCoverImages());
        leadsProduct.setVideoUrl(productDto.getVideoUrl());
        leadsProduct.setProductNo(productDto.getProductNo());
        leadsProduct.setSort(productDto.getSort());
        leadsProduct.setCategoryIds(productDto.getCategoryIds());
        leadsProduct.setCateGoryNames(productDto.getCateGoryNames());
        leadsProduct.setChannelIds(productDto.getChannelIds());
        leadsProduct.setDepartmentIds(productDto.getDepartmentIds());
        return leadsProduct;

    }


    private List<ContentDto> getContentList(ContentTypeEnum typeEnum, Integer bid, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ContentGetListRequest request = new ContentGetListRequest();
        request.setIds(Sets.newHashSet(ids));
        request.setBid(bid);
        request.setPageSize(1000);
        request.setPageNo(1);
        request.setType(Sets.newHashSet(typeEnum.getId()));

        return Optional.ofNullable(contentGetService.getList(request)).map(BaseResponse::getData).map(BasePaginationResponse::getList).orElse(Lists.newArrayList());
    }
    private List<Long> strIdsToList(String strIds) {
        return Lists.newArrayList(
                Splitter.on(InngkeAppConst.COMMA_STR)
                        .split(Optional.ofNullable(strIds).orElse(InngkeAppConst.EMPTY_STR))
        ).stream().filter(org.apache.commons.lang3.StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 对线索状态变更记录进行排序
     *
     * @param leadsDto
     * @param leadsFollowList
     */
    private void sortTheStates(Leads leads, LeadsVo leadsDto, List<LeadsFollow> leadsFollowList) {

        Map<Long, List<LeadsFollow>> leadsFollowGroup = leadsFollowList.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));
        List<LeadsFollow> leadsFollows = leadsFollowGroup.getOrDefault(leads.getId(), Lists.newArrayList());
        List<LeadsFollow> recoveryLeadsFollows = leadsFollowGroup.getOrDefault(leads.getRecoveryFrom(), Lists.newArrayList());

        if (Objects.nonNull(leads.getDistributeStaffId()) && leads.getDistributeStaffId() != 0L) {
            List<LeadsFollowStatusDto> followStatuses = Lists.newArrayList();
            leadsFollows.stream()
                    .filter(follow -> follow.getCreateTime().isAfter(leads.getDistributeTime()) && !follow.getLeadsStatus().equals(LeadsStatusEnum.DISTRIBUTED.getStatus()))
                    .forEach(follow -> {
                        LeadsFollowStatusDto lastStatus = null;
                        if (followStatuses.size() >= 1) {
                            lastStatus = followStatuses.get(followStatuses.size() - 1);
                        }
                        //如果最后一个状态和当前状态不一致，则添加
                        if (lastStatus == null || !lastStatus.getId().equals(follow.getLeadsStatus())) {
                            Optional.ofNullable(LeadsStatusEnum.parse(follow.getLeadsStatus())).ifPresent(status -> {
                                LeadsFollowStatusDto statusDto = new LeadsFollowStatusDto();
                                statusDto.setId(follow.getLeadsStatus());
                                statusDto.setName(status.getName());
                                followStatuses.add(statusDto);
                            });
                        }
                    });
            leadsDto.setFollowStatuses(followStatuses);
        }

        List<LeadsFollowStatusDto> recoveryFollowStatuses = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(recoveryLeadsFollows) && Objects.nonNull(leads.getRecoveryFrom()) && leads.getRecoveryFrom() != 0L) {
            Leads recoveryLeads = leadsManager.getById(leads.getBid(), leads.getRecoveryFrom());
            if (Objects.nonNull(recoveryLeads) && Objects.nonNull(recoveryLeads.getPreFollowStaffId()) && Objects.nonNull(recoveryLeads.getDistributeFollowTime())) {
                recoveryLeadsFollows.stream()
                        .filter(follow -> follow.getOperatorRole().equals(2))
                        .forEach(follow -> {
                            LeadsFollowStatusDto lastStatus = null;
                            if (recoveryFollowStatuses.size() >= 1) {
                                lastStatus = recoveryFollowStatuses.get(recoveryFollowStatuses.size() - 1);
                            }
                            //如果最后一个状态和当前状态不一致，则添加
                            if (lastStatus == null || !lastStatus.getId().equals(follow.getLeadsStatus())) {
                                Optional.ofNullable(LeadsStatusEnum.parse(follow.getLeadsStatus())).ifPresent(status -> {
                                    LeadsFollowStatusDto statusDto = new LeadsFollowStatusDto();
                                    statusDto.setId(follow.getLeadsStatus());
                                    statusDto.setName(status.getName());
                                    recoveryFollowStatuses.add(statusDto);
                                });
                            }
                        });
            }
        }

        List<LeadsFollowStatusDto> preFollowStatuses = Lists.newArrayList();
        if (Objects.nonNull(leads.getDistributeFollowTime())) {

            leadsFollows.stream()
                    .filter(follow -> follow.getOperatorRole().equals(2))
                    .forEach(follow -> {
                        LeadsFollowStatusDto lastStatus = null;
                        if (preFollowStatuses.size() >= 1) {
                            lastStatus = preFollowStatuses.get(preFollowStatuses.size() - 1);
                        }
                        //如果最后一个状态和当前状态不一致，则添加
                        if (lastStatus == null || !lastStatus.getId().equals(follow.getLeadsStatus())) {
                            Optional.ofNullable(LeadsStatusEnum.parse(follow.getLeadsStatus())).ifPresent(status -> {
                                LeadsFollowStatusDto statusDto = new LeadsFollowStatusDto();
                                statusDto.setId(follow.getLeadsStatus());
                                statusDto.setName(status.getName());
                                preFollowStatuses.add(statusDto);
                            });
                        }
                    });
        }
        preFollowStatuses.addAll(0, recoveryFollowStatuses);
        leadsDto.setKfFollowStatuses(preFollowStatuses);

        if (Objects.nonNull(leadsDto.getClientId()) && leadsDto.getClientId() != 0L) {
            LeadsFollowStatusDto leadsIntent = new LeadsFollowStatusDto();
            leadsIntent.setName(LeadsStatusEnum.INTENT.getName());

            List<LeadsFollowStatusDto> leadsStatusPath = clientApiForMp.getLeadsStatusChangePath(leads.getBid(), leadsDto.getId()).stream().map(statusText -> {
                LeadsFollowStatusDto leadsFollowStatusDto = new LeadsFollowStatusDto();
                leadsFollowStatusDto.setName(statusText);
                return leadsFollowStatusDto;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leadsDto.getFollowStatuses()) || !leadsDto.getFollowStatuses()
                    .get(leadsDto.getFollowStatuses().size() - 1).getName().equals(LeadsStatusEnum.INTENT.getName())) {
                leadsDto.getFollowStatuses().add(leadsIntent);
            }
            leadsDto.getFollowStatuses().addAll(leadsStatusPath);
        }
    }

    private Integer getFollowSize(Integer bid, Long id, String recoveryFromIds) {
        return leadsFollowManager.count(Wrappers.<LeadsFollow>query().eq(Leads.BID, bid)
                        .in(LeadsFollow.LEADS_ID, LeadsCommonUtil.strToIds(id, recoveryFromIds))
                        .ne(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
                        .apply("follow_content  NOT REGEXP '处回收'")
//                .notIn(request.getFilterInvalidFollow(), LeadsFollow.LEADS_STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.PUSH_BACK.getStatus(), LeadsStatusEnum.RECOVERY.getStatus())
        );
    }

    private void setClientLevel(int bid, LeadsVo leadsDto, List<ClientLevelListDto> client) {
        if (leadsDto == null || leadsDto.getLevelId() == null || leadsDto.getLevelId().equals(0)) {
            return;
        }
        if (CollectionUtils.isEmpty(client)) {
            return;
        }
        ClientLevelListDto clientLevelListDto = client.get(0);
        leadsDto.setLevelText(clientLevelListDto.getTitle());
        leadsDto.setLevelDes(clientLevelListDto.getExplainInfo());
    }

    private void buildDeptName(LeadsVo leadsDto) {
        if (LeadsStatusEnum.PUSH_BACK.getStatus() != leadsDto.getStatus()) {
            leadsDto.setDistributeStaffDepartment(getDeptNameByStaffId(leadsDto.getBid(), leadsDto));
        } else if (!Objects.equals(leadsDto.getPushBackStaffId(), 0L)) {
            DepartmentDto departmentDto = departmentClientForLeads.getDepartmentByStaffId(leadsDto.getBid(), leadsDto.getPushBackStaffId());
            leadsDto.setDistributeStaffDepartment(departmentDto.getName());
        }
    }

    private LeadsVo toLeadsListItem(Leads leads, Map<Long, Long> staffToAgentMap) {
        // 判空
        if (Objects.isNull(leads)) {
            return null;
        }
        LeadsVo leadsvo = new LeadsVo();
        leadsvo.setClientId(leads.getClientId());
        leadsvo.setId(leads.getId());
        leadsvo.setCreateTime(leads.getCreateTime() != null ? leads.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : System.currentTimeMillis());
        leadsvo.setBid(leads.getBid());
        leadsvo.setName(leads.getName());
        leadsvo.setMobile(leads.getMobile());
        leadsvo.setCustomerId(leads.getCustomerId());
        leadsvo.setCustomerUid(leads.getCustomerUid());
        if (Objects.nonNull(leads.getClientId()) && leads.getClientId() > 0) {
            leadsvo.setStatus(leads.getClientStatus());
        } else {
            leadsvo.setStatus(leads.getStatus());
        }
        leadsvo.setProvinceId(leads.getProvinceId());
        leadsvo.setWeChat(leads.getWeChat());
        leadsvo.setGoodsLink(leads.getGoodsLink());
        leadsvo.setChannelId(Optional.ofNullable(leads.getChannelId()).orElse(0L));
        leadsvo.setProvinceName(leads.getProvinceName());
        leadsvo.setCityId(leads.getCityId());
        leadsvo.setCityName(leads.getCityName());
        leadsvo.setAreaId(leads.getAreaId());
        leadsvo.setAreaName(leads.getAreaName());
        leadsvo.setAddress(leads.getAddress());
        leadsvo.setChannel(leads.getChannel());
        leadsvo.setChannelType(leads.getChannelType());
        leadsvo.setChannelSource(leads.getChannelSource());
        leadsvo.setOrderAccount(leads.getOrderAccount());
        leadsvo.setOrderSn(leads.getOrderSn());
        leadsvo.setGoodsName(leads.getGoodsName());
        leadsvo.setGoodsNum(leads.getGoodsNum());
        leadsvo.setRecoveryFromIds(leads.getRecoveryFromIds());
        LocalDateTime payTime = leads.getPayTime();
        if (payTime != null) {
            leadsvo.setPayTime(leads.getPayTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setPayAmount(leads.getPayAmount());
        leadsvo.setOrderMessage(leads.getOrderMessage());
        leadsvo.setRemark(leads.getRemark());
        leadsvo.setTpLeadsId(leads.getTpLeadsId());
        leadsvo.setPromotionName(leads.getPromotionName());
        LocalDateTime registryTime = leads.getRegistryTime();
        if (registryTime != null) {
            leadsvo.setRegistryTime(registryTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setExpectIn(leads.getExpectIn());
        leadsvo.setShowPhone(leads.getShowPhone());
        leadsvo.setStyle(leads.getStyle());
        leadsvo.setBatchId(leads.getBatchId());

        Long distributeStaffId = leads.getDistributeStaffId();
        leadsvo.setDistributeStaffId(distributeStaffId);
        fillDistributeAgentId(distributeStaffId, leadsvo, staffToAgentMap);

        leadsvo.setErrorMsg(leads.getErrorMsg());
        LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(leadsvo.getStatus());
        leadsvo.setStatusText(statusEnum == null ? InngkeAppConst.EMPTY_STR : statusEnum.getName());
        leadsvo.setChannelText(LeadsChannelUtil.getName(leads.getBid(), leads.getChannel()));
        LocalDateTime distributeTime = leads.getDistributeTime();
        if (distributeTime != null) {
            leadsvo.setDistributeTime(distributeTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        LocalDateTime updateTime = leads.getUpdateTime();
        if (updateTime != null) {
            leadsvo.setUpdateTime(updateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }

        LocalDateTime pushBackTime = leads.getPushBackTime();
        if (pushBackTime != null) {
            leadsvo.setPushBackTime(pushBackTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        //新增和修改时如果有自动分配员工则返回自动分配状态给前段
        if (distributeStaffId != null || leads.getPreFollowStaffId() != null) {
            leadsvo.setDistributeStatus(1);
        }
        leadsvo.setExtData(leads.getExtData());
        leadsvo.setChannelTypeText(LeadsDataSourceEnum.parse(leads.getChannelType()).getName());
        if (!com.inngke.common.utils.StringUtils.isEmpty(leads.getTags())) {
            leadsvo.setTags(Lists.newArrayList(leads.getTags().split(",")));
            leadsvo.setTagsSize(leadsvo.getTags().size());
        } else {
            leadsvo.setTagsSize(0);
        }

        if (!StringUtils.isEmpty(leads.getEnterpriseTags())){
            leadsvo.setEnterpriseTags(Lists.newArrayList(leads.getEnterpriseTags().split(",")));
        }

        if (!StringUtils.isEmpty(leads.getExternalTags())) {
            leadsvo.setExternalTags(Lists.newArrayList(leads.getExternalTags().split(",")));
            leadsvo.setTagsSize(Optional.ofNullable(leadsvo.getTags()).map(List::size).orElse(0) + leadsvo.getExternalTags().size());
        }

        leadsvo.setPushBackStaffId(leads.getPushBackStaffId());
        leadsvo.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsvo.setPreFollowStatus(leads.getPreFollowStatus());
        LeadsPreFollowStatusEnum preFollowStatusEnum = LeadsPreFollowStatusEnum.parse(leads.getPreFollowStatus());
        leadsvo.setPreFollowStatusText(preFollowStatusEnum == null ? InngkeAppConst.EMPTY_STR : preFollowStatusEnum.getName());
        leadsvo.setLevel(leads.getLevel());
        leadsvo.setLevelId(leads.getLevelId());
        leadsvo.setType(leads.getType());
        leadsvo.setCreateStaffId(leads.getCreateStaffId());
        leadsvo.setDemandProduct(leads.getDemandProduct());

        LeadsInputSourceEnum leadsInputSourceEnum = LeadsInputSourceEnum.parse(leads.getChannelSource());
        leadsvo.setChannelSourceText(leadsInputSourceEnum == null ? null : leadsInputSourceEnum.getName());

        if (leads.getDistributeFollowTime() != null) {
            leadsvo.setDistributeFollowTime(DateTimeUtils.format(leads.getDistributeFollowTime(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        }
        if (!StringUtils.isEmpty(leads.getFollowStatuses())) {
            leadsvo.setFollowStatuses(jsonService.toObjectList(leads.getFollowStatuses(), LeadsFollowStatusDto.class));
        }
        if (!StringUtils.isEmpty(leads.getKfFollowStatuses())) {
            leadsvo.setKfFollowStatuses(jsonService.toObjectList(leads.getKfFollowStatuses(), LeadsFollowStatusDto.class));
        }
        leadsvo.setFirstContactTime(DateTimeUtils.getMilli(leads.getFirstContactTime()));
        leadsvo.setTpId(leads.getTpId());
        leadsvo.setAttachmentList(
                Optional.ofNullable(leads.getAttachmentList()).map(attachmentList ->
                                jsonService.toObjectList(attachmentList, LeadsAttachment.class))
                        .orElse(Lists.newArrayList())
        );
        return leadsvo;
    }

    private void leadsDetailBuildLeadsName(Integer bid, LeadsVo leadsDto) {
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid,
                Sets.newHashSet(leadsDto.getCreateStaffId(), leadsDto.getPushBackStaffId(), leadsDto.getPreFollowStaffId()));

        // 创建人名称
        StaffDto staff = staffByIds.get(leadsDto.getCreateStaffId());
        if (!ObjectUtils.isEmpty(staff)) {
            leadsDto.setCreateStaffName(staff.getName());
        }
        if (leadsDto.getCreateStaffId().equals(0L)) {
            leadsDto.setCreateStaffName("系统对接");
        }

        // 退回员工名称
        StaffDto pushBackStaff = staffByIds.get(leadsDto.getPushBackStaffId());
        if (!ObjectUtils.isEmpty(pushBackStaff)) {
            leadsDto.setPushBackStaffName(pushBackStaff.getName());
        }

        // 客服名称
        StaffDto preFollowStaff = staffByIds.get(leadsDto.getPreFollowStaffId());
        if (preFollowStaff != null) {
            leadsDto.setPreFollowStaffName(preFollowStaff.getName());
        }

    }


    private String getDeptNameByStaffId(Integer bid, LeadsVo leadsDto) {
        Map<Long, DepartmentDto> staffDepartmentInfoMap = getStaffDepartmentInfosByStaffIds(bid, Sets.newHashSet(leadsDto.getDistributeStaffId()));
        DepartmentDto departmentDto = staffDepartmentInfoMap.get(leadsDto.getDistributeStaffId());
        if (Objects.nonNull(departmentDto)) {
            return departmentDto.getName();
        }
        return null;
    }

    private Integer getStoreOrderCount(String mobile, Integer bid) {
        QueryStoreOrderCountRequest request = new QueryStoreOrderCountRequest();
        request.setMobile(mobile);
        request.setBid(bid);
        BaseResponse<Integer> orderCountResp = storeOrderService.queryStoreOrderCount(request);
        if (!BaseResponse.responseSuccessWithNonNullData(orderCountResp)) {
            logger.error("获取客户门店订单数失败！");
            return 0;
        }
        return orderCountResp.getData();
    }


    private Integer getShopOrderCount(Long customerId, Integer bid) {
        if (Objects.isNull(customerId)) {
            return 0;
        }
        GetShopOrderCountByCustomerIdRequest request = new GetShopOrderCountByCustomerIdRequest();
        request.setBid(bid);
        request.setCustomerId(customerId);
        request.setStatus(2);
        BaseResponse<Integer> orderCountResp = orderGetService.queryShopOrderCount(request);
        if (!BaseResponse.responseSuccessWithNonNullData(orderCountResp)) {
            logger.error("线索详情获取客户订单数失败！");
            return 0;
        }
        return orderCountResp.getData();
    }


    /**
     * 批量删除线索
     *
     * @param request
     * @return
     */
    @Override
    @DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
    public BaseResponse<Boolean> deleteBatch(LeadsDeleteBatchRequest request) {
        Integer bid = request.getBid();
        Long operatorId = request.getOperatorId();
        List<Long> ids = Lists.newArrayList(request.getIds());
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.error("ids must not be empty");
        }

        List<Leads> list = leadsManager.list(new QueryWrapper<Leads>()
                .eq(Leads.BID, bid)
                .in(Leads.ID, ids)
                .select(Leads.ID, Leads.CLIENT_ID));
        for (Leads leads : list) {
            if (leads.getClientId() != null && !leads.getClientId().equals(0L)) {
                return BaseResponse.error("删除失败，已转客户的线索不允许删除");
            }
        }
        try {
            leadsManager.batchDelete(bid, ids, operatorId);
        } catch (Exception e) {
            return BaseResponse.error("删除线索信息失败");
        }

        //更新es索引
        //AsyncUtils.runAsync(() -> {
        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(ids);
        leadsUpdateRequest.setRefreshEs(true);
        leadsEsService.updateDocs(leadsUpdateRequest);
        //});
        return BaseResponse.success(true);
    }


    @Override
    public BaseResponse<LeadsListVo> searchLeads(LeadsQuery request) {
        LeadsListVo leadsListVo = new LeadsListVo();
        Boolean pcOperate = Optional.ofNullable(request.getPcOperate()).orElse(true);

        //组装聚合数据
        List<LeadsGroupDto> leadsGroupDtos = installIsGroupInfo(request);
        if (!CollectionUtils.isEmpty(leadsGroupDtos)) {
            leadsListVo.setGroups(leadsGroupDtos);
        }
        if (ObjectUtils.isEmpty(request.getSid())) {
            return BaseResponse.success(leadsListVo);
        }

        BoolQueryBuilder qb = getQueryBuilder(request);

        SearchSourceBuilder searchSourceBuilder = initSearchSourceBuilder(request, qb);

        buildSort(request.getSortType(), searchSourceBuilder);


        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);

        try {
            SearchResponse resp = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (resp.getHits() == null || resp.getHits().getTotalHits().value == 0) {
                leadsListVo.setList(Lists.newArrayList());
                return BaseResponse.success(leadsListVo);
            }
            //处理数据
            List<LeadsEsDto> esDtoList = Arrays.stream(resp.getHits().getHits()).map(
                    e -> jsonService.toObject(e.getSourceAsString(), LeadsEsDto.class)).collect(Collectors.toList());
            List<LeadsListItemDto> leadsListItemDtos = esDtoList.stream().map(esDto -> toLeadsListItemDto(esDto)).collect(Collectors.toList());

            // 跟进数量
            buildFollowCount(request, leadsListItemDtos);

            //组装跟进数据
            installFollowInfo(request, leadsListItemDtos, pcOperate);

            installStaffNameAndAgentName(leadsListItemDtos, request.getBid(), request.getStatus());

            //已分配状态添加部门信息
            if (Objects.nonNull(request.getStatusGroup()) && request.getStatusGroup().equals(5)) {
                installStaffDepartmentInfo(request.getBid(), leadsListItemDtos);
            }
            //组装preFollowStaffName
            buildPreFollowStaffName(request.getBid(), leadsListItemDtos);

            //组装缺失部分的数据
            this.installLeadsDefectInfo(leadsListItemDtos, request.getBid());
            if (!CollectionUtils.isEmpty(leadsListItemDtos)) {
                leadsListVo.setList(leadsListItemDtos);
                leadsListVo.setTotal(Integer.valueOf((int) resp.getHits().getTotalHits().value));
                return BaseResponse.success(leadsListVo);
            }
        } catch (IOException e) {
            logger.error("ES获取guide-cudtomer数据异常");
        }
        return BaseResponse.success(leadsListVo);
    }

    private SearchSourceBuilder initSearchSourceBuilder(LeadsQuery request, BoolQueryBuilder qb) {
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = 20;
        }
        pageNo = (pageNo - 1) * pageSize;
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(qb)
                .from(pageNo).size(pageSize);
        return searchSourceBuilder;
    }

    private LeadsListItemDto toLeadsListItemDto(LeadsEsDto esDto) {
        LeadsListItemDto leadsListItemDto = new LeadsListItemDto();
        leadsListItemDto.setBid(esDto.getBid());
        leadsListItemDto.setId(esDto.getLeadsId());
        leadsListItemDto.setAddress(esDto.getAddress());
        leadsListItemDto.setAreaId(esDto.getAreaId());
        leadsListItemDto.setAreaName(esDto.getAreaName());
        leadsListItemDto.setChannel(esDto.getChannel());
        leadsListItemDto.setCityId(esDto.getCityId());
        leadsListItemDto.setCityName(esDto.getCityName());
        leadsListItemDto.setCustomerId(esDto.getCustomerId());
        leadsListItemDto.setCustomerUid(esDto.getCustomerUid());
        leadsListItemDto.setProvinceId(esDto.getProvinceId());
        leadsListItemDto.setProvinceName(esDto.getProvinceName());
        leadsListItemDto.setMobile(esDto.getMobile());
        leadsListItemDto.setName(esDto.getName());
        leadsListItemDto.setStatus(esDto.getStatus());
        leadsListItemDto.setDistributeAgentId(esDto.getDistributeAgentId());
        leadsListItemDto.setDistributeStaffId(esDto.getDistributeStaffId());
        leadsListItemDto.setPushBackTime(esDto.getPushBackTime());
        leadsListItemDto.setDistributeTime(esDto.getDistributeTime());
        leadsListItemDto.setChannelSource(esDto.getChannelSource());
        leadsListItemDto.setChannelText(LeadsChannelUtil.getName(esDto.getBid(), esDto.getChannel()));
        leadsListItemDto.setChannelType(esDto.getChannelType());
        LeadsInputSourceEnum leadsInputSourceEnum = LeadsInputSourceEnum.parse(esDto.getChannelSource());
        leadsListItemDto.setChannelSourceText(leadsInputSourceEnum == null ? null : leadsInputSourceEnum.getName());
        leadsListItemDto.setCreateTime(esDto.getCreateTime());
        if (Objects.nonNull(esDto.getClientId()) && !Objects.equals(esDto.getClientId(), 0L)) {
            leadsListItemDto.setStatusText("已转客户");
        } else {
            LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(esDto.getStatus());
            leadsListItemDto.setStatusText(statusEnum == null ? InngkeAppConst.EMPTY_STR : statusEnum.getName());
            LeadsStatusEnum preStatusEnum = LeadsStatusEnum.parse(esDto.getPreFollowStatus());
            leadsListItemDto.setStatusText(preStatusEnum == null ? InngkeAppConst.EMPTY_STR : preStatusEnum.getName());
        }
        return leadsListItemDto;
    }

    private void buildFollowCount(LeadsQuery request, List<LeadsListItemDto> leadsListItemDtos) {
        List<Long> leadsIds = leadsListItemDtos.stream().map(LeadsListItemDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leadsIds)) {
            return;
        }
        List<LeadsFollow> leadsFollowList = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .eq(LeadsFollow.BID, request.getBid())
                        .in(LeadsFollow.LEADS_ID, leadsIds)
                        .select(LeadsFollow.ID, LeadsFollow.LEADS_ID)
        );
        Map<Long, List<LeadsFollow>> followCountMap = leadsFollowList.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));
        leadsListItemDtos.forEach(item -> {
            List<LeadsFollow> follows = followCountMap.get(item.getId());
            if (!CollectionUtils.isEmpty(follows)) {
                item.setFollowCount(follows.size());
            } else {
                item.setFollowCount(0);
            }
        });
    }

    private void buildSort(Integer sortType, SearchSourceBuilder searchSourceBuilder) {
        //根据排序类型对结果进行排序,0=按导入时间倒序 1=按分配时间倒序 2=更新时间，即最新的修改的线索时间排序
        if (sortType != null) {
            switch (sortType) {
                case 0:
                    searchSourceBuilder.sort(LeadsEsDto.CREATE_TIME, SortOrder.DESC);
                    break;
                case 1:
                    searchSourceBuilder.sort(LeadsEsDto.DISTRIBUTE_TIME, SortOrder.DESC);
                    break;
                case 2:
                    searchSourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.DESC);
                    break;
                case 3:
                    searchSourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.DESC);
                    break;
                default:
                    searchSourceBuilder.sort(LeadsEsDto.ID, SortOrder.DESC);
            }
        }
    }

    private BoolQueryBuilder getQueryBuilder(LeadsQuery request) {
        String keyword = request.getKeyword();
        Integer bid = request.getBid();

        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("bid", bid));
        if (!StringUtils.isEmpty(keyword)) {
            String keywordForSearch = InngkeAppConst.STAR_STR + keyword + InngkeAppConst.STAR_STR;
            qb.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery("name.keyword", keywordForSearch))
                    .should(QueryBuilders.wildcardQuery("address.keyword", keywordForSearch))
                    .should(QueryBuilders.wildcardQuery("mobile.keyword", keywordForSearch))
                    .should(QueryBuilders.wildcardQuery("provinceName.keyword", keywordForSearch))
                    .should(QueryBuilders.wildcardQuery("cityName.keyword", keywordForSearch))
                    .should(QueryBuilders.wildcardQuery("areaName.keyword", keywordForSearch))
                    .should(QueryBuilders.wildcardQuery("weChatId.keyword", keywordForSearch))
            );
        }


        Long lastUpdateTime = request.getLastUpdateTime();
        if (lastUpdateTime != null && lastUpdateTime > 0) {
            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(lastUpdateTime / 1000, 0, ZoneOffset.ofHours(8));
            QueryBuilders.rangeQuery(LeadsEsDto.UPDATE_TIME).gt(DateTimeUtils.getMilli(dateTime));
        }

        if (!StringUtils.isEmpty(request.getName())) {
            qb.must(QueryBuilders.wildcardQuery("name", "*" + request.getName() + "*"));
        }

        if (!StringUtils.isEmpty(request.getMobile())) {
            qb.must(QueryBuilders.wildcardQuery("mobile", "*" + request.getMobile() + "*"));
        }

        if (Objects.nonNull(request.getStatus())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, request.getStatus()));
        } else {
            qb.mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.RECOVERY.getStatus()));
            qb.mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DELETED.getStatus()));
        }

        if (Objects.nonNull(request.getPreFollowStatus())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STATUS, request.getPreFollowStatus()));
        }

        if (Objects.nonNull(request.getStatusGroup())) {
            List<Integer> leadsStatus = Lists.newArrayList();
            switch (request.getStatusGroup()) {
                case 0:
                case 5:
                    leadsStatus = getAllocatedLeadsStatus();
                    break;
                case 1:
                    leadsStatus = Lists.newArrayList(LeadsStatusEnum.DISTRIBUTED.getStatus());
                    break;
                case 2:
                    leadsStatus = Lists.newArrayList(LeadsStatusEnum.CONTACTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus());
                    break;
                case 3:
                    leadsStatus = Lists.newArrayList(LeadsStatusEnum.TRADED.getStatus());
                    break;
                case 4:
                    leadsStatus = Lists.newArrayList(LeadsStatusEnum.TO_DISTRIBUTE.getStatus());
                    break;
                case 6:
                    leadsStatus = Lists.newArrayList(LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.INVALID.getStatus());
                    break;
                default:
                    break;
            }
            qb.must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, leadsStatus));
        }

        if (!StringUtils.isEmpty(request.getOrderNum())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.ORDER_SN, request.getOrderNum()));
        }

        if (Objects.nonNull(request.getAgentId())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_AGENT_ID, request.getAgentId()));
        }

        if (Objects.isNull(request.getTargetDepartmentId()) && Objects.nonNull(request.getStaffId())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, request.getStaffId()));
        } else if (Objects.nonNull(request.getTargetDepartmentId())) {
            Set<Long> staffManageDepartment = staffManageDepartmentClientForLeads.getStaffManageDepartment(request.getBid(), request.getStaffId());
            // 取查询部门和员工有权限部门的交集
            qb.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, Lists.newArrayList(request.getTargetDepartmentId())));
            qb.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, staffManageDepartment));
        } else {
            // 企业管理员在：管理中心->线索数据->线索数据可以看到对应员工的线索
            qb.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, request.getSid()));
        }

        if (!StringUtils.isEmpty(request.getImportTimeStart()) && !StringUtils.isEmpty(request.getImportTimeEnd())) {
            QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME)
                    .gt(DateTimeUtils.getMilli(LocalDateTime.parse(request.getImportTimeStart())))
                    .lt(DateTimeUtils.getMilli(LocalDateTime.parse(request.getImportTimeEnd())));
        }

        if (!StringUtils.isEmpty(request.getDistributeTimeStart()) && !StringUtils.isEmpty(request.getDistributeTimeEnd())) {
            QueryBuilders.rangeQuery(LeadsEsDto.DISTRIBUTE_TIME)
                    .gt(DateTimeUtils.getMilli(LocalDateTime.parse(request.getDistributeTimeStart())))
                    .lt(DateTimeUtils.getMilli(LocalDateTime.parse(request.getDistributeTimeEnd())));
        }

        if (Objects.nonNull(request.getChannel())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL, request.getChannel()));
        }

        if (Objects.nonNull(request.getChannelType())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL_TYPE, request.getChannelType()));
        }

        if (Objects.nonNull(request.getChannelSource())) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL_SOURCE, request.getChannelSource()));
        }

        Integer level = 0;
        Integer regionId = request.getRegionId();
        if (regionId != null) {
            RegionGetRequest regionGetRequest = new RegionGetRequest();
            regionGetRequest.setId(regionId);
            // Region RPC调用
            BaseResponse<RegionDto> regionDtoBaseResponse = regionService.getRegion(regionGetRequest);
            if (BaseResponse.responseSuccessWithNonNullData(regionDtoBaseResponse)) {
                level = regionDtoBaseResponse.getData().getLevel();
            }
        }
        switch (level) {
            case 1:
                qb.must(QueryBuilders.termQuery(LeadsEsDto.PROVINCE_ID, regionId));
                break;
            case 2:
                qb.must(QueryBuilders.termQuery(LeadsEsDto.CITY_ID, regionId));
                break;
            case 3:
                qb.must(QueryBuilders.termQuery(LeadsEsDto.AREA_ID, regionId));
                break;
            default:
                break;
        }

        if (Objects.nonNull(request.getShopChannelId()) && request.getShopChannelId() >= 0) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL_ID, request.getShopChannelId()));
        }

        if (Objects.nonNull(request.getPreFollowStaffId()) && request.getPreFollowStaffId() >= 0) {
            qb.must(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, request.getPreFollowStaffId()));
        }

        if (Objects.equals(request.getShowFollowStaff(), Boolean.TRUE)) {
            QueryBuilders.rangeQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID)
                    .gt(0L);
        }

        if (!StringUtils.isEmpty(request.getTag())) {
            qb.must(QueryBuilders.wildcardQuery(LeadsEsDto.TAGS, "*" + request.getTag() + "*"));
        }
        return qb;
    }


    @Override
    public BaseResponse<LeadsTransmitStaffInfoListDto> leadsTransmitStaffList(LeadsDepEsGetRequest request) {
        Long operatorId = request.getOperatorId();
        Integer bid = request.getBid();
        LeadsTransmitStaffInfoListDto result = new LeadsTransmitStaffInfoListDto();

        StaffListRequest staffByCustomerId = new StaffListRequest();
        staffByCustomerId.setCustomerId(operatorId);
        staffByCustomerId.setBid(bid);
        // 唯一性
        StaffDto staffDto = staffClientForLeads.getStaffList(staffByCustomerId).get(0);

        // 查询出当前操作员可以管理的所有部门
        BaseIdRequest baseIdRequest = new BaseIdRequest();
        baseIdRequest.setBid(bid);
        baseIdRequest.setId(staffDto.getId());
        List<Long> permissions = Lists.newArrayList(staffManageDepartmentClientForLeads.getManageDepartmentByStaffId(baseIdRequest));
        if (CollectionUtils.isEmpty(permissions)) {
            //没有权限的话只能查看所在的当前部门
            //如果为null初始化permission
            permissions = Lists.newArrayList();
        }
        //select agent departmentIds
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(Collections.singletonList(staffDto.getId()));

        Long departmentId = request.getDepartmentId();
        boolean emptyDep = departmentId == null || request.getDepartmentId() == 0;
        //当前请求的部门编号信息为空时需要把当前的部门设定为可管理部门的第一个部门的上级部门
        if (emptyDep && !CollectionUtils.isEmpty(permissions)) {
            departmentId = permissions.get(0);
        }

        //获取部门信息
        GetDepartmentRequest depRequest = new GetDepartmentRequest();
        depRequest.setBid(bid);
        depRequest.setDepartmentId(departmentId);
        depRequest.setChildrenLevel(1);
        DepartmentDto treeDepartmentDto;
        //当前请求的部门编号信息为空时需要把当前的部门设定为可管理部门的第一个部门的上级部门
        if (emptyDep) {
            // 找出该部门的父部门，以及所有的同级部门
            treeDepartmentDto = departmentClientForLeads.getParentWithChildrenById(depRequest);
        } else {
            // 找出该部门，以及它所有的子部门
            treeDepartmentDto = departmentClientForLeads.getDepartment(depRequest);
        }
        departmentId = treeDepartmentDto.getId();

        // 组装部门层级信息
        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setChildrenLevel(0);
        getDepartmentRequest.setBid(bid);
        getDepartmentRequest.setDepartmentId(departmentId);
        List<DepartmentDto> departmentWithParentsList = departmentClientForLeads.getDepartmentWithParents(getDepartmentRequest);

        // 输出机构基本信息 --> result
        List<LeadsTransmitStaffInfoListDto.SimpleDepartment> resultDepSimpleList = departmentWithParentsList.stream().map(item -> {
            LeadsTransmitStaffInfoListDto.SimpleDepartment simpleDepartment = new LeadsTransmitStaffInfoListDto.SimpleDepartment();
            simpleDepartment.setDepartmentId(item.getId());
            simpleDepartment.setDepartmentName(item.getName());
            return simpleDepartment;
        }).collect(Collectors.toList());
        result.setDepSimpleInfoList(resultDepSimpleList);

        if (!StringUtils.isEmpty(request.getKeyword())) {
            if (permissions.contains(departmentId)) {
                StaffListRequest getStaffInfoByNameRequest = new StaffListRequest();
                getStaffInfoByNameRequest.setBid(bid);
                getStaffInfoByNameRequest.setDepartmentId(departmentId);
                getStaffInfoByNameRequest.setName(request.getKeyword());
                List<StaffDto> staffList = staffClientForLeads.getStaffList(getStaffInfoByNameRequest);
                Set<Long> staffIds = staffList.stream().map(StaffDto::getId).collect(Collectors.toSet());
                //获取员工关注信息
                StaffGetByIdsRequest staffGetByIdsRequest = new StaffGetByIdsRequest();
                staffGetByIdsRequest.setBid(bid);
                staffGetByIdsRequest.setIds(Lists.newArrayList(staffIds));
                // org.staff可以直接获取 是否关注微信公众号
                result.setStaffList(staffList.stream().map(dto -> {
                    LeadsTransmitStaffInfoListDto.Staff staff = new LeadsTransmitStaffInfoListDto.Staff();
                    staff.setStaffId(dto.getId());
                    staff.setStaffName(dto.getName());
                    return staff;
                }).collect(Collectors.toList()));
                //排除员工自己
                Long staffId = staffDto.getId();
                if (!CollectionUtils.isEmpty(result.getStaffList())) {
                    result.setStaffList(result.getStaffList().stream().filter(e -> !e.getStaffId().equals(staffId)).collect(Collectors.toList()));
                }
                return BaseResponse.success(result);
            }
        }

        //处理部门信息
        List<DepartmentDto> children = treeDepartmentDto.getChildren();
        if (!CollectionUtils.isEmpty(children)) {
            // 父类部门
            List<Long> finalPermissions = permissions;
            children = children.stream().filter(item -> finalPermissions.contains(item.getId())).collect(Collectors.toList());
            try {
                //组装当前部门下子级部门的聚合线索数据
                List<Long> depIds = children.stream().map(DepartmentDto::getId).collect(Collectors.toList());
                List<LeadsTransmitStaffInfoListDto.Department> transmitStaffDepartmentInfo = Lists.newArrayList();

                // 统计员工管理部门的员工数
                BaseIdsRequest req = new BaseIdsRequest();
                req.setBid(bid);
                req.setIds(depIds);
                // key-deptIds values-count
                Map<Long, Integer> departmentStaffCountMap = staffClientForLeads.getCountMapByDeptIds(req);

                if (!CollectionUtils.isEmpty(departmentStaffCountMap)) {
                    children.forEach(item -> {
                        LeadsTransmitStaffInfoListDto.Department department = new LeadsTransmitStaffInfoListDto.Department();
                        department.setDepartmentId(item.getId());
                        if (!CollectionUtils.isEmpty(departmentStaffCountMap)) {
                            Integer departStaffCount = departmentStaffCountMap.get(item.getId());
                            String departmentName;
                            if (departStaffCount == null) {
                                departmentName = item.getName() + " " + "(" + 0 + "人" + ")";
                            } else {
                                departmentName = item.getName() + " " + "(" + departStaffCount + "人" + ")";
                            }
                            department.setDepartmentName(departmentName);
                        }
                        transmitStaffDepartmentInfo.add(department);
                    });
                }
                result.setDepartmentList(transmitStaffDepartmentInfo);
            } catch (Exception e) {
                logger.error("查询部门信息失败", e);
            }
        }

        //处理当前部门员工
        if (departmentId != null) {
            //检查是否有权限
            if (permissions.contains(departmentId)) {
                StaffListRequest req = new StaffListRequest();
                req.setDepartmentId(departmentId);
                req.setBid(bid);
                List<LeadsTransmitStaffInfoListDto.Staff> staffDtoList = staffClientForLeads.getStaffList(req).stream().map(item -> {
                    LeadsTransmitStaffInfoListDto.Staff ltStaff = new LeadsTransmitStaffInfoListDto.Staff();
                    ltStaff.setStaffId(item.getId());
                    ltStaff.setStaffName(item.getName());
                    return ltStaff;
                }).collect(Collectors.toList());
                result.setStaffList(staffDtoList);
            }
        }
        //排除员工自己
        Long staffId = staffDto.getId();
        if (!CollectionUtils.isEmpty(result.getStaffList())) {
            result.setStaffList(result.getStaffList().stream().filter(e -> !e.getStaffId().equals(staffId)).collect(Collectors.toList()));
        }
        return BaseResponse.success(result);
    }

    @Override
    @DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
    public BaseResponse<List<LeadsDto>> getLeadsListByIds(LeadsIdsRequest request) {
        List<Long> ids = request.getIds();
        Integer bid = request.getBid();
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        List<Leads> list = leadsManager.list(Wrappers.<Leads>query()
                .eq(Leads.BID, bid)
                .in(Leads.ID, ids)
        );

        Map<Long, Long> staffAndAgentByStaffIds = staffToAgentUtil.getStaffAndAgent(list, bid);

        List<LeadsDto> resultList = list.stream()
                .map(item -> LeadsConverter.toLeadsListItem(item, staffAndAgentByStaffIds))
                .collect(Collectors.toList());
        fillStaffName(bid, resultList);
        return BaseResponse.success(resultList);
    }

    @Override
    public BaseResponse<List<LeadsDto>> getLeadsByIds(LeadsIdsRequest request) {
        List<Long> ids = request.getIds();
        Integer bid = request.getBid();
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        List<Leads> list = leadsManager.list(Wrappers.<Leads>query()
                .eq(Leads.BID, bid)
                .in(Leads.ID, ids)
        );
        List<LeadsDto> resultList = list.stream()
                .map(item -> LeadsConverter.toLeadsListItem(item, new HashMap<>()))
                .collect(Collectors.toList());

        return BaseResponse.success(resultList);
    }

    private void fillStaffName(Integer bid, List<LeadsDto> leadsDtoList) {
        Set<Long> staffIds = Sets.newHashSet();
        leadsDtoList.forEach(leadsDto -> {
            if (Objects.nonNull(leadsDto)) {
                if (leadsDto.getCreateStaffId() > 0) {
                    staffIds.add(leadsDto.getCreateStaffId());
                }
                if (leadsDto.getPreFollowStaffId() > 0) {
                    staffIds.add(leadsDto.getPreFollowStaffId());
                }
            }
        });
        Map<Long, StaffDto> staffDtoMap = staffClientForLeads.getStaffByIds(bid, staffIds);
        Map<Long, String> staffNameMap = staffDtoMap.keySet().stream().collect(Collectors.toMap(Function.identity(), staffId -> staffDtoMap.get(staffId).getName()));
        leadsDtoList.forEach(leadsDto -> {
            if (Objects.nonNull(leadsDto)) {
                leadsDto.setCreateStaffName(staffNameMap.getOrDefault(leadsDto.getCreateStaffId(), InngkeAppConst.EMPTY_STR));
                leadsDto.setPreFollowStaffName(staffNameMap.getOrDefault(leadsDto.getPreFollowStaffId(), InngkeAppConst.EMPTY_STR));
            }
        });
    }

    @Override
    public BaseResponse<List<LeadsDto>> queryLeadsByMobile(MobileLeadsGetRequest request) {
        String mobile = request.getMobile();
        Integer bid = request.getBid();
        List<Leads> list = leadsManager.list(Wrappers.<Leads>query()
                .eq(Leads.BID, bid)
                .eq(Leads.MOBILE, mobile)
        );
        List<LeadsDto> resultList = null;
        if (!CollectionUtils.isEmpty(list)) {
            Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(list, bid);
            resultList = list.stream().map(leadsDto -> LeadsConverter.toLeadsListItem(leadsDto, staffAndAgent)).collect(Collectors.toList());
        }
        return BaseResponse.success(resultList);
    }

    @Override
    public BaseResponse<Integer> historyCount(LeadsQuery request) {
        BoolQueryBuilder queryBuilder = builderBoolQueryBuilder(request);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder);

        SearchRequest searchRequest = new SearchRequest()
                .source(searchSourceBuilder);
        searchRequest.indices("leads");
        SearchResponse response = esDocService.search(searchRequest);
        SearchHits searchHits = response.getHits();
        int total = (int) searchHits.getTotalHits().value;
        return BaseResponse.success(total);
    }

    @Override
    public BaseResponse<LeadsListVo> historyList(LeadsQuery request) {
        LeadsListVo dto = new LeadsListVo();
        BaseResponse<LeadsListVo> baseResponse = BaseResponse.success(dto);
        Boolean pcOperate = Optional.ofNullable(request.getPcOperate()).orElse(true);

        LeadsQuery groupLeadsQuery = new LeadsQuery();
        groupLeadsQuery.setBid(request.getBid());
        groupLeadsQuery.setHistoryStaffIds(request.getHistoryStaffIds());

        List<LeadsGroupDto> leadsGroupDtoList = getLeadsGroupDtoList(groupLeadsQuery);
        dto.setGroups(leadsGroupDtoList);

        BoolQueryBuilder queryBuilder = builderBoolQueryBuilder(request);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder)
                .size(request.getPageSize())
                .from((request.getPageNo() - 1) * request.getPageSize());

        searchSourceBuilder.sort("distributeTime", SortOrder.DESC);

        SearchRequest searchRequest = new SearchRequest()
                .source(searchSourceBuilder);
        searchRequest.indices("leads");

        SearchResponse response = esDocService.search(searchRequest);
        SearchHits searchHits = response.getHits();
        int total = (int) searchHits.getTotalHits().value;
        dto.setTotal(total);

        List<LeadsListItemDto> leadsListItemDtoList = new ArrayList<>();
        searchHits.forEach(hit -> {
            LeadsEsDto leadsEsDto = jsonService.toObject(hit.getSourceAsString(), LeadsEsDto.class);
            LeadsListItemDto leadsListItemDto = new LeadsListItemDto();
            BeanUtils.copyProperties(leadsEsDto, leadsListItemDto);
            leadsListItemDto.setId(leadsEsDto.getLeadsId());
            LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(leadsEsDto.getStatus());

            if (Objects.nonNull(leadsEsDto.getClientId()) && !Objects.equals(leadsEsDto.getClientId(), 0L)) {
                statusEnum = LeadsStatusEnum.parse(leadsEsDto.getClientStatus());
            }
            leadsListItemDto.setStatusText(statusEnum == null ? InngkeAppConst.EMPTY_STR : statusEnum.getName());
            leadsListItemDtoList.add(leadsListItemDto);
        });
        if (leadsListItemDtoList.size() == 0) {
            dto.setList(Lists.newArrayList());
            return baseResponse;
        }
        dto.setList(leadsListItemDtoList);
        //组装名字
        installStaffNameAndAgentName(leadsListItemDtoList, request.getBid(), request.getStatus());

        //组装跟进数据
        installFollowInfo(request, leadsListItemDtoList, pcOperate);
        return baseResponse;
    }

    private List<LeadsGroupDto> getLeadsGroupDtoList(LeadsQuery request) {
        BoolQueryBuilder queryBuilder = builderBoolQueryBuilder(request);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder);
        searchSourceBuilder
                .aggregation(
                        AggregationBuilders.terms("aggTotal").field("bid")
                                .subAggregation(
                                        //总数
                                        AggregationBuilders.filter("totalCount", QueryBuilders.boolQuery()
                                                .must(getAllHistoryQuery()))
                                )
                                .subAggregation(
                                        //待联系
                                        AggregationBuilders.filter("noContactedCount",
                                                QueryBuilders.boolQuery().must(QueryBuilders.termQuery("status", 1)))
                                )
                                .subAggregation(
                                        //联系中
                                        AggregationBuilders.filter("contactedCount", QueryBuilders.boolQuery()
                                                        .must(getContactHistoryQuery()))
                                )
                                .subAggregation(
                                        //已成交
                                        AggregationBuilders.filter("tradedCount", QueryBuilders.boolQuery()
                                                .must(getTradedHistoryQuery()))
                                )
                );
        try {
            SearchRequest searchRequest = new SearchRequest()
                    .indices("leads")
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            List<? extends Terms.Bucket> inTotal = ((Terms) searchResponse.getAggregations().get("aggTotal")).getBuckets();
            List<String> statusName = Arrays.asList("全部", "待联系", "联系中", "已成交");
            List<Integer> statusId = Arrays.asList(0, 1, 2, 3);
            List<LeadsGroupDto> groupList = new ArrayList<>();
            inTotal.forEach(ele -> {
                List<String> statusCode = Arrays.asList("totalCount", "noContactedCount", "contactedCount", "tradedCount");
                Aggregations currentAggregations = ele.getAggregations();

                int index = 0;
                for (String name : statusName) {
                    ParsedFilter parsedFilter = currentAggregations.get(statusCode.get(index));
                    int count = Optional.of(parsedFilter.getDocCount()).orElse(0L).intValue();

                    LeadsGroupDto groupCountDto = new LeadsGroupDto();
                    groupCountDto.setGroupId(statusId.get(index));
                    groupCountDto.setGroupName(name);
                    groupCountDto.setLeadsCount(count);
                    groupList.add(groupCountDto);
                    index++;
                }
            });
            if (groupList.size() == 0) {
                int index = 0;
                for (String name : statusName) {
                    LeadsGroupDto groupCountDto = new LeadsGroupDto();
                    groupCountDto.setGroupId(statusId.get(index));
                    groupCountDto.setGroupName(name);
                    groupCountDto.setLeadsCount(0);
                    groupList.add(groupCountDto);
                    index++;
                }
            }
            return groupList;
        } catch (IOException e) {
            logger.error("获取状态统计失败！e={}", e);
            throw new InngkeServiceException("获取状态统计失败！");
        }
    }

    private BoolQueryBuilder builderBoolQueryBuilder(LeadsQuery request) {
        Integer bid = request.getBid();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("bid", bid));
        Set<Long> historyStaffIds = request.getHistoryStaffIds();
        if (historyStaffIds != null) {
            queryBuilder.must(QueryBuilders.termsQuery("historyDistributeStaffIds", historyStaffIds));
        }

        String keyword = request.getKeyword();
        if (org.apache.commons.lang3.StringUtils.isNoneEmpty(keyword)) {
            queryBuilder.must(
                    QueryBuilders.boolQuery()
                            .should(QueryBuilders.wildcardQuery("mobile.keyword", "*" + keyword + "*"))
                            .should(QueryBuilders.wildcardQuery("name.keyword", "*" + keyword + "*"))
            );
        }

        Integer statusGroup = request.getStatusGroup();
        if (statusGroup != null) {
            switch (statusGroup) {
                case 0:
                    queryBuilder.must(getAllHistoryQuery());
                    break;
                case 1:
                    queryBuilder.must(QueryBuilders.termQuery("status", 1));
                    break;
                case 2:
                    queryBuilder.must(getContactHistoryQuery());
                    break;
                case 3:
                    queryBuilder.must(getTradedHistoryQuery());
                    break;
                default:
                    break;
            }
        }
        return queryBuilder;
    }

    private void installLeadsDefectInfo(List<LeadsListItemDto> leadsListItemDtos, Integer bid) {
        if (CollectionUtils.isEmpty(leadsListItemDtos)) {
            return;
        }
        Set<Long> leadsIds = leadsListItemDtos.stream().map(LeadsListItemDto::getId).collect(Collectors.toSet());
        Map<Long, Leads> leadsMap = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, leadsIds)
        ).stream().collect(Collectors.toMap(Leads::getId, Function.identity()));
        // //回填错误信息 最新订单时间
        leadsListItemDtos.forEach(leadsListItemDto -> {
            if (!CollectionUtils.isEmpty(leadsMap)) {
                Leads leads = leadsMap.get(leadsListItemDto.getId());
                if (!StringUtils.isEmpty(leads.getErrorMsg())) {
                    leadsListItemDto.setErrorMsg(leads.getErrorMsg());
                }
                if (leads.getPayTime() != null) {
                    leadsListItemDto.setLastOrderTime(DateTimeUtils.getMilli(leads.getPayTime()));
                }
                leadsListItemDto.setUpdateTime(DateTimeUtils.getMilli(leads.getUpdateTime()));
                leadsListItemDto.setCreateTime(DateTimeUtils.getMilli(leads.getCreateTime()));
                leadsListItemDto.setPayTime(DateTimeUtils.getMilli(leads.getPayTime()));
            }
        });


        //回填员工名称以及经销商名称
        this.installStaffNameAndAgentName(leadsListItemDtos, bid, null);
        //组装跟进数据
        LeadsQuery leadsQuery = new LeadsQuery();
        leadsQuery.setWithFollow(true);
        this.installFollowInfo(leadsQuery, leadsListItemDtos, true);
    }

    @Override
    public BaseResponse<List<LeadsChannelVo>> getChannelList() {
        List<LeadsChannelVo> channels = Arrays.stream(LeadsChannelEnum.values()).map(e -> {
            LeadsChannelVo leadsChannelListResponse = new LeadsChannelVo();
            leadsChannelListResponse.setChannelId(e.getChannel());
            leadsChannelListResponse.setChannelName(e.getName());
            leadsChannelListResponse.setSortValue(e.getSort());
            return leadsChannelListResponse;
        }).collect(Collectors.toList());
        channels.remove(0); //去掉手工输入
        //按照排序值排序
        channels.sort(Comparator.comparing(LeadsChannelVo::getSortValue).reversed());
        return BaseResponse.success(channels);
    }

    @Override
    public BaseResponse<List<LeadsChannelVo>> getChannelTypeList() {
        List<Integer> eliminate = Lists.newArrayList(0, 1, 2);
        List<LeadsChannelVo> channels = Arrays.stream(LeadsDataSourceEnum.values()).map(e -> {
                    LeadsChannelVo leadsChannelListResponse = new LeadsChannelVo();
                    leadsChannelListResponse.setChannelId(e.getCode());
                    leadsChannelListResponse.setChannelName(e.getName());
                    return leadsChannelListResponse;
                }).filter(leadsChannelVo -> !eliminate.contains(leadsChannelVo.getChannelId()))
                .collect(Collectors.toList());
        return BaseResponse.success(channels);
    }

    @Override
    public BaseResponse<List<LeadsChannelVo>> getChannelSourceList() {
        List<LeadsChannelVo> channels = Arrays.stream(LeadsInputSourceEnum.values()).map(e -> {
            LeadsChannelVo leadsChannelListResponse = new LeadsChannelVo();
            leadsChannelListResponse.setChannelId(e.getCode());
            leadsChannelListResponse.setChannelName(e.getName());
            return leadsChannelListResponse;
        }).collect(Collectors.toList());
        return BaseResponse.success(channels);
    }

    @Override
    public BaseResponse<BindMobileDto> bindMobile(LeadsPrivateNumberBindRequest request) {

        Long leadsId = request.getLeadsId();
        Integer bid = request.getBid();

        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, leadsId)
        );
        if (null == leads) {
            return BaseResponse.error("线索" + leadsId + "不存在");
        }
        // 判断线索是否已经被回收
        if (LeadsStatusEnum.delStatus().contains(leads.getStatus())) {
            return BaseResponse.error(ErrorCode.NOT_FOUNT.getCode(), "线索已不存在");
        }

        // 点击获取完整手机号申请绑定，将showPhone设置为1
        if (null != request.getSource() && request.getSource() == 1) {
            leads.setShowPhone(1);
            leadsManager.updateById(leads);
        }

//        VoiceBindRequest voiceBindRequest = new VoiceBindRequest();
//        BeanUtils.copyProperties(request, voiceBindRequest);
//        voiceBindRequest.setBid(bid);
//        BidUtils.setBid(bid);
        BindMobileDto bindPhone = getBindPhone(bid, leads, request);
        return BaseResponse.success(bindPhone);
    }

    private BindMobileDto getBindPhone(Integer bid, Leads leads, LeadsPrivateNumberBindRequest bindRequest) {
        BindMobileDto result = new BindMobileDto();

        ValueOperations valueOperations = redisTemplate.opsForValue();
        Integer failNum = (Integer) valueOperations.get(LEADS_CONTACT_FAIL_KEY + bid + InngkeAppConst.CLN_STR + leads.getId());
        if (Objects.nonNull(failNum) && failNum >= 2) {
            result.setPrivateMobile(false);
            result.setMobile(leads.getMobile());
            // 设置通话记录
            LeadsPrivatePhoneCallLogRequest logRequest = new LeadsPrivatePhoneCallLogRequest();
            logRequest.setLeadsId(leads.getId());
            logRequest.setBid(bid);
            logRequest.setCallTime(LocalDateTime.now());
            logRequest.setPrivateVoiceRecordId(0L);
            leadsEventService.createCallLog(logRequest);
        } else {
            VoiceBindRequest voiceBindRequest = new VoiceBindRequest();
            BeanUtils.copyProperties(bindRequest, voiceBindRequest);
            voiceBindRequest.setBid(bid);
            BidUtils.setBid(bid);
            BaseResponse<String> bindResponse = privateBindService.privateBind(voiceBindRequest);
            if (!BaseResponse.responseSuccessWithNonNullData(bindResponse)) {
                throw new InngkeServiceException(bindResponse.getMsg());
            }
            result.setPrivateMobile(true);
            result.setMobile(bindResponse.getData());
        }
        return result;
    }

    @Override
    public BaseResponse<List<LeadsNameDto>> getLeadsName(LeadsAddRequest request) {
        Integer bid = request.getBid();
        String name = request.getName();
        Long operatorId = request.getOperatorId();
        logger.info("getLeadsName {}", request);

        List<Long> mangerDeptIds = loginClientForLeads.getMangerDeptIds(bid, request.getCustomerId());
        List<UserStaffDto> userStaffDtoList = staffClientForLeads.getStaffByDeptIds(request.getBid(), Sets.newHashSet(mangerDeptIds));
        List<Long> staffIds = userStaffDtoList.stream().map(UserStaffDto::getId).collect(Collectors.toList());


        StaffListRequest staffByCustomerId = new StaffListRequest();
        staffByCustomerId.setCustomerId(operatorId);
        staffByCustomerId.setBid(bid);
        // 唯一性
        StaffDto staffDto = staffClientForLeads.getStaffList(staffByCustomerId).get(0);
        Long staffId = staffDto.getId();

        LeadsSearchRequest leadsSearchRequest = new LeadsSearchRequest();
        leadsSearchRequest.setBid(bid);
        leadsSearchRequest.setName(name);
        leadsSearchRequest.setStaffId(staffIds);
        BaseResponse<List<LeadsEsDto>> response = leadsEsService.searchLeads(leadsSearchRequest);

        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        List<LeadsEsDto> leadsEsDtoList = response.getData();
        return BaseResponse.success(leadsEsDtoList.stream().map(leadEsDto -> {
            LeadsNameDto leadsNameDto = new LeadsNameDto();
            leadsNameDto.setId(leadEsDto.getLeadsId());
            leadsNameDto.setName(leadEsDto.getName());
            return leadsNameDto;
        }).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<LeadsTransmitStaffInfoListDto> leadsTransmitGuideList(LeadsDepEsGetRequest request) {
        Long operatorId = request.getOperatorId();
        Integer bid = request.getBid();
        LeadsTransmitStaffInfoListDto result = new LeadsTransmitStaffInfoListDto();

        StaffListRequest staffByCustomerId = new StaffListRequest();
        staffByCustomerId.setCustomerId(operatorId);
        staffByCustomerId.setBid(bid);
        // 唯一性
        StaffDto staffDto = staffClientForLeads.getStaffList(staffByCustomerId).get(0);

        //查询出当前操作员可以管理的所有部门
        BaseIdRequest baseIdRequest = new BaseIdRequest();
        baseIdRequest.setBid(bid);
        baseIdRequest.setId(staffDto.getId());

        List<Long> permissions = Lists.newArrayList(staffManageDepartmentClientForLeads.getManageDepartmentByStaffId(baseIdRequest));
        if (CollectionUtils.isEmpty(permissions)) {
            //没有权限的话只能查看所在的当前部门
            //如果为null初始化permission
            permissions = Lists.newArrayList();
        }

        Long departmentId = request.getDepartmentId();
        boolean emptyDep = departmentId == null || request.getDepartmentId() == 0;
        //当前请求的部门编号信息为空时需要把当前的部门设定为可管理部门的第一个部门的上级部门
        if (emptyDep && !CollectionUtils.isEmpty(permissions)) {
            departmentId = permissions.get(0);
        }

        //获取部门信息
        GetDepartmentRequest depRequest = new GetDepartmentRequest();
        depRequest.setBid(bid);
        depRequest.setDepartmentId(departmentId);
        depRequest.setChildrenLevel(1);
        DepartmentDto treeDepartmentDto;
        //当前请求的部门编号信息为空时需要把当前的部门设定为可管理部门的第一个部门的上级部门
        if (emptyDep) {
            // 找出该部门的父部门，以及所有的同级部门
            treeDepartmentDto = departmentClientForLeads.getParentWithChildrenById(depRequest);
        } else {
            // 找出该部门，以及它所有的子部门
            treeDepartmentDto = departmentClientForLeads.getDepartment(depRequest);
        }

        departmentId = treeDepartmentDto.getId();
        //组装部门层级信息
        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setChildrenLevel(0);
        getDepartmentRequest.setBid(bid);
        getDepartmentRequest.setDepartmentId(departmentId);
        List<DepartmentDto> departmentWithParents = departmentClientForLeads.getDepartmentWithParents(getDepartmentRequest);

        // 父级部门
        List<LeadsTransmitStaffInfoListDto.SimpleDepartment> resultDepSimpleList = departmentWithParents.stream().map(item -> {
            LeadsTransmitStaffInfoListDto.SimpleDepartment simpleDepartment = new LeadsTransmitStaffInfoListDto.SimpleDepartment();
            simpleDepartment.setDepartmentId(item.getId());
            simpleDepartment.setDepartmentName(item.getName());
            return simpleDepartment;
        }).collect(Collectors.toList());
        result.setDepSimpleInfoList(resultDepSimpleList);
        // 把父级部门也加入到权限队列中
        List<Long> deptIds = resultDepSimpleList.stream().map(LeadsTransmitStaffInfoListDto.SimpleDepartment::getDepartmentId).collect(Collectors.toList());
        permissions.addAll(deptIds);
        String keyword = request.getKeyword();
        if (!StringUtils.isEmpty(keyword)) {
            if (permissions.contains(departmentId)) {
                List<LeadsTransmitStaffInfoListDto.Staff> staffList = getStaffListByDeptId(bid, staffDto.getId(), departmentId);
                result.setStaffList(staffList);
                return BaseResponse.success(result);
            }
        }

        //处理部门信息
        List<DepartmentDto> children = treeDepartmentDto.getChildren();
        if (!CollectionUtils.isEmpty(children)) {
            GetDepartmentsRequest getDepartmentsRequest = new GetDepartmentsRequest();
            getDepartmentsRequest.setIds(permissions);
            getDepartmentsRequest.setBid(bid);
            getDepartmentsRequest.setChildrenLevel(0);
            // 父类部门
            List<Long> allDepartmentIdList = departmentClientForLeads.getDepartmentsWithParents(getDepartmentsRequest).stream().map(DepartmentDto::getId).collect(Collectors.toList());
            // 只计算父级部门
            children = children.stream().filter(item -> allDepartmentIdList.contains(item.getId())).collect(Collectors.toList());
            try {
                // 组装当前部门下子级部门的聚合线索数据
                List<Long> depIds = children.stream().map(DepartmentDto::getId).collect(Collectors.toList());
                List<LeadsTransmitStaffInfoListDto.Department> transmitStaffDepartmentInfo = Lists.newArrayList();


                BaseIdsRequest req = new BaseIdsRequest();
                req.setBid(bid);
                req.setIds(depIds);
                // key-deptIds values-count
                Map<Long, Integer> departmentStaffCountMap = staffClientForLeads.getCountMapByDeptIds(req);
                if (!CollectionUtils.isEmpty(departmentStaffCountMap)) {
                    children.forEach(item -> {
                        LeadsTransmitStaffInfoListDto.Department department = new LeadsTransmitStaffInfoListDto.Department();
                        department.setDepartmentId(item.getId());
                        if (!CollectionUtils.isEmpty(departmentStaffCountMap)) {
                            Integer departStaffCount = departmentStaffCountMap.get(item.getId());
                            String departmentName = "";
                            if (departStaffCount == null) {
                                departmentName = item.getName() + " " + "(" + 0 + "人" + ")";
                            } else {
                                departmentName = item.getName() + " " + "(" + departStaffCount + "人" + ")";
                            }
                            department.setDepartmentName(departmentName);
                        }
                        transmitStaffDepartmentInfo.add(department);
                    });
                }
                result.setDepartmentList(transmitStaffDepartmentInfo);
            } catch (Exception e) {
                logger.error("查询部门信息失败", e);
            }
        }

        //处理当前部门员工
        if (departmentId != null) {
            //检查是否有权限
            if (permissions.contains(departmentId)) {
                List<LeadsTransmitStaffInfoListDto.Staff> staffList = getStaffListByDeptId(bid, staffDto.getId(), departmentId);
                result.setStaffList(staffList);
            }
        }
        //排除员工自己
        Long staffId = staffDto.getId();
        if (!CollectionUtils.isEmpty(result.getStaffList())) {
            result.setStaffList(result.getStaffList().stream().filter(e -> !e.getStaffId().equals(staffId)).collect(Collectors.toList()));
        }
        return BaseResponse.success(result);
    }

    private List<LeadsTransmitStaffInfoListDto.Staff> getStaffListByDeptId(int bid, long currentStaffId, long departmentId) {
        Map<Long, LeadsTransmitStaffInfoListDto.Staff> customerStaffMap = Maps.newHashMap();
        StaffListRequest req = new StaffListRequest();
        req.setBid(bid);
        req.setDepartmentId(departmentId);
        List<LeadsTransmitStaffInfoListDto.Staff> staffList = staffClientForLeads.getStaffList(req)
                .stream()
                //排除员工自己
                .filter(dto -> !dto.getId().equals(currentStaffId))
                .map(dto -> {
                    LeadsTransmitStaffInfoListDto.Staff staff = new LeadsTransmitStaffInfoListDto.Staff();
                    staff.setStaffId(dto.getId());
                    staff.setStaffName(dto.getName());
                    Long customerId = dto.getCustomerId();
                    if (customerId != null && customerId > 0) {
                        customerStaffMap.put(customerId, staff);
                    }
                    return staff;
                }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customerStaffMap)) {
            //查询员工公众号订阅状态
            customerGetServiceClientForLeads.getCustomerInfoByIds(bid, customerStaffMap.keySet(), Sets.newHashSet("id", "wxPubSubscribe"))
                    .forEach((customerId, customerInfo) -> {
                        LeadsTransmitStaffInfoListDto.Staff staff = customerStaffMap.get(customerId);
                        if (staff == null) {
                            return;
                        }
                        Integer wxPubSubscribe = customerInfo.getWxPubSubscribe();
                        staff.setSubscribe(wxPubSubscribe != null && wxPubSubscribe == 1);
                    });
        }

        return staffList;
    }

    @Override
    public BaseResponse<Boolean> staffTransferLeads(LeadsTransferStaffRequest request) {
        Set<Long> oldStaffId = request.getOldStaffId();
        if (CollectionUtils.isEmpty(oldStaffId)) {
            throw new InngkeServiceException("旧负责人id列表为空");
        }
        boolean update = leadsManager.update(
                Wrappers.<Leads>update()
                        .eq(Leads.BID, request.getBid())
                        .in(Leads.DISTRIBUTE_STAFF_ID, oldStaffId)
                        .set(Leads.DISTRIBUTE_STAFF_ID, request.getStaffId())
                // .set(Leads.DISTRIBUTE_AGENT_ID, request.getAgentId())
        );

        return BaseResponse.success(update);
    }

    @Override
    public BaseResponse<Map<Long, LeadsFollowTimeDto>> getLeadsFollowTime(BaseIdsRequest request) {
        Map<Long, LeadsFollowTimeDto> leadsFollowTimeMap = leadsFollowTimeManager.list(Wrappers.<LeadsFollowTime>query()
                        .eq(LeadsFollowTime.BID, request.getBid())
                        .in(LeadsFollowTime.ID, request.getIds()))
                .stream().map(LeadsConverter::toLeadsFollowTimeDto)
                .collect(Collectors.toMap(LeadsFollowTimeDto::getId, Function.identity()));

        return BaseResponse.success(leadsFollowTimeMap);
    }

    @Override
    public BaseResponse<Map<Integer, Integer>> count(LeadsQuery request) {
        List<Integer> statusList = request.getStatusList();
        if (ObjectUtils.isEmpty(statusList)) {
            return BaseResponse.success(Maps.newHashMap());
        }
        Map<Integer, Integer> countMap = Maps.newHashMap();
        statusList.forEach(status -> {
            request.setStatusList(LeadsStatusEnum.toLeadsStatus(status));
            countMap.put(status, leadsManager.count(buildQuery(request, false)));
        });
        return BaseResponse.success(countMap);
    }

    @Override
    public BaseResponse<Long> bindClient(@Validated LeadsBindClientRequest request) {
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.ID, request.getLeadsId())
        );
        if (Objects.nonNull(leads.getClientId()) && !Objects.equals(leads.getClientId(), 0L)) {
            return BaseResponse.error("该线索已关联客户，请勿重复关联");
        }
        leads.setClientId(request.getClientId());
        leads.setRelationClientTime(LocalDateTime.now());
        String leadsOldStatusText;

        if (leads.getStatus().equals(LeadsStatusEnum.DISTRIBUTED.getStatus())) {
            String followInfo = "将线索状态由【" + LeadsStatusEnum.DISTRIBUTED.getName() + "】修改为【" + LeadsStatusEnum.CONTACTED.getName() + "】";
            leadsOldStatusText = LeadsStatusEnum.CONTACTED.getName();
            leadsManager.updateStatus(leads, request.getOperatorId(), followInfo, LeadsStatusEnum.CONTACTED.getStatus());
        } else {
            leadsOldStatusText = LeadsStatusEnum.INTENT.getName();
        }
        leads.setStatus(LeadsStatusEnum.INTENT.getStatus());
        leadsManager.updateById(leads);

        LeadsBasicDto leadsBasicDto = LeadsConverter.toLeadsBasicDto(leads);

        // 调用客户子域
        BaseResponse<Long> response = clientApiForMp.bindClient(
                request, leadsBasicDto, leadsOldStatusText, Objects.equals(leads.getChannelSource(), 2));
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            leads.setClientId(0L);
            leadsManager.updateById(leads);
            return BaseResponse.error(response.getMsg());
        }

        // 合伙人变更
        if (Objects.equals(leads.getChannelSource(), 2)) {
            distributorCustomerServiceClientForLeads.bindClient(request.getBid(), request.getLeadsId(), response.getData());
        }

        applicationEventPublisher.publishEvent(new LeadsBindClientEvent(this, request.getBid(), request.getLeadsId(), request.getClientId()));
        return response;
    }

    @Override
    public BaseResponse<Long> transformClient(TransformClientRequest request) {
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.ID, request.getLeadsId())
        );
        if (Objects.nonNull(leads.getClientId()) && !Objects.equals(leads.getClientId(), 0L)) {
            return BaseResponse.error("该线索已关联客户，请勿重复关联");
        }
        request.getClientInfo().setId(SnowflakeHelper.getId());

        Integer oldLeadsStatus = leads.getStatus();

        String leadsOldStatusText;
        String followInfo = null;
        if (oldLeadsStatus.equals(LeadsStatusEnum.DISTRIBUTED.getStatus())) {
            followInfo = "将线索状态由【" + LeadsStatusEnum.DISTRIBUTED.getName() + "】修改为【" + LeadsStatusEnum.CONTACTED.getName() + "】";
            leads.setStatus(LeadsStatusEnum.CONTACTED.getStatus());
            leadsOldStatusText = LeadsStatusEnum.CONTACTED.getName();

        } else {
            leadsOldStatusText = LeadsStatusEnum.INTENT.getName();
        }

        leads.setStatus(LeadsStatusEnum.INTENT.getStatus());
        leads.setClientId(request.getClientInfo().getId());
        leads.setRelationClientTime(LocalDateTime.now());
        LeadsBasicDto leadsBasicDto = LeadsConverter.toLeadsBasicDto(leads);

        // 报备线索
        // 客户子域
        BaseResponse<Long> response = clientApiForMp.transformClient(request, leadsOldStatusText, leadsBasicDto);
        if (BaseResponse.responseSuccessWithNonNullData(response)) {
            if (!StringUtils.isEmpty(followInfo)) {
                leadsManager.updateStatus(leads, request.getOperatorId(), followInfo, LeadsStatusEnum.CONTACTED.getStatus());
            } else {
                leadsManager.updateById(leads);
            }
            // 合伙人变更
            if (Objects.nonNull(leadsBasicDto) && LeadsChannelEnum.REPORT.getChannel().equals(leadsBasicDto.getChannel())) {
                distributorCustomerServiceClientForLeads.bindClient(request.getBid(), request.getLeadsId(), response.getData());
            }
            applicationEventPublisher.publishEvent(new LeadsBindClientEvent(this, request.getBid(), request.getLeadsId(), request.getClientInfo().getId()));
        }
        return response;
    }

    @Override
    public BaseResponse<List<SimpleLeadsDto>> findLeadsByClient(LeadsClientQuery query) {
        List<Leads> leadsArray = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, query.getBid())
                        .eq(Leads.CLIENT_ID, query.getClientId())
        );
        return BaseResponse.success(leadsArray.stream().map(LeadsConverter::toSimpleLeads).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<Boolean> existLeads(ExistLeadsRequest request) {

        Set<Integer> leadsDelStatus = new HashSet<>(2);
        leadsDelStatus.add(LeadsStatusEnum.RECOVERY.getStatus());
        leadsDelStatus.add(LeadsStatusEnum.DELETED.getStatus());

        int count = leadsManager.count(new QueryWrapper<Leads>()
                .eq(Leads.BID, request.getBid())
                .notIn(Leads.STATUS, leadsDelStatus)
                .ne(request.getNeClientId() != null, Leads.CLIENT_ID, request.getNeClientId())
                .eq(!StringUtils.isEmpty(request.getMobile()), Leads.MOBILE, request.getMobile())
                .eq(request.getLevelId() != null, Leads.LEVEL_ID, request.getLevelId())
                .last(" limit 1"));

        if (count > 0) {
            return BaseResponse.success(true);
        }

        return BaseResponse.success(false);
    }

    @Override
    public BaseResponse<List<LeadsEsDto>> batchGetLeads(BaseIdRequest request) {
        return leadsGetService.batchGetLeads(request);
    }

    @Override
    public BaseResponse<List<LeadsNameDto>> findLeadsByMobileOrWxWhenStaffIdEq(LeadsNoRelationClientQuery query) {
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, query.getBid())
                        .and(
                                wra -> wra.eq(!StringUtils.isEmpty(query.getMobile()), Leads.MOBILE, query.getMobile())
                                        .or().eq(!StringUtils.isEmpty(query.getWx()), Leads.WE_CHAT, query.getWx())
                        )
                        .eq(Leads.CLIENT_ID, 0)
                        .in(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.DISTRIBUTED.getStatus(),
                                LeadsStatusEnum.CONTACTED.getStatus(), LeadsStatusEnum.SUCCESS_CONTACT.getStatus(),
                                LeadsStatusEnum.INTENT.getStatus(), LeadsStatusEnum.UNSURE_INTENT.getStatus()))
                        .select(Leads.ID, Leads.MOBILE, Leads.PROVINCE_ID, Leads.PROVINCE_NAME,
                                Leads.CITY_ID, Leads.CITY_NAME,
                                Leads.AREA_ID, Leads.AREA_NAME,
                                Leads.ADDRESS
                        )
        );

        return BaseResponse.success(leadsList.stream().map(LeadsConverter::toLeadsNameDto).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<Boolean> leadsBatchRelationClient(LeadsBatchRelationClientRequest request) {
        if (CollectionUtils.isEmpty(request.getLeadsIds())) {
            return BaseResponse.success(true);
        }
        boolean update = leadsManager.update(
                Wrappers.<Leads>update()
                        .eq(Leads.BID, request.getBid())
                        .in(Leads.ID, request.getLeadsIds())
                        .set(Leads.CLIENT_ID, request.getClientId())
        );
        return BaseResponse.success(update);
    }

    private BoolQueryBuilder getAllHistoryQuery() {
        return QueryBuilders.boolQuery()
                .should(QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery("status").gt(0).lt(12))
                        .must(QueryBuilders.termQuery("clientId", 0))
                )
                .should(QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery("clientStatus").gt(0).lt(12))
                        .must(QueryBuilders.rangeQuery("clientId").gt(0))
                )
                .should(QueryBuilders.termQuery("status", -6))
                .should(QueryBuilders.termQuery("status", -10))
                .should(QueryBuilders.termQuery("status", -3));
    }

    private BoolQueryBuilder getContactHistoryQuery() {
        return QueryBuilders.boolQuery()
                .should(QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery("status").gt(1).lt(11))
                        .must(QueryBuilders.termQuery("clientId", 0))
                ).should(QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery("clientStatus").gt(1).lt(11))
                        .must(QueryBuilders.rangeQuery("clientId").gt(0))
                );
    }

    private BoolQueryBuilder getTradedHistoryQuery() {
        return QueryBuilders.boolQuery().should(
                QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("status", 11))
                        .must(QueryBuilders.termQuery("clientId", 0))
        ).should(
                QueryBuilders.termQuery("clientStatus", 11)
        );
    }

    @Override
    public BaseResponse<Boolean> leadsNotifyGuide(LeadsNotifyGuideRequest request) {
        leadsNotifyGuideCheck(request);

        // 异步通知
        AsyncUtils.runAsync(() -> {
            Integer bid = request.getBid();
            SearchLeadsRequest searchLeadsRequest = new SearchLeadsRequest();
            searchLeadsRequest.setBid(request.getBid());
            searchLeadsRequest.setSid(request.getSid());
            searchLeadsRequest.setStatusGroup(5);
            searchLeadsRequest.setPageNo(1);
            searchLeadsRequest.setPageSize(50000);
            searchLeadsRequest.setSortType(1);
            searchLeadsRequest.setStatusSet(Sets.newHashSet(LeadsStatusEnum.DISTRIBUTED.getStatus()));
            searchLeadsRequest.setAuthority(true);
            searchLeadsRequest.setStaffId(request.getStaffId());
            searchLeadsRequest.setClientId(0L);
            BaseResponse<List<LeadsEsDto>> listBaseResponse = leadsGetService.searchByEs(searchLeadsRequest);
            List<LeadsEsDto> data = listBaseResponse.getData();

            Set<Long> staffIds = data.stream().filter(item -> item != null && !item.getDistributeStaffId().equals(0L))
                    .map(LeadsEsDto::getDistributeStaffId).collect(Collectors.toSet());

            Map<Long, StaffDto> staffDtoMap = staffClientForLeads.getStaffByIds(bid, staffIds);
            Set<Long> customerIds = staffDtoMap.values().stream().map(StaffDto::getCustomerId).collect(Collectors.toSet());
            Map<Long, CustomerDto> customerDtoMap = customerGetServiceClientForLeads.getCustomerInfoByIds(bid, customerIds, Sets.newHashSet("id", "wxPubOpenId"));

            Map<Long, List<LeadsEsDto>> distributeStaffMap = data.stream().collect(Collectors.groupingBy(LeadsEsDto::getDistributeStaffId));
            LocalDateTime now = LocalDateTime.now();
            distributeStaffMap.forEach((staffId, leadsList) -> {
                AdminNotifyNotContactGuidesContext notifyContext = AdminNotifyNotContactGuidesContext.init(bid, leadsList);
                TemplateMessageContentBuilder<AdminNotifyNotContactGuidesContext> builder = templateMessageBuilderFactory.getBuilder(notifyContext);
                BaseResponse baseResponse = builder.sendMessage(notifyContext);
                logger.info("发送模板消息结果：{}", jsonService.toJson(baseResponse));
//                RemindersToFollowedContext messageContext = new RemindersToFollowedContext(request.getBid(), MessageTypeEnum.REMINDERS_TO_FOLLOWED);
//                messageContext.setLeadsEsDtoList(leadsList);
//                messageContext.setTargetSid(staffId);
//                messageContext.setDeliverAfter(2);
//                StaffDto staffDto = staffDtoMap.get(staffId);
//                if (staffDto == null || !staffDto.getStatus().equals(StaffStatusEnum.OPENED.getCode())) {
//                    logger.info("员工不存在,bid：{},staffId:{}", bid, staffId);
//                    return;
//                }
//                CustomerDto customerDto = customerDtoMap.get(staffDto.getCustomerId());
//                messageContext.setTargetQyUserId(staffDto.getQyUserId());
//                if (customerDto != null) {
//                    messageContext.setTargetWxPubOpenId(customerDto.getWxPubOpenId());
//                }
//                LeadsEsDto leadsEsDto = leadsList.get(leadsList.size() - 1);
//                String timeOut = LeadsCommonUtil.timeOut(DateTimeUtils.MillisToLocalDateTime(leadsEsDto.getDistributeTime()), now);
//                messageContext.setTimeOut(timeOut);
//                messageManagerService.send(messageContext);
            });
        });

        return BaseResponse.success(true);
    }

    private void leadsNotifyGuideCheck(LeadsNotifyGuideRequest request) {
        String leadsNotifyInc = LEADS_NOTIFY_INC + request.getBid();
        Long increment = Optional.ofNullable(redisTemplate.opsForValue().increment(leadsNotifyInc)).orElse(2L);
        if (increment.compareTo(2L) >= 1) {
            throw new InngkeServiceException("一天最多只能发送两次跟进提醒");
        }
        redisTemplate.expire(leadsNotifyInc, 24, TimeUnit.HOURS);
    }


}

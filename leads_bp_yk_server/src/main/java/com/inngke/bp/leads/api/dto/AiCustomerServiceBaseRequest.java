package com.inngke.bp.leads.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/3 9:57
 */
@Data
public class AiCustomerServiceBaseRequest implements Serializable {

    /**
     * 是	string	签名token
     */
    private String token;

    /**
     * 是	string	应用id
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 是	int	时间戳
     */
    @JsonProperty("timestamp")
    private Long time;

}

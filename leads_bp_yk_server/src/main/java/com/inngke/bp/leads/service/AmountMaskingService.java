package com.inngke.bp.leads.service;

import com.inngke.bp.leads.client.RbacUserClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.utils.LeadsSpringUtil;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDepItemDto;
import com.inngke.bp.leads.dto.response.LeadsStatisticsStaffItemDto;
import com.inngke.common.utils.BidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AmountMaskingService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/13 14:55
 */
@Service
@Slf4j
public class AmountMaskingService {

    private static final String AMOUNT_PERMISSION = "amount_masking:*";

    private static final String USER_PERMISSION_KEY = LeadsServiceConsts.APP_ID + "bid:{0}:sid:{1}:user_permission:";

    private static final String PERMISSION_MATCH_RESULT = LeadsServiceConsts.APP_ID + "permissionVal:{0}:matchPermissionVal:{1}:";


    public List<LeadsStatisticsDepItemDto> departmentDoMasking(Long staffId, List<LeadsStatisticsDepItemDto> toMaskingList) throws IOException {

        boolean hasPermission;

        // 获取用户权限
        String userPermission = getCurrentUserPermission(BidUtils.getBid(), staffId);
        // 权限是否命中
        hasPermission = matchPermission(userPermission);
        // 命中权限则脱敏
        if (hasPermission) {
            toMaskingList.forEach(item -> item.setSignBillAmount("***"));
        }
        return toMaskingList;
    }

    public List<LeadsStatisticsStaffItemDto> staffDoMasking(Long staffId, List<LeadsStatisticsStaffItemDto> toMaskingList) {
        boolean hasPermission;

        // 获取用户权限
        String userPermission = getCurrentUserPermission(BidUtils.getBid(), staffId);
        // 权限是否命中
        hasPermission = matchPermission(userPermission);
        // 命中权限则脱敏
        if (hasPermission) {
            toMaskingList.forEach(item -> item.setSignBillAmount("***"));
        }
        return toMaskingList;
    }

    private boolean ignoreMasking(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String staffId = request.getParameter("staffId");
        if (requestURI.equalsIgnoreCase("/mp/client/statistic/sales-overview") && staffId != null) {
            return true;
        }

        return false;
    }

    private Long getCreatorStaffId(Object currentValue, Class<?> clazz) {
        Field field = null;
        Long creatorStaffId = 0L;
        try {
            Field[] declaredFields = clazz.getDeclaredFields();
            List<String> fields = Arrays.stream(declaredFields).map(Field::getName).collect(Collectors.toList());
            if (fields.contains("creatorStaffId")) {
                field = clazz.getDeclaredField("creatorStaffId");
                field.setAccessible(true);
                creatorStaffId = (Long) (Optional.ofNullable(field.get(currentValue)).orElse(0L));
            }
            if (fields.contains("staffId")) {
                field = clazz.getDeclaredField("staffId");
                field.setAccessible(true);
                creatorStaffId = (Long) (Optional.ofNullable(field.get(currentValue)).orElse(0L));
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.info("获取creatorStaffId异常", e);
        }

        return creatorStaffId;
    }

    private String getCurrentUserPermission(Integer bid, Long sid) {
        StringRedisTemplate redisTemplate = LeadsSpringUtil.getBean(StringRedisTemplate.class);
        String userPermission = redisTemplate.opsForValue().get(MessageFormat.format(USER_PERMISSION_KEY, bid, sid));
        if (StringUtils.isBlank(userPermission)) {
            userPermission = getRbacUserClientForMp().getUserPermission(bid, sid);
        }
        redisTemplate.opsForValue().set(MessageFormat.format(USER_PERMISSION_KEY, bid, sid), userPermission, 10, TimeUnit.SECONDS);
        return userPermission;
    }

    public boolean matchPermission(String permissionVal) {
        StringRedisTemplate redisTemplate = LeadsSpringUtil.getBean(StringRedisTemplate.class);
        String result = redisTemplate.opsForValue().get(MessageFormat.format(PERMISSION_MATCH_RESULT, permissionVal, AMOUNT_PERMISSION));
        if (StringUtils.isBlank(result)) {
            boolean hasPermission = getRbacUserClientForMp().matchAllPermission(BidUtils.getBid(), permissionVal, AMOUNT_PERMISSION);
            result = String.valueOf(hasPermission);
        }
        redisTemplate.opsForValue().set(MessageFormat.format(PERMISSION_MATCH_RESULT, permissionVal, AMOUNT_PERMISSION), result, 10, TimeUnit.SECONDS);

        return Boolean.valueOf(result);
    }

    private RbacUserClientForLeads getRbacUserClientForMp() {
        return LeadsSpringUtil.getBean(RbacUserClientForLeads.class);
    }


}

package com.inngke.bp.leads.mq.process;

import com.inngke.bp.leads.mq.message.order.OrderChangeMessageDto;

/**
 * <AUTHOR>
 * @since 2021/8/31 3:41 PM
 */
public interface OrderProcess {
    /**
     * 需要处理的订单类型
     *
     * @return 订单类型
     */
    Integer getProcessOrderType();

    /**
     * 是否需要处理
     *
     * @param message 消息体
     * @return 是否需要继续处理
     */
    boolean match(OrderChangeMessageDto message);

    /**
     * 处理逻辑
     * @param message 消息体
     */
    void process(OrderChangeMessageDto message);
}

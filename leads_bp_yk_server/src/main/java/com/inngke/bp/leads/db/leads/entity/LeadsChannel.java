/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 父类Id
     */
    private Long parentId;

    /**
     * 渠道值
     */
    private Integer value;

    /**
     * 渠道名
     */
    private String name;

    /**
     * 状态（删除=-1 停用=0 启用=1）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 编辑类型（只读=-1 只排序=0 可编辑=1）
     */
    private Integer editType;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String PARENT_ID = "parent_id";

    public static final String VALUE = "value";

    public static final String NAME = "name";

    public static final String STATUS = "status";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String SORT = "sort";

    public static final String EDIT_TYPE = "edit_type";

}

package com.inngke.bp.leads.core.utils;

import com.inngke.common.core.InngkeAppConst;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/8 10:54
 */
public class LeadsCommonUtil {

    public static Set<Long> strToIds(Long leadsId, String recoveryFromIds) {
        Set<Long> result = new HashSet<>(4);
        result.add(leadsId);
        if (StringUtils.isEmpty(recoveryFromIds)) {
            return result;
        }
        String[] split = recoveryFromIds.split(InngkeAppConst.COMMA_STR);
        for (String s : split) {
            result.add(Long.valueOf(s));
        }
        return result;
    }

    /**
     * 去掉字符串左右的空格，保留中间空格
     */
    public static String trim(String str) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        return str.trim();
    }

    public static String timeOut(LocalDateTime time, LocalDateTime now) {

        long seconds = Duration.between(time, now).getSeconds();

        return timeOutMinute(seconds / 60);

    }

    public static String timeOutMinute(Long minute) {

        long hour = minute / 60;

        if (hour != 0) {
            return hour + "小时";
        }
        if (minute != 0) {
            return minute + "分钟";
        }
        return "1分钟";

    }


    /**
     * 判断时间是否在晚上十点到凌晨7点，此时为免打扰时间
     */
    public static boolean notNotify() {
        int hour = LocalDateTime.now().getHour();
        if (hour >= 22 || hour < 7) {
            return true;
        }
        return false;
    }


}

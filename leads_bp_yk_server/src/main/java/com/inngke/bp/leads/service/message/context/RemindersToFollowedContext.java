package com.inngke.bp.leads.service.message.context;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.service.message.MessageTypeEnum;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;

import java.util.List;

public class RemindersToFollowedContext extends MessageContext{

    private String timeOut;

    private List<Leads> leadsList;

    private List<LeadsEsDto> leadsEsDtoList;

    private TemplateMessageSendRequest templateMessageSendRequest;

    public RemindersToFollowedContext(int bid, MessageTypeEnum messageType) {
        super(bid, messageType);
    }

    public RemindersToFollowedContext(int bid, MessageTypeEnum messageType, String timeOut, List<Leads> leadsList,Long targetSid) {
        super(bid, messageType);
        this.timeOut = timeOut;
        this.leadsList = leadsList;
        setTargetSid(targetSid);
    }

    public TemplateMessageSendRequest getTemplateMessageSendRequest() {
        return templateMessageSendRequest;
    }

    public void setTemplateMessageSendRequest(TemplateMessageSendRequest templateMessageSendRequest) {
        this.templateMessageSendRequest = templateMessageSendRequest;
    }

    public List<LeadsEsDto> getLeadsEsDtoList() {
        return leadsEsDtoList;
    }

    public void setLeadsEsDtoList(List<LeadsEsDto> leadsEsDtoList) {
        this.leadsEsDtoList = leadsEsDtoList;
    }

    public String getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(String timeOut) {
        this.timeOut = timeOut;
    }

    public List<Leads> getLeadsList() {
        return leadsList;
    }

    public void setLeadsList(List<Leads> leadsList) {
        this.leadsList = leadsList;
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsChannel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-23
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsChannelManager extends IService<LeadsChannel> {

    void save(Integer bid, LeadsChannel leadsChannel, LeadsChannel newLeadsChannel);

    void moveSort(Integer bid, LeadsChannel prevLeadsChannel, LeadsChannel nextLeadsChannel);
}

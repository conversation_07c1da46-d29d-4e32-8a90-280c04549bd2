package com.inngke.bp.leads.dto;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsHistoryDistribute;

import java.io.Serializable;
import java.util.List;

/**
 * LeadsTransferResultDto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 17:20
 */
public class LeadsTransferResultDto implements Serializable {
    private List<Leads> transferLeadsList;

    private List<LeadsFollow> transferLeadsFollowList;

    private List<LeadsHistoryDistribute> transferLeadsHistoryDistributeList;

    public List<Leads> getTransferLeadsList() {
        return transferLeadsList;
    }

    public void setTransferLeadsList(List<Leads> transferLeadsList) {
        this.transferLeadsList = transferLeadsList;
    }

    public List<LeadsFollow> getTransferLeadsFollowList() {
        return transferLeadsFollowList;
    }

    public void setTransferLeadsFollowList(List<LeadsFollow> transferLeadsFollowList) {
        this.transferLeadsFollowList = transferLeadsFollowList;
    }

    public List<LeadsHistoryDistribute> getTransferLeadsHistoryDistributeList() {
        return transferLeadsHistoryDistributeList;
    }

    public void setTransferLeadsHistoryDistributeList(List<LeadsHistoryDistribute> transferLeadsHistoryDistributeList) {
        this.transferLeadsHistoryDistributeList = transferLeadsHistoryDistributeList;
    }
}

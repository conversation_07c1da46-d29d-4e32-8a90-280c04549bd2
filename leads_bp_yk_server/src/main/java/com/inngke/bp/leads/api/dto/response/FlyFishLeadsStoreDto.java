package com.inngke.bp.leads.api.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/24 16:08
 */
@JsonIgnoreProperties
@NoArgsConstructor
@Data
public class FlyFishLeadsStoreDto {

    /**
     * number 门店ID
     */
    @JsonProperty("store_id")
    private String storeId;

    /**
     * ; string 门店名称
     */
    @JsonProperty("store_name")
    private String storeName;

    /**
     * number 门店活动ID
     */
    @JsonProperty("store_pack_id")
    private String storePackId;

    /**
     * ; string 门店活动名称
     */
    @JsonProperty("store_pack_name")
    private String storePackName;

    /**
     * ; string 门店所在地
     */
    @JsonProperty("store_location")
    private String storeLocation;

    /**
     * ; string 门店详细地址
     */
    @JsonProperty("store_address")
    private String storeAddress;

    /**
     * ; string 门店备注
     */
    @JsonProperty("store_remark")
    private String storeRemark;

    /**
     * ; string 门店活动备注
     */
    @JsonProperty("store_pack_remark")
    private String storePackRemark;


}

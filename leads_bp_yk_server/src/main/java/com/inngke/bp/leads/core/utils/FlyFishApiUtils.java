package com.inngke.bp.leads.core.utils;

import com.inngke.bp.leads.api.dto.response.BaseFlyFishListResponse;
import com.inngke.bp.leads.api.dto.response.BaseFlyFishResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/21 17:07
 */
public class FlyFishApiUtils {

    static final Integer SUCCESS_CODE = 0;

    static public <T> T checkResponse(BaseFlyFishResponse<T> response){
        if (ObjectUtils.isEmpty(response)){
            throw new InngkeServiceException("调用飞鱼接口失败:返回结果为空");
        }

        if(!SUCCESS_CODE.equals(response.getCode())){
            throw new InngkeServiceException("调用飞鱼接口失败:"+response.getMessage());
        }

        return response.getData();
    }

    static public <T> List<T> checkResponseAsList(BaseFlyFishResponse<BaseFlyFishListResponse<T>> response){

        if(!SUCCESS_CODE.equals(response.getCode())){
            throw new InngkeServiceException("调用飞鱼接口失败:"+response.getMessage());
        }

        return response.getData().getList();
    }
}

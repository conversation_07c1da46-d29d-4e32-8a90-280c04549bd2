/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.db.leads.entity.LeadsEventLog;
import com.inngke.bp.leads.db.leads.dao.LeadsEventLogDao;
import com.inngke.bp.leads.db.leads.manager.LeadsEventLogManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线索事件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class LeadsEventLogManagerImpl extends ServiceImpl<LeadsEventLogDao, LeadsEventLog> implements LeadsEventLogManager {

}

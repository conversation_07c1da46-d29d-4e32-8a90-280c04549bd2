package com.inngke.bp.leads.service.strategy.permissions.list;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.common.dto.request.BaseIdsRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 未分配
 */
@Component
public class ListUnassignedPermissionsStrategy extends ListPermissionsStrategyAbstract {

    @Autowired
    private StaffClientForLeads staffClientForLeads;


    /**
     * 客资清洗客服: 只能看到自己创建的线索数据
     *
     * @param request      查询参数
     * @param queryBuilder Es查询条件
     */
    @Override
    protected void customerPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        //添加系统对接权限
        if (isHeadquarters(request)){
            addSystemPermissions(request, queryBuilder);
            return;
        }
        queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getSid()));
    }

    /**
     * 其他角色: 可以看到自己创建的和自己所管理部门的员工创建的
     *
     * @param request
     * @param queryBuilder
     */
    @Override
    protected void otherPermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        //添加系统对接权限
        if (isHeadquarters(request)){
            addSystemPermissions(request, queryBuilder);
            return;
        }
        defaultOtherPermissionsHandle(request, queryBuilder);
    }

    @Override
    protected void merchantManagePermissionsHandle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        defaultMerchantManagePermissionsHandle(request,queryBuilder);
    }

    private boolean isHeadquarters(SearchLeadsRequest request){
        Long sid = request.getSid();
        BaseIdsRequest getAgentRequest = new BaseIdsRequest();
        getAgentRequest.setIds(Lists.newArrayList(sid));
        getAgentRequest.setBid(request.getBid());

        StaffIdAndAgentIdDto staffIdAndAgentIdDto = staffClientForLeads
                .getStaffIdAndAgentIdDto(getAgentRequest).stream().findFirst().orElse(null);
        if (Objects.isNull(staffIdAndAgentIdDto)){
            return false;
        }

        return Objects.isNull(staffIdAndAgentIdDto.getAgentId()) || staffIdAndAgentIdDto.getAgentId().equals(0L);
    }

    private void  addSystemPermissions(SearchLeadsRequest request, BoolQueryBuilder queryBuilder){
        BoolQueryBuilder sonQueryBuilder = getDefaultPermissionsBuilder(request, queryBuilder);

        //系统对接的
        sonQueryBuilder.should(QueryBuilders.termQuery(LeadsEsDto.CREATE_DEPARTMENT_ID,0L))
                .should(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID,0L));

        queryBuilder.must(sonQueryBuilder);
    }
}

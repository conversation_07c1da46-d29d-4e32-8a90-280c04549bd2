/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 线索扩展表-信息类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsExtInformationDao extends BaseMapper<LeadsExtInformation> {

    @Select(" select lx.* from leads l " +
            " left join leads_ext_information lx on l.id = lx.id" +
            " where l.channel_type = ${channel}  and l.bid = ${bid} order by lx.update_time desc  limit 1")
    LeadsExtInformation getLastUpdate(@Param("bid") Integer bid, @Param("channel") Integer channel);

    @Select(" select lx.id from leads l" +
            " left join leads_ext_information lx on l.id = lx.id" +
            " where lx.external_id = ${externalId} and lx.bid = ${bid} order by lx.id desc limit 1")
    LeadsExtInformation getLeadsExtInformationSelectId(@Param("bid") Integer bid, @Param("externalId") String externalId);
}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.inngke.bp.client.dto.response.common.ClientLevelItemDto;
import com.inngke.bp.leads.client.BatchImportTaskClientForLeads;
import com.inngke.bp.leads.client.ChannelClientForLeads;
import com.inngke.bp.leads.client.ClientLevelClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.converter.LeadsBatchConverter;
import com.inngke.bp.leads.core.converter.LeadsDraftConverter;
import com.inngke.bp.leads.core.utils.LeadsCommonUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsBatch;
import com.inngke.bp.leads.db.leads.entity.LeadsDraft;
import com.inngke.bp.leads.db.leads.entity.LeadsInformationExtend;
import com.inngke.bp.leads.db.leads.manager.LeadsBatchManager;
import com.inngke.bp.leads.db.leads.manager.LeadsDraftManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.LeadsDraftExcelDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.LeadsBatchStatusEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import com.inngke.bp.leads.enums.LeadsTypeEnum;
import com.inngke.bp.leads.service.*;
import com.inngke.bp.leads.service.schedule.LeadsRegionCache;
import com.inngke.bp.organize.dto.response.channel.ChannelDto;
import com.inngke.bp.user.dto.request.customer.CustomerMobilesQuery;
import com.inngke.bp.user.dto.request.tags.CustomerTagsListRequest;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.dto.response.CustomerTagsListDto;
import com.inngke.bp.user.enums.CustomerTagsClassificationEnum;
import com.inngke.bp.user.service.CustomerService;
import com.inngke.bp.user.service.CustomerTagsService;
import com.inngke.common.attachment.service.InngkeUploaderService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.StaticResourceUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.DateUtils;
import com.inngke.common.utils.ExcelUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.request.ImportTaskUpdateRequest;
import com.inngke.ip.common.dto.response.RegionDto;
import com.inngke.ip.common.enums.BatchImportTaskStatusEnum;
import com.inngke.ip.common.service.MqService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/8 8:33 PM
 */
@DubboService(version = "1.0.0")
public class LeadsBatchServiceImpl implements LeadsBatchService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsBatchServiceImpl.class);
    private static final String MOBILE_REGEX = "[1][3456789]\\d{9}";
    private static final String SHEET_NAME = "导入失败记录";
    private static final Pattern MOBILE_PATTERN = Pattern.compile(MOBILE_REGEX);

    private static final String MOBILE_WECHAT_MAP_KEY = LeadsServiceConsts.APP_ID + ":lock:importLeadsBatch:mobileWechatMap:";

    private static final ThreadLocal<List<String>> MOBILE_WECHAT_MAP = ThreadLocal.withInitial(() -> Lists.newArrayList());

    private static final ThreadLocal<Boolean> OPEN_REPEAT_LEADS_CACHE = ThreadLocal.withInitial(() -> false);

    private static final List<String> LEVEL_LIST = Lists.newArrayList("A", "B", "C", "D");

    private final Integer OPEN_REPEAT_LEADS = 1;

    @Autowired
    private LeadsBatchManager leadsBatchManager;
    @Autowired
    private LeadsDraftManager leadsDraftManager;
    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private InngkeUploaderService inngkeUploaderService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @Autowired
    private LeadsRegionCache leadsRegionCache;
    @Autowired
    private LeadsConfService leadsConfService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerService customerService;
    @Autowired
    private LeadsChangeService leadsChangeService;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    ChannelClientForLeads channelClientForLeads;

    @Autowired
    private LeadsTagsService leadsTagsService;

    @Autowired
    private CustomerTagsService customerTagsService;

    @Autowired
    private LeadsBatchService leadsBatchService;

    @Autowired
    private BatchImportTaskClientForLeads batchImportTaskClientForLeads;

    @Autowired
    private LeadsRepeatService leadsRepeatService;

    @Autowired
    private LockService lockService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ClientLevelClientForLeads clientLevelClientForLeads;


    @Autowired
    private LeadsSearchService leadsSearchService;

    @Autowired
    private LeadsChannelService leadsChannelService;

    /**
     * 创建批量导入
     *
     * @param request 批量导入请求
     * @return 返回批次ID，可以通过此批次ID查询批次处理结果
     */
    @Override
    public BaseResponse<Long> create(LeadsBatchImportRequest request) {
        LeadsBatch leadsBatch = new LeadsBatch();
        leadsBatch.setBid(request.getBid());
        leadsBatch.setFileUrl(request.getFileUrl());
        leadsBatch.setCreateTime(LocalDateTime.now());
        leadsBatch.setStaffId(request.getOperatorId());
        leadsBatch.setProcessStatus(LeadsBatchStatusEnum.BEING.getStatus());

        leadsBatchManager.save(leadsBatch);
        AsyncUtils.runAsync(() -> {
            try {
                importDraft(request, leadsBatch);
            } catch (Exception e) {
                logger.error("批量导入预处理表失败", e);
            }
        });
        return BaseResponse.success(leadsBatch.getId());
    }

    /**
     * 查询线索导入批次信息
     *
     * @param request 批次请求
     * @return 线索批次信息
     */
    @Override
    public BaseResponse<LeadsBatchDto> get(LeadsBatchGetRequest request) {
        LeadsBatch leadsBatch = leadsBatchManager.getById(request.getLeadsBatchId());
        if (null == leadsBatch) {
            return BaseResponse.success(null);
        }
        LeadsBatchDto dto = LeadsBatchConverter.toLeadsBatchDto(leadsBatch);
        return BaseResponse.success(dto);
    }

    /**
     * 查询某个批次的线索预览
     *
     * @param request 批次请求
     * @return 某个批次的线索列表
     */
    @Override
    public BaseResponse<BasePaginationResponse<LeadsDraftDto>> getLeadsDraftList(LeadsBatchListRequest request) {
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = 20;
        }
        //构建分页参数和查询条件
//        Page<LeadsDraft> pageParam = new Page<>(pageNo, pageSize);
        Wrapper<LeadsDraft> wrapper = Wrappers.<LeadsDraft>query()
                .eq(LeadsDraft.BATCH_ID, request.getLeadsBatchId())
                .last("LIMIT " + (pageNo - 1) * pageSize + "," + pageSize);

//        IPage<LeadsDraft> pageResult = leadsDraftManager.page(pageParam, wrapper);
        List<LeadsDraft> result = leadsDraftManager.list(wrapper);
        int count = leadsDraftManager.count(wrapper);
//        List<LeadsDraft> list = pageResult.getRecords();
        //将分页结果写入response
        BasePaginationResponse<LeadsDraftDto> paginationDto = new BasePaginationResponse<>();
        paginationDto.setTotal(count);
        if (CollectionUtils.isEmpty(result)) {
            paginationDto.setList(Lists.newArrayList());
        }
        List<LeadsDraftDto> draftDtos = result.stream().map(LeadsDraftConverter::toLeadsDraftDto).collect(Collectors.toList());
        paginationDto.setList(draftDtos);

        return BaseResponse.success(paginationDto);
    }

    /**
     * 导入某个批次
     *
     * @param request 批次请求
     * @return 是否导入成功
     */
    @Override
    public BaseResponse<LeadsBatchImportDto> importLeadsBatch(LeadsBatchGetRequest request) {
        String errorFileUrl = "";
        Integer bid = request.getBid();
        Long operatorId = request.getOperatorId();
        LeadsBatch leadsBatch = null;
        List<LeadsDraft> draftList;
        if ( ObjectUtils.isEmpty(request.getUrl()) && ObjectUtils.isEmpty(request.getLeadsBatchId())) {
            return BaseResponse.error("导入文件地址不能为空");
        }
        String keyId = ObjectUtils.isEmpty(request.getUrl()) ? request.getLeadsBatchId().toString() : request.getUrl();
        String key = LeadsServiceConsts.APP_ID + ":lock:importLeadsBatch:"+ keyId;
        Lock lock = lockService.getLock(key, 60 * 10);
        if (lock == null) {
            //没拿到锁
            return BaseResponse.error("正在处理中，请稍等");
        }
        try {
            if (request.getLeadsBatchId() == null) {
                if (StringUtils.isEmpty(request.getUrl())) {
                    return BaseResponse.error("导入文件地址不能为空");
                }
                leadsBatch = new LeadsBatch();
                leadsBatch.setBid(bid);
                leadsBatch.setFileUrl(request.getUrl());
                leadsBatch.setCreateTime(LocalDateTime.now());
                leadsBatch.setStaffId(operatorId);
                leadsBatch.setProcessStatus(LeadsBatchStatusEnum.BEING.getStatus());

                leadsBatchManager.save(leadsBatch);

                LeadsBatchImportRequest leadsBatchImportRequest = new LeadsBatchImportRequest();
                leadsBatchImportRequest.setBid(bid);
                leadsBatchImportRequest.setSid(request.getStaffId());
                leadsBatchImportRequest.setOperatorId(operatorId);
                leadsBatchImportRequest.setFileUrl(request.getUrl());

                //处理Excel数据
                draftList = importDraft(leadsBatchImportRequest, leadsBatch);

                LeadsBatchGetRequest leadsBatchGetRequest = new LeadsBatchGetRequest();
                leadsBatchGetRequest.setBid(bid);
                leadsBatchGetRequest.setLeadsBatchId(leadsBatch.getId());
                BaseResponse<LeadsBatchDto> leadsBatchDtoBaseResponse = leadsBatchService.get(leadsBatchGetRequest);
                LeadsBatchDto data = leadsBatchDtoBaseResponse.getData();
                if (data == null) {
                    return BaseResponse.error("创建导入批次失败");
                }
                errorFileUrl = data.getErrorFileUrl();
                request.setLeadsBatchId(leadsBatch.getId());
            } else {
                //判断LeadsBatch的状态
                leadsBatch = leadsBatchManager.getById(request.getLeadsBatchId());
                if (null == leadsBatch) {
                    return BaseResponse.error("未找到相关批次信息");
                }
                //扫描draft草稿表，获取草稿记录
                draftList = leadsDraftManager.list(Wrappers.<LeadsDraft>query().eq(LeadsDraft.BATCH_ID, request.getLeadsBatchId()));
            }
            Long batchTask = createBatchTask(bid, operatorId, request.getUrl());

            LeadsBatchImportDto result = new LeadsBatchImportDto();
            result.setErrorFileUrl(errorFileUrl);

            if (CollectionUtils.isEmpty(draftList)) {
                leadsBatch.setProcessStatus(LeadsBatchStatusEnum.IMPORTED.getStatus());
                leadsBatchManager.updateById(leadsBatch);
                if (leadsBatch.getErrorCount() == 0 && leadsBatch.getSuccessCount() == 0) {
                    throw new InngkeServiceException(10091, "没有待导入的批次信息");
                }
                result.setSuccessCount(leadsBatch.getSuccessCount());
                result.setErrorCount(leadsBatch.getErrorCount());
                result.setAutoAllot(false);
                setTaskComplete(bid, operatorId, batchTask, result.getErrorCount(), result.getSuccessCount(), result.getErrorFileUrl());
                return BaseResponse.success(result);
            }

            List<LeadsInformationExtend> leads = draftList.stream().map(LeadsDraftConverter::toLeadsInformationExt).collect(Collectors.toList());
            //请求用户服务，根据customerId去匹配用户name
            matchingCustomer(request, leads);

            boolean isAutoAllot = false;
            BaseBidOptRequest bidOptRequest = new BaseBidOptRequest();
            bidOptRequest.setBid(bid);
            BaseResponse<LeadsConfDto> leadsConf = leadsConfService.getLeadsConf(bidOptRequest);
            if (BaseResponse.responseSuccessWithNonNullData(leadsConf)) {
                LeadsConfDto confData = leadsConf.getData();
                if (confData.getDistributeType().equals(1)) {
                    isAutoAllot = true;
                }
            }
            result.setAutoAllot(isAutoAllot);
            result.setSuccessCount(leadsBatch.getSuccessCount());
            result.setErrorCount(leadsBatch.getErrorCount());
            //保存
            Set<Long> ids = leadsManager.importLeadsAndUpdateLeadsBatch(leads, leadsBatch, operatorId);

            //发送线索添加MQ
            AsyncUtils.runAsync(() -> leadsEsService.sendLeadsChangeMq(bid, Lists.newArrayList(ids), false, null, 1));
            String errorFileUrlUpdate = Integer.valueOf(0).equals(result.getErrorCount()) ? request.getUrl() : result.getErrorFileUrl();
            setTaskComplete(bid, operatorId, batchTask, result.getErrorCount(), result.getSuccessCount(), errorFileUrlUpdate);


            //判断是否开启自动分配
            if (isAutoAllot && !CollectionUtils.isEmpty(ids)) {
                LeadsDistributeRequest distributeRequest = new LeadsDistributeRequest();
                distributeRequest.setIds(ids);
                distributeRequest.setBid(bid);
                distributeRequest.setOperatorId(operatorId);
                BaseResponse<LeadsDistributeResultDto> distribute = leadsChangeService.distributeWithFollowStaff(distributeRequest);
                if (distribute.getCode() != 0) {
                    throw new InngkeServiceException("线索导入自动分配异常");
                }
                result.setSuccessCount(distribute.getData().getDistributedCount());
                result.setErrorCount(distribute.getData().getDistributeErrorCount());
                result.setAutoAllot(isAutoAllot);
            }

            return BaseResponse.success(result);
        } catch (InngkeServiceException ex) {
            if (ex.getCode() == 10091) {
                assert leadsBatch != null;
                leadsBatch.setProcessStatus(2);
                leadsBatch.setErrorCount(9999);
                leadsBatchManager.updateById(leadsBatch);
                return BaseResponse.error("读取模板文件失败，去了检查模板未见类型");
            }

            return BaseResponse.error(ex.getMessage());
        } finally {
            lock.unlock();
            if (OPEN_REPEAT_LEADS_CACHE.get()) {
                MOBILE_WECHAT_MAP.get().forEach(value -> {
                    redisTemplate.opsForHash().delete(MOBILE_WECHAT_MAP_KEY + bid, value);
                });
                MOBILE_WECHAT_MAP.remove();
            }
            OPEN_REPEAT_LEADS_CACHE.remove();
        }

    }

    private Long createBatchTask(int bid, long operatorId, String url) {
        return batchImportTaskClientForLeads.create(bid, url, "leads-batch", operatorId);
    }

    private void setTaskComplete(int bid, long operatorId, Long taskId, Integer errorCount, Integer successCount
            , String errorUrl) {
        ImportTaskUpdateRequest updateTaskRequest = new ImportTaskUpdateRequest();
        updateTaskRequest.setTaskId(taskId);
        if (successCount.equals(0)) {
            updateTaskRequest.setStatus(BatchImportTaskStatusEnum.FAIl.getCode());
        } else {
            updateTaskRequest.setStatus(BatchImportTaskStatusEnum.FINISH.getCode());
        }


        updateTaskRequest.setErrorCount(errorCount);
        updateTaskRequest.setSuccessCount(successCount);
        updateTaskRequest.setErrorUrl(errorUrl);
        updateTaskRequest.setOperatorId(operatorId);
        updateTaskRequest.setBid(bid);

        batchImportTaskClientForLeads.update(updateTaskRequest);
    }

    private void matchingCustomer(LeadsBatchGetRequest request, List<LeadsInformationExtend> leads) {
        //获取列表中记录的所有手机号，调用用户服务，根据手机号批量获取用户信息
        Set<String> mobileSet = leads.stream().map(Leads::getMobile).filter(mobile -> !StringUtils.isEmpty(mobile)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mobileSet)) {
            return;
        }
        //拼装用户服务需要的参数
        CustomerMobilesQuery userRequest = new CustomerMobilesQuery();
        userRequest.setBid(request.getBid());
        userRequest.setOperatorId(request.getOperatorId());
        userRequest.setMobiles(mobileSet);
        //调用用户服务，返回带customerId，mobile的用户列表
        BaseResponse<List<CustomerDto>> resp = customerService.getCustomerByMobile(userRequest);
        if (resp.getCode() != 0) {
            throw new InngkeServiceException("获取用户信息失败");
        }
        if (!CollectionUtils.isEmpty(resp.getData())) {
            List<CustomerDto> customerList = resp.getData();
            List<CustomerDto> filterCustomerList = customerList.stream()
                    .sorted(Comparator.comparing(CustomerDto::getId).reversed())
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CustomerDto::getMobile))), ArrayList::new));
            //通过匹配手机号，setCustomerId,CustomerUid
            Map<String, CustomerDto> collect = filterCustomerList.stream().collect(Collectors.toMap(CustomerDto::getMobile, t -> t));
            for (Leads lead : leads) {
                if (collect.containsKey(lead.getMobile())) {
                    CustomerDto customerDto = collect.get(lead.getMobile());
                    lead.setCustomerId(customerDto.getId());
                }
            }
        }
    }

    @Override
    public BaseResponse<List<LeadsBatchDto>> getBatchList(BaseBidOptRequest request) {
        List<LeadsBatch> list = leadsBatchManager.list(Wrappers.<LeadsBatch>query()
                .eq(LeadsBatch.BID, request.getBid())
                .between(LeadsBatch.CREATE_TIME, LocalDateTime.now().minusMonths(1), LocalDateTime.now())
                .orderByDesc(LeadsBatch.CREATE_TIME)
                .select(LeadsBatch.ID, LeadsBatch.BID, LeadsBatch.PROCESS_STATUS,
                        LeadsBatch.CREATE_TIME, LeadsBatch.UPDATE_TIME, LeadsBatch.SUCCESS_COUNT,
                        LeadsBatch.ERROR_COUNT, LeadsBatch.FILE_TYPE, LeadsBatch.FILE_URL, LeadsBatch.ERROR_FILE_URL));
        List<LeadsBatchDto> dto = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success(dto);
        }
        dto = list.stream().map(LeadsBatchConverter::toLeadsBatchDto).collect(Collectors.toList());
        return BaseResponse.success(dto);
    }

    @Override
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> batchGetLeads(BatchGetLeadsQuery query) {

        if (CollectionUtils.isEmpty(query.getLeadsIds())) {
            BasePaginationResponse<LeadsBillingIndicatorsDto> response = new BasePaginationResponse<>();
            response.setTotal(0);
            response.setList(new ArrayList<>());
            return BaseResponse.success(response);
        }

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery(LeadsEsDto.ID, query.getLeadsIds()))
                .must(QueryBuilders.termQuery("bid", query.getBid()));

        BasePaginationResponse<LeadsBillingIndicatorsDto> result = null;
        try {
            result = leadsSearchService.getLeadsByEs(query.getBid(), queryBuilder, 0, query.getLeadsIds().size());
        } catch (IOException e) {
            logger.info("查询异常");
        }

        return BaseResponse.success(result);
    }


    /**
     * 根据excel的url路径，预导入草稿表
     *
     * @param request 批量导入请求
     * @param leadsBatch 批次z
     */
    private List<LeadsDraft> importDraft(LeadsBatchImportRequest request, LeadsBatch leadsBatch) {
        Integer bid = request.getBid();
        //处理excel
        ExcelUtils<LeadsDraftExcelDto> list = new ExcelUtils<>(LeadsDraftExcelDto.class);
        List<LeadsDraftExcelDto> leadsDraftExcelDtos;
        try {
            leadsDraftExcelDtos = list.importExcelByUrl(request.getFileUrl(), 1);
        } catch (Exception e) {
            throw new InngkeServiceException("读取Excel失败{}", e);
        }
        leadsBatch.setSuccessCount(0);
        if (CollectionUtils.isEmpty(leadsDraftExcelDtos) || Objects.isNull(leadsDraftExcelDtos.get(0))) {
            return Lists.newArrayList();
        }

        List<ClientLevelItemDto> clientLevelList = clientLevelClientForLeads.getClientLevelList(bid);
        Map<String, Integer> levelName2Id = new HashMap<>(clientLevelList.size());
        clientLevelList.forEach(item -> levelName2Id.put(item.getTitle(), item.getId()));

        Map<String, Integer> leadsChannelNameToIdMap = leadsChannelNameToIdMap(bid);
        //校验数据是否符合规范，不合法对象填充errorMessage
        checkField(request.getBid(), leadsDraftExcelDtos, levelName2Id, leadsChannelNameToIdMap);
        //如果有错误，需要在这里生成导入异常文件，上传到云端，而不应该在查询的时候再生成
        List<LeadsDraftExcelDto> errorList = leadsDraftExcelDtos.stream().
                filter(item -> !StringUtils.isEmpty(item.getErrorMsg()))
                .collect(Collectors.toList());
        String errorFileUrl = null;
        if (!CollectionUtils.isEmpty(errorList)) {
            errorFileUrl = exportErrorLeadsExcel(request, errorList);
            leadsBatch.setErrorCount(errorList.size());
            leadsBatch.setErrorFileUrl(errorFileUrl);
        } else {
            leadsBatch.setErrorCount(0);
            leadsBatch.setErrorFileUrl(InngkeAppConst.EMPTY_STR);
        }
        //过滤没有errorMessage的合法对象，之后保存
        List<LeadsDraft> drafts = leadsDraftExcelDtos.stream().
                filter(item -> StringUtils.isEmpty(item.getErrorMsg()))
                .peek(item -> item.setBid(bid))
                .map(item -> LeadsDraftConverter.toLeadsDraft(item, leadsChannelNameToIdMap))
                .peek(leadsDraft -> leadsDraft.setCreateStaffId(request.getSid()))
                .peek(leadsDraft -> leadsDraft.setLevelId(levelName2Id.get(leadsDraft.getLevel())))
                .collect(Collectors.toList());
        //设置批次id
        for (LeadsDraft draft : drafts) {
            draft.setBid(request.getBid());
            draft.setBatchId(leadsBatch.getId());
            //没有渠道默认其他
            if (null == draft.getChannel()) {
                draft.setChannel(100);
            }
        }
        leadsBatch.setSuccessCount(drafts.size());
        leadsDraftManager.importLeadsDraft(leadsBatch.getId(), drafts, errorList.size(), errorFileUrl);
        return drafts;
    }

    private Map<String, Integer> leadsChannelNameToIdMap(int bid) {
        LeadsChannelListRequest leadsChannelListRequest = new LeadsChannelListRequest();
        leadsChannelListRequest.setBid(bid);
        BaseResponse<List<LeadsChannelDto>> list = leadsChannelService.getList(leadsChannelListRequest);
        List<LeadsChannelDto> data = list.getData();

        Map<String, Integer> result = new HashMap<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        leadsChannelNameToIdMapBuild(data, result, "");
        return result;
    }

    private void leadsChannelNameToIdMapBuild(List<LeadsChannelDto> data, Map<String, Integer> result, String parentName) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        for (LeadsChannelDto leadsChannelDto : data) {
            if (StringUtils.isEmpty(parentName)) {
                result.put(leadsChannelDto.getName(), leadsChannelDto.getValue());
            } else {
                result.put(parentName + InngkeAppConst.OBLIQUE_LINE_STR + leadsChannelDto.getName(), leadsChannelDto.getValue());
            }
            leadsChannelNameToIdMapBuild(leadsChannelDto.getChildren(), result, leadsChannelDto.getName());
        }
    }

    /**
     * 生成导入失败的线索记录
     */
    private String exportErrorLeadsExcel(LeadsBatchImportRequest request, List<LeadsDraftExcelDto> errorList) {
        ExcelUtils<LeadsDraftExcelDto> excelUtils = new ExcelUtils<>(LeadsDraftExcelDto.class);
        File file = excelUtils.writeExcelFile(errorList, SHEET_NAME, "线索导入失败列表.xlsx");
        String url = InngkeAppConst.EMPTY_STR;
        try {
            url = inngkeUploaderService.builder(request.getBid(), request.getOperatorId(), null)
                    .setModelName(request.getBid() + "/leads")
                    .addMeteData("bid", request.getBid().toString())
                    .build()
                    .uploadFile(file);
        } catch (Exception e) {
            logger.error("OSS文件上传失败", e);
        } finally {
            file.delete();
        }
        return StaticResourceUtils.getFullUrl(url);
    }

    private void checkField(Integer bid, List<LeadsDraftExcelDto> excelDtoList, Map<String, Integer> levelName2Id, Map<String, Integer> channelNameToIdMap) {
        if (CollectionUtils.isEmpty(excelDtoList)) {
            return;
        }

        LeadsDraftExcelDto emptyLeadsDraftExcelDto = new LeadsDraftExcelDto();

        // key - channelName value - channelId
        Map<String, Integer> channelNameIdMap = channelClientForLeads.getAllChannel(bid).stream().filter(channelDto -> bid.equals(channelDto.getBid())).collect(Collectors.toMap(ChannelDto::getName, ChannelDto::getId));
        Boolean openRepeatLeads = getOpenRepeatLeads(bid);
        for (LeadsDraftExcelDto excelDto : excelDtoList) {
            excelDto.setChannelName(LeadsCommonUtil.trim(excelDto.getChannelName()));
            // 校验空json对象
            if (jsonService.toJson(emptyLeadsDraftExcelDto).equals(jsonService.toJson(excelDto))) {
                continue;
            }
            StringBuilder errorMsg = new StringBuilder("");
            String mobileWechatStr = Optional.ofNullable(excelDto.getMobile()).orElse(InngkeAppConst.EMPTY_STR) +
                    InngkeAppConst.MIDDLE_LINE_STR + Optional.ofNullable(excelDto.getWeChat()).orElse(InngkeAppConst.EMPTY_STR);

            if (openRepeatLeads) {
                checkRepeatLeads(bid, excelDto, errorMsg, mobileWechatStr);
            }

            LeadsTypeEnum parse = LeadsTypeEnum.parse(excelDto.getType());
            if (parse.equals(LeadsTypeEnum.NONE)) {
                errorMsg.append("线索类型错误");
            }

            if (StringUtils.isEmpty(excelDto.getName())) {
                errorMsg.append("姓名不能为空;");
            } else {
                if (excelDto.getName().length() > 100) {
                    errorMsg.append("姓名最大不能超过100字符;");
                }
            }

            if (StringUtils.isBlank(excelDto.getMobile()) && StringUtils.isBlank(excelDto.getWeChat())) {
                errorMsg.append("手机号和微信号不能同时为空;");
            }

            if (!StringUtils.isBlank(excelDto.getMobile())) {
                Matcher m = MOBILE_PATTERN.matcher(StringUtils.isBlank(excelDto.getMobile()) ? excelDto.getMobile() : excelDto.getMobile().trim());
                boolean matches = m.matches();
                if (!matches) {
                    errorMsg.append("手机号格式不正确;");
                }
            }
            if (!StringUtils.isBlank(excelDto.getPayTime())) {
                if (!checkTime(excelDto.getPayTime())) {
                    errorMsg.append("付款时间格式不正确");
                }
            }

            if (!StringUtils.isBlank(excelDto.getSubmitTime())) {
                if (!checkTime(excelDto.getSubmitTime())) {
                    errorMsg.append("留资时间格式不正确");
                }
            }

            if (!StringUtils.isBlank(excelDto.getRegistryTime())) {
                boolean isDateVail = DateUtils.isDateVail(excelDto.getRegistryTime().trim());
                try {
                    if (isDateVail) {
                        DateTimeUtils.toLocalDateTime(excelDto.getRegistryTime().trim());
                    } else {
                        LocalDateTime.parse(excelDto.getRegistryTime().trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    }
                } catch (Exception e) {
                    errorMsg.append("广告创建时间格式化错误;");
                }
            }

            if (!StringUtils.isBlank(excelDto.getProvinceName()) && excelDto.getProvinceName().length() > 60) {
                errorMsg.append("省/市/区最大不能超过60字符;");
            }
            if (!StringUtils.isBlank(excelDto.getCityName()) && excelDto.getCityName().length() > 60) {
                errorMsg.append("省/市/区最大不能超过60字符;");
            }
            if (!StringUtils.isBlank(excelDto.getAreaName()) && excelDto.getAreaName().length() > 60) {
                errorMsg.append("省/市/区最大不能超过60字符;");
            }
            if (!StringUtils.isBlank(excelDto.getAddress()) && excelDto.getAddress().length() > 300) {
                errorMsg.append("地址信息最大不能大于300字符;");
            }
            if (!StringUtils.isBlank(excelDto.getOrderAccount()) && excelDto.getOrderAccount().length() > 200) {
                errorMsg.append("下单账号最大不能大于200字符;");
            }
            if (!StringUtils.isBlank(excelDto.getOrderSn()) && excelDto.getOrderSn().length() > 100) {
                errorMsg.append("订单编号最大不能超过100字符;");
            }
            if (!StringUtils.isBlank(excelDto.getGoodsName()) && excelDto.getGoodsName().length() > 1000) {
                errorMsg.append("商品名称最大不能超过1000字符;");
            }
            if (null != excelDto.getGoodsNum() && String.valueOf(excelDto.getGoodsNum()).length() > 11) {
                errorMsg.append("商品数量过大;");
            }
            if (!StringUtils.isBlank(excelDto.getOrderMessage()) && excelDto.getOrderMessage().length() > 1000) {
                errorMsg.append("订单留言最大不能超过1000字符;");
            }
            if (!StringUtils.isBlank(excelDto.getRemark()) && excelDto.getRemark().length() > 1000) {
                errorMsg.append("其他备注最大不能超过1000字符;");
            }
            if (!StringUtils.isBlank(excelDto.getTpLeadsId()) && excelDto.getTpLeadsId().length() > 100) {
                errorMsg.append("线索id最大不能超过100字符;");
            }
            if (!StringUtils.isBlank(excelDto.getPromotionName()) && excelDto.getPromotionName().length() > 100) {
                errorMsg.append("广告活动名称最大不能超过100字符;");
            }

            //校验省市区
            checkArea(excelDto, errorMsg);

            if (StringUtils.isEmpty(excelDto.getChannelName())) {
                errorMsg.append("渠道来源不能为空;");
            } else if (channelNameToIdMap.get(excelDto.getChannelName()) == null) {
                errorMsg.append("渠道来源不存在;");
            }

            if (!StringUtils.isEmpty(excelDto.getChannelSource()) && LeadsInputSourceEnum.parse(excelDto.getChannelSource()) == null) {
                errorMsg.append("线索来源不存在;");
            }

            if (!StringUtils.isBlank(excelDto.getShopChannelName()) && !channelNameIdMap.containsKey(excelDto.getShopChannelName())) {
                errorMsg.append("产品渠道不存在;");
            }
            if (StringUtils.isNotEmpty(excelDto.getDemandProduct()) && excelDto.getDemandProduct().length() > 200) {
                errorMsg.append("需求产品字符数量不能超过200个");
            }
            if (!StringUtils.isBlank(excelDto.getTags())) {
                String notFoundTags = checkTagsGetNotFoundTags(bid, excelDto.getTags(), CustomerTagsClassificationEnum.CLIENT_TAG);
                if (!StringUtils.isBlank(notFoundTags)) {
                    errorMsg.append("客户标签“").append(notFoundTags).append("”不存在");
                }
            }
            if (!StringUtils.isBlank(excelDto.getEnterpriseTags())) {
                String notFoundTags = checkTagsGetNotFoundTags(bid, excelDto.getEnterpriseTags(), CustomerTagsClassificationEnum.ENTERPRISE_TAG);
                if (!StringUtils.isBlank(notFoundTags)) {
                    errorMsg.append("企业标签“").append(notFoundTags).append("”不存在");
                }
            }
            if (!StringUtils.isBlank(excelDto.getLevel())) {
                if (!levelName2Id.containsKey(excelDto.getLevel())) {
                    errorMsg.append("客户等级匹配失败");
                }
            }
            if (!ObjectUtils.isEmpty(excelDto.getPayAmount()) && excelDto.getPayAmount().scale() > 2) {
                errorMsg.append("付款金额格式不正确");
            }

            if (channelNameIdMap.containsKey(excelDto.getShopChannelName())) {
                excelDto.setShopChannelId(Long.valueOf(channelNameIdMap.get(excelDto.getShopChannelName())));
            }
            if (!StringUtils.isEmpty(excelDto.getTpId()) && excelDto.getTpId().length() > 100){
                errorMsg.append("平台ID不能超过100个字符");
            }
            excelDto.setErrorMsg(errorMsg.toString());
        }
    }

    private void checkRepeatLeads(Integer bid, LeadsDraftExcelDto excelDto, StringBuilder errorMsg, String mobileWechatStr) {
        if (ObjectUtils.isEmpty(redisTemplate.opsForHash().get(MOBILE_WECHAT_MAP_KEY + bid, mobileWechatStr))) {
            List<Leads> leadsList = leadsRepeatService.checkRepeat(bid, excelDto.getMobile(), excelDto.getWeChat(), null);
            if (!CollectionUtils.isEmpty(leadsList)) {
                errorMsg.append("重复线索");
            }else {
                redisTemplate.opsForHash().put(MOBILE_WECHAT_MAP_KEY + bid, mobileWechatStr,0);
                redisTemplate.expire(MOBILE_WECHAT_MAP_KEY + bid, 5, TimeUnit.MINUTES);
                MOBILE_WECHAT_MAP.get().add(mobileWechatStr);
            }
        }else {
            errorMsg.append("重复线索");
        }
    }

    private Boolean getOpenRepeatLeads(Integer bid) {
        BaseBidOptRequest baseBidOptRequest = new BaseBidOptRequest();
        baseBidOptRequest.setBid(bid);

        BaseResponse<LeadsConfDto> response = leadsConfService.getLeadsConf(baseBidOptRequest);
        LeadsConfDto leadsConfDto = response.getData();
        if (Objects.nonNull(leadsConfDto) && OPEN_REPEAT_LEADS.equals(leadsConfDto.getOpenRepeatRemove())) {
            OPEN_REPEAT_LEADS_CACHE.set(true);
            return true;
        }
        return false;
    }

    private void checkArea(LeadsDraftExcelDto excelDto, StringBuilder errorMsg) {
        //全部为空
        if (StringUtils.isBlank(excelDto.getProvinceName()) && StringUtils.isBlank(excelDto.getCityName()) && StringUtils.isBlank(excelDto.getAreaName())) {
            //订单类必填
            if (LeadsTypeEnum.ORDER.getCode().equals(LeadsTypeEnum.parse(excelDto.getType()).getCode())) {
                errorMsg.append("省市区不能为空;");
                return;
            }
            return;
        }

        //省为空
        if (StringUtils.isBlank(excelDto.getProvinceName())) {
            errorMsg.append("省不能为空;");
            return;
        }

        //区不为空 但市为空
        if (StringUtils.isBlank(excelDto.getCityName()) && !StringUtils.isBlank(excelDto.getAreaName())) {
            errorMsg.append("市不能为空;");
            return;
        }

        RegionDto province = leadsRegionCache.getProvince(excelDto.getProvinceName());
        RegionDto city = leadsRegionCache.getCity(excelDto.getCityName());
        RegionDto area = leadsRegionCache.getAreaByParentNameAndAreaName(excelDto.getCityName() + excelDto.getAreaName());

        //省匹配错误
        if (ObjectUtils.isEmpty(province)) {
            errorMsg.append("省匹配错误;");
            return;
        }

        excelDto.setProvinceId(province.getId());
        excelDto.setProvinceName(province.getFullName());
        if (!ObjectUtils.isEmpty(city)) {
            //判断当前city的Pid是否是上面匹配
            excelDto.setCityId(city.getId());
            excelDto.setCityName(city.getFullName());
        } else {
            //市不为空且匹配失败
            if (!StringUtils.isBlank(excelDto.getCityName())) {
                errorMsg.append("市匹配错误;");
            }
        }
        if (!ObjectUtils.isEmpty(area)) {
            excelDto.setAreaId(area.getId());
            excelDto.setAreaName(area.getFullName());
        } else {
            //区不为空且匹配失败
            if (!StringUtils.isBlank(excelDto.getAreaName())) {
                errorMsg.append("区匹配错误;");
            }
        }
    }

    private boolean checkTime(String payTime) {
        boolean isDateVail = DateUtils.isDateVail(payTime.trim());
        try {
            if (isDateVail) {
                DateTimeUtils.toLocalDateTime(payTime.trim());
            } else {
                LocalDateTime.parse(payTime.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private String checkTagsGetNotFoundTags(Integer bid, String tags, CustomerTagsClassificationEnum tagClassificationEnum) {
        CustomerTagsListRequest request = new CustomerTagsListRequest();
        request.setClassification(tagClassificationEnum.getValue());
        request.setBid(bid);
        request.setCustomerId(0L);

        List<String> tagNameList = Optional.ofNullable(customerTagsService.tagsList(request)).map(BaseResponse::getData)
                .orElse(Lists.newArrayList()).stream()
                .filter(tag -> tag.getTagEnable() > 0).map(CustomerTagsListDto::getChildren).flatMap(Collection::stream)
                .map(CustomerTagsListDto::getTagsName).collect(Collectors.toList());

        List<String> notFoundTags = Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(tags)).stream()
                .filter(tagStr -> Objects.nonNull(tagStr) && !tagNameList.contains(tagStr)).collect(Collectors.toList());


        return Joiner.on(InngkeAppConst.COMMA_STR).join(notFoundTags);
    }
}

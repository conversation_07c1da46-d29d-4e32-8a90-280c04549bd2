package com.inngke.bp.leads.client;

import com.inngke.bp.organize.dto.request.GetAllChannelRequest;
import com.inngke.bp.organize.dto.response.channel.ChannelDto;
import com.inngke.bp.organize.service.ChannelService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/12 10:03
 **/
@Component
public class ChannelClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private ChannelService channelService;

    public List<ChannelDto> getAllChannel(Integer bid) {
        GetAllChannelRequest getAllChannelRequest = new GetAllChannelRequest();
        getAllChannelRequest.setBid(bid);
        BaseResponse<List<ChannelDto>> response = channelService.getAllChannelList(getAllChannelRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }
}

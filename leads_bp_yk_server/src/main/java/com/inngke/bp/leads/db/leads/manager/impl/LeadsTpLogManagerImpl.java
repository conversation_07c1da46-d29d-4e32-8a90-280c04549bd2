/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpLog;
import com.inngke.bp.leads.db.leads.dao.LeadsTpLogDao;
import com.inngke.bp.leads.db.leads.manager.LeadsTpLogManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsTpLogManagerImpl extends ServiceImpl<LeadsTpLogDao, LeadsTpLog> implements LeadsTpLogManager {

    @Override
    public void saveOrUpdateBatch(Integer bid,List<LeadsTpLog> leadsTpLogList) {
        if (CollectionUtils.isEmpty(leadsTpLogList)){
            return;
        }

        Set<String> tpIds = leadsTpLogList.stream().map(LeadsTpLog::getTpId)
                .filter(tpId -> !StringUtils.isEmpty(tpId))
                .collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(tpIds)){
            List<LeadsTpLog> tpLogIds = this.list(
                    Wrappers.<LeadsTpLog>query()
                            .in(LeadsTpLog.TP_ID, tpIds)
                            .eq(LeadsTpLog.BID, bid)
                            .select(LeadsTpLog.ID,LeadsTpLog.TP_ID)
            );
            if (!CollectionUtils.isEmpty(tpLogIds)){
                Map<String, Long> logIdsMap =
                        tpLogIds.stream().collect(Collectors.toMap(LeadsTpLog::getTpId, LeadsTpLog::getId));

                leadsTpLogList.forEach(leadsTpLog -> {
                    if (logIdsMap.containsKey(leadsTpLog.getTpId())){
                        leadsTpLog.setId(logIdsMap.get(leadsTpLog.getTpId()));
                    }
                });
            }
        }

        this.saveOrUpdateBatch(leadsTpLogList);
    }
}

package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.client.enums.ClientBusinessEnum;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.LeadsTransferResultDto;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.enums.LeadsTransferEnum;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.mq.process.client.ClientChangeProcess;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClientTransferListener
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 14:27
 */
@Component
@MqConsumer(value = ClientChangeListenerForLeads.TOPIC_CLIENT_CHANGE, consumerName = "leads_bp_yk_client_change", messageType = ClientTransferMessage.class)
@Slf4j
public class ClientChangeListenerForLeads implements InngkeMqListener<ClientTransferMessage> {
    public static final String TOPIC_CLIENT_CHANGE = "client_change";
    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private List<ClientChangeProcess> clientChangeProcess;

    /**
     * 消费消息
     *
     * @param clientTransferMessage    消息实例
     */
    @Override
    public void process(ClientTransferMessage clientTransferMessage) {
        log.info("message content: {}", jsonService.toJson(clientTransferMessage));
        clientChangeProcess.forEach(process -> {
            log.info("process: {} handle", process.getClass().getName());
            try {
                process.process(clientTransferMessage);
            }catch (Exception e){
                log.info("process: {} handle error", process.getClass().getName(), e);
            }
        });
    }

}

package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPushBackLogManager;
import com.inngke.bp.leads.dto.LeadsSnapshotDto;
import com.inngke.bp.leads.dto.RejectPushBackDto;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.request.RejectLeadsPushBackRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.notify.context.RejectLeadsPushBackNotifyContext;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsPushBackLogService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * LeadsPushBackLogServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/6 17:53
 */
@DubboService(version = "1.0.0", timeout = 3000)
@Slf4j
public class LeadsPushBackLogServiceImpl implements LeadsPushBackLogService {
    @Resource
    private LeadsPushBackLogManager leadsPushBackLogManager;

    @Resource
    private LeadsManager leadsManager;

    @Resource
    private StaffClientForLeads staffClientForLeads;

    @Resource
    private LeadsEsService leadsEsService;

    @Resource
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    @Override
    public BaseResponse<Long> rejectLeadsPushBack(RejectLeadsPushBackRequest request) {
        Long pushBackLogId = request.getPushBackLogId();
        LeadsPushBackLog pushBackLog = leadsPushBackLogManager.findLeadsPushBackLogById(request.getBid(), pushBackLogId);
        if (Objects.isNull(pushBackLog)) {
            log.info("未找到退回记录:{}", pushBackLogId);
            return BaseResponse.error("未找到退回记录");
        }
        if (pushBackLog.getIsReject() == 1) {
            log.info("申请已被驳回:{}", JsonUtil.toJsonString(pushBackLog));
            return BaseResponse.error("申请已被驳回");
        }
        Leads leads = leadsManager.getById(request.getBid(), pushBackLog.getLeadsId());
        if (Objects.isNull(leads)) {
            log.info("未找到线索信息:{}", pushBackLog.getLeadsId());
            return BaseResponse.error("未找到线索信息");
        }
        if (LeadsStatusEnum.PUSH_BACK.getStatus() != leads.getStatus()) {
            log.info("线索已不是退回状态:{}", leads.getStatus());
            return BaseResponse.error("线索已不是退回状态，无法驳回");
        }
        String leadsSnapshotStr = pushBackLog.getLeadsSnapshot();
        LeadsSnapshotDto leadsSnapshot = JsonUtil.jsonToObject(leadsSnapshotStr, LeadsSnapshotDto.class);
        if (Objects.isNull(leadsSnapshot) || Objects.isNull(leadsSnapshot.getDistributeStaffId())) {
            log.info("获取线索快照信息失败:{}", JsonUtil.toJsonString(pushBackLog));
            return BaseResponse.error("获取线索快照信息失败，无法退回");
        }

        StaffDto staff = staffClientForLeads.getStaffById(request.getBid(), leadsSnapshot.getDistributeStaffId());
        if (Objects.isNull(staff) || StaffStatusEnum.OPENED.getCode() != staff.getStatus()) {
            log.info("员工已被删除或禁用:{}", JsonUtil.toJsonString(staff));
            return BaseResponse.error("员工已被删除或禁用，无法退回");
        }
        RejectPushBackDto rejectPushBackDto = converterToRejectPushBackDto(request);
        if (Objects.nonNull(rejectPushBackDto)) {
            rejectPushBackDto.setRejectReason(request.getRejectReason());
        }
        Long leadsId = leadsPushBackLogManager.rejectPushBack(rejectPushBackDto);
        if (Objects.nonNull(leadsId)) {
            // 更新ES
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setId(leadsId);
            leadsUpdateRequest.setIds(Lists.newArrayList(leadsId));
            leadsEsService.updateDocs(leadsUpdateRequest);
            // 发送通知
            AsyncUtils.runAsync(() -> {
                RejectLeadsPushBackNotifyContext notifyContext = RejectLeadsPushBackNotifyContext.init(request.getBid(), leadsId, request.getRejectReason());
                TemplateMessageContentBuilder<RejectLeadsPushBackNotifyContext> builder = templateMessageBuilderFactory.getBuilder(notifyContext);
                BaseResponse baseResponse = builder.sendMessage(notifyContext);
                log.info("发送驳回线索退回申请模板消息结果：{}", JsonUtil.toJsonString(baseResponse));
            });

        }

        return BaseResponse.success(leadsId);
    }


    private RejectPushBackDto converterToRejectPushBackDto(RejectLeadsPushBackRequest rejectLeadsPushBackRequest) {
        if (Objects.isNull(rejectLeadsPushBackRequest)) {
            return null;
        }
        RejectPushBackDto rejectPushBackDto = new RejectPushBackDto();
        rejectPushBackDto.setPushBackLogId(rejectLeadsPushBackRequest.getPushBackLogId());
        rejectPushBackDto.setBid(rejectLeadsPushBackRequest.getBid());
        rejectPushBackDto.setRejectReason(rejectLeadsPushBackRequest.getRejectReason());
        rejectPushBackDto.setRejectBy(rejectLeadsPushBackRequest.getOperatorStaffId());
        rejectPushBackDto.setRejectTime(DateTimeUtils.getMilli(LocalDateTime.now()));
        return rejectPushBackDto;

    }

    private RejectPushBackDto converterToRejectPushBackDto(LeadsPushBackLog leadsPushBackLog) {

        if (Objects.isNull(leadsPushBackLog)) {
            return null;
        }
        RejectPushBackDto rejectPushBackDto = new RejectPushBackDto();
        rejectPushBackDto.setPushBackLogId(leadsPushBackLog.getId());
        rejectPushBackDto.setBid(leadsPushBackLog.getBid());
        rejectPushBackDto.setRejectReason(leadsPushBackLog.getRejectReason());
        rejectPushBackDto.setRejectBy(leadsPushBackLog.getRejectBy());
        rejectPushBackDto.setRejectTime(DateTimeUtils.getMilli(LocalDateTime.now()));
        return rejectPushBackDto;
    }
}

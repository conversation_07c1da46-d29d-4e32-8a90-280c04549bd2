package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.manager.LeadsTpAccountInfoManager;
import com.inngke.bp.leads.dto.request.tp.FeiYuLeadsPushDto;
import com.inngke.bp.leads.service.LeadsTpConserveService;
import com.inngke.bp.leads.service.tp.LeadsFlyFishCallbackService;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/7 15:36
 */
@Service
@DubboService(version = "1.0.0")
public class LeadsFlyFishCallbackServiceImpl implements LeadsFlyFishCallbackService {

    private final Logger logger = LoggerFactory.getLogger(LeadsFlyFishCallbackServiceImpl.class);

    @Autowired
    private LeadsTpConserveService leadsTpConserveService;

    @Autowired
    private LeadsTpAccountInfoManager leadsTpAccountInfoManager;

    @Override
    public BaseResponse<String> handle(FeiYuLeadsPushDto request) {
        List<LeadsTpAccountInfo> list = leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, request.getBid())
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .eq(LeadsTpAccountInfo.ACCOUNT_ID, request.getAdvId()));
        if (!CollectionUtils.isEmpty(list)){
            logger.info("广告主{},已授权主动拉取此回调忽略",request.getAdvId());
            return BaseResponse.success("success");
        }
        BaseResponse<Long> conserve = leadsTpConserveService.conserve(request.getBid(), request);
        if (BaseResponse.responseSuccess(conserve)) {
            return BaseResponse.success("success");
        }

        return BaseResponse.error("同步失败");
    }
}

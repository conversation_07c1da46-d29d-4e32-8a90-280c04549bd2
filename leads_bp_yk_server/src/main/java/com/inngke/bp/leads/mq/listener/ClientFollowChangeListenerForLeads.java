package com.inngke.bp.leads.mq.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.client.dto.response.client.ClientFollowDto;
import com.inngke.bp.client.request.ClientFollowOpenQuery;
import com.inngke.bp.client.service.ClientFollowOpenService;
import com.inngke.bp.client.service.ClientsFollowService;
import com.inngke.bp.leads.client.ClientGetClientFollowForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.mq.message.client.ClientTransferMessage;
import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.bp.leads.mq.process.client.ClientFollowChangeProcess;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@MqConsumer(value = "client_follow_change", consumerName = "leads_bp_yk@client_follow_change", messageType = EntityChangeMessageDto.class)
@Slf4j
public class ClientFollowChangeListenerForLeads implements InngkeMqListener<EntityChangeMessageDto> {

    private static final Logger logger = LoggerFactory.getLogger(ClientFollowChangeListenerForLeads.class);

    @Autowired
    private List<ClientFollowChangeProcess> clientFollowChangeProcesses;

    @Autowired
    private JsonService jsonService;

    @Override
    public void process(EntityChangeMessageDto message) {
        logger.info("message content: {}", jsonService.toJson(message));
        clientFollowChangeProcesses.forEach(process->{
            logger.info("process: {}", process.getClass().getName());
            try {
                process.process(message);
            }catch (Exception e){
                logger.info("process error: {}", e.getMessage(), e);
            }
        });
    }
}

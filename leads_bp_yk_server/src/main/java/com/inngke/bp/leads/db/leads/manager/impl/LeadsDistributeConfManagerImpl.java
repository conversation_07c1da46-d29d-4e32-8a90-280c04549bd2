/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConf;
import com.inngke.bp.leads.db.leads.dao.LeadsDistributeConfDao;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线索下发配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsDistributeConfManagerImpl extends ServiceImpl<LeadsDistributeConfDao, LeadsDistributeConf> implements LeadsDistributeConfManager {

}

package com.inngke.bp.leads.client;

import com.inngke.bp.client.request.SendLeadsGroupMsgRequest;
import com.inngke.bp.client.service.QyGroupNotifyService;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-11-22 16:21
 **/
@Service
public class QyGroupNotifyServiceForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private QyGroupNotifyService qyGroupNotifyService;

    public Boolean sendLeadsGroupMsg(int bid, Long leadsFollowId) {
        SendLeadsGroupMsgRequest request = new SendLeadsGroupMsgRequest();
        request.setBid(bid);
        request.setLeadsFollowId(leadsFollowId);
        BaseResponse<Boolean> response = qyGroupNotifyService.sendLeadsGroupMsg(request);

        return response.getData();

    }


}

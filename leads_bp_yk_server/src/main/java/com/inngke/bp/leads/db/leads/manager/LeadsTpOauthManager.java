/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 广告平台授权信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
public interface LeadsTpOauthManager extends IService<LeadsTpOauth> {

    boolean delSaveBatch(Integer bid,List<LeadsTpOauth> leadsTpOauthList);

    boolean delSaveBatchByAccountId(Integer bid, String accountId, List<LeadsTpOauth> leadsTpOauthList);

    boolean saveAccount(Integer bid, LeadsTpOauth leadsTpOauth, List<LeadsTpAccountInfo> leadsTpAccountInfoList);

    boolean saveToken(Integer bid, LeadsTpOauth leadsTpOauth);

}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.manager.LeadsTpAccountInfoManager;
import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.bp.leads.service.LeadsTpConserveService;
import com.inngke.bp.leads.service.tp.LeadsTencentCallbackService;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/8 10:22
 */
@Service
@DubboService(version = "1.0.0")
public class LeadsTencentCallbackServiceImpl implements LeadsTencentCallbackService {

    private final Logger logger = LoggerFactory.getLogger(LeadsFlyFishCallbackServiceImpl.class);

    @Autowired
    private LeadsTpConserveService leadsTpConserveService;

    @Autowired
    private LeadsTpAccountInfoManager leadsTpAccountInfoManager;

    @Override
    public BaseResponse<String> handle(TencentLeadsPushDto tencentLeadsPushDto) {
        List<LeadsTpAccountInfo> list = leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, tencentLeadsPushDto.getBid())
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .eq(LeadsTpAccountInfo.ACCOUNT_ID, tencentLeadsPushDto.getAccountId()));
        if (!CollectionUtils.isEmpty(list)){
            logger.info("广告主{},已授权主动拉取此回调忽略",tencentLeadsPushDto.getAccountId());
            return BaseResponse.success("success");
        }
        if (tencentLeadsPushDto.getLeadsId() == null){
            logger.info("参数错误");
            return BaseResponse.success("success");
        }

        BaseResponse<Long> conserveResponse = leadsTpConserveService.conserve(tencentLeadsPushDto.getBid(), tencentLeadsPushDto);

        if (BaseResponse.responseSuccess(conserveResponse)) {
            return BaseResponse.success("success");
        }

        return BaseResponse.error("fail");
    }
}

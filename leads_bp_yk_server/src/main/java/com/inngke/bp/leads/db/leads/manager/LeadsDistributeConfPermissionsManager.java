/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConfPermissions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 区域接收人配置的-权限配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
public interface LeadsDistributeConfPermissionsManager extends IService<LeadsDistributeConfPermissions> {

    boolean saveOrUpdateByRegionId(LeadsDistributeConfPermissions leadsDistributeConfPermissions);

    Map<Long, List<Integer>> getStaffPermissionsMap(Integer bid);

    void removeDistributeConfPermissions(Integer bid, Long staffId);
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsHistoryDistribute;
import com.inngke.bp.leads.db.leads.entity.LeadsTransferRollbackTodo;
import com.inngke.bp.leads.db.leads.dao.LeadsTransferRollbackTodoDao;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsHistoryDistributeManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsTransferRollbackTodoManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.ds.annotation.DS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 线索转交撤回代办 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsTransferRollbackTodoManagerImpl extends ServiceImpl<LeadsTransferRollbackTodoDao, LeadsTransferRollbackTodo> implements LeadsTransferRollbackTodoManager {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTransferRollbackTodoManagerImpl.class);

    @Resource
    private LeadsManager leadsManager;
    @Resource
    private LeadsFollowManager leadsFollowManager;
    @Resource
    private LeadsHistoryDistributeManager leadsHistoryDistributeManager;

    @Override
    public void add(Integer bid, Long id, Long forwardStaffId, Long targetStaffId) {
        LeadsTransferRollbackTodo leadsTransferRollbackTodo = new LeadsTransferRollbackTodo();
        leadsTransferRollbackTodo.setBid(bid);
        leadsTransferRollbackTodo.setLeadsId(id);
        leadsTransferRollbackTodo.setSourceStaffId(forwardStaffId);
        leadsTransferRollbackTodo.setCreateTime(LocalDateTime.now());
        leadsTransferRollbackTodo.setTargetStaffId(targetStaffId);

        save(leadsTransferRollbackTodo);
    }

    @Override
    public List<LeadsTransferRollbackTodo> getRollbackList(Integer bid) {
        return list(Wrappers.<LeadsTransferRollbackTodo>query().lt(LeadsTransferRollbackTodo.CREATE_TIME, LocalDateTime.now().minusHours(24)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollback(Leads leads, StaffDto staff, LeadsTransferRollbackTodo todo) {
        if (!StaffStatusEnum.OPENED.equals(StaffStatusEnum.parse(staff.getStatus()))){
            logger.info("线索转交撤回失败，员工已离职");
            removeById(todo.getId());
            return false;
        }
        if (!leads.getDistributeStaffId().equals(todo.getTargetStaffId())) {
            logger.info("线索转交撤回失败，线索已被重新转交");
            removeById(todo.getId());
            return false;
        }

        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(leads.getBid());
        leadsFollow.setLeadsStatus(leads.getStatus());
        leadsFollow.setLeadsId(leads.getId());
        leadsFollow.setFollowContent("线索超时未跟进，自动回收到【" + staff.getName() + "】");
        leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
        leadsFollow.setCreateTime(LocalDateTime.now());
        leadsFollow.setStaffId(todo.getTargetStaffId());
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());

        //转发时添加跟进记录
        if (!leadsFollowManager.save(leadsFollow)){
            logger.info("线索转交撤回失败，添加跟进记录失败");
            return false;
        }

        boolean update = leadsManager.update(new UpdateWrapper<Leads>()
                .eq(Leads.BID, leads.getBid())
                .eq(Leads.ID, leads.getId())
                .set(Leads.DISTRIBUTE_TIME, leads.getDistributeTime())
                .set(Leads.DISTRIBUTE_STAFF_ID, todo.getSourceStaffId())
                .set(Leads.LAST_FOLLOW_ID, leadsFollow.getId())
                .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
        );
        if (!update){
            logger.info("线索转交撤回失败，更新线索信息失败");
            return false;
        }
        leadsHistoryDistributeManager.remove(Wrappers.<LeadsHistoryDistribute>query()
                .eq(LeadsHistoryDistribute.BID,leads.getBid())
                .eq(LeadsHistoryDistribute.LEADS_ID,leads.getId())
                .eq(LeadsHistoryDistribute.DISTRIBUTE_STAFF_ID,todo.getSourceStaffId())
        );

        return removeById(todo.getId());
    }
}

package com.inngke.bp.leads.dto.request;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.mq.message.leads.LeadsFollowMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/18 8:23 PM
 */
public class ToFollowLeadsMessageDto extends LeadsFollowMessage {

    /**
     * 线索数量
     */
    private List<Leads> leadsList;

    /**
     * 超市类型
     */
    private Integer type;

    public List<Leads> getLeadsList() {
        return leadsList;
    }

    public void setLeadsList(List<Leads> leadsList) {
        this.leadsList = leadsList;
    }

    @Override
    public Integer getType() {
        return type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }
}

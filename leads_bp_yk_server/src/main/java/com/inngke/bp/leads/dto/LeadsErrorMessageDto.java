package com.inngke.bp.leads.dto;

import com.inngke.bp.leads.db.leads.entity.LeadsDraft;
import com.inngke.bp.leads.dto.response.LeadsDraftDto;

/**
 * 导入leads时的错误信息字段
 * <AUTHOR>
 */
public class LeadsErrorMessageDto{
    private LeadsDraftExcelDto errorData;

    private String errorMessage;

    public LeadsDraftExcelDto getErrorData() {
        return errorData;
    }

    public void setErrorData(LeadsDraftExcelDto errorData) {
        this.errorData = errorData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LeadsErrorMessageDto() {
    }

}

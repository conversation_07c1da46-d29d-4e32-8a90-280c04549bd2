package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.leads.mq.message.customer.CusromerChangeMessageDto;
import com.inngke.bp.leads.mq.process.CustomerProcess;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 *
 * <AUTHOR>
 * @date 2021/9/17 14:33
 **/
@Service
@MqConsumer(value = "customer_change", consumerName = "leads_bp_yk_customer_change", messageType = CusromerChangeMessageDto.class)
public class LeadsCustomerChangeListener implements InngkeMqListener<CusromerChangeMessageDto> {
    private static final Logger logger = LoggerFactory.getLogger(LeadsCustomerChangeListener.class);

    @Autowired
    private CustomerProcess customerProcesses;

    @Autowired
    protected JsonService jsonService;

    @PostConstruct
    public void init() {
        logger.info("客户状态变更");
    }

    @Override
    public void process(CusromerChangeMessageDto customerChangeMessage) {
        String msg = jsonService.toJson(customerChangeMessage);
        logger.info("客户变更事件监听开始，事件信息体:{}",msg);
        Integer event = customerChangeMessage.getEvent();
        if (event == null) {
            return;
        }
        if(customerProcesses == null){
            logger.warn("客户变更处理器不存在");
        }
        if (customerProcesses == null) {
            logger.warn("客户状态变更处理器失效");
            return;
        }

        if (customerProcesses.match(customerChangeMessage)) {
            customerProcesses.process(customerChangeMessage);
        }
    }
}
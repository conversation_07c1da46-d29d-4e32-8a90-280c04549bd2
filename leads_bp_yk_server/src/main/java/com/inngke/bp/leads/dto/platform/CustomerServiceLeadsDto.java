package com.inngke.bp.leads.dto.platform;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Ai客服线索详情
 *
 * <AUTHOR>
 * @date 2022/2/26 14:16
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerServiceLeadsDto implements Serializable {

    /**
     * 42797,    用户id
     */
    private String id;

    /**
     * "CbFjfSM68222", 应用id
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * "wx947e4309d8ef4ca6", 微信公众号id
     */
    @JsonProperty("wx_app_id")
    private String wxAppId;

    /**
     * "黄生",   客户名称
     */
    private String name;

    /**
     * "1866xxx9614",  手机号
     */
    private String phone;

    /**
     * "",  微信号
     */
    @JsonProperty("weixin")
    private String weiXin;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * "广东",  省份
     */
    private String province;

    /**
     * "oneD41e2J5Z4vbEYVwoX9le1zU98",   用户openid
     */
    private String openid;

    /**
     * "wx",    用户来源渠道 wx 微信公众号  wx_kf 微信客服  dy 抖音
     */
    private String source;

    /**
     * "",  用户备注
     */
    private String remark;

    /**
     * "2021-10-24 10:25:35",  关注时间
     */
    @JsonProperty("subscribe_at")
    private String subscribeAt;

    /**
     * "2021-10-24 10:25:36", 创建时间
     */
    @JsonProperty("created_at")
    private String createdAt;

    /**
     * "2021-12-15 15:48:59", 更新时间
     */
    @JsonProperty("updated_at")
    private String updatedAt;

    /**
     * "", 用户客资城市
     */
    private String city;

    private String district;

    /**
     * "优居装修案例",  用户关注的微信公众号名称
     */
    @JsonProperty("wx_account_name")
    private String wxAccountName;

    /**
     * "ADD_SCENE_WECHAT_ADVERTISEMENT", 用户关注类型
     */
    @JsonProperty("subscribe_scene")
    private String subscribeScene;

    /**
     * 用户标签
     */
    private List<CustomerServiceTagDto> tags;

    /**
     * 广告信息
     */
    @JsonProperty("ad_info")
    private CustomerServiceAdInfoDto adInfo;
}
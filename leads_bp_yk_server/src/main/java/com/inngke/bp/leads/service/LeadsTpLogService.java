package com.inngke.bp.leads.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inngke.bp.leads.db.leads.entity.LeadsTpLog;
import com.inngke.bp.leads.db.leads.manager.LeadsTpLogManager;
import com.inngke.bp.leads.service.tp.LeadsFlyFishCallbackService;
import com.inngke.bp.leads.service.tp.LeadsTencentCallbackService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/8 13:43
 */
@Aspect
@Component
public class LeadsTpLogService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpLogService.class);

    private static final Map<String,Integer> SERVICE_TYPE_MAP = new HashMap<String,Integer>(){{
        put(LeadsTencentCallbackService.class.toString(),1);
        put(LeadsFlyFishCallbackService.class.toString(),2);
    }};

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private LeadsTpLogManager leadsTpLogManager;

    @Pointcut("execution(public * com.inngke.bp.leads.service.tp.LeadsTencentCallbackService.handle(..))")
    public void tencentLog() {
    }

    @Pointcut("execution(public * com.inngke.bp.leads.service.tp.LeadsFlyFishCallbackService.handle(..)))")
    public void flyFishLog() {
    }

    @Pointcut("execution(public * com.inngke.bp.leads.service.LeadsAICustomerService.handle(..)))")
    public void aiLog() {
    }

    @Before(value = "tencentLog() || flyFishLog()")
    public void writeLog(JoinPoint joinpoint) {
        try {
            Object[] args = joinpoint.getArgs();
            if (args.length < 1) {
                return;
            }
            Object arg = args[0];

            ByteArrayOutputStream out = new ByteArrayOutputStream();

            objectMapper.writeValue(out, arg);

            saveLog(joinpoint,out.toString());
        }catch (Exception e){
            logger.error("记录线索回调信息失败", e);
        }
    }
    private void saveLog(JoinPoint joinPoint,String jsonString){

        LeadsTpLog leadsTpLog = new LeadsTpLog();

        String interfaceName = joinPoint.getThis().getClass().getSuperclass().getInterfaces()[0].toString();

        if (SERVICE_TYPE_MAP.containsKey(interfaceName)){
            leadsTpLog.setType(SERVICE_TYPE_MAP.get(interfaceName));
        }

        leadsTpLog.setContent(jsonString);

        leadsTpLogManager.save(leadsTpLog);
    }
}

package com.inngke.bp.leads.service.template.leadsAchievement;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 业绩数据报表统计策略
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 16:41
 */
@Component
@Slf4j
public abstract class AbstractLeadsAchievement {

    @Autowired
    private RestHighLevelClient esClient;

    @Autowired
    private JsonService jsonService;

    public abstract String getDimensionCode();

    protected abstract String getGroupByFiled(LeadsAchievementReportContext context);

    protected Script initScript(LeadsAchievementReportContext context) {

        Script script = new Script("doc['" + context.getGroupByFiled() + "']");
        context.setScript(script);
        return script;
    }

    protected SearchSourceBuilder initSearchSource(LeadsAchievementReportContext context) {
        SearchSourceBuilder queryBuilder = new SearchSourceBuilder();
        queryBuilder.size(0).trackTotalHits(true);

        context.setQueryDataSource(queryBuilder);
        return queryBuilder;
    }

    protected BoolQueryBuilder initQueryBuilder(LeadsAchievementReportContext context) {
        BoolQueryBuilder qb = QueryBuilders.boolQuery();
        qb.must(QueryBuilders.termQuery("bid", context.getBid()));
        qb.must(QueryBuilders.rangeQuery("createTime").gte(DateTimeUtils.dateTimeStrToMilli(context.getStartEventTime())));
        qb.must(QueryBuilders.rangeQuery("createTime").lte(DateTimeUtils.dateTimeStrToMilli(context.getEndEventTime())));
        if (StringUtils.isNotEmpty(context.getStartCreateTime()) && StringUtils.isNotEmpty(context.getEndCreateTime())) {
            qb.must(QueryBuilders.rangeQuery("leadsCreateTime").gte(DateTimeUtils.dateTimeStrToMilli(context.getStartCreateTime())));
            qb.must(QueryBuilders.rangeQuery("leadsCreateTime").lte(DateTimeUtils.dateTimeStrToMilli(context.getEndCreateTime())));
        }
        if (StringUtils.isNotEmpty(context.getStartDistributeTime()) && StringUtils.isNotEmpty(context.getEndDistributeTime())) {
            qb.must(QueryBuilders.rangeQuery("leadsDistributeTime").gte(DateTimeUtils.dateTimeStrToMilli(context.getStartDistributeTime())));
            qb.must(QueryBuilders.rangeQuery("leadsDistributeTime").lte(DateTimeUtils.dateTimeStrToMilli(context.getEndDistributeTime())));
        }

        context.setBoolQueryBuilder(qb);
        return qb;
    }

    protected List<TermsAggregationBuilder> initTermsAggregationBuilder(LeadsAchievementReportContext context) {
        int skip = (context.getPageNo() - 1) * context.getPageSize();

        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("distribute._count").order(SortOrder.DESC);
        TermsAggregationBuilder staffAggregation = AggregationBuilders.terms("group").field(context.getGroupByFiled()).size(1000000)
                .subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", Lists.newArrayList(sortBuilder)).from(skip).size(context.getPageSize()))
                .subAggregation(AggregationBuilders.terms("leadsIds").field("leadsId").size(1000000))
                .subAggregation(AggregationBuilders.terms("groupEvent").field("eventId").size(1000000)
                        .subAggregation(AggregationBuilders.cardinality("cardinalVal").field("leadsId")))
                .subAggregation(AggregationBuilders.filter("distribute", QueryBuilders.termQuery("eventId", 8))
                        .subAggregation(AggregationBuilders.cardinality("cardinalVal").field("leadsId")))
                .subAggregation(AggregationBuilders.sum("depositAmount").field("deposit"))
                .subAggregation(AggregationBuilders.sum("payAmount").field("payAmount"));
        context.setAggregationBuilder(staffAggregation);
        TermsAggregationBuilder leadsAggregation = AggregationBuilders.terms("groupLeads").field("leadsId").size(1000000)
                .subAggregation(AggregationBuilders.sum("depositAmount").field("deposit"))
                .subAggregation(AggregationBuilders.sum("payAmount").field("payAmount"));

        List<TermsAggregationBuilder> termsAggregationBuilders = Lists.newArrayList(staffAggregation, leadsAggregation);
        return termsAggregationBuilders;
    }


    protected void buildSearchRequest(LeadsAchievementReportContext context) {
        this.getGroupByFiled(context);
        SearchSourceBuilder queryBuilder = this.initSearchSource(context);
        BoolQueryBuilder qb = this.initQueryBuilder(context);
        List<TermsAggregationBuilder> termsAggregationBuilders = this.initTermsAggregationBuilder(context);

        // 拓展点
        this.afterQueryBuildPlugin(context);
        for (TermsAggregationBuilder termsAggregationBuilder : termsAggregationBuilders) {
            queryBuilder.aggregation(termsAggregationBuilder);
        }
        queryBuilder.query(qb);
        SearchRequest searchRequest = new SearchRequest()
                .indices("leads-event-log")
                .source(queryBuilder);
        SearchResponse response;
        try {
            log.info("查询业绩数据报表请求：{}", queryBuilder.toString());
            response = esClient.search(searchRequest, RequestOptions.DEFAULT);
            context.setResponse(response);
        } catch (IOException e) {
            log.error("查询业绩数据报表异常：", e);
        }
    }

    protected void buildPageCount(LeadsAchievementReportContext context) {
        SearchRequest countRequest = new SearchRequest();
        countRequest.indices("leads-event-log");
        SearchSourceBuilder countSourceBuilder = new SearchSourceBuilder();
        countSourceBuilder.query(context.getBoolQueryBuilder());
        countSourceBuilder.aggregation(AggregationBuilders.cardinality(context.getGroupByFiled()).field(context.getGroupByFiled()).precisionThreshold(10000));
        countRequest.source(countSourceBuilder);

        SearchResponse countResponse = null;
        try {
            log.info("查询业绩数据报表total请求:{}", countRequest.toString());
            countResponse = esClient.search(countRequest, RequestOptions.DEFAULT);
            ParsedCardinality parsedCardinality = countResponse.getAggregations().get(context.getGroupByFiled());
            context.setResultCount((int) parsedCardinality.getValue());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    protected void afterQueryBuildPlugin(LeadsAchievementReportContext context) {

    }

    protected void parseResponse(LeadsAchievementReportContext context) {
        List<LeadsAchievementReportResponse> result = Lists.newArrayList();
        SearchResponse response = context.getResponse();
        Aggregations aggregations = response.getAggregations();
        ParsedLongTerms parsedStringTerms = aggregations.get("group");
        List<? extends Terms.Bucket> buckets;
        buckets = parsedStringTerms.getBuckets();

        ParsedLongTerms leadsTerms = aggregations.get("groupLeads");
//        List<? extends Terms.Bucket> leadsBuckets;
//        leadsBuckets = leadsTerms.getBuckets();
//        for (Terms.Bucket bucket : leadsBuckets) {
//            Long leadsId = Long.valueOf(bucket.getKeyAsString());
//            ParsedSum depositParsed = bucket.getAggregations().get("depositAmount");
//            ParsedSum payAmount = bucket.getAggregations().get("payAmount");
//            Map<Long, BigDecimal> leadsAndDepositMap = context.getLeadsAndDepositMap();
//            Map<Long, BigDecimal> leadsAndPayAmountMap = context.getLeadsAndPayAmountMap();
//            BigDecimal depositVal = Optional.ofNullable(leadsAndDepositMap.get(leadsId)).orElse(BigDecimal.ZERO);
//            BigDecimal payAmountVal = Optional.ofNullable(leadsAndPayAmountMap.get(leadsId)).orElse(BigDecimal.ZERO);
//            leadsAndDepositMap.put(leadsId, depositVal.add(BigDecimal.valueOf(depositParsed.getValue())));
//            leadsAndPayAmountMap.put(leadsId, payAmountVal.add(BigDecimal.valueOf(payAmount.getValue())));
//        }

        for (Terms.Bucket bucket : buckets) {
            LeadsAchievementReportResponse dto = new LeadsAchievementReportResponse();

            ParsedFilter parsedFilter = bucket.getAggregations().get("distribute");
            ParsedCardinality parsedCardinality = parsedFilter.getAggregations().get("cardinalVal");
            dto.setTotal((int) parsedCardinality.getValue());

            ParsedLongTerms groupTerms = bucket.getAggregations().get("groupEvent");
            ParsedSum depositAmount = bucket.getAggregations().get("depositAmount");
            ParsedSum payAmount = bucket.getAggregations().get("payAmount");
            dto.setDepositAmount(BigDecimal.valueOf(Optional.ofNullable(depositAmount).map(ParsedSum::getValue).orElse(0.00)));
            dto.setTradingAmount(BigDecimal.valueOf(Optional.ofNullable(payAmount).map(ParsedSum::getValue).orElse(0.00)));


            List<? extends Terms.Bucket> eventBuckets = groupTerms.getBuckets();
            for (Terms.Bucket eventBucket : eventBuckets) {
                setFollowNum(eventBucket, dto);
            }
            ParsedLongTerms leadsIdsTerms = bucket.getAggregations().get("leadsIds");
            Set<Long> leadsIds = Sets.newHashSet();
            for (Terms.Bucket leadsIdsTermsBucket : leadsIdsTerms.getBuckets()) {
                Long leadsId = Long.valueOf(leadsIdsTermsBucket.getKeyAsString());
                leadsIds.add(leadsId);
//
//                Map<Long, BigDecimal> leadsAndDepositMap = context.getLeadsAndDepositMap();
//                Map<Long, BigDecimal> leadsAndPayAmountMap = context.getLeadsAndPayAmountMap();
//                BigDecimal depositVal = leadsAndDepositMap.get(leadsId);
//                BigDecimal payAmountVal = leadsAndPayAmountMap.get(leadsId);
//                dto.setDepositAmount(Optional.ofNullable(dto.getDepositAmount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(depositVal).orElse(BigDecimal.ZERO)));
//                dto.setTradingAmount(Optional.ofNullable(dto.getTradingAmount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(payAmountVal).orElse(BigDecimal.ZERO)));
            }
            dto.setLeadsIds(leadsIds);

            afterParseResponsePlugin(context, bucket, dto);
            result.add(dto);
        }
        context.setReportResult(result);
        afterBuildResponsePlugin(context);

    }

    protected void afterBuildResponsePlugin(LeadsAchievementReportContext context) {

    }

    protected void afterParseResponsePlugin(LeadsAchievementReportContext context, Terms.Bucket bucket, LeadsAchievementReportResponse dto) {

    }

    public List<LeadsAchievementReportResponse> getReportData(LeadsAchievementReportContext context) {
        this.buildSearchRequest(context);
        this.parseResponse(context);
        this.afterGetReportDataPlugin(context);
        this.buildPageCount(context);
        return context.getReportResult();
    }

    protected void afterGetReportDataPlugin(LeadsAchievementReportContext context) {

    }

    private void setFollowNum(Terms.Bucket eventBucket, LeadsAchievementReportResponse dto) {
        String eventKey = eventBucket.getKeyAsString();
        ParsedCardinality cardinality = eventBucket.getAggregations().get("cardinalVal");
        switch (eventKey) {
            case "12":
                dto.setMeasureNum((int) cardinality.getValue());
                break;
            case "13":
                dto.setIntoStoreNum((int) cardinality.getValue());
                break;
            case "15":
                dto.setDepositNum((int) cardinality.getValue());
                break;
            case "18":
                dto.setTradingNum((int) cardinality.getValue());
                break;
            case "1":
                dto.setLossNum((int) cardinality.getValue());
                break;
        }
    }

}

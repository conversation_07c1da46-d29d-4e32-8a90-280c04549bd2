package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.client.dto.response.client.ClientDto;
import com.inngke.bp.common.db.card.entity.ShopOrder;
import com.inngke.bp.common.db.card.entity.ShopOrderGoods;
import com.inngke.bp.common.db.card.enums.OrderStatusEnum;
import com.inngke.bp.common.db.card.manager.ShopOrderGoodsManager;
import com.inngke.bp.common.db.card.manager.ShopOrderManager;
import com.inngke.bp.distribute.dto.request.DistributorCustomerRequest;
import com.inngke.bp.distribute.dto.response.DistributorCustomerDto;
import com.inngke.bp.distribute.dto.response.GetDistributorDto;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsChannel;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.bp.leads.db.leads.manager.LeadsChannelManager;
import com.inngke.bp.leads.db.leads.manager.LeadsExtInformationManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPushBackLogManager;
import com.inngke.bp.leads.dto.LeadsExtDataDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.*;
import com.inngke.bp.leads.service.*;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.UserStaffDto;
import com.inngke.bp.user.dto.request.CustomerTagsTypeJudgeRequest;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.BidUtils;
import com.inngke.ip.common.dto.request.AddressRegionRequest;
import com.inngke.ip.common.dto.response.AddressRegionDto;
import com.inngke.ip.common.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/8 15:17
 */
@Service
@Slf4j
@DubboService(version = "1.0.0")
public class LeadsServiceV2Impl implements LeadsServiceV2 {
    private final Logger logger = LoggerFactory.getLogger(LeadsServiceV2Impl.class);

    private final static Integer VAILLANT_BID = 43;

    private final static String VAILLANT_MOBILE = "18900000000";

    private final static String VAILLANT_MOBILE2 = "13800000000";

    private final static String VAILLANT_MOBILE3 = "13900000000";

    private final static String VAILLANT_WECHAT = "微信同号";

    @Autowired
    private LeadsService leadsService;

    @Autowired
    private LeadsChangeService leadsChangeService;

    @Autowired
    private LeadsInformationService leadsInformationService;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;

    @Autowired
    private RegionService regionService;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private ShopOrderManager shopOrderManager;

    @Autowired
    private ShopOrderGoodsManager shopOrderGoodsManager;

    @Autowired
    private LeadsTagsService leadsTagsService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private DistributorServiceClientForLeads distributorServiceClientForLeads;

    @Autowired
    private DistributorCustomerServiceClientForLeads distributorCustomerServiceClientForLeads;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsFollowService leadsFollowService;

    @Autowired
    private LeadsRepeatService leadsRepeatService;

    @Autowired
    private LeadsGetService leadsGetService;

    @Autowired
    private LeadsChannelCacheFactory leadsChannelCacheFactory;

    @Resource
    private LeadsPushBackLogManager leadsPushBackLogManager;

    @Resource
    private LeadsChannelManager leadsChannelManager;

    private static final Integer IN_BOUND = 0;
    private static final Integer OUT_BOUND = 1;
    private static final Integer CONNECT = 1;
    private static final Integer NOT_CONNECT = 0;

    private static final Set<String> imgType = Sets.newHashSet("jpg", "jpeg", "png");


    @Autowired
    private ClientApiForMp clientApiForMp;

    @Override
    public BaseResponse<LeadsInformationDto> getLeads(LeadsGetRequest request) {
        BaseResponse<LeadsDto> leadsResponse = leadsService.getLeads(request);
        if (!BaseResponse.responseSuccessWithNonNullData(leadsResponse)) {
            return BaseResponse.success(null);
        }
        LeadsDto basicData = leadsResponse.getData();

        //获取扩展信息
        BaseResponse<LeadsInformationDto> informationResponse = leadsInformationService.getInfo(request);
        LeadsInformationDto leadsInformationDto;
        if (!BaseResponse.responseSuccessWithNonNullData(informationResponse)) {
            leadsInformationDto = new LeadsInformationDto();
        } else {
            leadsInformationDto = informationResponse.getData();
        }
        //合并扩展信息和基础信息
        fillBasicInfoToDto(leadsInformationDto, basicData);

        return BaseResponse.success(leadsInformationDto);
    }


    @Override
    public BaseResponse<LeadsInformationVo> getLeadsDetail(LeadsGetRequest request) {
        BaseResponse<LeadsVo> leadsDetailResponse = leadsService.getLeadsDetail(request);
        if (!BaseResponse.responseSuccessWithNonNullData(leadsDetailResponse)) {
            return BaseResponse.success(null);
        }
        LeadsVo leadsDetail = leadsDetailResponse.getData();
        if (Integer.valueOf(LeadsStatusEnum.DELETED.getStatus()).equals(leadsDetail.getStatus()) || Integer.valueOf(LeadsStatusEnum.RECOVERY.getStatus()).equals(leadsDetail.getStatus())) {
            return BaseResponse.error("线索不存在");
        }

        BaseResponse<LeadsInformationVo> leadsInformationDetailResponse = leadsInformationService
                .getLeadsDetail(request);

        LeadsInformationVo leadsInformationVo = new LeadsInformationVo();
        if (BaseResponse.responseSuccessWithNonNullData(leadsInformationDetailResponse)) {
            leadsInformationVo = leadsInformationDetailResponse.getData();
        }

        fillBasicInfoToVo(leadsInformationVo, leadsDetail);

        // 设置转客户后的客户信息
        setClientInfo(request.getBid(), leadsInformationVo);

        // 设置标签类型
        CustomerTagsTypeJudgeRequest customerTagsTypeJudgeRequest = new CustomerTagsTypeJudgeRequest();
        customerTagsTypeJudgeRequest.setBid(request.getBid());
        customerTagsTypeJudgeRequest.setTags(leadsInformationVo.getTags());
        leadsInformationVo.setTagsList(leadsTagsService.customerTagsDetailTagsDto(customerTagsTypeJudgeRequest));

        // 报备来源新增三个字段返回
        setLeadsPartnerInfo(request.getBid(), leadsDetail.getChannel(), leadsInformationVo);

        // 信息类、订单类历史数据处理
        Integer type = getLeadsDetailTypeHandle(leadsInformationVo.getType(), leadsDetail.getChannel());
        leadsInformationVo.setType(type);

        // 设置返回渠道和父级渠道
        leadsChannelList(request.getBid(), leadsInformationVo);

        leadsInformationVo.setLeadsRepeatItemDtoList(getRepeatLeads(request, leadsInformationVo));

        LeadsPushBackLog leadsPushBackLog = leadsPushBackLogManager.findLastOneLeadsPushBackLogByLeadsId(request.getBid(), leadsInformationVo.getId(), 0);
        leadsInformationVo.setPushBackId(Optional.ofNullable(leadsPushBackLog).map(LeadsPushBackLog::getId).orElse(null));

        return BaseResponse.success(leadsInformationVo);
    }

    private void leadsChannelList(int bid, LeadsInformationVo leadsInformationVo) {
        if (leadsInformationVo == null || leadsInformationVo.getChannel() == null) {
            return;
        }
        LeadsChannelCacheFactory.LeadsChannelValueCache cache = leadsChannelCacheFactory.getCache(bid);
        List<LeadsChannelValueDto> list = cache.getList();
        Map<Integer, LeadsChannelValueDto> valueDtoMap = new HashMap<>(list.size());
        Map<Long, LeadsChannelValueDto> parentIdDtoMap = new HashMap<>(list.size());
        list.forEach(item -> {
            valueDtoMap.put(item.getValue(), item);
            parentIdDtoMap.put(item.getId(), item);
        });
        LeadsChannelValueDto leadsChannelValueDto = valueDtoMap.get(leadsInformationVo.getChannel());
        if (leadsChannelValueDto == null) {
            return;
        }
        LeadsChannelValueDto parentDto = parentIdDtoMap.get(leadsChannelValueDto.getParentId());
        List<Integer> channelList = new ArrayList<>();
        if (parentDto != null) {
            channelList.add(parentDto.getValue());
        }
        channelList.add(leadsChannelValueDto.getValue());
        leadsInformationVo.setChannelList(channelList);
    }

    private void setClientInfo(int bid, LeadsInformationVo leadsInformationVo) {
        if (leadsInformationVo == null || leadsInformationVo.getClientId() == null || leadsInformationVo.getClientId().equals(0L)) {
            return;
        }
        ClientDto clientById = clientApiForMp.getClientById(bid, leadsInformationVo.getClientId());
        LeadsClientInfoDto result = new LeadsClientInfoDto();
        if (clientById == null) {
            return;
        }
        result.setId(clientById.getId());
        result.setMobile(clientById.getMobile());
        result.setWx(clientById.getWx());
        result.setName(clientById.getName());

        leadsInformationVo.setClientInfo(result);
    }

    private List<LeadsRepeatItemDto> getRepeatLeads(LeadsGetRequest request, LeadsInformationVo leadsInformationVo) {
        Integer bid = leadsInformationVo.getBid();
        Long id = leadsInformationVo.getId();
        String mobile = leadsInformationVo.getMobile();
        String weChat = leadsInformationVo.getWeChat();
        if (StringUtils.isEmpty(mobile) && StringUtils.isEmpty(weChat)) {
            return Lists.newArrayList();
        }
        if (VAILLANT_BID.equals(bid) && (VAILLANT_MOBILE.equals(mobile) || VAILLANT_MOBILE2.equals(mobile) || VAILLANT_MOBILE3.equals(mobile))) {
            //威能这个手机号不返回重复线索
            return Lists.newArrayList();
        }

        if (VAILLANT_BID.equals(bid) && VAILLANT_WECHAT.equals(weChat)) {
            //威能这个微信号不返回重复线索
            return Lists.newArrayList();
        }

        ListByMobileOrWeChatRequest listByMobileOrWeChatRequest = new ListByMobileOrWeChatRequest();
        if(!StringUtils.isEmpty(mobile)){
            listByMobileOrWeChatRequest.setMobile(mobile);
        }

        if (!StringUtils.isEmpty(weChat)) {
            listByMobileOrWeChatRequest.setWeChat(weChat);
        }
        listByMobileOrWeChatRequest.setBid(bid);
        listByMobileOrWeChatRequest.setId(id);
        List<Leads> leadsList = leadsManager.listByKeyword(listByMobileOrWeChatRequest);

        StaffDto staffDto = staffClientForLeads.getStaffByCid(request.getBid(), request.getOperatorId());
        Set<Long> managerStaffIds = Sets.newHashSet();
        if (Objects.nonNull(staffDto)) {
            DepartmentDto department = departmentClientForLeads.getDepartmentById(request.getBid(), staffDto.getDepartmentId());
            if (Objects.nonNull(department)) {
                Set<Long> deptIds = Sets.newHashSet();
                if (department.getAgentId() != null && department.getAgentId() > 0L) {
                    deptIds = departmentClientForLeads.getDepIdsByAgentId(request.getBid(), department.getAgentId());
                } else {
                    deptIds = staffClientForLeads.getStaffManageDepartmentIds(request.getBid(), staffDto.getId());
                    deptIds.add(staffDto.getDepartmentId());
                }
                managerStaffIds = staffClientForLeads.getStaffByDeptIds(request.getBid(), deptIds).stream().map(UserStaffDto::getId).collect(Collectors.toSet());
            }
        }
        final Set<Long> finalManagerStaffIds = managerStaffIds;
        return leadsList.stream().filter(item -> finalManagerStaffIds.contains(item.getDistributeStaffId()) || finalManagerStaffIds.contains(item.getPreFollowStaffId())).map(leads -> {
            LeadsRepeatItemDto leadsRepeatItemDto = new LeadsRepeatItemDto();
            leadsRepeatItemDto.setId(leads.getId());
            leadsRepeatItemDto.setName(leads.getName());
            if (!ObjectUtils.isEmpty(leads.getMobile())) {
                leadsRepeatItemDto.setMobile(leads.getMobile());
            } else {
                leadsRepeatItemDto.setMobile(leads.getWeChat());
            }
            return leadsRepeatItemDto;
        }).collect(Collectors.toList());
    }

    /**
     * 历史数据处理：
     * 1.渠道来源为：天猫、京东、抖音订单的为订单类
     * 2.渠道来源不为上述三种的为信息类
     */
    @Override
    public Integer getLeadsDetailTypeHandle(Integer type, Integer channel) {
        if (!LeadsTypeEnum.NONE.getCode().equals(type)) {
            return type;
        }

        List<Integer> parentOrderChannelValues = Lists.newArrayList(
                LeadsChannelEnum.TMALL.getChannel(),
                LeadsChannelEnum.JD.getChannel(),
                LeadsChannelEnum.TITOK_ORDER.getChannel()
        );
        List<LeadsChannel> list = leadsChannelManager.list(Wrappers.<LeadsChannel>query()
                .eq(LeadsChannel.BID, BidUtils.getBid())
                .eq(LeadsChannel.STATUS, 1)
        );

        List<LeadsChannel> parentOrderChannel = list.stream().filter(dbChannel -> parentOrderChannelValues.contains(dbChannel.getValue())).collect(Collectors.toList());

        List<Long> parentOrderChannelId = parentOrderChannel.stream().map(LeadsChannel::getId).collect(Collectors.toList());

        List<LeadsChannel> orderChannelList = list.stream().filter(dbChannel->parentOrderChannelId.contains(dbChannel.getParentId())).collect(Collectors.toList());
        orderChannelList.addAll(parentOrderChannel);

        List<Integer> orderChannelValue = orderChannelList.stream().map(LeadsChannel::getValue).collect(Collectors.toList());
        if (orderChannelValue.contains(channel)){
            return LeadsTypeEnum.ORDER.getCode();
        }

        return LeadsTypeEnum.INFORMATION.getCode();
    }

    private void setLeadsPartnerInfo(Integer bid, Integer channel, LeadsInformationVo leadsInformationVo) {
        if (leadsInformationVo == null || leadsInformationVo.getId() == null) {
            return;
        }
        if (!LeadsChannelEnum.REPORT.getChannel().equals(channel)) {
            return;
        }
        leadsInformationVo.setCreateStaffName("");

        DistributorCustomerRequest distributorCustomerRequest = new DistributorCustomerRequest();
        distributorCustomerRequest.setBid(bid);
        distributorCustomerRequest.setLeadsId(leadsInformationVo.getId());
        List<DistributorCustomerDto> list = distributorCustomerServiceClientForLeads.getList(distributorCustomerRequest);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        DistributorCustomerDto distributorCustomerDto = list.get(0);
        leadsInformationVo.setPartnerName(distributorCustomerDto.getDistributorName());
        leadsInformationVo.setPartnerRoleName(distributorCustomerDto.getDistributorRoleName());

        GetDistributorDto distributorById = distributorServiceClientForLeads.getDistributorDto(bid, distributorCustomerDto.getGuideId(), distributorCustomerDto.getDistributorId());
        if (distributorById == null) {
            return;
        }
        leadsInformationVo.setPartnerCompanyName(distributorById.getCompanyName());
    }

    @Override
    public BaseResponse<Boolean> updateLeads(LeadsInformationUpdateRequest request) {
        checkRequest(request.getBid(), request.getMobile(), request.getWeChat(), Sets.newHashSet(request.getId()));
        //获取基础字段
        BaseResponse<Boolean> response = leadsChangeService.updateLeads(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return response;
        }

        return leadsInformationService.update(request);
    }

    /**
     * 解析请求的省市区信息
     *
     * @param request 线索创建请求
     * @return 省市区信息
     */
    private void fillRegionInformation(LeadsInformationAddRequest request) {
        // 根据地址转换省市区id
        AddressRegionRequest addressRegionRequest = new AddressRegionRequest();
        String provinceName = StringUtils.isEmpty(request.getProvinceName()) ? "" : request.getProvinceName();
        String cityName = StringUtils.isEmpty(request.getCityName()) ? "" : request.getCityName();
        String areaName = StringUtils.isEmpty(request.getAreaName()) ? "" : request.getAreaName();
        String address = StringUtils.isEmpty(request.getAddress()) ? "" : request.getAddress();
        addressRegionRequest.setAddress(provinceName + cityName + areaName + address);
        BaseResponse<AddressRegionDto> response = regionService.getAddressRegion(addressRegionRequest);

        // 适配add方法
        if (BaseResponse.responseSuccessWithNonNullData(response)) {
            AddressRegionDto addressRegionDto = response.getData();
            request.setProvinceId(addressRegionDto.getProvinceId());
            request.setCityId(addressRegionDto.getCityId());
            request.setAreaId(addressRegionDto.getAreaId());
        }
    }

    /**
     * 添加线索 对外接口
     *
     * @param request 线索信息
     * @return 返回添加线索是否成功
     */
    public BaseResponse<Long> addFromOtherSystem(LeadsInformationAddRequest request) {
        // 添加索引前 从入参中加工地址信息
        fillRegionInformation(request);
        // 添加索引
        BaseResponse<LeadsInformationDto> response = add(request);

        if (BaseResponse.responseSuccessWithNonNullData(response)) {
            return BaseResponse.success(response.getData().getId());
        }

        return BaseResponse.error("LeadsServiceV2Impl add 响应体为空");
    }

    @Override
    public BaseResponse<List<MallOrderDto>> getMallOrderList(LeadsGetRequest request) {
        Integer bid = request.getBid();
        Leads leads = leadsManager.getById(bid, request.getId());
        if (Objects.isNull(leads)) {
            throw new InngkeServiceException("线索不存在");
        }
        List<ShopOrder> shopOrderList = shopOrderManager.getByUid(bid, leads.getCustomerUid());
        if (CollectionUtils.isEmpty(shopOrderList)) {
            return BaseResponse.success(Lists.newArrayList());
        }
        Map<Long, List<ShopOrderGoods>> goodsMap = shopOrderGoodsManager.getMapByOrderIds(bid, getOrderIds(shopOrderList));
        List<MallOrderDto> mallOrderDtoList = Lists.newArrayList();
        shopOrderList.stream().forEach(shopOrder -> {
            MallOrderDto mallOrderDto = buildMallOrderDto(shopOrder);
            if (Objects.isNull(goodsMap.get(shopOrder.getId()))) {
                return;
            }
            List<GoodDto> goodDtoList = getGoodDtoList(goodsMap, shopOrder.getId());
            mallOrderDto.setGoodDtoList(goodDtoList);
            mallOrderDto.setGoodsTotalMoney(getGoodsTotalMoney(goodDtoList));
            mallOrderDtoList.add(mallOrderDto);
        });

        return BaseResponse.success(mallOrderDtoList);
    }

    private List<GoodDto> getGoodDtoList(Map<Long, List<ShopOrderGoods>> goodsMap, Long orderId) {
        return goodsMap.get(orderId)
                .stream().map(shopOrderGood -> toGoodDto(shopOrderGood)).collect(Collectors.toList());
    }

    private List<Long> getOrderIds(List<ShopOrder> shopOrderList) {
        return shopOrderList
                .stream().map(ShopOrder::getId).collect(Collectors.toList());
    }

    private String getGoodsTotalMoney(List<GoodDto> goodDtoList) {
        return goodDtoList.stream()
                .filter(goodDto -> !StringUtils.isEmpty(goodDto.getPrice()))
                .map(goodDto -> new BigDecimal(goodDto.getPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
    }

    private GoodDto toGoodDto(ShopOrderGoods shopOrderGood) {
        GoodDto goodDto = new GoodDto();
        goodDto.setThumb(shopOrderGood.getThumb());
        goodDto.setTitle(shopOrderGood.getTitle());
        goodDto.setPrice(shopOrderGood.getPrice().toString());
        goodDto.setTotal(shopOrderGood.getTotal());
        goodDto.setOrderId(shopOrderGood.getOrderid());
        goodDto.setGoodSid(shopOrderGood.getGoodsid());
        goodDto.setStatus(shopOrderGood.getStatus());
        goodDto.setOptionId(shopOrderGood.getOptionid());
        goodDto.setOptionName(shopOrderGood.getOptionname());
        goodDto.setIsComment(shopOrderGood.getIscomment());
        goodDto.setIsPreSale(shopOrderGood.getIsPreSale());
        goodDto.setPreSalePrice(shopOrderGood.getPreSalePrice().toString());
        goodDto.setPreSaleTime(shopOrderGood.getPreSaleTime());
        goodDto.setPreDepositPrice(shopOrderGood.getPreDepositPrice().toString());
        goodDto.setPreBalancePrice(shopOrderGood.getPreBalancePrice().toString());
        return goodDto;
    }

    private MallOrderDto buildMallOrderDto(ShopOrder shopOrder) {
        MallOrderDto mallOrderDto = new MallOrderDto();
        mallOrderDto.setId(shopOrder.getId());
        mallOrderDto.setOrderSn(shopOrder.getOrdersn());
        mallOrderDto.setPrice(shopOrder.getPrice().toString());
        mallOrderDto.setNewPrice(shopOrder.getNewPrice().toString());
        mallOrderDto.setCredit(shopOrder.getCredit());
        mallOrderDto.setDispatch(shopOrder.getDispatch());
        mallOrderDto.setDispatchName(shopOrder.getDispatchName());
        mallOrderDto.setDispatchPrice(shopOrder.getDispatchprice().toString());
        mallOrderDto.setStatus(shopOrder.getStatus());
        mallOrderDto.setIsComment(shopOrder.getIscomment());
        mallOrderDto.setCreateTime(shopOrder.getCreatetime());
        mallOrderDto.setCoupon_id(shopOrder.getCouponId());
        mallOrderDto.setCouponMoney(shopOrder.getCouponMoney().toString());
        mallOrderDto.setOrderType(shopOrder.getOrderType());
//        mallOrderDto.setPayType(shopOrder.getPaytype());
        mallOrderDto.setPayTypeName(shopOrder.getPaytypename());
        mallOrderDto.setDiscount(shopOrder.getDiscount().toString());
        mallOrderDto.setIsPreOrder(shopOrder.getIsPreOrder().toString());
        mallOrderDto.setPreStatus(shopOrder.getPreStatus());
        mallOrderDto.setIsOfflinePayPre(shopOrder.getIsOfflinePayPre());
        mallOrderDto.setCardId(shopOrder.getCardId());
        mallOrderDto.setAddressAddress(shopOrder.getAddressAddress());
        mallOrderDto.setAddressArea(shopOrder.getAddressArea());
        mallOrderDto.setAddressCity(shopOrder.getAddressCity());
        mallOrderDto.setAddressProvince(shopOrder.getAddressProvince());
        mallOrderDto.setAddressRealName(shopOrder.getAddressRealname());
        mallOrderDto.setAddressMobile(shopOrder.getAddressMobile());
        mallOrderDto.setStatusName(OrderStatusEnum.parse(shopOrder.getStatus()).getName());
        return mallOrderDto;
    }

    @Override
    public BaseResponse<List<LeadsLostReasonDto>> getLeadsLostReasonList() {
        return BaseResponse.success(LeadsLostReasonEnum.getLeadsLostReasonList());
    }

    @Override
    public BaseResponse<LeadsInformationDto> add(LeadsInformationAddRequest request) {
        checkRequest(request.getBid(), request.getMobile(), request.getWeChat(), null);
        Integer channelType = request.getChannelType();
        // 当channelType值为空时 修改成手工录入
        request.setChannelType(Objects.nonNull(channelType) ? channelType : LeadsDataSourceEnum.MANUAL_IMPORT.getCode());

        // 保存索引数据 如果是自动分配就顺带分配
        BaseResponse<LeadsDto> addBasicResponse = leadsChangeService.add(request);
        if (!BaseResponse.responseSuccessWithNonNullData(addBasicResponse)) {
            return BaseResponse.error(addBasicResponse.getMsg());
        }

        // 持久化线索扩展信息 返回线索集合信息用于最后的返回
        LeadsDto leadsDto = addBasicResponse.getData();
        request.setId(leadsDto.getId());
        BaseResponse<LeadsInformationDto> addInfoResponse = leadsInformationService.add(request);
        LeadsInformationDto leadsInformationDto;
        if (!BaseResponse.responseSuccessWithNonNullData(addInfoResponse)) {
            leadsInformationDto = new LeadsInformationDto();
        } else {
            leadsInformationDto = addInfoResponse.getData();
        }

        // 实体类之间转换
        fillBasicInfoToDto(leadsInformationDto, addBasicResponse.getData());

        return BaseResponse.success(leadsInformationDto);
    }

    public void checkRequest(Integer bid, String mobile, String weChat, Set<Long> excludeIds) {
        if (StringUtils.isEmpty(weChat) && StringUtils.isEmpty(mobile)) {
            throw new InngkeServiceException("手机号和微信不能同时为空");
        }

        List<Leads> leadsList = leadsRepeatService.checkRepeat(bid, mobile, weChat, excludeIds);
        String msg = "";
        for (Leads leads : leadsList) {
            if (!ObjectUtils.isEmpty(mobile)
                    && Objects.equals(mobile, leads.getMobile())) {
                msg = "已存在相同手机号的线索，请重新输入";
                break;
            }

            if (!ObjectUtils.isEmpty(weChat)
                    && Objects.equals(weChat, leads.getWeChat())) {
                msg = "已存在相同微信号的线索，请重新输入";
                break;
            }

            if (!ObjectUtils.isEmpty(weChat)
                    && Objects.equals(weChat, leads.getMobile())) {
                msg = "已存在手机号与微信号重复的线索，请重新输入";
                break;
            }

            if (!ObjectUtils.isEmpty(mobile)
                    && Objects.equals(mobile, leads.getWeChat())) {
                msg = "已存在手机号与微信号重复的线索，请重新输入";
                break;
            }
        }

        if (!ObjectUtils.isEmpty(msg)) {
            throw new InngkeServiceException(msg);
        }
    }

    @Override
    public BaseResponse<Boolean> editByOrder(LeadsEditByOrderRequest request) {
        Long id = request.getId();
        Integer bid = request.getBid();

        LambdaQueryWrapper<Leads> queryWrapper = new LambdaQueryWrapper<>();
        Leads leads = leadsManager.getOne(queryWrapper.eq(Leads::getId, id));
        if (Objects.isNull(leads)) {
            return BaseResponse.error("数据不存在");
        }

        String extData = leads.getExtData();
        UpdateWrapper<Leads> updateWrapper = Wrappers.<Leads>update()
                .eq(Leads.BID, bid).eq(Leads.ID, id)
                .set(Leads.NAME, request.getName());

        Leads newLeads = new Leads();
        BeanUtils.copyProperties(request, newLeads);
        LeadsConverter.updateWrapperSetLeadsInfo(updateWrapper, newLeads);

        Boolean isTrue = checkRefundRemarkAndIsRefund(request, extData);
        updateWrapper.set(Leads.EXT_DATA, request.getExtData());
        boolean isSuccess = leadsManager.update(updateWrapper);
        return BaseResponse.success(isSuccess);
    }

    @Override
    public BaseResponse<Boolean> updateLeadsStatusRecord(UpdateLeadsStatusRecordRequest request) {
        Long leadsId = request.getLeadsId();
        Integer bid = request.getBid();
        Integer status = request.getStatus();
        Set<Long> leadsIds = request.getLeadsIds();
        LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(status);
        if (Objects.isNull(statusEnum)) {
            logger.warn("不存在该线索状态修改失败！，线索id={}", leadsId);
            return BaseResponse.success(false);
        }

        if (LeadsStatusEnum.DISTRIBUTED.getStatus() == statusEnum.getStatus()) {
            logger.warn("待跟进状态不记录!");
            BaseResponse.success(false);
        }

        if (LeadsStatusEnum.PRE_FOLLOW.getStatus() == statusEnum.getStatus()) {
            logger.warn("客服接待状态不记录!");
            BaseResponse.success(false);
        }

        //批量修改
        if (CollectionUtils.isEmpty(leadsIds)) {
            leadsIds = Sets.newHashSet(leadsId);
        }

        //获取线索信息
        List<Leads> leadses = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.ID, leadsIds)
                        .select(Leads.PRE_FOLLOW_STATUS, Leads.PRE_FOLLOW_STAFF_ID, Leads.STATUS, Leads.ID, Leads.FOLLOW_STATUSES, Leads.KF_FOLLOW_STATUSES, Leads.BID)
        );
        if (CollectionUtils.isEmpty(leadses)) {
            logger.warn("不存在该线索！，线索id={}", leadsIds);
            return BaseResponse.success(false);
        }
        //跟新线索状态记录列表
        boolean update = updateLeadsStatusRecord(bid, status, leadses);
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(request.getBid());
            leadsUpdateRequest.setIds(Lists.newArrayList(request.getLeadsId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return BaseResponse.success(update);
    }


    @Override
    public BaseResponse<Boolean> clearLeadsStatusRecord(ClearLeadsStatusRecordRequest request) {
        Set<Long> leadsIds = request.getLeadsIds();
        Integer bid = request.getBid();
        Integer status = request.getStatus();
        Boolean hasKf = request.getHasKf();
        if (CollectionUtils.isEmpty(leadsIds)) {
            logger.warn("线索id不能为空！");
            return BaseResponse.success(false);
        }

        UpdateWrapper<Leads> updateWrapper = Wrappers.<Leads>update()
                .eq(Leads.BID, bid)
                .in(Leads.ID, leadsIds);
        if (Boolean.FALSE.equals(hasKf) && LeadsStatusEnum.RECOVERY.getStatus() == status) {
            updateWrapper.set(Leads.KF_FOLLOW_STATUSES, InngkeAppConst.EMPTY_STR);
        }

        if (LeadsStatusEnum.RECOVERY.getStatus() == status || LeadsStatusEnum.PUSH_BACK.getStatus() == status) {
            updateWrapper.set(Leads.FOLLOW_STATUSES, InngkeAppConst.EMPTY_STR);
        }
        return BaseResponse.success(leadsManager.update(updateWrapper));
    }

    @Override
    public BaseResponse updateLeadsInfoByAliCloudCallCenterMessage(AliCloudCallCenterMessageRequest request) {
        Integer callType = request.getCallType();
        Long callDuration = request.getCallDuration();
        Integer bid = request.getBid();
        Long leadsId = request.getLeadsId();
        //Long followTime = request.getFollowTime();
        Long staffId = request.getStaffId();
        Integer connected = request.getConnected();
        String reason = request.getReason();
        String callDurationStr = null;
        if (!ObjectUtils.isEmpty(callDuration)){
            callDurationStr = timeTransition(callDuration);
        }
        String content = null;
        //根据leadsId查询leads信息
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, leadsId)
        );
        if (ObjectUtils.isEmpty(leads)) {
            log.error("使用阿里云云呼叫信息更leads时,获取leads信息失败,请求体:",jsonService.toJson(request));
            return BaseResponse.error("获取leads信息错误");
        }

        LeadsFollowCreateRequest leadsFollowCreateRequest = new LeadsFollowCreateRequest();
        leadsFollowCreateRequest.setStatus(leads.getPreFollowStatus());

        //呼出
        if (OUT_BOUND.equals(callType)) {
            Integer status = leads.getStatus();
            String customerName = leads.getName();
            //接通
            if (CONNECT.equals(connected)) {
                content = "客服打给客户【"+ customerName + "】，通话时长" + callDurationStr ;
                leads.setLastContactTime(LocalDateTime.now());
            }
            //未接通
            if (NOT_CONNECT.equals(connected)){
                content = "客服打给客户【"+ customerName + "】，未接通，原因：" + reason;
                leadsFollowCreateRequest.setReason(reason);
            }

            //判断当前线索是否为待联系
            if (status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus()) && leads.getPreFollowStatus().equals(1)){
                //更改成已联系
                leads.setPreFollowStatus(2);
                leadsFollowCreateRequest.setStatus(LeadsStatusEnum.CONTACTED.getStatus());
                content = content + "，系统自动将线索状态由【待联系】改为【已联系】";
            }
        }

        //呼入
        if (IN_BOUND.equals(callType)){
            if (ObjectUtils.isEmpty(leads)) {
                log.error("使用阿里云云呼叫信息更leads时,获取leads信息失败,请求体:",jsonService.toJson(request));
                return BaseResponse.error("获取leads信息错误");
            }
            String customerName = leads.getName();
            //接通
            if (CONNECT.equals(connected)) {
                content = "客户【"+ customerName + "】回拨，通话时长" + callDurationStr;
                leads.setLastContactTime(LocalDateTime.now());
            }
            //未接通
            if (NOT_CONNECT.equals(connected)){
                content = "客户【"+ customerName + "】回拨，未接通，原因：" + reason;
                leadsFollowCreateRequest.setReason(reason);
            }
        }

        if (!StringUtils.isEmpty(content)){
            leadsFollowCreateRequest.setContent(content);
        }

        leadsFollowCreateRequest.setId(leadsId);
        leadsFollowCreateRequest.setBid(bid);
        leadsFollowCreateRequest.setStaffId(staffId);

        BaseResponse<Boolean> followResponse = leadsFollowService.createFollowForOpt(leadsFollowCreateRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(followResponse)){
            log.error("云呼叫创建线索跟进记录失败,失败原因:" + followResponse.getMsg());
            return BaseResponse.error(followResponse.getMsg());
        }

        //保存到数据库中
        leads.setUpdateTime(LocalDateTime.now());
        if (!ObjectUtils.isEmpty(leads)){
            boolean result = leadsManager.updateById(leads);
            if (!result){
                return BaseResponse.error("更新线索信息失败");
            }
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<List<LeadsCustomerInfoDto>> getLeadsCustomerInfoByKeyword(GetLeadsCustomerInfoByKeywordRequest request) {
        String keyword = request.getKeyword();
        Integer bid = request.getBid();
        Long sid = request.getSid();
        SearchLeadsRequest searchLeadsRequest = new SearchLeadsRequest();
        searchLeadsRequest.setCustomerKeyword(keyword);
        searchLeadsRequest.setStatusGroup(10);
        searchLeadsRequest.setPageNo(1);
        searchLeadsRequest.setPageSize(999);
        searchLeadsRequest.setSid(sid);
        searchLeadsRequest.setBid(bid);
        BaseResponse<LeadsListVo> response = leadsGetService.search(searchLeadsRequest);
        if (BaseResponse.responseError(response)){
            return BaseResponse.error(response.getMsg());
        }
        return BaseResponse.success(createLeadsCustomerInfoDto(response.getData()));
    }

    private List<LeadsCustomerInfoDto> createLeadsCustomerInfoDto(LeadsListVo leadsListVo){
        List<LeadsListItemDto> list = leadsListVo.getList();
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
       return list.stream().map(leadsListItemDto -> {
            LeadsCustomerInfoDto leadsCustomerInfoDto = new LeadsCustomerInfoDto();
            leadsCustomerInfoDto.setCustomerName(leadsListItemDto.getName());
            leadsCustomerInfoDto.setLeadsId(leadsListItemDto.getId());
            leadsCustomerInfoDto.setCustomerMobile(leadsListItemDto.getMobile());
            return leadsCustomerInfoDto;
        }).collect(Collectors.toList());
    }

    private boolean updateLeadsStatusRecord(Integer bid, Integer status, List<Leads> leadses) {
        if (status.equals(LeadsStatusEnum.DISTRIBUTED.getStatus()) || status.equals(LeadsStatusEnum.TO_DISTRIBUTE.getStatus())) {
            return false;
        }
        ArrayList<Leads> leadsArrayList = Lists.newArrayList();
        //检查是否为客服线索
        leadses.forEach(
                leads -> {
                    Leads newleads = new Leads();
                    newleads.setBid(leads.getBid());
                    newleads.setId(leads.getId());
                    String kfFollowStatuses = leads.getKfFollowStatuses();
                    String followStatuses = leads.getFollowStatuses();
                    boolean isKfLeads = checkPreFollowStatus(leads.getStatus(), leads);
                    //设置客服跟进状态记录
                    if (isKfLeads) {
                        String kfStatus = getStatusRecords(status, kfFollowStatuses, true);
                        newleads.setKfFollowStatuses(kfStatus);
                    } else {
                        //设置线索状态跟进记录
                        String followStatus = getStatusRecords(status, followStatuses, false);
                        newleads.setFollowStatuses(followStatus);
                    }
                    leadsArrayList.add(newleads);
                }
        );

        return leadsManager.updateBatchById(leadsArrayList);
    }

    /**
     * 获取线索状态记录列表
     *
     * @param
     * @param followStatuses
     * @return
     */
    private String getStatusRecords(Integer status, String followStatuses, Boolean isKf) {
        List<LeadsFollowStatusDto> leadsFollowStatusDtoList = Lists.newArrayList();
        if (!StringUtils.isEmpty(followStatuses)) {
            leadsFollowStatusDtoList = jsonService.toObjectList(followStatuses, LeadsFollowStatusDto.class);
        }
        boolean isContain = leadsFollowStatusDtoList.stream().anyMatch(i -> status.equals(i.getId()));
        if (!isContain) {
            LeadsFollowStatusDto leadsFollowStatusDto = new LeadsFollowStatusDto();
            leadsFollowStatusDto.setId(status);
            leadsFollowStatusDto.setName(isKf ? LeadsPreFollowStatusEnum.parse(status).getName() : LeadsStatusEnum.parse(status).getName());
            leadsFollowStatusDtoList.add(leadsFollowStatusDto);
        }
        return jsonService.toJson(new ArrayList<>(
                leadsFollowStatusDtoList.stream().sorted(Comparator.comparing(LeadsFollowStatusDto::getId)).collect(Collectors.toList())
        ));
    }


    /**
     * 检查是否为客服线索
     *
     * @param status
     * @return
     */
    private boolean checkPreFollowStatus(Integer status, Leads leads) {
        return Objects.nonNull(status) && status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus());
    }

    /**
     * 检测退款备注和退款状态
     *
     * @param request
     * @return java.lang.Boolean
     */
    private Boolean checkRefundRemarkAndIsRefund(LeadsEditByOrderRequest request, String extData) {
        if (!StringUtils.isEmpty(request.getExtData()) && !StringUtils.isEmpty(extData)) {
            LeadsExtDataDto leadsExtDataDto = jsonService.toObject(request.getExtData(), LeadsExtDataDto.class);
            if (Objects.nonNull(leadsExtDataDto)) {
                Integer isRefund = leadsExtDataDto.getIsRefund();
                String refundRemark = leadsExtDataDto.getRefundRemark();
                if (isRefund.equals(1) && StringUtils.isEmpty(refundRemark)) {
                    throw new InngkeServiceException("请输入退款备注");
                }
                if (!StringUtils.isEmpty(refundRemark) && refundRemark.length() > 500) {
                    throw new InngkeServiceException("退款备注不能超过500个字符");
                }
                if (Objects.equals(isRefund, 0)) {
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    public BaseResponse<Boolean> editByInfo(LeadsEditByInforRequest request) {
        checkRequest(request.getBid(), request.getMobile(), request.getWeChat(), Sets.newHashSet(request.getId()));
        Boolean isSuccess = leadsManager.editByInfo(request);
        return BaseResponse.success(isSuccess);
    }

    /**
     * @param leadsInformationDto
     * @param leadsDto
     */
    private LeadsInformationDto fillBasicInfoToDto(LeadsInformationDto leadsInformationDto, LeadsDto leadsDto) {
        leadsInformationDto.setTagsList(leadsDto.getTagsList());
        leadsInformationDto.setEnterpriseTags(leadsDto.getEnterpriseTags());
        leadsInformationDto.setTagsSize(leadsDto.getTagsSize());
        leadsInformationDto.setDistributeStatus(leadsDto.getDistributeStatus());
        leadsInformationDto.setOrderAccount(leadsDto.getOrderAccount());
        leadsInformationDto.setOrderSn(leadsDto.getOrderSn());
        leadsInformationDto.setGoodsName(leadsDto.getGoodsName());
        leadsInformationDto.setGoodsNum(leadsDto.getGoodsNum());
        leadsInformationDto.setPayTime(leadsDto.getPayTime());
        leadsInformationDto.setPayAmount(leadsDto.getPayAmount());
        leadsInformationDto.setOrderMessage(leadsDto.getOrderMessage());
        leadsInformationDto.setRemark(leadsDto.getRemark());
        leadsInformationDto.setTpLeadsId(leadsDto.getTpLeadsId());
        leadsInformationDto.setPromotionName(leadsDto.getPromotionName());
        leadsInformationDto.setRegistryTime(leadsDto.getRegistryTime());
        leadsInformationDto.setExpectIn(leadsDto.getExpectIn());
        leadsInformationDto.setStyle(leadsDto.getStyle());
        leadsInformationDto.setBatchId(leadsDto.getBatchId());
        leadsInformationDto.setExtData(leadsDto.getExtData());
        leadsInformationDto.setChannelTypeText(leadsDto.getChannelTypeText());
        leadsInformationDto.setTags(leadsDto.getTags());
        leadsInformationDto.setShowPhone(leadsDto.getShowPhone());
        leadsInformationDto.setGoodsLink(leadsDto.getGoodsLink());
        leadsInformationDto.setChannelId(leadsDto.getChannelId());
        leadsInformationDto.setCreateStaffId(leadsDto.getCreateStaffId());
        leadsInformationDto.setCreateStaffName(leadsDto.getCreateStaffName());
        leadsInformationDto.setType(leadsDto.getType());
        leadsInformationDto.setLevel(leadsDto.getLevel());
        leadsInformationDto.setPreFollowStaffId(leadsDto.getPreFollowStaffId());
        leadsInformationDto.setPreFollowStaffName(leadsDto.getPreFollowStaffName());
        leadsInformationDto.setChannelText(leadsDto.getChannelText());
        leadsInformationDto.setStatusText(leadsDto.getStatusText());
        leadsInformationDto.setDistributeStaffDepartment(leadsDto.getDistributeStaffDepartment());
        leadsInformationDto.setId(leadsDto.getId());
        leadsInformationDto.setBid(leadsDto.getBid());
        leadsInformationDto.setCustomerId(leadsDto.getCustomerId());
        leadsInformationDto.setName(leadsDto.getName());
        leadsInformationDto.setMobile(leadsDto.getMobile());
        leadsInformationDto.setStatus(leadsDto.getStatus());
        leadsInformationDto.setProvinceId(leadsDto.getProvinceId());
        leadsInformationDto.setProvinceName(leadsDto.getProvinceName());
        leadsInformationDto.setCityId(leadsDto.getCityId());
        leadsInformationDto.setCityName(leadsDto.getCityName());
        leadsInformationDto.setAreaId(leadsDto.getAreaId());
        leadsInformationDto.setAreaName(leadsDto.getAreaName());
        leadsInformationDto.setAddress(leadsDto.getAddress());
        leadsInformationDto.setChannel(leadsDto.getChannel());
        leadsInformationDto.setChannelType(leadsDto.getChannelType());
        leadsInformationDto.setChannelSource(leadsDto.getChannelSource());
        leadsInformationDto.setChannelSourceText(leadsDto.getChannelSourceText());
        leadsInformationDto.setDistributeAgentId(leadsDto.getDistributeAgentId());
        leadsInformationDto.setDistributeStaffId(leadsDto.getDistributeStaffId());
        leadsInformationDto.setDistributeTime(leadsDto.getDistributeTime());
        leadsInformationDto.setErrorMsg(leadsDto.getErrorMsg());
        leadsInformationDto.setFollow(leadsDto.getFollow());
        leadsInformationDto.setLastOrderTime(leadsDto.getLastOrderTime());
        leadsInformationDto.setCreateTime(leadsDto.getCreateTime());
        leadsInformationDto.setUpdateTime(leadsDto.getUpdateTime());
        leadsInformationDto.setCustomerUid(leadsDto.getCustomerUid());
        leadsInformationDto.setDistributeStaffName(leadsDto.getDistributeStaffName());
        leadsInformationDto.setDistributeAgentName(leadsDto.getDistributeAgentName());
        leadsInformationDto.setPushBackTime(leadsDto.getPushBackTime());
        leadsInformationDto.setPushBackStaffId(leadsDto.getPushBackStaffId());
        leadsInformationDto.setPushBackStaffName(leadsDto.getPushBackStaffName());
        leadsInformationDto.setPayTime(leadsDto.getPayTime());
        leadsInformationDto.setFollowCount(leadsDto.getFollowCount());
        leadsInformationDto.setWeChat(leadsDto.getWeChat());
        leadsInformationDto.setPreFollowStatus(leadsDto.getPreFollowStatus());
        leadsInformationDto.setPreFollowStatusText(leadsDto.getPreFollowStatusText());
        leadsInformationDto.setAttachmentList(leadsDto.getAttachmentList());
        if (!StringUtils.isEmpty(leadsInformationDto.getExtData())) {
            String extData = leadsInformationDto.getExtData();
            if (extData.contains("\"isRefund\":\"0\"")) {
                leadsInformationDto.setExtData(extData.replace("\"isRefund\":\"0\"", "\"isRefund\":0"));
            }
            if (extData.contains("\"isRefund\":\"1\"")) {
                leadsInformationDto.setExtData(extData.replace("\"isRefund\":\"1\"", "\"isRefund\":1"));
            }
        }
        return leadsInformationDto;
    }

    private void fillBasicInfoToVo(LeadsInformationVo leadsInformationVo, LeadsVo leadsDto) {
        fillBasicInfoToDto(leadsInformationVo, leadsDto);
        leadsInformationVo.setClosePrivatePhoneCallLogList(leadsDto.getClosePrivatePhoneCallLogList());
        leadsInformationVo.setCallLogList(leadsDto.getCallLogList());
        leadsInformationVo.setTransactionOrderCount(leadsDto.getTransactionOrderCount());
        leadsInformationVo.setLeadsFollowCount(leadsDto.getLeadsFollowCount());
        leadsInformationVo.setCanRollback(leadsDto.getCanRollback());
        leadsInformationVo.setCanForward(leadsDto.getCanForward());
        leadsInformationVo.setGoodsLink(leadsDto.getGoodsLink());
        leadsInformationVo.setPreFollowStaffName(leadsDto.getPreFollowStaffName());
        leadsInformationVo.setPreFollowStaffId(leadsDto.getPreFollowStaffId());
        leadsInformationVo.setPreFollowStatus(leadsDto.getPreFollowStatus());
        leadsInformationVo.setDistributeFollowTime(leadsDto.getDistributeFollowTime());
        leadsInformationVo.setFollowStatuses(leadsDto.getFollowStatuses());
        leadsInformationVo.setKfFollowStatuses(leadsDto.getKfFollowStatuses());
        leadsInformationVo.setDemandProduct(leadsDto.getDemandProduct());
        leadsInformationVo.setLevelId(leadsDto.getLevelId());
        leadsInformationVo.setLevelText(leadsDto.getLevelText());
        leadsInformationVo.setLevelDes(leadsDto.getLevelDes());
        leadsInformationVo.setClientId(leadsDto.getClientId());
        leadsInformationVo.setFirstContactTime(leadsDto.getFirstContactTime());
        leadsInformationVo.setExternalTags(leadsDto.getExternalTags());
        leadsInformationVo.setTpId(leadsDto.getTpId());
        leadsInformationVo.setProducts(leadsDto.getProducts());
        List<LeadsAttachment> attachmentList = leadsDto.getAttachmentList();
        if (!CollectionUtils.isEmpty(attachmentList)) {
            List<LeadsAttachment> imgAttachmentList = attachmentList.stream().filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getFileType()))
                    .filter(item -> org.apache.commons.collections4.CollectionUtils.containsAny(Sets.newHashSet(item.getFileType().split(InngkeAppConst.OBLIQUE_LINE_STR)), imgType))
                    .collect(Collectors.toList());
            leadsInformationVo.setImgTypeAttachmentList(imgAttachmentList);

            List<LeadsAttachment> otherTypeAttachementList = Lists.newCopyOnWriteArrayList(leadsDto.getAttachmentList());
            Collections.copy(otherTypeAttachementList, leadsDto.getAttachmentList());
            otherTypeAttachementList.removeAll(imgAttachmentList);

            leadsInformationVo.setOtherTypeAttachtmentList(otherTypeAttachementList);
        }
    }

    /**
     * 时间转换     将Long类型 单位秒 转换成 00:00:00格式字符串
     * @return
     */
    private String timeTransition(Long seconds){
        // 计算小时、分钟、秒数
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long remainingSeconds = seconds % 60;

        // 将小时、分钟、秒数转换为字符串，并在需要的地方添加前导零
        String hoursString = String.format("%02d", hours);
        String minutesString = String.format("%02d", minutes);
        String secondsString = String.format("%02d", remainingSeconds);

        // 将字符串连接起来，以形成格式为00:00:00的字符串
        String formattedTime = hoursString + ":" + minutesString + ":" + secondsString;

        return formattedTime;
    }

}

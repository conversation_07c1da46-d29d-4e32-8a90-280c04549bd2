package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.LeadsAchievementReportDetailRequest;
import com.inngke.bp.leads.dto.request.LeadsAchievementReportRequest;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.service.LeadsReportService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * LeadsReportController
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 18:06
 */
@RestController
@RequestMapping("/api/leads-report")
@Slf4j
public class LeadsReportController {


    @Autowired
    private LeadsReportService leadsReportService;


    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> list(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload,
            @Validated LeadsAchievementReportRequest request
    ) {

        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setCurrentStaffId(jwtPayload.getSid());
        return leadsReportService.getReportList(request);
    }


    /**
     * 查看明细
     */
    @GetMapping("/listDetail")
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload,
            @Validated LeadsAchievementReportDetailRequest request
    ){

        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setCurrentStaffId(jwtPayload.getSid());
        return leadsReportService.getReportListDetail(request);
    }





    @GetMapping("/export/list")
    public BaseResponse<List<LeadsAchievementReportResponse>> exportList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestAttribute JwtPayload jwtPayload,
            @Validated LeadsAchievementReportRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setCurrentStaffId(jwtPayload.getSid());

        return leadsReportService.exportList(request);
    }

}

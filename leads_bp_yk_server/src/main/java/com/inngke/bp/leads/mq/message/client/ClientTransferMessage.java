package com.inngke.bp.leads.mq.message.client;

import com.inngke.common.mq.dto.BaseMessage;

/**
 * 客户转交消息体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 15:04
 */
public class ClientTransferMessage extends BaseMessage {
    private Long sourceStaffId;

    private Long targetStaffId;

    /**
     * ID
     */
    private Long id;

    /**
     * 变更类型
     *
     * @see com.inngke.bp.client.enums.EntityChangeMqEventEnum
     */
    private Integer event;

    /**
     * 业务类型
     *
     * @see com.inngke.bp.client.enums.ClientFollowModeEnum
     */
    private String business;

    public Long getSourceStaffId() {
        return sourceStaffId;
    }

    public void setSourceStaffId(Long sourceStaffId) {
        this.sourceStaffId = sourceStaffId;
    }

    public Long getTargetStaffId() {
        return targetStaffId;
    }

    public void setTargetStaffId(Long targetStaffId) {
        this.targetStaffId = targetStaffId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }
}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeForwardConf;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeForwardConfManager;
import com.inngke.bp.leads.dto.request.SaveForwardConfRequest;
import com.inngke.bp.leads.dto.response.StaffForwardConfDto;
import com.inngke.bp.leads.dto.response.StaffSimpleInfoDto;
import com.inngke.bp.leads.service.LeadsDistributeForwardService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class LeadsDistributeForwardServiceImpl implements LeadsDistributeForwardService {

    @Resource
    private LeadsDistributeForwardConfManager leadsDistributeForwardConfManager;
    @Resource
    private StaffClientForLeads staffClientForLeads;

    @Override
    public BaseResponse<Boolean> saveForwardConf(SaveForwardConfRequest request) {

        if (leadsDistributeForwardConfManager.existForward(request.getBid(), request.getForwardStaffId())) {
            return BaseResponse.error("该员工已开启线索自动分配");
        }
        if (leadsDistributeForwardConfManager.isForwarder(request.getBid(), request.getStaffId())){
            return BaseResponse.error("您已被设置为线索接收人");
        }

        StaffDto forwardStaff = staffClientForLeads.getStaffById(request.getBid(), request.getForwardStaffId());
        if (!StaffStatusEnum.OPENED.equals(StaffStatusEnum.parse(forwardStaff.getStatus()))){
            return BaseResponse.error("请重新指定员工");
        }

        LeadsDistributeForwardConf entity = new LeadsDistributeForwardConf();
        entity.setBid(request.getBid());
        entity.setStaffId(request.getStaffId());
        entity.setForwardStaffId(request.getForwardStaffId());

        if (!leadsDistributeForwardConfManager.saveOrUpdateByStaffId(entity)) {
            return BaseResponse.error("保存失败");
        }

        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<StaffForwardConfDto> getForwardConf(BaseIdRequest request) {
        LeadsDistributeForwardConf leadsDistributeForwardConf = leadsDistributeForwardConfManager.getOne(Wrappers.<LeadsDistributeForwardConf>query().eq(LeadsDistributeForwardConf.BID, request.getBid())
                .eq(LeadsDistributeForwardConf.STAFF_ID, request.getId()));

        StaffForwardConfDto staffForwardConfDto = new StaffForwardConfDto();
        staffForwardConfDto.setIsForward(false);

        if (Objects.isNull(leadsDistributeForwardConf)) {
            return BaseResponse.success(staffForwardConfDto);
        }

        staffForwardConfDto.setIsForward(true);
        staffForwardConfDto.setForwardStaffId(leadsDistributeForwardConf.getForwardStaffId());
        Optional.ofNullable(leadsDistributeForwardConf.getForwardStaffId()).map(staffId->staffClientForLeads.getStaffById(request.getBid(),staffId))
                .map(this::toSimpleStaffDto).ifPresent(staffForwardConfDto::setForwardStaff);

        return BaseResponse.success(staffForwardConfDto);
    }

    private StaffSimpleInfoDto toSimpleStaffDto(StaffDto staffDto) {
        StaffSimpleInfoDto staffSimpleInfoDto = new StaffSimpleInfoDto();
        staffSimpleInfoDto.setId(staffDto.getId());
        staffSimpleInfoDto.setName(staffDto.getName());
        staffSimpleInfoDto.setStatus(staffDto.getStatus());
        return staffSimpleInfoDto;
    }

    @Override
    public BaseResponse<Set<Long>> getExistForwardStaffIds(BaseBidRequest request) {
        return BaseResponse.success(
                leadsDistributeForwardConfManager.list(
                        Wrappers.<LeadsDistributeForwardConf>query().eq(LeadsDistributeForwardConf.BID,request.getBid()).select(LeadsDistributeForwardConf.STAFF_ID)
                ).stream().map(LeadsDistributeForwardConf::getStaffId).collect(Collectors.toSet())
        );
    }

    @Override
    public BaseResponse<Boolean> closeForward(SaveForwardConfRequest request) {
        Long staffId = request.getStaffId();

        leadsDistributeForwardConfManager.remove(Wrappers.<LeadsDistributeForwardConf>query().eq(LeadsDistributeForwardConf.BID,request.getBid()).eq(LeadsDistributeForwardConf.STAFF_ID,staffId));

        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<Boolean> isForwarder(BaseBidOptRequest request) {
        return BaseResponse.success(leadsDistributeForwardConfManager.isForwarder(request.getBid(), request.getOperatorStaffId()));
    }
}

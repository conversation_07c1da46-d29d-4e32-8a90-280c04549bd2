package com.inngke.bp.leads.service.impl.tp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.inngke.bp.leads.db.leads.manager.LeadsTpAccountInfoManager;
import com.inngke.bp.leads.dto.request.tp.*;
import com.inngke.bp.leads.dto.response.tp.TpAccountInfoDto;
import com.inngke.bp.leads.dto.response.tp.TpLaunchOauthDataDto;
import com.inngke.bp.leads.enums.LeadsTpAccountTypeEnum;
import com.inngke.bp.leads.service.LeadsTpOauthTypeService;
import com.inngke.bp.leads.service.impl.tp.oauth.LeadsTpOauthFactory;
import com.inngke.bp.leads.service.tp.LeadsTpOauthService;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.tencent.ads.ApiException;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:05
 */
@Service
@DubboService(version = "1.0.0")
public class LeadsTpOauthServiceImpl implements LeadsTpOauthService {
    @Autowired
    private LeadsTpOauthFactory leadsTpOauthTypeFactory;

    @Autowired
    private LeadsTpAccountInfoManager leadsTpAccountInfoManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Resource
    private DepartmentClientForLeads departmentClientForLeads;

    @Override
    public BaseResponse<List<TpAccountInfoDto>> getAlreadyOauthList(BaseBidOptRequest request) {
        List<LeadsTpAccountInfo> list = leadsTpAccountInfoManager.list(Wrappers.<LeadsTpAccountInfo>query()
                .eq(LeadsTpAccountInfo.BID, request.getBid())
                .eq(LeadsTpAccountInfo.ENABLE,1)
                .orderByAsc(LeadsTpAccountInfo.TYPE)
        );
        List<Long> operatorStaffIds = list.stream().filter(item -> item.getOperatorStaffId() != 0L).map(LeadsTpAccountInfo::getOperatorStaffId).collect(Collectors.toList());
        Map<Long, StaffDto> staffMap = Maps.newConcurrentMap();
        Map<Long, DepartmentDto> departmentMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(operatorStaffIds)) {
            staffMap = staffClientForLeads.getStaffByIds(request.getBid(), Sets.newHashSet(operatorStaffIds));
            departmentMap = departmentClientForLeads.getDepartmentByIds(request.getBid(), Lists.newArrayList(staffMap.values().stream().map(StaffDto::getDepartmentId).collect(Collectors.toSet()))).stream().collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
        }
        Map<Long, StaffDto> finalStaffMap = staffMap;
        Map<Long, DepartmentDto> finalDepartmentMap = departmentMap;
        List<TpAccountInfoDto> result = list.stream().map(item -> tpAccountInfoDto(item, finalStaffMap, finalDepartmentMap)).collect(Collectors.toList());

        return BaseResponse.success(result);
    }

    private TpAccountInfoDto tpAccountInfoDto(LeadsTpAccountInfo leadsTpAccountInfo, Map<Long, StaffDto> staffMap, Map<Long, DepartmentDto> departmentMap){
        LeadsTpAccountTypeEnum leadsTpAccountTypeEnum = LeadsTpAccountTypeEnum.parse(leadsTpAccountInfo.getAccountType());

        TpAccountInfoDto tpAccountInfoDto = new TpAccountInfoDto();
        tpAccountInfoDto.setType(leadsTpAccountInfo.getType());
        tpAccountInfoDto.setAccountId(leadsTpAccountInfo.getAccountId());
        tpAccountInfoDto.setCorporationName(leadsTpAccountInfo.getAccountName());
        tpAccountInfoDto.setBusinessId(leadsTpAccountInfo.getBusinessId());
        tpAccountInfoDto.setAccountType(leadsTpAccountTypeEnum == null ? "" : leadsTpAccountTypeEnum.getName());
        tpAccountInfoDto.setAccountName(leadsTpAccountInfo.getAccountName());
        tpAccountInfoDto.setAccountRole(leadsTpAccountInfo.getAccountRole());
        if (Objects.nonNull(leadsTpAccountInfo.getOperatorStaffId()) && 0L != leadsTpAccountInfo.getOperatorStaffId()) {
            StaffDto staffDto = staffMap.get(leadsTpAccountInfo.getOperatorStaffId());
            if (Objects.nonNull(staffDto)) {
                tpAccountInfoDto.setOperatorCreateStaffId(staffDto.getId());
                String staffName = StaffStatusEnum.DELETE.getCode() == staffDto.getStatus() ? staffDto.getName() + "(已删除)" : staffDto.getName();
                tpAccountInfoDto.setOperatorCreateStaffName(staffName);

                DepartmentDto departmentDto = departmentMap.get(staffDto.getDepartmentId());
                if (Objects.nonNull(departmentDto)) {
                    tpAccountInfoDto.setOperatorCreateStaffDepartmentId(departmentDto.getId());
                    tpAccountInfoDto.setOperatorCreateStaffDepartmentName(departmentDto.getName());
                }
            }
        }

        tpAccountInfoDto.setCreateTime(leadsTpAccountInfo.getCreateTime());
        tpAccountInfoDto.setUpdateTime(leadsTpAccountInfo.getUpdateTime());
        return tpAccountInfoDto;
    }

    @Override
    public BaseResponse<TpLaunchOauthDataDto> getLaunchOauthData(GetTpLaunchOauthDataRequest request) {
        try {
            LeadsTpOauthTypeService leadsTpOauthTypeService = leadsTpOauthTypeFactory.getInstance(request.getType());
            return leadsTpOauthTypeService.getLaunchOauthData(request);
        }catch (ApiException e){
            throw new InngkeServiceException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<Boolean> oauthCallback(OauthCallbackRequest request) {
        try {
            LeadsTpOauthTypeService leadsTpOauthTypeService = leadsTpOauthTypeFactory.getInstance(request.getType());
            return leadsTpOauthTypeService.oauthCallback(request);
        }catch (ApiException e){
            throw new InngkeServiceException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<String> getAccessToken(GetTpAccessTokenRequest request) {
        LeadsTpOauthTypeService leadsTpOauthTypeService = leadsTpOauthTypeFactory.getInstance(request.getType());
        return leadsTpOauthTypeService.getAccessToken(request);
    }

    @Override
    public BaseResponse<Boolean> unbindOauth(UnbindOauthRequest request) {
        LeadsTpAccountInfo one = leadsTpAccountInfoManager.getOne(new QueryWrapper<LeadsTpAccountInfo>()
                .eq(LeadsTpAccountInfo.BID, request.getBid())
                .eq(LeadsTpAccountInfo.ENABLE, 1)
                .eq(LeadsTpAccountInfo.ACCOUNT_ID, request.getAccountId()));

        if (one == null) {
            return BaseResponse.error("授权账号不存在，请刷新页面");
        }

        LeadsTpAccountInfo leadsTpAccountInfo = new LeadsTpAccountInfo();
        leadsTpAccountInfo.setId(one.getId());
        leadsTpAccountInfo.setEnable(false);

        return BaseResponse.success(leadsTpAccountInfoManager.updateById(leadsTpAccountInfo));
    }

    @Override
    public BaseResponse<Boolean> settingOperatorStaff(AccountSettingOperatorStaffRequest request) {
        Long operatorCreateStaffId = request.getOperatorCreateStaffId();
        if (Objects.nonNull(request.getOperatorCreateStaffId())) {
            StaffDto staffDto = staffClientForLeads.getStaffById(request.getBid(), operatorCreateStaffId);
            if (Objects.isNull(staffDto)) {
                return BaseResponse.error("未找到员工信息");
            }
            if (staffDto.getStatus() == StaffStatusEnum.DELETE.getCode()) {
                return BaseResponse.error("员工已离职");
            }
        }
        if (Objects.isNull(request.getOperatorCreateStaffId())) {
            request.setOperatorCreateStaffId(0L);
        }
        boolean update = leadsTpAccountInfoManager.update(
                Wrappers.<LeadsTpAccountInfo>update()
                        .eq(LeadsTpAccountInfo.BID, request.getBid())
                        .eq(LeadsTpAccountInfo.ACCOUNT_ID, request.getAccountId())
                        .set(LeadsTpAccountInfo.OPERATOR_STAFF_ID, request.getOperatorCreateStaffId())
        );

        return BaseResponse.success(update);
    }
}

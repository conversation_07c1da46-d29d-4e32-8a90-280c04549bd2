package com.inngke.bp.leads.mq.message.customer;

import com.inngke.common.mq.dto.BaseMessage;

/**
 * <AUTHOR>
 * @since 2021/9/17 14:33
 */
public class CusromerChangeMessageDto extends BaseMessage {

    /**
     * 客户ID 即customer.id，如果本字段存在时，优先使用
     */
    private Long id;

    /**
     * event=4时才有值
     *
     * @demo 123456
     */
    private String mobile;

//    /**
//     * 客户UID，即customer.uid，如果id不存在时，使用本字段
//     */
//    private Long uid;

    /**
     * 事件类型：1=添加客户 2=修改客户信息 3=禁用客户 4=手机号码授权
     */
    private Integer event;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

//    public Long getUid() {
//        return uid;
//    }
//
//    public void setUid(Long uid) {
//        this.uid = uid;
//    }

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }
}

package com.inngke.bp.leads.notify.builder;

import com.inngke.bp.leads.notify.context.LeadsOrderRefundMessageContext;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LeadsOrderRefundContentBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/22 18:07
 */
@Component(value = "reminder_refund_notification")
@Slf4j
public class LeadsOrderRefundContentBuilder extends TemplateMessageContentBuilder<LeadsOrderRefundMessageContext> {
    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(LeadsOrderRefundMessageContext ctx) {
        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);

        return builder
                .setTitle("退款通知")
                .setDescription("有一位客户发起退款")
                .setVar("customerName", ctx.getName())
                .setVar("orderNo", ctx.getOrderNo())
                .setVar("tradeName", ctx.getGoodsName())
                .setVar("orderAmount", ctx.getPrice().toString())
                .setVar("cancelReault", ctx.getRefundReason())
                // 企业微信
                .setVar("phone", ctx.getMobile())
                .setVar("time", ctx.getPayTime());
    }
}

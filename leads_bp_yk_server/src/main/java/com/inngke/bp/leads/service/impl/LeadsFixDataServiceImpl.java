package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.client.dto.response.demand.DemandItemDto;
import com.inngke.bp.client.service.DemandService;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPushBackLogManager;
import com.inngke.bp.leads.dto.LeadsSnapshotDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsFixDataService;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * LeadsFixDataServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/11/20 09:42
 */
@DubboService(version = "1.0.0", timeout = 9000)
@Slf4j
public class LeadsFixDataServiceImpl implements LeadsFixDataService {
    @Resource
    private LeadsManager leadsManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private DemandService demandService;

    @Resource
    private LeadsFollowManager leadsFollowManager;

    @Resource
    private LeadsPushBackLogManager leadsPushBackLogManager;

    @Override
    public BaseResponse<Integer> fixLeadsContactFollow(Integer bid) {
        List<Leads> notContactLeads = leadsManager.findRelationClientAndNotContactLeads(bid);
        if (CollectionUtils.isEmpty(notContactLeads)) {
            log.info("没有待修复的leads数据");
            return BaseResponse.success();
        }
        Map<Integer, List<Leads>> leadsBigGroup = notContactLeads.stream().collect(Collectors.groupingBy(Leads::getBid));
        List<DemandItemDto> demandList = Lists.newArrayList();

        List<LeadsFollow> leadsFollows = Lists.newArrayList();
        leadsBigGroup.forEach((leadsBid, leadsBidList) -> {

            BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
            baseIdsRequest.setBid(leadsBid);
            baseIdsRequest.setIds(leadsBidList.stream().map(Leads::getId).collect(Collectors.toList()));
            BaseResponse<List<DemandItemDto>> demandResponse = demandService.getDemandByLeadsIds(baseIdsRequest);
            List<DemandItemDto> demandItemList = demandResponse.getData();
            Map<Long, DemandItemDto> leadsIdDemandMap = demandItemList.stream().collect(Collectors.toMap(DemandItemDto::getLeadsId, Function.identity(), (k1, k2) -> k2));

            leadsBidList.forEach(leads -> {
                LeadsFollow leadsFollow = new LeadsFollow();
                leadsFollow.setId(SnowflakeHelper.getId());
                leadsFollow.setBid(leadsBid);
                leadsFollow.setLeadsId(leads.getId());
                leadsFollow.setStaffId(leads.getDistributeStaffId());
                leadsFollow.setUserId(0L);
                leadsFollow.setFollowType(0);
                leadsFollow.setBeforeLeadsStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
                leadsFollow.setLeadsStatus(LeadsStatusEnum.CONTACTED.getStatus());
                leadsFollow.setFollowContent("将线索状态由【待联系】修改为【已联系】");
                DemandItemDto demandItemDto = leadsIdDemandMap.get(leads.getId());

                leadsFollow.setCreateTime(Objects.nonNull(demandItemDto) ? DateTimeUtils.MillisToLocalDateTime(demandItemDto.getCreateTime()) : LocalDateTime.now());
                leadsFollows.add(leadsFollow);
            });

        });
        boolean save = leadsFollowManager.saveBatch(leadsFollows);

        return BaseResponse.success(save ? leadsFollows.size() : 0);
    }

    @Override
    public BaseResponse<Integer> fixLeadsPushBackLog(Integer bid) {
        BidUtils.setBid(bid);
        List<Leads> pushBackLeads = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
        );
        if (CollectionUtils.isEmpty(pushBackLeads)) {
            return BaseResponse.success(0);
        }
        Map<Long, Leads> pushBackLeadsMap = pushBackLeads.stream().collect(Collectors.toMap(Leads::getId, Function.identity()));

        List<LeadsFollow> leadsFollowList = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .in(LeadsFollow.LEADS_ID, pushBackLeads.stream().map(Leads::getId).collect(Collectors.toSet()))
                        .eq(LeadsFollow.LEADS_STATUS, LeadsStatusEnum.PUSH_BACK.getStatus())
                        .apply("leads_status <> before_leads_status")
                        .select(LeadsFollow.ID, LeadsFollow.BID, LeadsFollow.LEADS_ID, LeadsFollow.LEADS_STATUS, LeadsFollow.STAFF_ID, LeadsFollow.BEFORE_LEADS_STATUS, "MAX(" + LeadsFollow.CREATE_TIME + ") AS createTime")
                        .groupBy(LeadsFollow.LEADS_ID)
        );

        if (CollectionUtils.isEmpty(leadsFollowList)) {
            return BaseResponse.success(0);
        }

        List<LeadsPushBackLog> pushBackLogs = Lists.newArrayList();

        for (LeadsFollow leadsFollow : leadsFollowList) {
            Leads leads = pushBackLeadsMap.get(leadsFollow.getLeadsId());
            if (Objects.isNull(leads)) {
                continue;
            }

            LeadsPushBackLog pushBackLog = new LeadsPushBackLog();
            pushBackLog.setId(SnowflakeHelper.getId());
            pushBackLog.setBid(leadsFollow.getBid());
            pushBackLog.setLeadsId(leadsFollow.getLeadsId());
            pushBackLog.setPushBackReasonId(leadsFollow.getReasonId());
            pushBackLog.setPushBackReason(leadsFollow.getReason());
            pushBackLog.setPushBackBy(leadsFollow.getStaffId());
            pushBackLog.setLeadsStatus(leadsFollow.getBeforeLeadsStatus());
            pushBackLog.setPushBackTime(leadsFollow.getCreateTime());
            pushBackLog.setCreateTime(leadsFollow.getCreateTime());

            LeadsSnapshotDto leadsSnapshotDto = new LeadsSnapshotDto();
            leadsSnapshotDto.setId(leads.getId());
            leadsSnapshotDto.setBid(leadsSnapshotDto.getBid());
            leadsSnapshotDto.setDistributeStaffId(leadsFollow.getStaffId());
            pushBackLog.setLeadsSnapshot(JsonUtil.toJsonString(leadsSnapshotDto));

            pushBackLogs.add(pushBackLog);
        }

        leadsPushBackLogManager.saveBatch(pushBackLogs);

        return BaseResponse.success(leadsFollowList.size());
    }
}

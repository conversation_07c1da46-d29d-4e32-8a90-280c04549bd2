/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 线索下发配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsDistributeConfDao extends BaseMapper<LeadsDistributeConf> {

}

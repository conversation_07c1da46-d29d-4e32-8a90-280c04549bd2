/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConfPermissions;
import com.inngke.bp.leads.db.leads.dao.LeadsDistributeConfPermissionsDao;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfPermissionsManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 区域接收人配置的-权限配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsDistributeConfPermissionsManagerImpl extends ServiceImpl<LeadsDistributeConfPermissionsDao, LeadsDistributeConfPermissions> implements LeadsDistributeConfPermissionsManager {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateByRegionId(LeadsDistributeConfPermissions leadsDistributeConfPermissions) {
        LeadsDistributeConfPermissions exist = getOne(Wrappers.<LeadsDistributeConfPermissions>query()
                .eq(LeadsDistributeConfPermissions.REGION_ID, leadsDistributeConfPermissions.getRegionId())
                .eq(LeadsDistributeConfPermissions.BID, leadsDistributeConfPermissions.getBid()));

        if (Objects.nonNull(exist)){
            leadsDistributeConfPermissions.setId(exist.getId());
        }else {
            leadsDistributeConfPermissions.setId(SnowflakeHelper.getId());
            leadsDistributeConfPermissions.setCreateTime(LocalDateTime.now());
        }

        return saveOrUpdate(leadsDistributeConfPermissions);
    }

    @Override
    public Map<Long, List<Integer>> getStaffPermissionsMap(Integer bid) {
        Map<Long, List<Integer>> staffRegionMap = Maps.newHashMap();
        list(Wrappers.<LeadsDistributeConfPermissions>query().eq(LeadsDistributeConfPermissions.BID, bid))
                .forEach(distributeConfPermissions->{
                    Integer regionId = distributeConfPermissions.getRegionId();
                    Splitter.on(InngkeAppConst.COMMA_STR).split(distributeConfPermissions.getStaffIds())
                            .forEach(staffId ->
                                    staffRegionMap.computeIfAbsent(Long.valueOf(staffId), key -> Lists.newArrayList()).add(regionId)
                            );
                });

       return staffRegionMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDistributeConfPermissions(Integer bid, Long staffId) {
        List<LeadsDistributeConfPermissions> list = list(
                Wrappers.<LeadsDistributeConfPermissions>query().eq(LeadsDistributeConfPermissions.BID, bid)
        );

        List<LeadsDistributeConfPermissions> hasStaffConf = list.stream().filter(conf -> {
            List<String> staffIds = Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(conf.getStaffIds()));
            return staffIds.contains(staffId.toString());
        }).collect(Collectors.toList());

        List<LeadsDistributeConfPermissions> needUpdate = Lists.newArrayList();
        List<Long> needDelete = Lists.newArrayList();
        for (LeadsDistributeConfPermissions conf : hasStaffConf) {
            conf.setUpdateTime(null);

            List<String> staffIds = Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(conf.getStaffIds()));
            staffIds.remove(staffId.toString());
            conf.setStaffIds(Joiner.on(InngkeAppConst.COMMA_STR).join(staffIds));
            if (CollectionUtils.isEmpty(staffIds)){
                needDelete.add(conf.getId());
            }else {
                needUpdate.add(conf);
            }
        }

        if (!CollectionUtils.isEmpty(needDelete)){
            removeByIds(needDelete);
        }
        if(!CollectionUtils.isEmpty(needUpdate)){
            updateBatchById(needUpdate);
        }
    }
}

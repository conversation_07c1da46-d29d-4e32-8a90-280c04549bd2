package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.service.JsonService;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by emiya on 2022/6/13 14:49
 *
 * <AUTHOR>
 * @date 2022/6/13 14:49
 */
@Component(value = "LeadsContactIn24Strategy")
public class LeadsContactIn24Strategy implements LeadsFollowTimeStrategy {
    @Autowired
    private JsonService jsonService;

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "contactIn24",
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("contactIn24", 1))
                        )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter contactIn24ParseFilter = bucketAggregations.get("contactIn24");
        long docCount = contactIn24ParseFilter.getDocCount();

        // 24小时联系率
        BigDecimal rate = BigDecimal.valueOf(docCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(dto.getLeadsCount()), 2, RoundingMode.HALF_UP);
        dto.setContactIn24Rate(rate + "%");
        dto.setContactIn24Count((int) docCount);
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        // 24小时联系数
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getContactIn24Count).reduce(Integer::sum).get();
        dto.setContactIn24Count(sum);
        if (dto.getLeadsCount() > 0) {
            // 24小时联系率
            String rate = BigDecimal.valueOf(sum)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(dto.getLeadsCount()), 2, RoundingMode.HALF_UP).toString();
            dto.setContactIn24Rate(rate + "%");
        }

    }
}

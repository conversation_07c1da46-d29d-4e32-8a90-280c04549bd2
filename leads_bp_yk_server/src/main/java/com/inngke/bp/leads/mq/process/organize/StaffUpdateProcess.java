package com.inngke.bp.leads.mq.process.organize;

import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.RbacClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfPermissionsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPreFollowConfigManager;
import com.inngke.bp.leads.dto.request.LeadsUpdateByStaffIdRequest;
import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.PreFollowStaffService;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.EntityChangeTypeEnum;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Set;

@Service
@Slf4j
public class StaffUpdateProcess extends StaffChangeAbstract implements StaffChangeProcess {

    private final Set<StaffStatusEnum> NEED_DELETE_STATUS = Sets.newHashSet(StaffStatusEnum.CLOSED, StaffStatusEnum.DISABLED, StaffStatusEnum.INACTIVATED);

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsPreFollowConfigManager leadsPreFollowConfigManager;

    @Autowired
    private PreFollowStaffService preFollowStaffService;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private LeadsDistributeConfPermissionsManager leadsDistributeConfPermissionsManager;

    /*@DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private LeadsPreFollowStaffService leadsPreFollowStaffService;*/

    @Override
    public void handle(EntityChangeMessageDto entityChangeMessageDto) {
        StaffDto staff = staffClientForLeads
                .getStaffById(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());
        if (ObjectUtils.isEmpty(staff)) {
            log.info("获取员工数据为空,请求数据{}", jsonService.toJson(entityChangeMessageDto));
            return;
        }
        StaffStatusEnum statusEnum = StaffStatusEnum.parse(staff.getStatus());

        if (NEED_DELETE_STATUS.contains(statusEnum)) {
            deleteDistributeConf(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());

            //删除区域接收人权限配置
            leadsDistributeConfPermissionsManager.removeDistributeConfPermissions(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());
        }
        updateLeadsEsByStaffId(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());

        updatePreFollowConfig(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());
    }

    @Override
    public EntityChangeTypeEnum getHandleEntityChangeType() {
        return EntityChangeTypeEnum.EDIT;
    }

    private void updatePreFollowConfig(int bid, Long staffId) {
        log.info("更新客服配置接收到员工Mq:{},{}", bid, staffId);
        if (staffId == null) {
            return;
        }
        LeadsPreFollowConfig byStaffId = leadsPreFollowConfigManager.getByStaffId(bid, staffId);
        if (byStaffId != null) {
            log.info("更新客服配置接收到员工Mq,开始刷新配置,staffId{}", staffId);
            preFollowStaffService.incPreFollowVersion(bid);
            log.info("更新客服配置接收到员工Mq,刷新配置完成,staffId{}", staffId);
        }

        /*GetLeadsPreFollowStaffRequest getLeadsPreFollowStaffRequest = new GetLeadsPreFollowStaffRequest();
        getLeadsPreFollowStaffRequest.setBid(bid);
        getLeadsPreFollowStaffRequest.setEnable(true);
        getLeadsPreFollowStaffRequest.setStaffIds(Sets.newHashSet(staffId));
        BaseResponse<List<GetLeadsPreFollowStaffDto>> followStaffConfig = leadsPreFollowStaffService.getFollowStaffConfig(getLeadsPreFollowStaffRequest);
        List<GetLeadsPreFollowStaffDto> data = followStaffConfig.getData();
        if (staffList.contains(staffId) && CollectionUtils.isEmpty(data)) {
            log.info("更新客服配置接收到员工Mq，添加客服角色：{}", staffId);
            BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
            baseIdsRequest.setBid(bid);
            baseIdsRequest.setIds(Lists.newArrayList(staffId));
            leadsPreFollowStaffService.addPreStaff(baseIdsRequest);
        }
        if (!staffList.contains(staffId) && !CollectionUtils.isEmpty(data)) {
            log.info("更新客服配置接收到员工Mq，删除客服角色：{}", staffId);
            BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
            baseIdsRequest.setBid(bid);
            baseIdsRequest.setIds(Lists.newArrayList(staffId));
            leadsPreFollowStaffService.cleanPreStaff(baseIdsRequest);
        }*/


    }


    private void updateLeadsEsByStaffId(Integer bid, Long staffId) {
        AsyncUtils.runAsync(() -> {
            log.info("员工变更，修改leadsEs！bid={}，staffId={}", bid, staffId);
            LeadsUpdateByStaffIdRequest leadsUpdateByStaffIdRequest = new LeadsUpdateByStaffIdRequest();
            leadsUpdateByStaffIdRequest.setBid(bid);
            leadsUpdateByStaffIdRequest.setStaffId(staffId);
            leadsEsService.updateByStaffId(leadsUpdateByStaffIdRequest);
        });
    }
}

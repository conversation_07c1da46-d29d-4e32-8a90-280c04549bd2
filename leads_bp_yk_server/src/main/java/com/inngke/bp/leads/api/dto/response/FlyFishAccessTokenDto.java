package com.inngke.bp.leads.api.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:16
 */
@NoArgsConstructor
@Data
public class FlyFishAccessTokenDto {

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("expires_in")
    private Integer expiresIn;

    @JsonProperty("refresh_token")
    private String refreshToken;

    @JsonProperty("advertiser_id")
    private String advertiserId;

    @JsonProperty("advertiser_name")
    private String advertiserName;

    @JsonProperty("advertiser_ids")
    private List<Long> advertiserIds;

    @JsonProperty("refresh_token_expires_in")
    private Integer refreshTokenExpiresIn;

}

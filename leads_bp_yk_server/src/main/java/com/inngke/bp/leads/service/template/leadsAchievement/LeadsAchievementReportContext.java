package com.inngke.bp.leads.service.template.leadsAchievement;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import lombok.Data;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * LeadsAchievementReportContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 17:08
 */
@Data
public class LeadsAchievementReportContext {
    /** 查询条件 **/
    private Integer pageNo;

    private Integer pageSize;

    private Integer bid;

    private Long selectStaffId;

    private Long selectDeptId;

    private String groupByFiled;

    private String startEventTime;

    private String endEventTime;

    private String startDistributeTime;

    private String endDistributeTime;

    private String startCreateTime;

    private String endCreateTime;

    private Long currentStaffId;

    private Set<Long> hasPermissionDeptIds;

    /** 中间变量 **/
    private SearchSourceBuilder queryDataSource;

    private Script script;

    private BoolQueryBuilder boolQueryBuilder;

    private SearchSourceBuilder queryCountSource;

    private TermsAggregationBuilder aggregationBuilder;

    private Map<Long, BigDecimal> leadsAndDepositMap = Maps.newHashMap();

    private Map<Long, BigDecimal> leadsAndPayAmountMap = Maps.newHashMap();

    /** ES查询返回 **/
    private SearchResponse response;

    /** 解析后封装的结果集 **/
    private List<LeadsAchievementReportResponse> reportResult;

    private Integer resultCount;
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 线索扩展表-信息类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface LeadsExtInformationManager extends IService<LeadsExtInformation> {

    LeadsExtInformation getLastUpdate(Integer bid,Integer channel);

    LeadsExtInformation getLeadsExtInformationSelectId(Integer bid, String externalId);

    List<LeadsExtInformation> getByIds(Integer bid, Set<Long> leadsIds);
}

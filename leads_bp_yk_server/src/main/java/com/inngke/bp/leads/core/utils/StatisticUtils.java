package com.inngke.bp.leads.core.utils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021/8/19 9:38 PM
 */
public class StatisticUtils {
    private StatisticUtils() {
    }

    public static int getStatisticDate(LocalDateTime time) {
        return (time.getYear() % 100) * 10000 + time.getMonthValue() * 100 + time.getDayOfMonth();
    }

    public static String sum(String filed) {
        return "SUM(" + filed + ") AS " + filed;
    }

    public static String max(String filed) {
        return "MAX(" + filed + ") AS " + filed;
    }

}

package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 有效客户
 * Created by emiya on 2022/6/14 15:31
 *
 * <AUTHOR>
 * @date 2022/6/14 15:31
 */
@Component(value = "LeadsValidStrategy")
public class LeadsValidStrategy implements LeadsFollowTimeStrategy {

    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateContact(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "validCount",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.rangeQuery("stateContact").gt(0))
                                .mustNot(QueryBuilders.termsQuery("status",
                                        Lists.newArrayList(LeadsStatusEnum.DISTRIBUTED.getStatus(),LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.LOST.getStatus())))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        // 获取无效客户数
//        ParsedFilter noAvailFilter = bucketAggregations.get("stateNoAvail");
//        long docCount = noAvailFilter.getDocCount();
        // 获取已联系客户数
        ParsedFilter contactFilter = bucketAggregations.get("validCount");
        long contactCount = contactFilter.getDocCount();

        dto.setStateAvail((int) contactCount);
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer stateAvail = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateAvail).reduce(Integer::sum).get();
        dto.setStateAvail(stateAvail);
    }
}

package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Component;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeOfferPriceStrategy")
public class LeadsFollowTimeOfferPriceStrategy implements LeadsFollowTimeStrategy {
    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateOfferPrice(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateOfferPrice",
                        QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("stateOfferPrice").gt(0)))
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
    }
}

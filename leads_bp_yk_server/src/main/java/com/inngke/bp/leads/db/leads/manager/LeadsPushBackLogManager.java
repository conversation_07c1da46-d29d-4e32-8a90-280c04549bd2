/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.bp.leads.dto.CommitLeadsPushBackLogDto;
import com.inngke.bp.leads.dto.RejectPushBackDto;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 线索退回记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
public interface LeadsPushBackLogManager extends IService<LeadsPushBackLog> {

    /**
     * getById
     * @param bid 租户id
     * @param id logId
     * @return Entity
     */
    LeadsPushBackLog findLeadsPushBackLogById(Integer bid, Long id);

    /**
     * 获取线索最近的退回记录
     * @param bid 商户id
     * @param leadsId 线索id
     * @param isReject 是否被驳回
     * @return Entity
     */
    LeadsPushBackLog findLastOneLeadsPushBackLogByLeadsId(Integer bid, Long leadsId, Integer isReject);

    /**
     * 批量获取线索最近的跟进记录
     *
     * @param bid 商户id
     * @param leadsIds 线索id
     * @param isReject 是否驳回
     * @return
     */
    List<LeadsPushBackLog> batchFindLastOneLeadsPushBackLogByLeadsId(Integer bid, Set<Long> leadsIds, Integer isReject);

    /**
     * 提交线索退回表单
     *
     * @param commitLeadsPushBackLog 提交表单内容
     * @return logId
     */
    Long commitPushBackLog(CommitLeadsPushBackLogDto commitLeadsPushBackLog);

    /**
     * 驳回线索退回申请
     * @param refusePushBack 拒绝申请表单
     * @return
     */
    Long rejectPushBack(RejectPushBackDto refusePushBack);
}

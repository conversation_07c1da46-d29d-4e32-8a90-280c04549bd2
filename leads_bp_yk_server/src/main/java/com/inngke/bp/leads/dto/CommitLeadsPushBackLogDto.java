package com.inngke.bp.leads.dto;

import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * CommitLeadsPushBackLogDto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/6 15:14
 */
public class CommitLeadsPushBackLogDto implements Serializable {
    private Long id;

    private Integer bid;

    private Long leadsId;

    private Long pushBackReasonId;

    private String pushBackReason;

    private List<String> pushBackReasonImages;

    private LeadsSnapshotDto leadsSnapshot;

    private Integer leadsStatus;

    private Long pushBackBy;

    private Long pushBackTime;

    private Long createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getPushBackReasonId() {
        return pushBackReasonId;
    }

    public void setPushBackReasonId(Long pushBackReasonId) {
        this.pushBackReasonId = pushBackReasonId;
    }

    public String getPushBackReason() {
        return pushBackReason;
    }

    public void setPushBackReason(String pushBackReason) {
        this.pushBackReason = pushBackReason;
    }

    public List<String> getPushBackReasonImages() {
        return pushBackReasonImages;
    }

    public void setPushBackReasonImages(List<String> pushBackReasonImages) {
        this.pushBackReasonImages = pushBackReasonImages;
    }

    public Integer getLeadsStatus() {
        return leadsStatus;
    }

    public void setLeadsStatus(Integer leadsStatus) {
        this.leadsStatus = leadsStatus;
    }

    public Long getPushBackBy() {
        return pushBackBy;
    }

    public void setPushBackBy(Long pushBackBy) {
        this.pushBackBy = pushBackBy;
    }

    public Long getPushBackTime() {
        return pushBackTime;
    }

    public void setPushBackTime(Long pushBackTime) {
        this.pushBackTime = pushBackTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public LeadsSnapshotDto getLeadsSnapshot() {
        return leadsSnapshot;
    }

    public void setLeadsSnapshot(LeadsSnapshotDto leadsSnapshot) {
        this.leadsSnapshot = leadsSnapshot;
    }

    public LeadsPushBackLog convertLeadsPushBackLog() {
        LeadsPushBackLog leadsPushBackLog = new LeadsPushBackLog();
        leadsPushBackLog.setId(Optional.ofNullable(this.getId()).orElse(SnowflakeHelper.getId()));
        leadsPushBackLog.setBid(this.getBid());
        leadsPushBackLog.setLeadsId(this.getLeadsId());
        leadsPushBackLog.setPushBackReasonId(this.getPushBackReasonId());
        leadsPushBackLog.setPushBackReason(this.getPushBackReason());
        leadsPushBackLog.setPushBackReasonImages(JsonUtil.toJsonString(this.getPushBackReasonImages()));
        leadsPushBackLog.setLeadsStatus(this.getLeadsStatus());
        leadsPushBackLog.setLeadsSnapshot(JsonUtil.toJsonString(this.getLeadsSnapshot()));
        leadsPushBackLog.setPushBackBy(this.getPushBackBy());
        leadsPushBackLog.setPushBackTime(Optional.ofNullable(this.getPushBackTime()).map(DateTimeUtils::MillisToLocalDateTime).orElse(LocalDateTime.now()));
        leadsPushBackLog.setCreateTime(Optional.ofNullable(this.getCreateTime()).map(DateTimeUtils::MillisToLocalDateTime).orElse(LocalDateTime.now()));
        return leadsPushBackLog;

    }
}

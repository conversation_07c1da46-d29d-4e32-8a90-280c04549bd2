/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.controller;


import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.service.PreFollowStaffService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 分配客服规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
@RestController
@RequestMapping("/api/pre-follow-staff")
public class LeadsPreFollowStaffController {

    @Resource
    private PreFollowStaffService preFollowStaffService;

    /**
     * 客服列表
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    筛选条件
     * @return 线索列表
     */
    @GetMapping("/list")
    public BaseResponse<List<SimplePreFollowStaffDto>> list(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            PreFollowStaffListRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return preFollowStaffService.list(request);
    }

    /**
     * 获取所有客服和设置
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    筛选条件
     */
    @GetMapping("/preFollowAndSet")
    public BaseResponse<List<PreFollowStaffAndSetDto>> preFollowAndSet(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            PreFollowStaffListRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return preFollowStaffService.preFollowAndSet(request);
    }

    /**
     * 通过条件查询客服
     * 废弃改为通过web_api_yk的WebLoginController.getUserData方法返回的role参数判断是否啥客服
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    筛选条件
     * @return 线索
     */
    @Deprecated
    @GetMapping("/getFollowStaff")
    public BaseResponse<SimplePreFollowStaffDto> getFollowStaff(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            GetPreFollowStaffRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setStaffId(jwtPayload.getSid());
        return preFollowStaffService.getPreFollowStaff(request);
    }


    /**
     * 通过条件查询业绩报表
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return
     */
    @GetMapping("/getFollowStaffReport")
    public BaseResponse<List<PreFollowStaffReportDto>> getFollowStaffReport(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            GetPreFollowStaffReportRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return preFollowStaffService.getPreFollowStaffReport(request);
    }


    /**
     * 添加规则
     *
     * @param bid
     * @param operatorId
     * @param request
     * @return
     */
    @PostMapping("/addRule")
    public BaseResponse<Boolean> addRule(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated PreFollowStaffAddRuleRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return preFollowStaffService.addRule(request);
    }


    /**
     * 修改规则
     */
    @PutMapping("/updateRule")
    public BaseResponse<Boolean> updateRule(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated PreFollowStaffUpdateRuleRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return preFollowStaffService.updateRule(request);
    }

    /**
     * 获取该客服的规则列表
     *
     * @param bid bid
     * @param id 员工Id
     * @return 规则列表
     */
    @GetMapping("/getRulesByStaffId/{id:\\d+}")
    public BaseResponse<List<SimplePreFollowStaffRuleDto>> getRulesByStaffId(
            @RequestHeader int bid,
            @PathVariable Long id
    ) {
        return preFollowStaffService.getRulesByStaffId(bid, id);
    }

    /**
     * 获取客服所有的渠道
     *
     * @param bid bid
     * @param id  员工Id
     * @return 规则列表
     */
    @GetMapping("/staffChannelRegion/{id:\\d+}")
    public BaseResponse<GetStaffAllChannelAndRegionDto> staffChannelRegion(
            @RequestHeader int bid,
            @PathVariable Long id
    ) {
        return preFollowStaffService.staffChannelRegion(bid, id);
    }

    /**
     * 规则详情
     *
     * @param bid bid
     * @param id  规则Id
     * @return 规则详情
     */
    @GetMapping("/getRuleDetail/{id:\\d+}")
    public BaseResponse<FollowStaffRuleDto> getRuleDetail(
            @RequestHeader int bid,
            @PathVariable Long id
    ) {
        return preFollowStaffService.getRuleDetail(bid, id);
    }

    /**
     * 获取该客服已选择的渠道
     *
     * @param bid bid
     * @param id  员工Id
     * @return 渠道列表
     */
    @GetMapping("/getChooseChannelsByStaffId/{id:\\d+}")
    public BaseResponse<List<SimpleChannelDto>> getChooseChannelsByStaffId(
            @RequestHeader int bid,
            @PathVariable Long id
    ) {
        return preFollowStaffService.getChooseChannelsByStaffId(bid, id);
    }

}


package com.inngke.bp.leads.core.converter;

import com.inngke.bp.organize.dto.response.StaffIdAndAgentIdDto;
import com.inngke.bp.user.dto.response.AgentIdAndNameDto;
import com.inngke.bp.user.dto.response.StaffIdAndNameInfoVo;

/**
 * <AUTHOR>
 * @since 2022/8/10 15:52
 **/
public class StaffAndAgentConverter {

    /**
     * 实体之间类型转换
     */
    public static AgentIdAndNameDto toAgentIdAndNameDto(StaffIdAndAgentIdDto staffIdAndAgentIdDto) {
        AgentIdAndNameDto agentIdAndNameDto = new AgentIdAndNameDto();
        agentIdAndNameDto.setId(staffIdAndAgentIdDto.getAgentId());
        agentIdAndNameDto.setName(staffIdAndAgentIdDto.getAgentName());
        return agentIdAndNameDto;
    }

    public static StaffIdAndNameInfoVo toStaffIdAndNameInfoVo(StaffIdAndAgentIdDto staffIdAndAgentIdDto) {
        StaffIdAndNameInfoVo staffIdAndNameInfoVo = new StaffIdAndNameInfoVo();
        staffIdAndNameInfoVo.setId(staffIdAndAgentIdDto.getId());
        staffIdAndNameInfoVo.setName(staffIdAndAgentIdDto.getAgentName());
        staffIdAndNameInfoVo.setShopAgentId(staffIdAndAgentIdDto.getAgentId());
        return staffIdAndNameInfoVo;
    }

}

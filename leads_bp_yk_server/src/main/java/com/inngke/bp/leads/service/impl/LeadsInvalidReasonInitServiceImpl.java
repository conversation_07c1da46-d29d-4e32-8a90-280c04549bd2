package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.MerchantClientForLeads;
import com.inngke.bp.leads.db.leads.entity.LeadsInvalidReason;
import com.inngke.bp.leads.db.leads.manager.LeadsInvalidReasonManager;
import com.inngke.bp.leads.dto.request.LeadsInvalidReasonSaveRequest;
import com.inngke.bp.leads.service.LeadsInvalidReasonService;
import com.inngke.bp.leads.service.leadsInvalidReasonInitService;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class LeadsInvalidReasonInitServiceImpl implements leadsInvalidReasonInitService {

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @Autowired
    private LeadsInvalidReasonService leadsInvalidReasonService;

    @Autowired
    private LeadsInvalidReasonManager leadsInvalidReasonManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private MerchantClientForLeads merchantClientForLeads;



    @Override
    public BaseResponse init() {
        List<String> reasonList = getInitReasons();

        merchantClientForLeads.getMerchantList().forEach(bid ->{
            BaseBidRequest request = new BaseBidRequest();
            request.setBid(bid);

            List<LeadsInvalidReason> leadsInvalidReasonList = leadsInvalidReasonManager.list(Wrappers.<LeadsInvalidReason>query()
                    .eq(LeadsInvalidReason.BID, request.getBid())
                    .eq(LeadsInvalidReason.ENABLE, 1));

            if (leadsInvalidReasonList.size() > 0) {
                return;
            }
            reasonList.forEach(reason -> {
                LeadsInvalidReasonSaveRequest leadsInvalidReasonSaveRequest = new LeadsInvalidReasonSaveRequest();
                leadsInvalidReasonSaveRequest.setReason(reason);
                leadsInvalidReasonSaveRequest.setBid(bid);
                leadsInvalidReasonService.save(leadsInvalidReasonSaveRequest);
            });
        });
        return BaseResponse.success();
    }

    private List<String> getInitReasons() {
        List<String> reasonList = Lists.newArrayList();
        reasonList.add("客户明确表示无需求");
        reasonList.add("客户地址超出门店服务范围");
        reasonList.add("多次联系不上/空号");
        reasonList.add("添加微信好友不成功");
        reasonList.add("其他");
        return reasonList;
    }

}

package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.common.db.card.entity.ShopOrder;
import com.inngke.bp.common.db.card.manager.ShopOrderManager;
import com.inngke.bp.leads.client.CardClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.converter.LeadsStatisticsConverter;
import com.inngke.bp.leads.core.utils.StatisticUtils;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsConf;
import com.inngke.bp.leads.db.leads.manager.LeadsConfManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsEsInfoGetService;
import com.inngke.bp.leads.service.LeadsStatisticsService;
import com.inngke.bp.organize.dto.request.card.GetCardByStaffIdsRequest;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDepartmentAgentSimpleDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.card.CardDto;
import com.inngke.bp.store.dto.request.GetLeadsStoreOrderRequest;
import com.inngke.bp.store.dto.request.StoreOrderMobileQuery;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.attachment.service.InngkeUploaderService;
import com.inngke.common.core.utils.StaticResourceUtils;
import com.inngke.common.ds.annotation.DS;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.ExcelUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.inngke.bp.leads.enums.LeadsStatusEnum.getNonAllocatedLeadsStatus;

/**
 * <AUTHOR>
 * @since 2021/9/8 10:45 PM
 */
@Service
@DubboService(version = "1.0.0")
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsStatisticsServiceImpl implements LeadsStatisticsService {
    private static final Logger logger = LoggerFactory.getLogger(LeadsStatisticsServiceImpl.class);

    @Autowired
    private CardClientForLeads cardClientForLeads;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private ShopOrderManager shopOrderManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store_bp_yk:}")
    private StoreOrderService storeOrderService;

    @Autowired
    private InngkeUploaderService inngkeUploaderService;

    // @Autowired
    // private LeadsStaffCache leadsStaffCache;

    @Autowired
    private LeadsConfManager leadsConfManager;

    @Autowired
    private LeadsEsInfoGetService leadsEsInfoGetService;

    @Autowired
    private RestHighLevelClient esClient;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    private static final String SHEET_NAME = "leadsStatisticsInfos";

    /**
     * 线索统计查询，先根据过滤条件，查询出基本信息，后根据聚合的customerIds、Mobiles，分别去查询商城订单，和门店订单的的订单金额，订单数，之后聚合统计结果
     *
     * @param request 筛选条件
     * @return 统计信息列表
     */
    @Override
    public BaseResponse<BasePaginationResponse<LeadsStatisticsDto>> getLeadsStatistics(LeadsStatisticsQuery request) {
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = 20;
        }
        pageNo = (pageNo - 1) * pageSize;

        //构建分页返回结果
        BasePaginationResponse<LeadsStatisticsDto> result = new BasePaginationResponse<>();
        List<LeadsStatisticsDto> dtoList = Lists.newArrayList();

        Script script = new Script("doc['distributeAgentId']+'-'+doc['distributeStaffId']");

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder qb = QueryBuilders.boolQuery();
        qb.must(QueryBuilders.termQuery("bid", request.getBid()));
        qb.mustNot(QueryBuilders.termQuery("distributeStaffId", 0));
        if (request.getAgentId() != null) {
            qb.must(QueryBuilders.termQuery("distributeAgentId", request.getAgentId()));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeStart())) {
            qb.must(QueryBuilders.rangeQuery("distributeTime").gte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeEnd())) {
            qb.must(QueryBuilders.rangeQuery("distributeTime").lte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeEnd())));
        }
        qb.mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(0, -4, -5)));
        sourceBuilder.query(qb);
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("groupFiled").script(script)
                .subAggregation(
                        AggregationBuilders.terms("statusCount").field("status")
                )
                .subAggregation(
                        AggregationBuilders.filter(
                                "contactIn24",
                                QueryBuilders.boolQuery().must(QueryBuilders.termQuery("contactIn24", 1)).mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(-3, -4, -5))))
                )
                .subAggregation(
                        AggregationBuilders.filter(
                                "ContactNotInCount",
                                QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(3)))
                                        .mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(-3, -4, -5)))
                                        .mustNot(QueryBuilders.termQuery("contactIn24", 1)))
                )
                .subAggregation(
                        AggregationBuilders.filter(
                                "reachTheStoreCount",
                                QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(6))).mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(-3, -4, -5))))
                )
                .subAggregation(AggregationBuilders.topHits("record"))
                .size(10000)
                .subAggregation(new BucketSortPipelineAggregationBuilder("bucket_field", null).from(pageNo).size(pageSize));
        if (null != request.getAgentId()) {
        }
        aggregation.size(pageSize);
        sourceBuilder.aggregation(aggregation);
        sourceBuilder.size(0);
        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(sourceBuilder);
        SearchResponse response = null;
        List<? extends Terms.Bucket> buckets = Lists.newArrayList();
        try {
            logger.info("========>{}" + sourceBuilder.toString());
            response = esClient.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = response.getAggregations();
            ParsedStringTerms parsedStringTerms = aggregations.get("groupFiled");
            buckets = parsedStringTerms.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                LeadsStatisticsDto dto = new LeadsStatisticsDto();
                //获取staffId和AgentId
                String key = bucket.getKey().toString();
                String[] split = key.split("-");
                dto.setAgentId(Long.valueOf(split[0].substring(1, split[0].length() - 1)));
                dto.setStaffId(Long.valueOf(split[1].substring(1, split[1].length() - 1)));
                //获取总线索数
                long docCount = bucket.getDocCount();
                dto.setLeadsCount((int) docCount);
                Aggregations bucketAggregations = bucket.getAggregations();
                //获取doc, concat uid,mobile
                ParsedTopHits record = bucketAggregations.get("record");
                SearchHit[] hits = record.getHits().getHits();
                List<SearchHit> searchHits = Lists.newArrayList(hits);
                List<LeadsEsDto> esDtoList = searchHits.stream().map(SearchHit::getSourceAsString).map(item -> jsonService.toObject(item, LeadsEsDto.class)).collect(Collectors.toList());
                Set<String> mobileSet = esDtoList.stream().map(LeadsEsDto::getMobile).collect(Collectors.toSet());
                Set<Long> uidSet = esDtoList.stream().map(LeadsEsDto::getCustomerUid).collect(Collectors.toSet());
                dto.setMobiles(mobileSet);
                dto.setCustomerUids(uidSet);
                //获取24小时内联系线索数
                ParsedFilter contactIn24 = bucketAggregations.get("contactIn24");
                long status2Count = contactIn24.getDocCount();
                dto.setStatus2((int) status2Count);
                //获取24小时后联系数
                ParsedFilter contactNotInCount = bucketAggregations.get("ContactNotInCount");
                long status3Count = contactNotInCount.getDocCount();
                dto.setStatus3((int) status3Count);
                //获取到店线索数
                ParsedFilter reachTheStoreCount = bucketAggregations.get("reachTheStoreCount");
                long status6Count = reachTheStoreCount.getDocCount();
                dto.setStatus6((int) status6Count);
                //获取其他状态统计
                ParsedTerms statusCount = bucketAggregations.get("statusCount");
                List<? extends Terms.Bucket> statusCountBuckets = statusCount.getBuckets();
                for (Terms.Bucket statusCountBucket : statusCountBuckets) {
                    String statusKey = statusCountBucket.getKey().toString();
                    switch (statusKey) {
                        case "-3":
                            long statusInvalid = statusCountBucket.getDocCount();
                            dto.setStatusInvalid(Optional.of(statusInvalid).orElse(0L).intValue());
                            break;
                        case "-2":
                            long statusDistributeError = statusCountBucket.getDocCount();
                            dto.setStatusDistributeError(Optional.of(statusDistributeError).orElse(0L).intValue());
                            break;
                        case "-1":
                            long statusPushBack = statusCountBucket.getDocCount();
                            dto.setStatusPushBack(Optional.of(statusPushBack).orElse(0L).intValue());
                            break;
                        case "0":
                            long status0Count = statusCountBucket.getDocCount();
                            dto.setStatus0(Optional.of(status0Count).orElse(0L).intValue());
                            break;
                        case "1":
                            long status1Count = statusCountBucket.getDocCount();
                            dto.setStatus1(Optional.of(status1Count).orElse(0L).intValue());
                            break;
                        case "4":
                            long status4Count = statusCountBucket.getDocCount();
                            dto.setStatus4(Optional.of(status4Count).orElse(0L).intValue());
                            break;
                        case "5":
                            long status5Count = statusCountBucket.getDocCount();
                            dto.setStatus5(Optional.of(status5Count).orElse(0L).intValue());
                            break;
                        case "7":
                            long status7Count = statusCountBucket.getDocCount();
                            dto.setStatus7(Optional.of(status7Count).orElse(0L).intValue());
                            break;
                        case "8":
                            long status8Count = statusCountBucket.getDocCount();
                            dto.setStatus8(Optional.of(status8Count).orElse(0L).intValue());
                            break;
                        case "9":
                            long status9Count = statusCountBucket.getDocCount();
                            dto.setStatus9(Optional.of(status9Count).orElse(0L).intValue());
                            break;
                        case "10":
                            long status10Count = statusCountBucket.getDocCount();
                            dto.setStatus10(Optional.of(status10Count).orElse(0L).intValue());
                            break;
                        case "11":
                            long status11Count = statusCountBucket.getDocCount();
                            dto.setStatus11(Optional.of(status11Count).orElse(0L).intValue());
                            break;
                    }
                }
                dtoList.add(dto);
            }

        } catch (IOException exception) {
            logger.error("获取ES数据失败:", exception);
        }
        statisticsOrder(request.getBid(), dtoList, request.getDistributeTimeStart(), request.getDistributeTimeEnd());

        result.setList(dtoList);
        result.setTotal(buckets.size());
        return BaseResponse.success(result);
    }

    /**
     * 统计订单数量和订单金额，通过手机号，staffId，线索最早的分配时间 >= 订单的创建时间，来统计线索聚合的订单信息
     *
     * @param records 初步统计线索得到的DTO
     */
    private void statisticsOrder(Integer bid, List<LeadsStatisticsDto> records, String startTime, String endTime) {
        //构建所有列的uid、mobiles集合，发起远程请求一次性获取所需要的订单数据，之后根据关联字段匹配结果
        Set<Long> uids = new HashSet<>();
        Set<String> mobiles = new HashSet<>();
        for (LeadsStatisticsDto record : records) {
            uids.addAll(record.getCustomerUids());
            mobiles.addAll(record.getMobiles());
        }
        //查询CustomerUid对应的线索的最早分配时间，用户筛选客户订单
        Map<Long, LocalDateTime> uidDistributeTimeCache = leadsManager.list(
                Wrappers.<Leads>query()
                        .isNotNull(Leads.DISTRIBUTE_TIME)
                        .eq(Leads.BID, bid)
                        .groupBy(Leads.CUSTOMER_UID)
                        .notIn(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.RECOVERY.getStatus(), LeadsStatusEnum.DELETED.getStatus()))
                        .ge(!StringUtils.isEmpty(startTime), Leads.DISTRIBUTE_TIME, DateTimeUtils.toLocalDateTime(startTime))
                        .le(!StringUtils.isEmpty(endTime), Leads.DISTRIBUTE_TIME, DateTimeUtils.toLocalDateTime(endTime))
                        .select(Leads.CUSTOMER_UID, "min(distribute_time) as distributeTime")
        ).stream().collect(Collectors.toMap(Leads::getCustomerUid, Leads::getDistributeTime));
        //查询手机号对应的线索的最早分配时间，用户筛选客户订单
        Map<String, LocalDateTime> mobileDistributeTimeCache = leadsManager.list(
                Wrappers.<Leads>query()
                        .isNotNull(Leads.DISTRIBUTE_TIME)
                        .eq(Leads.BID, bid)
                        .groupBy(Leads.MOBILE)
                        .notIn(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.RECOVERY.getStatus(), LeadsStatusEnum.DELETED.getStatus()))
                        .ge(!StringUtils.isEmpty(startTime), Leads.DISTRIBUTE_TIME, DateTimeUtils.toLocalDateTime(startTime))
                        .le(!StringUtils.isEmpty(endTime), Leads.DISTRIBUTE_TIME, DateTimeUtils.toLocalDateTime(endTime))
                        .select(Leads.MOBILE, "min(distribute_time) as distributeTime")
        ).stream().collect(Collectors.toMap(Leads::getMobile, Leads::getDistributeTime));

        //首先查询商城订单
        List<ShopOrder> shopOrders = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(uids)) {
            shopOrders = shopOrderManager.list(Wrappers.<ShopOrder>query()
                    .in(ShopOrder.UID, uids)
                    .eq(ShopOrder.BEID, bid)
                    .select(ShopOrder.UID, ShopOrder.PRICE, ShopOrder.CARD_ID, ShopOrder.CREATETIME, ShopOrder.ID)
                    .in(ShopOrder.STATUS, Lists.newArrayList(1, 2, 3)));
        }

        BaseResponse<List<StoreOrderDto>> storeOrders = new BaseResponse<>();
        //发起远程请求，查询门店的订单数据
        if (!CollectionUtils.isEmpty(mobiles)) {
            StoreOrderMobileQuery storeRequest = new StoreOrderMobileQuery();
            storeRequest.setMobiles(mobiles);
            storeRequest.setBid(bid);
            storeOrders = storeOrderService.getStoreOrderListByMobile(storeRequest);
        }

        // 最后的统计结果，订单数、订单金额
        int orderCount = 0;
        BigDecimal orderAmount = new BigDecimal(0);

        //商城订单，通过uid筛选
        List<Long> staffIds = records.stream().map(LeadsStatisticsDto::getStaffId).collect(Collectors.toList());
        GetCardByStaffIdsRequest getCardByStaffIdRequest = new GetCardByStaffIdsRequest();
        getCardByStaffIdRequest.setFieldSet(Sets.newHashSet("id"));
        getCardByStaffIdRequest.setIds(staffIds);
        getCardByStaffIdRequest.setBid(bid);
        Map<Long, CardDto> cardInfos = cardClientForLeads.getCardInfos(getCardByStaffIdRequest);

        //这里统计聚合后的订单数量
        for (LeadsStatisticsDto record : records) {
            //先把单条记录的手机号和Uid聚合起来
            Set<Long> recordUid = Sets.newHashSet(record.getCustomerUids());
            Set<String> recordMobile = Sets.newHashSet(record.getMobiles());
            //获取staffId对应的cardId，用以匹配小程序订单

            CardDto cardDto = cardInfos.get(record.getStaffId());
            List<ShopOrder> shopOrder = shopOrders.stream().filter(
                            item -> recordUid.contains(item.getUid())
                                    && item.getCreatetime() >= DateTimeUtils.getMilli(uidDistributeTimeCache.get(item.getUid())) / 1000
                                    && item.getCardId().equals(cardDto.getId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(shopOrder)) {
                BigDecimal orderPrice = CollectionUtils.isEmpty(shopOrder) ? BigDecimal.ZERO : shopOrder.stream().map(ShopOrder::getPrice).reduce(BigDecimal::add).get();
                orderCount += shopOrder.size();
                orderAmount = orderAmount.add(orderPrice);
            }

            //门店订单，通过mobile筛选
            if (storeOrders.getCode() == 0 && !CollectionUtils.isEmpty(storeOrders.getData())) {
                List<StoreOrderDto> storeOrderDto = storeOrders.getData();
                List<StoreOrderDto> storeOrderDtoList = storeOrderDto
                        .stream()
                        .filter(
                                item -> recordMobile.contains(item.getCustomerMobile())
                                        && item.getStaffId().equals(record.getStaffId())
                                        && item.getCreateTime() >= DateTimeUtils.getMilli(mobileDistributeTimeCache.get(item.getCustomerMobile()))
                        ).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(storeOrderDtoList)) {
                    BigDecimal storePrice = storeOrderDtoList.stream().map(StoreOrderDto::getOrderAmount).map(BigDecimal::new).reduce(BigDecimal::add).get();
                    orderCount += storeOrderDtoList.size();
                    orderAmount = orderAmount.add(storePrice);
                }
            }
            record.setOrderCount(orderCount);
            record.setOrderTotalAmount(orderAmount);
            //清空数据
            orderCount = 0;
            orderAmount = new BigDecimal(0);
        }
    }

    /**
     * 导出线索统计查询
     *
     * @param request 筛选条件
     * @return 生成的统计文件链接
     */
    @Override
    public BaseResponse<String> getLeadsStatisticsExport(LeadsStatisticsQuery request) {
        BaseResponse<BasePaginationResponse<LeadsStatisticsDto>> leadsStatistics = this.getLeadsStatistics(request);
        if (!BaseResponse.responseSuccessWithNonNullData(leadsStatistics)) {
            return BaseResponse.error("没有需要导出的数据");
        }
        List<LeadsStatisticsDto> list = leadsStatistics.getData().getList();

        //转换为excel实体类
        List<LeadsStatisticsExcelDto> dto = LeadsStatisticsConverter.toLeadsStatisticsExcelDto(list);
        List<com.inngke.bp.leads.dto.LeadsStatisticsExcelDto> excelDto = new ArrayList<>();
        Integer bid = request.getBid();
        for (LeadsStatisticsExcelDto leadsStatisticsExcelDto : dto) {
            Long staffId = leadsStatisticsExcelDto.getStaffId();

            com.inngke.bp.leads.dto.LeadsStatisticsExcelDto exDto = new com.inngke.bp.leads.dto.LeadsStatisticsExcelDto();
            //获取负责人name
            BaseIdsRequest staffListRequest = new BaseIdsRequest();
            staffListRequest.setIds(Lists.newArrayList(staffId));
            staffListRequest.setBid(bid);
            StaffDepartmentAgentSimpleDto staffDepartmentAgentSimpleDto = staffClientForLeads.getStaffDepartmentAgentSimple(staffListRequest).get(0);

            BeanUtils.copyProperties(leadsStatisticsExcelDto, exDto);
            exDto.setStaffName(staffDepartmentAgentSimpleDto.getStaffName());
            //获取经销商name
            exDto.setAgentName(staffDepartmentAgentSimpleDto.getAgentName());
            excelDto.add(exDto);
        }

        ExcelUtils<com.inngke.bp.leads.dto.LeadsStatisticsExcelDto> excelUtils = new ExcelUtils<>(com.inngke.bp.leads.dto.LeadsStatisticsExcelDto.class);
        //将文件上传到OSS服务器，返回文件地址
        File file = excelUtils.writeExcelFile(excelDto, SHEET_NAME, "转化数据.xlsx");
        try {
            String url = inngkeUploaderService.builder(bid, request.getOperatorId(), null)
                    .setModelName(bid + "/leads")
                    .addMeteData(InngkeApiConst.STR_BID, String.valueOf(bid))
                    .build()
                    .uploadFile(file);
            return BaseResponse.success(StaticResourceUtils.getFullUrl(url));
        } catch (Exception e) {
            logger.error("OSS文件上传失败", e);
            return BaseResponse.error("文件上传失败，请重试");
        } finally {
            file.delete();
        }
    }

    /**
     * 查询待跟进线索数
     *
     * @param request 请求
     * @return 待跟进线索数
     */
    @Override
    public BaseResponse<LeadsStatusDto> getLeadsStatusStatistics(LeadsStatusRequest request) {
        List<Leads> statusCount = leadsManager.list(Wrappers.<Leads>query()
                .eq(Leads.BID, request.getBid())
                .eq(Leads.DISTRIBUTE_STAFF_ID, request.getStaffId())
                .eq(Leads.CLIENT_ID, 0)
                .select(Leads.STATUS, Leads.LAST_FOLLOW_ID)
        );

        LeadsConf leadsConf = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, request.getBid()));
        boolean leadsEnable = false;
        if (!ObjectUtils.isNull(leadsConf)) {
            leadsEnable = leadsConf.getEnable() != null && leadsConf.getEnable();
        }
        //已完成数量
        long all = statusCount.stream().filter(e -> {
            return !Lists.newArrayList(
                    LeadsStatusEnum.RECOVERY.getStatus(), LeadsStatusEnum.DELETED.getStatus(),
                    LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(), LeadsStatusEnum.PUSH_BACK.getStatus(), LeadsStatusEnum.TO_DISTRIBUTE.getStatus()
            ).contains(e.getStatus());
        }).count();

        long completedCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.TRADED.getStatus())).count();

        long notContactedCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.DISTRIBUTED.getStatus())).count();

        long followedCount = statusCount.stream().filter(status -> (status.getStatus() > LeadsStatusEnum.DISTRIBUTED.getStatus()) && (status.getStatus() < LeadsStatusEnum.TRADED.getStatus())
        ).count();

        LeadsStatusDto leadsStatusDto = new LeadsStatusDto();
        leadsStatusDto.setAllLeadsCount((int) all);
        leadsStatusDto.setLeadsEnable(leadsEnable);
        leadsStatusDto.setNotContactedCount((int) notContactedCount);
        leadsStatusDto.setCompletedCount((int) completedCount);
        leadsStatusDto.setFollowedCount((int) followedCount);
        return BaseResponse.success(leadsStatusDto);
    }

    /**
     * 查询待跟进线索数
     *
     * @param request 请求
     * @return 待跟进线索数
     */
    @Override
    public BaseResponse<LeadsStatusDto> getLeadsStatusStatisticsForList(LeadsStatusRequest request) {
        List<Leads> statusCount = leadsManager.list(Wrappers.<Leads>query()
                .eq(Leads.BID, request.getBid())
                .eq(Leads.DISTRIBUTE_STAFF_ID, request.getStaffId())
                .notIn(Leads.STATUS, getNonAllocatedLeadsStatus())
                .between(Objects.nonNull(request.getStartDistributeTime()) && Objects.nonNull(request.getEndDistributeTime()), Leads.DISTRIBUTE_TIME, request.getStartDistributeTime(), request.getEndDistributeTime())
                .between(Objects.nonNull(request.getStartLastFollowTime()) && Objects.nonNull(request.getEndLastFollowTime()), Leads.LAST_FOLLOW_TIME, request.getStartLastFollowTime(), request.getEndLastFollowTime())
                .select(Leads.STATUS, Leads.LAST_FOLLOW_ID, Leads.ID)
        );

        LeadsConf leadsConf = leadsConfManager.getOne(Wrappers.<LeadsConf>query().eq(LeadsConf.ID, request.getBid()));
        boolean leadsEnable = false;
        if (!ObjectUtils.isNull(leadsConf)) {
            leadsEnable = leadsConf.getEnable() != null && leadsConf.getEnable();
        }
        Set<Integer> allStatus = Sets.newHashSet(
                LeadsStatusEnum.RECOVERY.getStatus(), LeadsStatusEnum.DELETED.getStatus(),
                LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(), LeadsStatusEnum.PUSH_BACK.getStatus(), LeadsStatusEnum.TO_DISTRIBUTE.getStatus()
        );
        // 全部
        long all = statusCount.stream().filter(e -> !allStatus.contains(e.getStatus())).count();
        // 待联系
        long notContactedCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.DISTRIBUTED.getStatus())).count();
        // 跟进中
        allStatus.addAll(Sets.newHashSet(LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus(),
                LeadsStatusEnum.LOST.getStatus(), LeadsStatusEnum.INVALID.getStatus(),
                LeadsStatusEnum.TRADED.getStatus(), LeadsStatusEnum.TO_INSTALL.getStatus()));

        long followingUpCount = statusCount.stream().filter(status -> !allStatus.contains(status.getStatus())).count();

        // 已签单
        GetLeadsStoreOrderRequest orderRequest = new GetLeadsStoreOrderRequest();
        orderRequest.setBid(request.getBid());
        orderRequest.setLeadsIds(statusCount.stream().map(Leads::getId).collect(Collectors.toSet()));
        Map<Long, List<StoreOrderDto>> orderResponse = storeOrderService.getStoreOrderListByLeads(orderRequest).getData();
        List<Long> orderLeadsIds = orderResponse.values().stream().flatMap(Collection::stream).filter(item -> item.getType() == 2).map(StoreOrderDto::getLeadsId).collect(Collectors.toList());

        List<Leads> orderLeadsList = statusCount.stream().filter(item -> orderLeadsIds.contains(item.getId())).collect(Collectors.toList());
        long tradedCount = orderLeadsList.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.TRADED.getStatus())).count();

        long storeOrderCount = orderLeadsList.size();
        // 已交付
        long installedCount = orderLeadsList.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.INSTALLED.getStatus())).count();
        // 待安装
        long toInstallCount = orderLeadsList.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.TO_INSTALL.getStatus())).count();

        // 已流失
        long lostCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.LOST.getStatus())).count();
        // 待再次联系
        long unsureIntentCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.UNSURE_INTENT.getStatus())).count();
        // 已下定
        long orderedCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.ORDERED.getStatus())).count();
        // 已量尺
        long measuredCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.MEASURED.getStatus())).count();
        // 已联系
        Set<Integer> contractStatusSet = Sets.newHashSet(LeadsStatusEnum.CONTACTED.getStatus(), LeadsStatusEnum.SUCCESS_CONTACT.getStatus(), LeadsStatusEnum.UNSURE_INTENT.getStatus());
        long contractedCount = statusCount.stream().filter(status -> contractStatusSet.contains(status.getStatus())).count();
        // 有意向
        long intentCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.INTENT.getStatus())).count();
        // 已到店
        long storedCount = statusCount.stream().filter(status -> status.getStatus().equals(LeadsStatusEnum.STORED.getStatus())).count();

        LeadsStatusDto leadsStatusDto = new LeadsStatusDto();
        leadsStatusDto.setAllLeadsCount((int) all);
        leadsStatusDto.setLeadsEnable(leadsEnable);
        leadsStatusDto.setNotContactedCount((int) notContactedCount);
        leadsStatusDto.setFollowingUpCount((int) followingUpCount);
        leadsStatusDto.setInstalledCount((int) installedCount);
        leadsStatusDto.setLostCount((int) lostCount);
        leadsStatusDto.setUnsureIntentCount((int) unsureIntentCount);
        leadsStatusDto.setOrderedCount((int) orderedCount);
        leadsStatusDto.setMeasuredCount((int) measuredCount);
        leadsStatusDto.setTradedCount((int) tradedCount);
        leadsStatusDto.setToInstallCount((int) toInstallCount);
        leadsStatusDto.setContractedCount((int) contractedCount);
        leadsStatusDto.setIntentCount((int) intentCount);
        leadsStatusDto.setStoredCount((int) storedCount);
        leadsStatusDto.setStoreOrderCount((int) storeOrderCount);
        return BaseResponse.success(leadsStatusDto);
    }

    /**
     * 获取某天各员工分配的线索数量
     *
     * @param request 请求
     * @return 某天各员工分配的线索数量
     */
    @Override
    public BaseResponse<List<StaffLeadsCountDto>> getStaffLeadsCount(StaffLeadsCountRequest request) {
        LocalDateTime start;
        if (request.getDate() != null) {
            Integer date = request.getDate();
            int year = date / 10000;
            int month = date % 10000 / 100;
            start = LocalDateTime.of(year, month, date % 100, 0, 0, 0);
        } else {
            start = LocalDateTime.now().toLocalDate().atStartOfDay().minusDays(1);
        }
        LocalDateTime end = start.minusDays(-1);

        Map<Long, StaffLeadsCountDto> staffLeadsCountMap = Maps.newHashMap();
        //分配的总数
        List<StaffLeadsCountDto> list = leadsManager.list(
                Wrappers.<Leads>query()
                        .ge(Leads.DISTRIBUTE_TIME, start)
                        .lt(Leads.DISTRIBUTE_TIME, end)
                        //临时存放在goods_num字段
                        .select(Leads.BID, Leads.DISTRIBUTE_STAFF_ID, StatisticUtils.sum(Leads.GOODS_NUM))
                        .groupBy(Leads.BID, Leads.DISTRIBUTE_STAFF_ID)
        ).stream().map(leads -> {
            StaffLeadsCountDto dto = new StaffLeadsCountDto();
            dto.setBid(leads.getBid());
            dto.setStaffId(leads.getDistributeStaffId());
            dto.setCount(leads.getGoodsNum());
            staffLeadsCountMap.put(leads.getDistributeStaffId(), dto);
            return dto;
        }).collect(Collectors.toList());

        leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED)
                        .gt(Leads.DISTRIBUTE_STAFF_ID, 0)
                        //临时存放在goods_num字段
                        .select(Leads.BID, Leads.DISTRIBUTE_STAFF_ID, StatisticUtils.sum(Leads.GOODS_NUM))
                        .groupBy(Leads.BID, Leads.DISTRIBUTE_STAFF_ID)
        ).forEach(leads -> {
            StaffLeadsCountDto dto = staffLeadsCountMap.computeIfAbsent(leads.getDistributeStaffId(), staffId -> {
                StaffLeadsCountDto counter = new StaffLeadsCountDto();
                counter.setBid(leads.getBid());
                counter.setStaffId(leads.getDistributeStaffId());
                counter.setCount(0);
                list.add(counter);
                return counter;
            });
            dto.setToFollowCount(leads.getGoodsNum());
        });

        return BaseResponse.success(list);
    }

    @Override
    public BaseResponse<Integer> getNotContactLeads(LeadsStatusRequest request) {

        int count = leadsManager.count(
                Wrappers.<Leads>query()
                        .eq(Leads.DISTRIBUTE_STAFF_ID, request.getStaffId())
                        .eq(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
        );
        return BaseResponse.success(count);
    }

    @Override
    public BaseResponse<List<LeadsEsDto>> getLeadsStatisticsByStaffName(LeadsStaffNameEsGetRequest request) {
        String staffName = request.getLeadsStaffName();

        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setName(staffName);
        List<StaffDto> staffList = staffClientForLeads.getStaffList(staffListRequest);

        if (CollectionUtils.isEmpty(staffList)) {
            logger.warn("未找到员工姓名：【{}】匹配的记录", staffName);
            throw new InngkeServiceException("未找到相应的员工信息");
        }
        Set<Long> staffIds = staffList.stream().map(StaffDto::getId).collect(Collectors.toSet());

        LeadsStaffIdsEsGetRequest staffIdsRequest = new LeadsStaffIdsEsGetRequest();
        staffIdsRequest.setStaffIds(staffIds);
        staffIdsRequest.setBid(request.getBid());
//        return leadsEsInfoGetService.getLeadsListByStaffIds(staffIdsRequest);
        return null;
    }

}

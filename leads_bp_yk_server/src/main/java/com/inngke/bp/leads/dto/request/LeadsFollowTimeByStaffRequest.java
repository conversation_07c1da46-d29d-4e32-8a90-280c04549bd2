package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/8 10:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LeadsFollowTimeByStaffRequest extends BaseBidOptPageRequest {

    /**
     * 1：总客户数、2：有效客户数、3：无效客户数、4：待联系、5：联系数、6：24小时联系数、7：成功联系数、8：量尺数、9：到店数、10：流失数、11：定金数、12：成交数
     */
    private Integer type;

    /**
     * 部门id
     *
     * @demo 1023
     */
    private Long departmentId;


    /**
     * 员工ID
     *
     * @demo 1434
     */
    private Long staffId;

    /**
     * 分配时间-起始，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-10 10:23:45
     */
    private String distributeTimeStart;

    /**
     * 分配时间-结束，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-11 10:23:45
     */
    private String distributeTimeEnd;

    /**
     * 创建时间-起始，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-10 10:23:45
     */
    private String createTimeStart;

    /**
     * 创建时间-结束，yyyy-MM-dd HH:mm:ss格式
     *
     * @demo 2021-09-11 10:23:45
     */
    private String createTimeEnd;



    private Long sId;


}

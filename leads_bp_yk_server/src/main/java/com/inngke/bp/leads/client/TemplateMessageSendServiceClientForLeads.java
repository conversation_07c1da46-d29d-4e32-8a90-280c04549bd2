package com.inngke.bp.leads.client;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.service.MqService;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import com.inngke.ip.reach.service.TemplateMessageSendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TemplateMessageSendServiceClientForLeads {
    private static final String STR_NOTIFY_SEND = "notify_send";
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    private TemplateMessageSendService templateMessageSendService;

    @Autowired
    private JsonService jsonService;


    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    public boolean sendNotify(TemplateMessageSendRequest request) {
        BaseResponse<Boolean> response = templateMessageSendService.send(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("发送模板消失失败, request: {}, response: {}", jsonService.toJson(request), jsonService.toJson(response));
            return false;
        }
        return true;
    }

}

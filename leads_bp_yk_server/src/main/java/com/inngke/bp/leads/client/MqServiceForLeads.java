package com.inngke.bp.leads.client;

import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.mq.listener.LeadsFollowListener;
import com.inngke.bp.leads.mq.message.leads.LeadsFollowMessage;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.request.MqDelaySendRequest;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.service.MqService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-07-10 17:13
 **/
@Component
public class MqServiceForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @Autowired
    private JsonService jsonService;


    public Boolean send(MqSendRequest request) {
        BaseResponse<Boolean> response = mqService.send(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public Boolean sendDelay(MqDelaySendRequest request) {
        BaseResponse<Boolean> response = mqService.sendDelay(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public Boolean sendLeadsFollowMq(LeadsFollow leadsFollow){
        if (Objects.isNull(leadsFollow)) {
            return false;
        }
        MqSendRequest mqSendRequest = new MqSendRequest();
        mqSendRequest.setBid(leadsFollow.getBid());
        mqSendRequest.setTopic(LeadsFollowListener.LEADS_TO_FOLLOW_TOPIC_NAME);
        mqSendRequest.setOperatorStaffId(leadsFollow.getStaffId());
        LeadsFollowMessage leadsFollowMessage = new LeadsFollowMessage();
        leadsFollowMessage.setId(leadsFollow.getId());
        leadsFollowMessage.setStaffId(leadsFollow.getStaffId());
        leadsFollowMessage.setBid(leadsFollow.getBid());
        mqSendRequest.setPayload(jsonService.toJson(leadsFollowMessage));
        return send(mqSendRequest);
    }

    public Boolean sendLeadsFollowMq(LeadsFollow leadsFollow, Integer time) {
        if (Objects.isNull(leadsFollow)) {
            return false;
        }
        MqDelaySendRequest mqSendRequest = new MqDelaySendRequest();
        mqSendRequest.setBid(leadsFollow.getBid());
        mqSendRequest.setTopic(LeadsFollowListener.LEADS_TO_FOLLOW_TOPIC_NAME);
        mqSendRequest.setOperatorStaffId(leadsFollow.getStaffId());
        mqSendRequest.setDeliverAfter(time);
        LeadsFollowMessage leadsFollowMessage = new LeadsFollowMessage();
        leadsFollowMessage.setId(leadsFollow.getId());
        leadsFollowMessage.setStaffId(leadsFollow.getStaffId());
        leadsFollowMessage.setBid(leadsFollow.getBid());
        mqSendRequest.setPayload(jsonService.toJson(leadsFollowMessage));
        return sendDelay(mqSendRequest);
    }

}

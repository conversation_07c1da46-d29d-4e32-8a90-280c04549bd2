/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线索导入批次
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    private Integer channel;

    /**
     * 批量导入文件URL
     */
    private String fileUrl;

    /**
     * 批量导入文件类型：0=未知 1=excel
     */
    private Integer fileType;

    /**
     * 处理状态：0=未处理 1=处理中 2=已处理（包括处理失败）
     */
    private Integer processStatus;

    /**
     * 成功导入数量
     */
    private Integer successCount = 0;

    /**
     * 未成功导入数量
     */
    private Integer errorCount = 0;

    /**
     * 上传者员工ID
     */
    private Long staffId;

    /**
     * 导入错误文件下载路径
     */
    private String errorFileUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String CHANNEL = "channel";

    public static final String FILE_URL = "file_url";

    public static final String FILE_TYPE = "file_type";

    public static final String PROCESS_STATUS = "process_status";

    public static final String SUCCESS_COUNT = "success_count";

    public static final String ERROR_COUNT = "error_count";

    public static final String STAFF_ID = "staff_id";

    public static final String ERROR_FILE_URL = "error_file_url";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

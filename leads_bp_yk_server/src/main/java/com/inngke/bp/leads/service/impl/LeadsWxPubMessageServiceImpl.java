package com.inngke.bp.leads.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.common.db.card.manager.ShopManager;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.StaffLeadsCountDto;
import com.inngke.bp.leads.dto.request.ToFollowLeadsMessageDto;
import com.inngke.bp.leads.notify.context.LeadsDistributeMessageContext;
import com.inngke.bp.leads.notify.context.LeadsReportMessageContext;
import com.inngke.bp.leads.notify.context.LeadsReportMessageContext;
import com.inngke.bp.leads.service.LeadsWxPubMessageService;
import com.inngke.bp.merchant.dto.response.SimpleShopConfigDto;
import com.inngke.bp.merchant.service.ShopConfigService;
import com.inngke.bp.organize.dto.request.card.GetCardByStaffIdsRequest;
import com.inngke.bp.organize.dto.request.merchant.MerchantDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.dto.response.card.CardDto;
import com.inngke.bp.user.dto.request.customer.CustomerInfosGetByStaffIdsRequest;
import com.inngke.bp.user.dto.request.staff.QyUserTokenGetRequest;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.bp.user.service.QyWxSelfAppUserService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.factory.TemplateMessageBuilderFactory;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.common.wx.core.utils.QyWxMessageBuilder;
import com.inngke.common.wx.core.utils.WxTemplateMessageBuilder;
import com.inngke.common.wx.rpc.api.mp.WxMpModifyDomainApi;
import com.inngke.common.wx.rpc.dto.request.mp.JumpWxaDto;
import com.inngke.common.wx.rpc.dto.request.mp.WxMpUrlSchemeRequest;
import com.inngke.common.wx.rpc.dto.response.mp.WxMpUrlSchemeResponse;
import com.inngke.ip.common.dto.request.BatchMqSendRequest;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.dto.request.NotifyMessageRequest;
import com.inngke.ip.common.dto.response.WxMpSimpleInfoDto;
import com.inngke.ip.common.dto.response.WxTpAccessTokenDto;
import com.inngke.ip.common.service.MqService;
import com.inngke.ip.common.service.wx.WxMpCommonService;
import com.inngke.ip.reach.dto.request.SendSmsRequest;
import com.inngke.ip.reach.dto.request.SmsTemplateRequest;
import com.inngke.ip.reach.dto.request.VarDto;
import com.inngke.ip.reach.dto.response.SmsTemplateResponse;
import com.inngke.ip.reach.service.SmsService;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/17 7:46 PM
 */
@Service
public class LeadsWxPubMessageServiceImpl implements LeadsWxPubMessageService {
    private static final Logger logger = LoggerFactory.getLogger(LeadsWxPubMessageServiceImpl.class);

    private static final String STR_NOTIFY_SEND = "notify_send";
    private static final String MESSAGE_TEMPLATE = "OPENTM409367506";
    private static final String SMS_TEMPLATE_NAME = "leads_transmit_sms";
    protected static final int A_HOUR = 3600;
    private static final String CUSTOMER_DISTRIBUTE = "customer_distribute";

    @Autowired
    private ShopManager shopManager;

    @DubboReference(version = "1.0.0")
    private CustomerGetService customerGetService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private WxMpCommonService wxMpCommonService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    private SmsService smsService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.merchant_bp_yk:}")
    private ShopConfigService shopConfigService;

    @Autowired
    private CardClientForLeads cardClientForLeads;

    @Autowired
    private JsonService jsonService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private QyWxSelfAppUserService qyWxSelfAppUserService;

    @Autowired
    private CustomerGetServiceClientForLeads customerGetServiceClientForLeads;

    @Autowired
    private WxMpModifyDomainApi wxMpModifyDomainApi;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private MerchantClientForLeads merchantClientForLeads;

    @Autowired
    private TemplateMessageSendServiceClientForLeads templateMessageSendServiceClientForLeads;

    @Autowired
    private TemplateMessageBuilderFactory templateMessageBuilderFactory;

    /**
     * 批量获取名片列表信息
     *
     * @return
     */
    private Map<Long, CardDto> getCardInfos(Integer bid, List<Long> staffIds) {
        GetCardByStaffIdsRequest request = new GetCardByStaffIdsRequest();
        request.setBid(bid);
        request.setIds(staffIds);
        return cardClientForLeads.getCardInfos(request);
    }


    /**
     * 发送线索新增微信公众号消息
     *
     * @param bid                 商户ID
     * @param operatorId          操作者ID
     * @param staffLeadsCountList 员工新分配线索数量
     */
    @Override
    public void batchSendDistributeMessage(int bid, long operatorId, List<StaffLeadsCountDto> staffLeadsCountList, String timeInfo, int leadsType) {
        if (CollectionUtils.isEmpty(staffLeadsCountList)) {
            logger.info("员工线索数量为空");
            return;
        }
        Set<Long> staffIds = staffLeadsCountList.stream().map(StaffLeadsCountDto::getStaffId).collect(Collectors.toSet());
        Map<Long, String> guideWxPubOpenIdMap = getStaffIdOpenIdMap(bid, staffIds);
        if (CollectionUtils.isEmpty(guideWxPubOpenIdMap)) {
            logger.info("获取到员工openId为空");
        }


        //获取员工卡片信息
        Map<Long, CardDto> cardInfos = getCardInfos(bid, Lists.newArrayList(staffIds));

        Map<Integer, WxMpSimpleInfoDto> bidMpAppIdMap = Maps.newHashMap();
        WxMpSimpleInfoDto wxMpSimpleInfoDto = bidMpAppIdMap.computeIfAbsent(bid, beid -> {
            BaseBidRequest req = new BaseBidRequest();
            req.setBid(bid);
            BaseResponse<WxMpSimpleInfoDto> wxMpAppInfoResp = wxMpCommonService.getWxMpAppInfo(req);
            if (BaseResponse.responseSuccessWithNonNullData(wxMpAppInfoResp)) {
                return wxMpAppInfoResp.getData();
            }
            return null;
        });
        if (wxMpSimpleInfoDto == null) {
            //无法获取到小程序appId，丢弃
            return;
        }

        //获取短信模板
        SmsTemplateRequest smsTemplateRequest = new SmsTemplateRequest();
        smsTemplateRequest.setBid(bid);
        smsTemplateRequest.setCode(CUSTOMER_DISTRIBUTE);
        BaseResponse<SmsTemplateResponse> smsTemplateResp = smsService.getSmsTemplate(smsTemplateRequest);
        if (smsTemplateResp.getCode() != 0 && smsTemplateResp.getData() == null) {
            logger.warn("获取短信模板失败");
        }

        //获取商户号信息
        BaseResponse<Map<Integer, SimpleShopConfigDto>> shopInfosResp = shopConfigService.getShopInfos();
        if (shopInfosResp.getCode() != 0 || shopInfosResp.getData() == null) {
            logger.warn("获取品牌配置信息失败");
        }
        SmsTemplateResponse template = smsTemplateResp.getData();

        Map<Integer, SimpleShopConfigDto> shopInfoMap = shopInfosResp.getData();
        List<String> payloads = Lists.newArrayList();
        List<SendSmsRequest> smsSendRequests = Lists.newArrayList();
        staffLeadsCountList.forEach(staffLeadsCount -> {
            if (staffLeadsCount.getCount() == null || staffLeadsCount.getCount() <= 0) {
                return;
            }
            Long staffId = staffLeadsCount.getStaffId();
            String wxPubOpenId = guideWxPubOpenIdMap.get(staffId);
            StaffDto staff = staffClientForLeads.getStaffById(bid, staffId);
            if (Objects.isNull(staff)) {
                logger.warn("获取员工信息失败 ");
                return;
            }
            String qyUserId = staff.getQyUserId();
            CardDto cardDto = cardInfos.get(staffId);
            String enterpriseName;
            Long leadsId = staffLeadsCount.getLeadsId();
            if (StringUtils.isEmpty(wxPubOpenId) && StringUtils.isEmpty(qyUserId)) {
                logger.info("员工[staffId={}]未绑定公众号/企业微信,使用短信发送", staffId);

                //获取小程序accesstoken
                BaseBidRequest request = new BaseBidRequest();
                request.setBid(bid);
                BaseResponse<WxTpAccessTokenDto> accessTokenResp = wxMpCommonService.getAccessToken(request);
                if (accessTokenResp.getCode() != 0 || accessTokenResp.getData() == null || StringUtils.isEmpty(accessTokenResp.getData().getAccessToken())) {
                    logger.warn("获取小程序accessToken失败，bid={}", bid);
                    return;
                }

                if (Objects.isNull(cardDto)) {
                    logger.info("员工名片信息为空，staffId={}", staffId);
                    return;
                }

                //手机号为空则不发送
                String mobile = Optional.of(cardDto).map(CardDto::getMobile).orElse("");
                if (StringUtils.isEmpty(mobile)) {
                    return;
                }

                //短信模板为空不发
                if (Objects.isNull(template)) {
                    return;
                }

                BaseBidRequest baseBidRequest = new BaseBidRequest();
                baseBidRequest.setBid(bid);
                MerchantDto merchant = merchantClientForLeads.getMerchant(baseBidRequest);
                if (Objects.isNull(merchant)) {
                    logger.warn("获取商户配置失败！");
                }
                //获取企业名称
                enterpriseName = Objects.isNull(merchant) ? InngkeAppConst.EMPTY_STR : merchant.getEnterpriseAbbreviation();
                enterpriseName = "(" + enterpriseName + ")";
                SendSmsRequest smsContentRequest = getSmsContentRequest(bid, mobile, staffLeadsCount.getCount(), enterpriseName, template, staffId, leadsId, accessTokenResp.getData().getAccessToken());
                logger.info("请求request,request={}", jsonService.toJson(smsContentRequest));
                if (Objects.nonNull(smsContentRequest)) {
                    smsSendRequests.add(smsContentRequest);
                }


            } else {
                if (leadsType != 1) {
                    LeadsDistributeMessageContext notifyContext = LeadsDistributeMessageContext.init(bid, leadsId, staffLeadsCount, leadsType);
                    if (Objects.isNull(timeInfo)) {
                        notifyContext.setFormatTime(DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
                    }

                    TemplateMessageContentBuilder<LeadsDistributeMessageContext> notifyBuilder = templateMessageBuilderFactory.getBuilder(notifyContext);
                    BaseResponse notifyResponse = notifyBuilder.sendMessage(notifyContext);
                    logger.info("发送通知消息结果：{}", jsonService.toJson(notifyResponse));
                }
                //合伙人报备线索
                if (leadsType == 1){
                    LeadsReportMessageContext reportMessageContext = LeadsReportMessageContext.init(bid, leadsId, staffLeadsCount, leadsType);
                    reportMessageContext.setFormatTime(DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
                    TemplateMessageContentBuilder<LeadsReportMessageContext> reportBuilder = templateMessageBuilderFactory.getBuilder(reportMessageContext);

                    BaseResponse notifyResponse = reportBuilder.sendMessage(reportMessageContext);
                    logger.info("发送通知消息结果：{}", jsonService.toJson(notifyResponse));
                }
            }
//            else if (!StringUtils.isEmpty(qyUserId)) {
//                //组装发送企业微信的入参
//                logger.info("发送企业微信消息 leads_id:{},staffId:{}", leadsId, staffId);
//                payloads.add(getQyWxMessagePayloads(bid, qyUserId, wxMpSimpleInfoDto, staffLeadsCount.getCount(), staffLeadsCount.getMessageName(), staffLeadsCount.getMobile(), staffId, timeInfo, leadsType, leadsId));
//            } else if (!StringUtils.isEmpty(wxPubOpenId)) {
//                //组装发送公众号的入参
//                logger.info("发送公众号消息 leads_id:{},staffId:{}", leadsId, staffId);
//                payloads.add(getNewLeadsWxPubMessagePayloads(bid, wxPubOpenId, wxMpSimpleInfoDto, staffLeadsCount.getCount(), staffLeadsCount.getMessageName(), staffLeadsCount.getMobile(), staffId, timeInfo, leadsType, leadsId));
//            }
        });
//        batchSend(bid, payloads);
        //批量发送短息
        if (!CollectionUtils.isEmpty(smsSendRequests)) {
            smsSendRequests.forEach(
                    smsSendRequest -> {
                        smsService.sendSms(smsSendRequest);
                    }
            );
        }
    }

    private Map<Long, String> getStaffIdOpenIdMap(int bid, Set<Long> staffIds) {
        Map<Long, String> guideWxPubOpenIdMap = Maps.newHashMap();

        List<CustomerDto> staffCustomerDtoList = getStaffCustomerDtoList(bid, staffIds);
        if (CollectionUtils.isEmpty(staffCustomerDtoList)) {
            logger.info("员工微信公众号openId为空");
            return guideWxPubOpenIdMap;
        }

        Map<Long, String> customerIdOpenIdMap = staffCustomerDtoList.stream()
                .filter(item -> !StringUtils.isEmpty(item.getWxPubOpenId()))
                .collect(Collectors.toMap(CustomerDto::getId, CustomerDto::getWxPubOpenId));
        if (CollectionUtils.isEmpty(customerIdOpenIdMap)) {
            logger.info("未获取到员工openid");
            return guideWxPubOpenIdMap;
        }

        Map<Long, StaffDto> staffIdMap = staffClientForLeads.getStaffByIds(bid, staffIds);

        Map<Long, Long> staffIdCustomerIdMap = staffIdMap.values().stream()
                .filter(staff -> staff.getCustomerId() != null && staff.getCustomerId() != 0)
                .collect(Collectors.toMap(StaffDto::getId, StaffDto::getCustomerId));


        staffIdCustomerIdMap.forEach((staffId, customerId) -> {
            if (StringUtils.isNotEmpty(customerIdOpenIdMap.get(customerId))) {
                guideWxPubOpenIdMap.put(staffId, customerIdOpenIdMap.get(customerId));
            }
        });

        return guideWxPubOpenIdMap;
    }

    /**
     * 您有XX条线索超时未跟进！
     *
     * @param request 待跟进消息请求
     */
    @Override
    public void sendToFollowLeadsMessage(ToFollowLeadsMessageDto request) {
        List<Leads> leadsList = request.getLeadsList();
        int bid = request.getBid();
        Long staffId = request.getStaffId();
        //CustomerDto staffCustomerDto = customerGetServiceClientForLeads.getStaffCustomer(bid, staffId, Sets.newHashSet("id", "wxPubOpenId"));
        CustomerDto staffCustomerDto = customerGetServiceClientForLeads.getCustomerDtoByStaffId(bid, staffId);
        //Set<Long> staffIds = Sets.newHashSet(staffId);
        //getStaffCustomerDtoList(bid, staffIds);
        if (staffCustomerDto != null && StringUtils.isEmpty(staffCustomerDto.getWxPubOpenId())) {
            logger.warn("员工微信公众号openId/企业员工数据为空");
            return;
        }

        Map<Integer, WxMpSimpleInfoDto> bidMpAppIdMap = Maps.newHashMap();
        WxMpSimpleInfoDto wxMpSimpleInfoDto = bidMpAppIdMap.computeIfAbsent(bid, beid -> {
            BaseBidRequest req = new BaseBidRequest();
            req.setBid(bid);
            BaseResponse<WxMpSimpleInfoDto> wxMpAppInfoResp = wxMpCommonService.getWxMpAppInfo(req);
            if (BaseResponse.responseSuccessWithNonNullData(wxMpAppInfoResp)) {
                return wxMpAppInfoResp.getData();
            }
            return null;
        });
        if (wxMpSimpleInfoDto == null) {
            //无法获取到小程序appId，丢弃
            return;
        }

        Integer type = request.getType();
        int size = leadsList.size();
        Leads firstLeads = leadsList.get(0);
        String name = firstLeads.getName();

        String mobile = getMobile(firstLeads.getMobile());
        if (size > 1) {
            name += InngkeAppConst.COMMA_STR + leadsList.get(1).getName();
            mobile += InngkeAppConst.COMMA_STR + getMobile(leadsList.get(1).getMobile());
        }
        if (size > 2) {
            name += "...";
            mobile += "...";
        }
        List<StaffDto> staffList = staffClientForLeads.getStaffListByCustomerId(bid, staffCustomerDto.getId());
        if (CollectionUtils.isEmpty(staffList) || CollectionUtils.isEmpty(getOpenStaff(staffList))) {
            logger.info("获取员工信息失败");
            return;
        }
        String qyUserId = getOpenStaff(staffList).get(0).getQyUserId();
        String wxPubOpenId = staffCustomerDto.getWxPubOpenId();
        Leads leads = leadsList.stream().min(Comparator.comparing(Leads::getDistributeTime)).get();
        Long paramLeadsId = null;
        if (leadsList.size() == 1) {
            paramLeadsId = leads.getId();
        }

        String distributeTime = InngkeAppConst.EMPTY_STR;
        if (leads.getDistributeTime() != null) {
            distributeTime = DateTimeUtils.format(leads.getDistributeTime(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
        }
        String payload;
        if (!StringUtils.isEmpty(qyUserId)) {
            payload = getToFollowLeadsQyWxMessagePayloads(bid, qyUserId, wxMpSimpleInfoDto, size, name, mobile, type, staffId, distributeTime, paramLeadsId);
        } else if (!StringUtils.isEmpty(wxPubOpenId)) {
            payload = getToFollowLeadsWxPubMessagePayloads(bid, wxPubOpenId, wxMpSimpleInfoDto, size, name, mobile, type, staffId, distributeTime, paramLeadsId);
        } else {
            //无法获取到公众号openId
            logger.warn("员工[staffId={}]未绑定公众号/未加入企业微信", staffId);
            return;
        }
        MqSendRequest sendRequest = new MqSendRequest();
        sendRequest.setTopic(STR_NOTIFY_SEND);
        sendRequest.setPayload(payload);
        sendRequest.setOperatorId(request.getOperatorId());
        sendRequest.setBid(request.getBid());
        mqService.send(sendRequest);
    }

    private String getMobile(String mobile) {
        if (mobile == null) {
            return "****";
        }
        if (mobile.length() < 10) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    private List<StaffDto> getOpenStaff(List<StaffDto> staffList) {
        return staffList.stream().filter(staffDto -> staffDto.getStatus().equals(2)).collect(Collectors.toList());
    }

    private List<CustomerDto> getStaffCustomerDtoList(int bid, Set<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Lists.newArrayList();
        }
        CustomerInfosGetByStaffIdsRequest customerInfosGetByStaffIdsRequest = new CustomerInfosGetByStaffIdsRequest();
        customerInfosGetByStaffIdsRequest.setStaffIds(staffIds);
        customerInfosGetByStaffIdsRequest.setBid(bid);
        BaseResponse<List<CustomerDto>> resp = customerGetService.getCustomerInfoByStaffIds(customerInfosGetByStaffIdsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(resp)) {
            String requestJson = jsonService.toJson(customerInfosGetByStaffIdsRequest);
            String responseJson = jsonService.toJson(resp);
            logger.error("查询员工微信公众号失败：req={},resp={}", requestJson, responseJson);
            return Lists.newArrayList();
        }
        return resp.getData();
    }

    private void batchSend(int bid, List<String> payloads) {
        BatchMqSendRequest batchSendRequest = new BatchMqSendRequest();
        batchSendRequest.setTopic(STR_NOTIFY_SEND);
        batchSendRequest.setPayloads(payloads);
        batchSendRequest.setOperatorId(0L);
        batchSendRequest.setBid(bid);
        mqService.batchSend(batchSendRequest);
        payloads.clear();
    }

    private String getNewLeadsWxPubMessagePayloads(int bid, String wxPubOpenId, WxMpSimpleInfoDto wxMpSimpleInfoDto, int count, String name, String mobile, Long staffId, String timeInfo, int leadsType, Long leadsId) {
        String title = "有" + count + "条新的线索分配给您，请尽快处理！";
        String defaultTime = "15分钟内";
        //默认的时间为15分钟，需要特殊时间描述通过参数传递
        if (!StringUtils.isEmpty(timeInfo)) {
            defaultTime = timeInfo;
        }
        String pagePath = "Clue/Clue/myClue/myClue?staffId=" + staffId + InngkeAppConst.AND_STR + "mes_type=wx_pub" + InngkeAppConst.AND_STR + "mes_title=" + title;
        if (leadsId != null) {
            pagePath = pagePath + "&id=" + leadsId;
        }
        mobile = mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        NotifyMessageRequest notifyMessageRequest = WxTemplateMessageBuilder.create(wxPubOpenId)
                .setMessageTemplateShortId(MESSAGE_TEMPLATE)
                .setMpLink(wxMpSimpleInfoDto.getAppId(), pagePath)
                .putData("first", title)
                .putData("keyword1", name)
                .putData("keyword2", StringUtils.isBlank(mobile) ? "未知" : mobile)
                .putData("keyword3", DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                .putData("keyword4", "请在" + defaultTime + "跟进处理以免错过最佳跟进时间！")
                .getNotifyMessageRequestBuilder(bid, WxTemplateMessageBuilder.STR_WX_PUB);
        return jsonService.toJson(notifyMessageRequest);
    }

    /**
     * 组装短信发送请求
     */
    private SendSmsRequest getSmsContentRequest(Integer bid, String mobile, Integer customerCount, String enterpriseName, SmsTemplateResponse template, Long staffId, Long leadsId, String accessToken) {
        String pagePath = "Clue/Clue/myClue/myClue";
        String query = "staffId=" + staffId;
        if (leadsId != null && customerCount == 1) {//只有一条的话需要进入线索详情
            query = query + "&id=" + leadsId;
        }
        JumpWxaDto jumpWxaDto = new JumpWxaDto();
        jumpWxaDto.setPath(pagePath);
        jumpWxaDto.setQuery(query);
        WxMpUrlSchemeRequest wxMpUrlSchemeRequest = new WxMpUrlSchemeRequest();
        wxMpUrlSchemeRequest.setExpireType(1);//过期类型使用天数
        wxMpUrlSchemeRequest.setExpire(true);
        wxMpUrlSchemeRequest.setExpireInterval(7);//过期天数为七天
        wxMpUrlSchemeRequest.setJumpWxaDto(jumpWxaDto);
        WxMpUrlSchemeResponse mpUrlSchemeResp = wxMpModifyDomainApi.getMpUrlScheme(accessToken, wxMpUrlSchemeRequest);
        if (mpUrlSchemeResp.getErrCode() != 0) {
            logger.warn("获取小程序 scheme 码失败，丢弃该条短信发送，mobile={}", mobile);
            return null;
        }
        String openlink = mpUrlSchemeResp.getOpenlink();
        String[] split = openlink.split(InngkeAppConst.EQUAL_STR);
        List<VarDto> varDtos = Lists.newArrayList();
        Map<String, String> params = Maps.newHashMap();
        //["customerNum","shortKey","brandName"]
        params.put("customerNum", customerCount > 0 ? String.valueOf(customerCount) : "");
        params.put("shortKey", StringUtils.isEmpty(split[1]) ? "" : split[1]);
        params.put("brandName", StringUtils.isEmpty(enterpriseName) ? "" : enterpriseName);

        SendSmsRequest sendSmsRequest = new SendSmsRequest();
        sendSmsRequest.setBid(bid);
        sendSmsRequest.setVarConf(template.getVarConf());
        sendSmsRequest.setMobiles(Lists.newArrayList(mobile));
        sendSmsRequest.setVarParam(params);
        sendSmsRequest.setTemplateId(template.getTemplateId());
        sendSmsRequest.setPlatform(template.getPlatform());
        sendSmsRequest.setTemplateCode(template.getTemplateCode());
        return sendSmsRequest;

    }

    private String getQyWxMessagePayloads(int bid, String qyUserId, WxMpSimpleInfoDto wxMpSimpleInfoDto, int count, String name, String mobile, Long staffId, String timeInfo, int leadsType, Long leadsId) {
        String defaultTime = "15分钟内";
        //默认的时间为15分钟，需要特殊时间描述通过参数传递
        if (!StringUtils.isEmpty(timeInfo)) {
            defaultTime = timeInfo;
        }
        String description = "有" + count + "条新的线索分配给您";
        String timeKeyInfo = "分配时间";
        //0为正常线索 1为报备线索
        if (leadsType == 1) {
            description = "有合伙人提交了新的线索";
            timeKeyInfo = "提交时间";
        }
        String pagePath = "Clue/Clue/myClue/myClue?staffId=" + staffId + InngkeAppConst.AND_STR + "mes_type=qy_wx" + InngkeAppConst.AND_STR + "mes_title=" + description;
        if (leadsId != null) {
            pagePath = pagePath + "&id=" + leadsId;
        }

        QyUserTokenGetRequest qyUserTokenGetRequest = new QyUserTokenGetRequest();
        qyUserTokenGetRequest.setQyUserId(qyUserId);
        qyUserTokenGetRequest.setBid(bid);
        BaseResponse<String> userToken = qyWxSelfAppUserService.getUserToken(qyUserTokenGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(userToken)) {
            logger.error("发送企业微信失败，获取user对应的token失败");
            return null;
        }

        QyWxMessageBuilder qyWxMessageBuilder = QyWxMessageBuilder.create();
        NotifyMessageRequest notifyMessageRequestBuilder = qyWxMessageBuilder
                .setMsgType("miniprogram_notice")
                .setAppId(wxMpSimpleInfoDto.getAppId())
                .setTitle(description)
                .setPage(pagePath)
                .setToUser(qyUserId)
                .setToken(userToken.getData())
                .putContentItem("姓名", name)
                .putContentItem("手机", StringUtils.isBlank(mobile) ? "未知" : mobile)
                .putContentItem(timeKeyInfo, DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.YYYY_MM_DD_HH_MM_SS))
                .putContentItem("跟进建议", "请在" + defaultTime + "跟进处理以免错过最佳跟进时间！")
                .getNotifyMessageRequestBuilder(bid, QyWxMessageBuilder.QY_WX);
        return jsonService.toJson(notifyMessageRequestBuilder);
    }


    private String getToFollowLeadsWxPubMessagePayloads(int bid, String wxPubOpenId, WxMpSimpleInfoDto wxMpSimpleInfoDto, int count, String name, String mobile, Integer type, Long staffId, String distributeTime, Long leadsId) {
        String time = type == 1 ? "15分钟" : "24小时";
        String pagePath = "Clue/Clue/myClue/myClue?umaEvent=clueRemind&staffId=" + staffId;
        if (leadsId != null) {
            pagePath = pagePath + "&id=" + leadsId;
        }

        mobile = mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        NotifyMessageRequest notifyMessageRequest = WxTemplateMessageBuilder.create(wxPubOpenId)
                .setMessageTemplateShortId(MESSAGE_TEMPLATE)
                .setMpLink(wxMpSimpleInfoDto.getAppId(), pagePath)
                .putData("first", "您有" + count + "条线索超时未跟进！")
                .putData("keyword1", name)
                .putData("keyword2", mobile)
                .putData("keyword3", distributeTime)
                .putData("keyword4", "线索下发已超过" + time + "，请及时跟进处理以免错过最佳跟进时间！")
                .getNotifyMessageRequestBuilder(bid, WxTemplateMessageBuilder.STR_WX_PUB);
        return jsonService.toJson(notifyMessageRequest);
    }

    private String getToFollowLeadsQyWxMessagePayloads(int bid, String qyUserId, WxMpSimpleInfoDto wxMpSimpleInfoDto, int count, String name, String mobile, Integer type, Long staffId, String distributeTime, Long leadsId) {
        String time = type == 1 ? "15分钟" : "24小时";

        String pagePath = "Clue/Clue/myClue/myClue?umaEvent=clueRemind&staffId=" + staffId;
        if (leadsId != null) {
            pagePath = pagePath + "&id=" + leadsId;
        }

        QyUserTokenGetRequest qyUserTokenGetRequest = new QyUserTokenGetRequest();
        qyUserTokenGetRequest.setQyUserId(qyUserId);
        qyUserTokenGetRequest.setBid(bid);
        BaseResponse<String> userToken = qyWxSelfAppUserService.getUserToken(qyUserTokenGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(userToken)) {
            logger.error("发送企业微信失败，获取user对应的token失败");
            return null;
        }

        QyWxMessageBuilder qyWxMessageBuilder = QyWxMessageBuilder.create();
        mobile = mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        NotifyMessageRequest notifyMessageRequestBuilder = qyWxMessageBuilder
                .setMsgType("miniprogram_notice")
                .setAppId(wxMpSimpleInfoDto.getAppId())
                .setPage(pagePath)
                .setTitle("您有" + count + "条线索超时未跟进！")
                .setToUser(qyUserId)
                .setToken(userToken.getData())
                .putContentItem("姓名", name)
                .putContentItem("手机", mobile)
                .putContentItem("分配时间", distributeTime)
                .putContentItem("跟进建议", "线索下发已超过" + time + "，请及时跟进处理以免错过最佳跟进时间！")
                .getNotifyMessageRequestBuilder(bid, QyWxMessageBuilder.QY_WX);
        return jsonService.toJson(notifyMessageRequestBuilder);
    }
}

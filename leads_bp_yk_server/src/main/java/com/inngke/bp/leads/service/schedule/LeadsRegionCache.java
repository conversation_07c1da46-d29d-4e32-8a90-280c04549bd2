package com.inngke.bp.leads.service.schedule;

import cn.hutool.core.text.TextSimilarity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.common.cache.service.ApplicationCacheService;
import com.inngke.common.cache.service.RegionApplicationCacheService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.request.RegionGetRequest;
import com.inngke.ip.common.dto.response.RegionDto;
import com.inngke.ip.common.service.RegionService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2021/8/18 6:37 PM
 */
@Component
public class LeadsRegionCache implements ApplicationCacheService {
    private static final Logger logger = LoggerFactory.getLogger(LeadsRegionCache.class);
    private final Map<String, RegionDto> cityNameMap = Maps.newHashMap();
    private final Map<String, RegionDto> provinceNameMap = Maps.newHashMap();
    private final Map<String, RegionDto> areaNameMap = Maps.newHashMap();
    private final Map<Integer,RegionDto> areaNameNumMap = Maps.newHashMap();
    private final Map<String, RegionDto> pNameAndAreaNameMap = Maps.newHashMap();
    private final Map<Integer,RegionDto> cityNameNumMap = Maps.newHashMap();
    private final Map<Integer,RegionDto> provinceNameNumMap = Maps.newHashMap();

    public final static String STR_PROVINCE = "province";
    public final static String STR_CITY = "city";
    public final static String STR_COUNTY = "county";
    public final static String STR_TOWN = "town";
    public final static String STR_VILLAGE = "village";
    private final Map<String,RegionDto> regionFullNameMap = Maps.newConcurrentMap();
    private final Map<Integer,RegionDto> regionMap = Maps.newConcurrentMap();
    private final List<String> otherProvinceNameEnd =
            Lists.newArrayList("省","市","维吾尔自治区","特别行政区","回族自治区","壮族自治区","自治区");



    @DubboReference(version = "1.0.0")
    private RegionService regionService;

    private RegionApplicationCacheService regionApplicationCacheService;

    //@PostConstruct
    public void init() {
        AsyncUtils.sleep(10000);
        RegionGetRequest regionRequest = new RegionGetRequest();
        regionRequest.setId(1);
        regionRequest.setBid(1);
        regionRequest.setWithChildren(true);
        BaseResponse<RegionDto> regionResp;
        try {
            regionResp = regionService.getRegion(regionRequest);
            if (regionResp.getCode() != 0) {
                throw new InngkeServiceException("获取中国省份数据失败！");
            }
        } catch (Exception e) {
            AsyncUtils.sleep(100);
            logger.info("region等待...");
            init();
            return;
        }
        RegionDto country = regionResp.getData();
        country.getChildren().forEach(province -> {
            provinceNameMap.put(province.getFullName(), province);
            provinceNameMap.put(province.getName(),province);
            provinceNameNumMap.put(province.getId(),province);
            regionMap.put(province.getId(), province);
            province.setChildren(setCityRegion(province.getId()));
        });

        String fullName = InngkeAppConst.EMPTY_STR;
        RegionDto region = new RegionDto();
        region.setChildren(country.getChildren());
        region.setLevel(0);
        region.setName(InngkeAppConst.EMPTY_STR);
        region.setFullName(InngkeAppConst.EMPTY_STR);

        getAllRegionFullNamePath(fullName, region);
    }

    private void getAllRegionFullNamePath(String fullName, RegionDto regionDto) {
        if (Objects.nonNull(regionDto)) {
            //去除同名
            if (!fullName.contains(regionDto.getFullName())){
                fullName += regionDto.getFullName();
            }

            regionDto.setPinYin(fullName);
            regionFullNameMap.put(fullName, regionDto);
            if (Objects.nonNull(regionDto.getChildren())) {
                for (RegionDto child : regionDto.getChildren()) {
                    getAllRegionFullNamePath(fullName, child);
                }
            }
        }
    }

    public RegionDto analysisRegion(String regionString){
        Map<String, RegionDto> regionSourceMap = Maps.newHashMap();

        for (String name : regionFullNameMap.keySet()) {
            String similar = TextSimilarity.longestCommonSubstring(regionString, name);
            regionSourceMap.put(similar, regionFullNameMap.get(name));
        }

        int longest = 0;
        RegionDto region = null;
        for (String key : regionSourceMap.keySet()) {
            if (StringUtils.hasLength(key) && key.length() > longest){
                longest = key.length();
                region = regionSourceMap.get(key);
            }
        }
        if (Objects.isNull(region)){
            return null;
        }

        region = copyRegion(region);

        while (Objects.nonNull(region.getParentId()) && region.getParentId() != 0) {
            RegionDto parent = regionMap.get(region.getParentId());
            if (Objects.isNull(parent)){
                return region;
            }
            parent.setChildren(Lists.newArrayList(region));
            region = parent;
        }

        return region;
    }

    private RegionDto copyRegion(RegionDto region) {
        RegionDto regionDto = new RegionDto();
        regionDto.setId(region.getId());
        regionDto.setName(region.getName());
        regionDto.setPinYin(region.getPinYin());
        regionDto.setParentId(region.getParentId());
        regionDto.setLevel(region.getLevel());
        regionDto.setCreateTime(region.getCreateTime());
        regionDto.setUpdateTime(region.getUpdateTime());
        regionDto.setFullName(region.getFullName());
        regionDto.setScoreValue(region.getScoreValue());
        regionDto.setWxCode(region.getWxCode());
        regionDto.setGeoLon(region.getGeoLon());
        regionDto.setGeoLat(region.getGeoLat());
        return regionDto;
    }

    private void setPNameAndCityRegion(int provinceId) {
        RegionGetRequest regionRequest = new RegionGetRequest();
        regionRequest.setId(provinceId);
        regionRequest.setBid(1);
        regionRequest.setWithChildren(true);
        BaseResponse<RegionDto> regionResp = regionService.getRegion(regionRequest);
        if (regionResp.getCode() != 0) {
            logger.error("获取省份数据失败！id={}", provinceId);
            return;
        }

        regionResp.getData().getChildren().forEach(city -> {
            cityNameMap.put(city.getFullName(), city);
            cityNameNumMap.put(city.getId(),city);
            setAreaRegion(city.getId());
        });
    }

    /**
     * 设置所在城市信息
     *
     * @param provinceId 省份ID
     * @return
     */
    private List<RegionDto> setCityRegion(int provinceId) {
        RegionGetRequest regionRequest = new RegionGetRequest();
        regionRequest.setId(provinceId);
        regionRequest.setBid(1);
        regionRequest.setWithChildren(true);
        BaseResponse<RegionDto> regionResp = regionService.getRegion(regionRequest);
        if (regionResp.getCode() != 0) {
            logger.error("获取省份数据失败！id={}", provinceId);
            return null;
        }
        String provinceFullName = regionResp.getData().getFullName();
        RegionDto data = regionResp.getData();
        data.getChildren().forEach(city -> {
            cityNameMap.put(city.getFullName(), city);
            cityNameMap.put(city.getName(),city);
            cityNameNumMap.put(city.getId(),city);
            regionMap.put(city.getId(),city);
            city.setChildren(setAreaRegion(city.getId()));
        });
        return data.getChildren();
    }

    private List<RegionDto> setAreaRegion(int cityId) {
        RegionGetRequest regionRequest = new RegionGetRequest();
        regionRequest.setId(cityId);
        regionRequest.setBid(1);
        regionRequest.setWithChildren(true);
        BaseResponse<RegionDto> regionResp = regionService.getRegion(regionRequest);
        if (regionResp.getCode() != 0) {
            logger.error("获取城市数据失败！id={}", cityId);
            return null;
        }
        String cityFullName = regionResp.getData().getFullName();
        RegionDto data = regionResp.getData();
        data.getChildren().forEach(area -> {
            areaNameMap.put(area.getFullName(), area);
            pNameAndAreaNameMap.put(cityFullName + area.getFullName(), area);
            pNameAndAreaNameMap.put(cityFullName + area.getName(), area);
            regionMap.put(area.getId(),area);
            areaNameNumMap.put(area.getId(),area);
        });
        return data.getChildren();
    }

    public RegionDto getProvince(String provinceName) {
        if (provinceName == null) {
            return null;
        }
        RegionDto regionDto = provinceNameMap.get(provinceName);
        if (regionDto != null) {
            return regionDto;
        }
        if (provinceName.endsWith("省") && provinceName.length() > 2) {
            return getProvince(provinceName.substring(0, provinceName.length() - 1));
        }
        //尝试加上 "省","市" 再匹配
        for (String otherEnd : otherProvinceNameEnd) {
            if (!provinceName.contains(otherEnd)) {
                RegionDto province = provinceNameMap.get(provinceName + otherEnd);
                if (province != null) {
                    return province;
                }
            }
        }
        return null;
    }

    public RegionDto getCity(String cityName) {
        if (cityName == null) {
            return null;
        }
        RegionDto regionDto = cityNameMap.get(cityName);
        if (regionDto != null) {
            return regionDto;
        }
        if (cityName.endsWith("市") && cityName.length() > 2) {
            return getProvince(cityName.substring(0, cityName.length() - 1));
        }
        if (!cityName.contains("市")){
            regionDto = cityNameMap.get(cityName + "市");
            return regionDto;
        }
        return null;
    }

    public RegionDto getArea(String areaName) {
        if (areaName == null) {
            return null;
        }
        RegionDto regionDto = areaNameMap.get(areaName);
        List<String> suffixList = Lists.newArrayList("市","县","区");
        if (regionDto != null) {
            return regionDto;
        }
        for (String suffix : suffixList) {
            regionDto = areaNameMap.get(areaName + suffix);
            if (regionDto != null) {
                return regionDto;
            }
        }
        return null;
    }

    public RegionDto getAreaByParentNameAndAreaName(String parentAndAreaName) {
        if (parentAndAreaName == null) {
            return null;
        }
        RegionDto regionDto = pNameAndAreaNameMap.get(parentAndAreaName);
        if (regionDto != null) {
            return regionDto;
        }
        return null;
    }

    public RegionDto getAreaByParentNameAndAreaName(String parentName,String areaName) {
        if (areaName == null ){
            return null;
        }
        RegionDto regionDto = getAreaByParentNameAndAreaName(parentName + areaName);
        if (!ObjectUtils.isEmpty(regionDto)){
            return regionDto;
        }
        List<String> suffixList = Lists.newArrayList("市","县","区");
        for (String suffix : suffixList) {
            regionDto = getAreaByParentNameAndAreaName(parentName + areaName + suffix);
            if (regionDto != null) {
                return regionDto;
            }
        }

        return null;
    }

    public Map<String, String> addressResolution(String address) {
        String regex = "(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String province, city, county, town, village;
        Map<String, String> row = new HashMap<>(5);
        while (m.find()) {
            province = m.group(STR_PROVINCE);
            row.put(STR_PROVINCE, province == null ? "" : province.trim());
            city = m.group(STR_CITY);
            row.put(STR_CITY, city == null ? "" : city.trim());
            county = m.group(STR_COUNTY);
            row.put(STR_COUNTY, county == null ? "" : county.trim());
            town = m.group(STR_TOWN);
            row.put(STR_TOWN, town == null ? "" : town.trim());
            village = m.group(STR_VILLAGE);
            row.put(STR_VILLAGE, village == null ? "" : village.trim());
        }
        return row;
    }

    public Map<String, RegionDto> getProvinceNameMap() {
        return provinceNameMap;
    }

    public Map<String, RegionDto> getCityNameMap() {
        return cityNameMap;
    }

    public Map<String, RegionDto> getAreaNameMap() {
        return areaNameMap;
    }

    public Map<Integer, RegionDto> getAreaNumMap() {
        return areaNameNumMap;
    }
    public Map<Integer, RegionDto> getCityNumMap() {
        return cityNameNumMap;
    }
    public Map<Integer, RegionDto> getProvinceNumMap() {
        return provinceNameNumMap;
    }

    @Override
    public void initData() {
        init();
    }

}

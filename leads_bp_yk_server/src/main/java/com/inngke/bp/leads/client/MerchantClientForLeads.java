package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.inngke.bp.organize.dto.request.merchant.MerchantDto;
import com.inngke.bp.organize.service.MerchantService;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/19 14:54
 */
@Component
public class MerchantClientForLeads {
    private static final Logger LOG = LoggerFactory.getLogger(MerchantClientForLeads.class);

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private MerchantService merchantService;

    public MerchantDto getMerchant(BaseBidRequest request) {
        BaseResponse<MerchantDto> merchantInfoResp = merchantService.getMerchant(request);
        if (!BaseResponse.responseSuccessWithNonNullData(merchantInfoResp)) {
            LOG.error("通过bid获取商户配置数据失败！");
            return null;
        }
        return merchantInfoResp.getData();
    }

    public MerchantDto getMerchant(int bid) {
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        BaseResponse<MerchantDto> merchantInfoResp = merchantService.getMerchant(request);
        if (!BaseResponse.responseSuccessWithNonNullData(merchantInfoResp)) {
            LOG.error("通过bid获取商户配置数据失败！");
            throw new InngkeServiceException(merchantInfoResp.getMsg());
        }
        return merchantInfoResp.getData();
    }

    /**
     * 查询全部商户信息
     * @return
     */
    public List<Integer> getMerchantList() {
        BaseBidRequest request = new BaseBidRequest();
        BaseResponse<List<MerchantDto>> response = merchantService.getList(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.error("获取商户配置数据失败！");
            return Lists.newArrayList();
        }

        return response.getData().stream().filter(i -> Objects.equals(i.getStatus(),1)).map(MerchantDto::getId).collect(Collectors.toList());
    }
}

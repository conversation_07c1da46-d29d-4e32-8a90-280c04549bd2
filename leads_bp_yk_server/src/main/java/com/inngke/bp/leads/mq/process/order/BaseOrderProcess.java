package com.inngke.bp.leads.mq.process.order;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.MqServiceForLeads;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.ClearLeadsStatusRecordRequest;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.request.UpdateLeadsStatusRecordRequest;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.message.order.OrderChangeMessageDto;
import com.inngke.bp.leads.mq.process.OrderProcess;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsFollowTimeService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/17 15:38 PM
 */
abstract class BaseOrderProcess implements OrderProcess {
    private static final Logger logger = LoggerFactory.getLogger(BaseOrderProcess.class);

    @Autowired
    protected JsonService jsonService;

    @Autowired
    protected LeadsManager leadsManager;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsFollowTimeService leadsFollowTimeService;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @Autowired
    private MqServiceForLeads mqServiceForLeads;

    private List<LeadsFollow> installFollowInfo(Integer bid, Long uid, String mobile, Integer event, Long leadsId) {
        //加上无效/流失 【客户&客资】优化需求
        QueryWrapper<Leads> leadsWrapper = Wrappers.<Leads>query().eq(Leads.BID, bid)
                .and(qw ->
                        qw.between(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus())
                                .or().in(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.LOST.getStatus(),LeadsStatusEnum.UNSURE_INTENT.getStatus()))
                );

        List<LeadsFollow> leadsFollows = Lists.newArrayList();
        if (leadsId != null) {
            leadsWrapper.eq(Leads.ID, leadsId);
        } else {
            return Lists.newArrayList();
        }
        //查询出需要修改状态的所有的线索，用于组装跟进记录数据
        List<Leads> list = leadsManager.list(leadsWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                LeadsFollow leadsFollow = new LeadsFollow();
                leadsFollow.setLeadsStatus(LeadsStatusEnum.TRADED.getStatus());
                if (event == null) {
                    leadsFollow.setFollowContent("客户在小程序内产生订单，系统自动将线索状态由【" + LeadsStatusEnum.parse(item.getStatus()).getName() + "】" + "修改为" + "【" +
                            LeadsStatusEnum.TRADED.getName() + "】");
                    leadsFollow.setLeadsStatus(LeadsStatusEnum.TRADED.getStatus());
                } else {
                    if (event.equals(4)) {
                        leadsFollow.setFollowContent("客户已下定，系统自动将线索状态由【" + LeadsStatusEnum.parse(item.getStatus()).getName() + "】" + "修改为" + "【" +
                                LeadsStatusEnum.ORDERED.getName() + "】");
                        leadsFollow.setLeadsStatus(LeadsStatusEnum.ORDERED.getStatus());
                    } else {
                        leadsFollow.setFollowContent("客户已成交，系统自动将线索状态由【" + LeadsStatusEnum.parse(item.getStatus()).getName() + "】" + "修改为" + "【" +
                                LeadsStatusEnum.TRADED.getName() + "】");
                        leadsFollow.setLeadsStatus(LeadsStatusEnum.TRADED.getStatus());
                    }
                }
                leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
                leadsFollow.setCreateTime(LocalDateTime.now());
                leadsFollow.setStaffId(0L);
                leadsFollow.setUserId(0L);
                leadsFollow.setBid(item.getBid());
                leadsFollow.setLeadsId(item.getId());
                leadsFollow.setBeforeLeadsStatus(item.getStatus());
                leadsFollows.add(leadsFollow);
            });
        }
        return leadsFollows;
    }

    protected void changeLeadsStatus(OrderChangeMessageDto message) {
        String msg = jsonService.toJson(message);
        logger.info("订单变更，修改线索状态{}", msg);
        Integer bid = message.getBid();
        String mobile = message.getMobile();
        Long uid = Optional.ofNullable(message.getUid()).orElse(0L);
        Long operatorId = Optional.ofNullable(message.getOperatorId()).orElse(0L);
        Integer status = null;
        if (StringUtils.isEmpty(mobile) && uid == 0 && operatorId == 0) {
            logger.error("修改线索状态--》订单变更mq失败，失败原因，php端没传递相应的客户信息，无法找到对应的线索");
            return;
        }
        Integer orderType = message.getOrderType();
        Integer event = message.getEvent();
        Long leadsId = message.getLeadsId();
        UpdateWrapper<Leads> uidWrapper = Wrappers.<Leads>update()
                .eq(Leads.BID, bid)
                .set(Leads.PAY_TIME, LocalDateTime.now());

        if (event != null && event.equals(4)) {
            uidWrapper.set(Leads.STATUS, LeadsStatusEnum.ORDERED.getStatus());
            status = LeadsStatusEnum.ORDERED.getStatus();
            logger.info("定金！");
        } else {
            uidWrapper.set(Leads.STATUS, LeadsStatusEnum.TRADED.getStatus());
            status = LeadsStatusEnum.TRADED.getStatus();
        }

        List<LeadsFollow> leadsFollows = Lists.newArrayList();
        //订单类型：1=商城订单 2=门店订单（开单）
        if (orderType.equals(1)) {
            uidWrapper.between(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus());
            logger.info("-----------------shop-order-----------------");
            //如果是商城订单，则直接根据operatorId（用户的uid）查询出所有的线索后进行线索状态修改
            leadsFollows = installFollowInfo(bid, operatorId, null, null, leadsId);
            if (operatorId != 0) {
                uidWrapper.eq(Leads.CUSTOMER_UID, operatorId);
                leadsManager.update(uidWrapper);
            }
        } else {
            uidWrapper.and(qw ->
                    qw.between(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus())
                            //加上无效/流失 【客户&客资】优化需求
                            .or().in(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.LOST.getStatus(),LeadsStatusEnum.UNSURE_INTENT.getStatus()))
            );
            logger.info("===================store-order=================");
            boolean uidUpdate = false;
            //如果是门店订单，则需要分两种情况，当有uid传递过来时，根据uid进行匹配并修改
            if (leadsId != null) {
                leadsFollows = installFollowInfo(bid, uid, null, event, leadsId);
                uidWrapper.eq(Leads.ID, leadsId);
                uidUpdate = leadsManager.update(uidWrapper);
            } else if (uid != 0) {
                leadsFollows = installFollowInfo(bid, uid, null, event, leadsId);
                uidWrapper.eq(Leads.CUSTOMER_UID, uid);
                uidUpdate = leadsManager.update(uidWrapper);
            }

            //根据uid修改失败情况下根据手机号码修改，说明此时线索中的对应的号码还没有填充客户信息，那么此时就根据手机号码进行匹配
            if (!uidUpdate && !StringUtils.isEmpty(mobile)) {
                UpdateWrapper<Leads> queryWrapper = Wrappers.<Leads>update()
                        .eq(Leads.BID, bid)
                        .eq(leadsId == null, Leads.MOBILE, mobile)
                        .eq(leadsId != null, Leads.ID, leadsId)
                        .and(qw ->
                                qw.between(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus(), LeadsStatusEnum.INSTALLED.getStatus())
                                        //加上无效/流失 【客户&客资】优化需求
                                        .or().in(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.LOST.getStatus(),LeadsStatusEnum.UNSURE_INTENT.getStatus()))
                        )
                        .set(Leads.PAY_TIME, LocalDateTime.now());

                if (event != null && event.equals(4)) {
                    queryWrapper.set(Leads.STATUS, LeadsStatusEnum.ORDERED.getStatus());
                    logger.info("定金！");
                } else {
                    queryWrapper.set(Leads.STATUS, LeadsStatusEnum.TRADED.getStatus());
                }

                //根据手机号码查询线索数据组装跟进记录
                leadsFollows = installFollowInfo(bid, null, mobile, event, leadsId);
                //如果mq传递过来的信息中带了uid那么顺便在此时将uid填充到数据库相对应号码的线索的CUSTOMER_UID字段中
                if (uid != 0) {
                    queryWrapper.set(Leads.CUSTOMER_UID, uid);
                }
                leadsManager.update(queryWrapper);
            }

        }

        //如果该线索没有手机号，回填下单导购填写的手机号
        repairLeadsMobile(bid, mobile, leadsId);

        //当两种订单变更生效时对跟进记录新增，先进行跟进记录的新增，新增完以后再根据新增的自增id对线索中最后一条跟进记录id进行修改
        if (!CollectionUtils.isEmpty(leadsFollows)) {
            List<LeadsFollow> collect = leadsFollows.stream().filter(item -> item.getLeadsId() != null).collect(Collectors.toList());
            leadsFollowManager.saveBatch(collect);
            // 发送添加线索跟进记录Mq
            AsyncUtils.runAsync(() -> collect.forEach(item -> mqServiceForLeads.sendLeadsFollowMq(item,2)));

            collect.forEach(item -> leadsManager.update(Wrappers.<Leads>update().eq(Leads.BID, bid).eq(Leads.ID, item.getLeadsId())
                    .set(Leads.LAST_FOLLOW_ID, item.getId())
                    .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
                    )
            );
            //ES文档更新，更新状态变动的leads
            if (!CollectionUtils.isEmpty(collect)) {
                logger.info("====+++++++++.{}", collect);
                Set<Long> leadsIds = collect.stream().map(LeadsFollow::getLeadsId).collect(Collectors.toSet());
                logger.info("===============>>>>>>需要变更的leads: 【{}】", leadsIds);
                LeadsUpdateRequest updateRequest = new LeadsUpdateRequest();
                updateRequest.setBid(bid);
                updateRequest.setIds(Lists.newArrayList(leadsIds));
                leadsEsService.updateDocs(updateRequest);
            }
        }

        //更新线索状态记录
        if (Objects.nonNull(leadsId) && Objects.nonNull(status)) {
            UpdateLeadsStatusRecordRequest updateLeadsStatusRecordRequest = new UpdateLeadsStatusRecordRequest();
            updateLeadsStatusRecordRequest.setLeadsId(leadsId);
            updateLeadsStatusRecordRequest.setBid(bid);
            updateLeadsStatusRecordRequest.setStatus(status);
            leadsServiceV2.updateLeadsStatusRecord(updateLeadsStatusRecordRequest);
        }
        // 线索状态为已成交也要更新ES
        if (CollectionUtils.isEmpty(leadsFollows) && leadsId != null) {
            LeadsUpdateRequest updateRequest = new LeadsUpdateRequest();
            updateRequest.setBid(bid);
            updateRequest.setIds(Lists.newArrayList(leadsId));
            leadsEsService.updateDocs(updateRequest);
        }
    }

    private void repairLeadsMobile(Integer bid, String mobile, Long leadsId) {
        if (Objects.isNull(leadsId)) {
            return;
        }
        Leads leads = leadsManager.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, leadsId)
                        .select(Leads.ID, Leads.MOBILE)
        );

        String leadsMobile = leads.getMobile();
        if (StringUtils.isEmpty(leadsMobile) && !StringUtils.isEmpty(mobile)) {
            leadsManager.update(
                    Wrappers.<Leads>update()
                            .eq(Leads.ID, leadsId)
                            .eq(Leads.BID, bid)
                            .set(Leads.MOBILE, mobile)
            );
        }
    }

}

package com.inngke.bp.leads.events.leads;

import com.inngke.bp.leads.db.leads.entity.Leads;
import org.springframework.context.ApplicationEvent;

/**
 * LeadsCreateEvent
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/13 13:46
 */
public class LeadsCreateEvent extends ApplicationEvent {
    private Leads leads;


    public LeadsCreateEvent(Object source, Leads leads) {
        super(source);
        this.leads = leads;
    }
}

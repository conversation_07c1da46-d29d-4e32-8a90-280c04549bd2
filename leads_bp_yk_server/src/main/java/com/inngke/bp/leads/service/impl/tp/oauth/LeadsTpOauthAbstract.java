package com.inngke.bp.leads.service.impl.tp.oauth;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.db.leads.manager.LeadsTpOauthManager;
import com.inngke.bp.leads.service.LeadsTpOauthTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

/**
 * <AUTHOR>
 * @date 2022/4/22 15:21
 */
public abstract class LeadsTpOauthAbstract implements LeadsTpOauthTypeService {

    @Autowired
    protected LeadsTpOauthManager leadsTpOauthManager;

    @Autowired
    protected RedisTemplate redisTemplate;

    /**
     * 从数据库获取token
     *
     * @param bid
     * @param accountId
     * @return
     */
    protected String getTokenFromDb(Integer bid, String accountId) {
        LeadsTpOauth one = leadsTpOauthManager.getOne(Wrappers.<LeadsTpOauth>query()
                .eq(LeadsTpOauth.BID, bid)
                .eq(LeadsTpOauth.ACCOUNT_ID, accountId)
                .select(LeadsTpOauth.ACCESS_TOKEN)
        );

        return ObjectUtils.isEmpty(one) || ObjectUtils.isEmpty(one.getAccessToken()) ? "" : one.getAccessToken();
    }

    /**
     * 从缓存获取token
     *
     * @param bid
     * @param accountId
     * @return
     */
    protected String getTokenFromCache(Integer bid, String accountId) {
        ValueOperations operations = redisTemplate.opsForValue();

        return (String) operations.get(getAccessTokenCacheKey(bid, accountId));
    }

    /**
     * 缓存token
     *
     * @param bid
     * @param accountId
     * @param accessToken
     */
    protected void cacheToken(Integer bid, String accountId, String accessToken) {

        ValueOperations operations = redisTemplate.opsForValue();

        operations.set(getAccessTokenCacheKey(bid, accountId), accessToken);
    }


    /**
     * 获取token缓存key
     *
     * @param bid
     * @param accountId
     * @return
     */
    abstract protected String getAccessTokenCacheKey(Integer bid, String accountId);

}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.dto.LeadsFollowBatchCountDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 线索跟进记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface LeadsFollowManager extends IService<LeadsFollow> {

    /**
     * 创建导购联系线索跟进记录
     * @param leadsFollow
     * @return
     */
    Boolean createContactFollow(LeadsFollow leadsFollow);

    /**
     * 保存跟进记录
     *
     */
    Boolean saveFollow(LeadsFollow leadsFollow);

    /**
     * 查询线索的跟进数量
     * @param leadsIds
     * @return
     */
    List<LeadsFollowBatchCountDTO> countBatch(Set<Long> leadsIds);


    Boolean createFollow(Leads leads,LeadsFollow leadsFollow,Integer leadsStatus);

    /**
     * 通过id获取列表
     *
     * @param lastId 获取此ID后的数据
     * @param size 数据量
     */
    List<LeadsFollow> getListByLastId(Long lastId,Integer size);

    /**
     * 查询最新的根进记录
     * @return
     */
    List<LeadsFollow> getLeadsLatestFollowList(Integer bid, List<Long> leadsIds);

    /**
     * 线索集合
     * @return
     */
    List<LeadsFollow> getLastFollowRecordList(List<Long> leadsIds,boolean isPC);

    Integer getFollowCountByLeadsIds(Integer bid, Set<Long> collect);

    Map<Long,Integer> getFollowCountMapByLeadsIds(Integer bid, Set<Long> collect);

    List<LeadsFollow> findFollowByLeadsIds(Integer bid, Set<Long> leadsIds);

    Map<Long, LocalDateTime> getLeadsFirstCallTime(Integer bid, Set<Long> leadsIds);

    LocalDateTime getLeadsFirstCallTime(Integer bid, Long leadsId);

    List<LeadsFollow> getByLeadsIds(Integer bid, List<Long> ids);

    List<LeadsFollow> getLeadsFollowList(Integer bid, Long leadsId, LocalDateTime createTime, Integer limit);
}

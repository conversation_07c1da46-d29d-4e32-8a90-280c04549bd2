package com.inngke.bp.leads.mq.message.order;

import com.inngke.common.mq.dto.BaseMessage;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since  2021/9/17 14:33
 */
public class OrderChangeMessageDto extends BaseMessage {
    /**
     * 订单类型：1=商城订单 2=门店订单（开单）
     *
     * @demo 1
     * @required
     */
    private Integer orderType;

    /**
     * 订单ID
     *
     * @demo 123456
     * @required
     */
    private Long orderId;


    /**
     * 手机号
     *
     * @demo
     *
     */
    private String mobile;

    private Long cid;
    /**
     * 客户ID，即customer.uid
     */
    private Long uid;

    /**
     * 订单状态变更动作：-6已退款 -5已退货 -4退货中， -3换货中， -2退款中，-1取消状态，0普通状态，1为已付款，2为已发货，3为成功
     */
    private Integer event;

    /**
     * 线索Id
     */
    private Long leadsId;

    /**
     * 支付金额（总额）
     */
    private BigDecimal payAmount;

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getCid() {
        return cid;
    }

    public void setCid(Long cid) {
        this.cid = cid;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }
}

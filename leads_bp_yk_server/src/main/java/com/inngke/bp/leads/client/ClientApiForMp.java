package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.inngke.bp.client.dto.response.client.ClientDto;
import com.inngke.bp.client.request.ClientBindLeadsRequest;
import com.inngke.bp.client.request.ClientTransformRequest;
import com.inngke.bp.client.service.ClientService;
import com.inngke.bp.client.service.ClientStatusSyncLeadsService;
import com.inngke.bp.leads.core.converter.ClientConverterOfLeads;
import com.inngke.bp.leads.core.converter.DemandConverterOfLeads;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.dto.request.DemandCreateRequest;
import com.inngke.bp.leads.dto.request.LeadsBindClientRequest;
import com.inngke.bp.leads.dto.request.TransformClientRequest;
import com.inngke.bp.leads.dto.response.LeadsBasicDto;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ClientApiForMp
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/13 17:58
 */
@Component
@Slf4j
public class ClientApiForMp {
    @DubboReference(version = "1.0.0", timeout = 6000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientService clientService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientStatusSyncLeadsService clientStatusSyncLeadsService;


    public BaseResponse<Long> bindClient(
            LeadsBindClientRequest request,LeadsBasicDto leadsBasicDto ,String oldLeadsText, Boolean reporting) {
        ClientBindLeadsRequest bindRequest = new ClientBindLeadsRequest();
        bindRequest.setBid(request.getBid());
        bindRequest.setClientId(request.getClientId());
        bindRequest.setLeadsBasicDto(LeadsConverter.leadsBasicDtoToClientLeadsBasicDto(leadsBasicDto));
        bindRequest.setDemandInfo(toDemandCreateRequest(request.getDemandInfo()));
        bindRequest.setFollowInfo(ClientConverterOfLeads.toAddFollowRequest(request.getLeadsFollow()));
        bindRequest.setStaffId(request.getStaffId());
        bindRequest.setReporting(reporting);
        bindRequest.setOldLeadsStatusText(oldLeadsText);

        return clientService.bindClient(bindRequest);
    }

    private com.inngke.bp.client.request.DemandCreateRequest toDemandCreateRequest(DemandCreateRequest request) {
        com.inngke.bp.client.request.DemandCreateRequest demandCreateRequest = new com.inngke.bp.client.request.DemandCreateRequest();
        demandCreateRequest.setDemandName(request.getDemandName());
        demandCreateRequest.setProvinceId(request.getProvinceId());
        demandCreateRequest.setCityId(request.getCityId());
        demandCreateRequest.setAreaId(request.getAreaId());
        demandCreateRequest.setAddress(request.getAddress());
        demandCreateRequest.setOperatorId(request.getOperatorId());
        demandCreateRequest.setBid(request.getBid());
        demandCreateRequest.setCommunityId(request.getCommunityId());
        demandCreateRequest.setHousingArea(request.getHousingArea());
        return demandCreateRequest;

    }

    public BaseResponse<Long> transformClient(TransformClientRequest request, String leadsOldStatusText, LeadsBasicDto leadsBasicDto) {
        ClientTransformRequest transferClientRequest = new ClientTransformRequest();
        transferClientRequest.setBid(request.getBid());
        transferClientRequest.setLeadsId(request.getLeadsId());
        transferClientRequest.setStaffId(request.getStaffId());
        transferClientRequest.setClientInfo(ClientConverterOfLeads.leadsClientCreateRequestToClientCreateRequest(request.getBid(), request.getClientInfo()));
        transferClientRequest.setDemandInfo(DemandConverterOfLeads.leadsDemandCreateRequestToClientDemandCreateRequest(request.getBid(), request.getDemandInfo()));
        transferClientRequest.setFollowInfo(ClientConverterOfLeads.toAddFollowRequest(request.getLeadsFollow()));
        transferClientRequest.setLeadsInfo(LeadsConverter.leadsBasicDtoToClientLeadsBasicDto(leadsBasicDto));
        transferClientRequest.setReporting(LeadsChannelEnum.REPORT.getChannel().equals(leadsBasicDto.getChannel()));
        transferClientRequest.setOldLeadsStatusText(leadsOldStatusText);
        return clientService.leadsTransformClient(transferClientRequest);
    }

    public ClientDto getClientById(int bid, long clientId) {
        BaseIdRequest baseIdRequest = new BaseIdRequest();
        baseIdRequest.setBid(bid);
        baseIdRequest.setId(clientId);
        BaseResponse<ClientDto> response = clientService.getClientById(baseIdRequest);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public List<String> getLeadsStatusChangePath(Integer bid,Long leadsId){
        BaseIdRequest request = new BaseIdRequest();
        request.setId(leadsId);
        request.setBid(bid);

        BaseResponse<List<String>> response = clientStatusSyncLeadsService.getLeadsStatusChangePath(request);
        if (!BaseResponse.responseSuccess(response)) {
            return Lists.newArrayList();
        }

        return response.getData();
    }
}

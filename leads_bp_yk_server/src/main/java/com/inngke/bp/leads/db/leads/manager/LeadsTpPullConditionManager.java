/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsTpPullCondition;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 拉取第三方平台数据条件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
public interface LeadsTpPullConditionManager extends IService<LeadsTpPullCondition> {

    List<LeadsTpPullCondition> saveOrUpdateBatch(Integer bid, List<LeadsTpPullCondition> leadsTpPullConditionList);
}

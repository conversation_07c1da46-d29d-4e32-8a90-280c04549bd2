package com.inngke.bp.leads.client;

import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.response.DomainResponse;
import com.inngke.ip.common.service.EnterpriseDomainService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-07-11 10:29
 **/
@Component
public class EnterpriseDomainServiceForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private EnterpriseDomainService enterpriseDomainService;

    public DomainResponse getDomainByBid(int bid){
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(bid);
        BaseResponse<DomainResponse> response = enterpriseDomainService.getDomainByBid(request);
        if(!BaseResponse.responseSuccess(response)){
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.dao.LeadsExtInformationDao;
import com.inngke.bp.leads.db.leads.manager.LeadsExtInformationManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 线索扩展表-信息类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsExtInformationManagerImpl extends ServiceImpl<LeadsExtInformationDao, LeadsExtInformation> implements LeadsExtInformationManager {

    @Override
    public LeadsExtInformation getLastUpdate(Integer bid, Integer channel) {
        return baseMapper.getLastUpdate(bid, channel);
    }

    @Override
    public LeadsExtInformation getLeadsExtInformationSelectId(Integer bid, String externalId) {
        return baseMapper.getLeadsExtInformationSelectId(bid, externalId);
    }

    @Override
    public List<LeadsExtInformation> getByIds(Integer bid, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)){
            return Lists.newArrayList();
        }
        return list(Wrappers.<LeadsExtInformation>query().eq(LeadsExtInformation.BID, bid).in(LeadsExtInformation.ID, leadsIds));
    }
}

package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.LeadsBatchGetRequest;
import com.inngke.bp.leads.dto.request.LeadsBatchListRequest;
import com.inngke.bp.leads.dto.request.LeadsBatchImportRequest;
import com.inngke.bp.leads.dto.response.LeadsBatchDto;
import com.inngke.bp.leads.dto.response.LeadsBatchImportDto;
import com.inngke.bp.leads.dto.response.LeadsDraftDto;
import com.inngke.bp.leads.service.LeadsBatchService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索批量接口
 * @since 2021/9/8 8:25 PM
 */
@RestController
@RequestMapping("/api/leads-batch")
public class LeadsBatchApiController {
    @Autowired
    private LeadsBatchService leadsBatchService;

    /**
     * 创建批量导入
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    批量导入请求
     * @return 返回批次ID，可以通过此批次ID查询批次处理结果
     */
    @PostMapping
    public BaseResponse<Long> create(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated LeadsBatchImportRequest request,
            @RequestAttribute JwtPayload jwtPayload) {

        request.setBid(bid);
        request.setOperatorId(operatorId);
        request.setSid(jwtPayload.getSid());
        return leadsBatchService.create(request);
    }

    /**
     * 查询线索导入批次信息
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return 线索批次信息
     */
    @GetMapping("/{id:\\d+}")
    public BaseResponse<LeadsBatchDto> get(
            @PathVariable long id,
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        LeadsBatchGetRequest request = new LeadsBatchGetRequest();
        request.setLeadsBatchId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsBatchService.get(request);
    }

    /**
     * 查询某个批次的线索预览
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    批次请求
     * @return 某个批次的线索列表
     */
    @GetMapping("/leads-list")
    public BaseResponse<BasePaginationResponse<LeadsDraftDto>> getLeadsDraftList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsBatchListRequest request
    ) {
        request.setBid(bid);
        request.setOperatorId(operatorId);
        return leadsBatchService.getLeadsDraftList(request);
    }

    /**
     * 导入某个批次
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    批次请求
     * @return 是否导入成功
     */
    @PostMapping("/import")
    public BaseResponse<LeadsBatchImportDto> importLeadsBatch(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Validated LeadsBatchGetRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setStaffId(jwtPayload.getSid());
        return leadsBatchService.importLeadsBatch(request);
    }

    /**
     * 获取30天内所有的批次信息
     *
     * @param bid
     * @param operatorId
     * @return
     */
    @GetMapping
    public BaseResponse<List<LeadsBatchDto>> getBatchList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId
    ) {
        LeadsBatchGetRequest request = new LeadsBatchGetRequest();
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsBatchService.getBatchList(request);
    }
}

package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptPageRequest;
import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.NotNull;

/**
 * 线索标签详情
 *
 * <AUTHOR>
 * @since 2022/6/9
 **/
public class LeadsTagsDetailRequest extends BaseBidOptRequest {

    /**
     * 线索Id
     * @demo 1
     */
    @NotNull
    private Long leadsId;
    /**
     * 客户ID
     * @demo 1
     */
    @NotNull
    private Long customerId;


    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
}

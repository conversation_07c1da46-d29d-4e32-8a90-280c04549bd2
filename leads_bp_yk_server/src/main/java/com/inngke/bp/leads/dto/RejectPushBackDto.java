package com.inngke.bp.leads.dto;

import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.common.utils.DateTimeUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 拒绝线索退回表单dto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/6 15:34
 */
public class RejectPushBackDto implements Serializable {
    private Long pushBackLogId;

    private Integer bid;

    private String rejectReason;

    private Long rejectBy;

    private Long rejectTime;

    public Long getPushBackLogId() {
        return pushBackLogId;
    }

    public void setPushBackLogId(Long pushBackLogId) {
        this.pushBackLogId = pushBackLogId;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public Long getRejectBy() {
        return rejectBy;
    }

    public void setRejectBy(Long rejectBy) {
        this.rejectBy = rejectBy;
    }

    public Long getRejectTime() {
        return rejectTime;
    }

    public void setRejectTime(Long rejectTime) {
        this.rejectTime = rejectTime;
    }

    public LeadsPushBackLog convertLeadsPushBackLog() {
        LeadsPushBackLog leadsPushBackLog = new LeadsPushBackLog();
        leadsPushBackLog.setId(this.getPushBackLogId());
        leadsPushBackLog.setBid(this.getBid());
        leadsPushBackLog.setIsReject(1);
        leadsPushBackLog.setRejectBy(this.getRejectBy());
        leadsPushBackLog.setRejectTime(Optional.ofNullable(this.getRejectTime()).map(DateTimeUtils::MillisToLocalDateTime).orElse(LocalDateTime.now()));
        leadsPushBackLog.setRejectReason(this.getRejectReason());
        leadsPushBackLog.setUpdateTime(LocalDateTime.now());
        return leadsPushBackLog;

    }
}

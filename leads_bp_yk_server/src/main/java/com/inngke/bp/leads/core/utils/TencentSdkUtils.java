package com.inngke.bp.leads.core.utils;

import com.inngke.common.utils.StringUtils;
import com.tencent.ads.ApiContextConfig;
import com.tencent.ads.TencentAds;

/**
 * <AUTHOR>
 * @date 2022/4/21 10:58
 */
public class TencentSdkUtils {

    public static TencentAds getTencentAds(String accessToken){
        TencentAds tencentAds = TencentAds.getInstance();

        ApiContextConfig apiContextConfig = new ApiContextConfig();
        if (!StringUtils.isEmpty(accessToken)){
            apiContextConfig.setAccessToken(accessToken);
        }
        tencentAds.init(apiContextConfig);
        tencentAds.setDebug(true);
        tencentAds.useProduction();

        return tencentAds;
    }

    public static TencentAds getTencentAds(){
        return getTencentAds("");
    }
}

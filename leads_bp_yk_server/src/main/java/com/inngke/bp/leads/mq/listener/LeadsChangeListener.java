package com.inngke.bp.leads.mq.listener;

import com.inngke.bp.leads.mq.message.leads.LeadsChangeMessage;
import com.inngke.bp.leads.mq.process.LeadsProcess;
import com.inngke.common.mq.InngkeMqListener;
import com.inngke.common.mq.annotation.MqConsumer;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@MqConsumer(value = LeadsChangeListener.TOPIC_LEADS_CHANGE, consumerName = "leads_bp_yk_leads_change", messageType = LeadsChangeMessage.class)
public class LeadsChangeListener implements InngkeMqListener<LeadsChangeMessage> {
    private static final Logger logger = LoggerFactory.getLogger(LeadsChangeListener.class);
    public static final String TOPIC_LEADS_CHANGE = "leads_change";

    @Autowired
    private List<LeadsProcess> leadsProcessList;
    private Map<Integer, LeadsProcess> leadsProcessMap;

    @Autowired
    protected JsonService jsonService;

    @PostConstruct
    public void init() {
        leadsProcessMap = leadsProcessList.stream().collect(Collectors.toMap(LeadsProcess::type, process -> process));
    }

    @Override
    public void process(LeadsChangeMessage leadsChangeMessage) {
        List<Long> leadsIds = leadsChangeMessage.getLeadsId();
        if (CollectionUtils.isEmpty(leadsIds)) {
            return;
        }
        Integer event = leadsChangeMessage.getEvent();
        LeadsProcess leadsProcess = null;
        //event不传值默认为新增
        if (Objects.isNull(event)) {
            leadsProcess = leadsProcessMap.get(1);
        }
        leadsProcess = leadsProcessMap.get(event);
        if (Objects.isNull(leadsProcess)) {
            logger.warn("未处理的线索事件类型：event={}", leadsChangeMessage.getEvent());
            return;
        }
        leadsProcess.process(leadsChangeMessage);
    }
}

package com.inngke.bp.leads.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.client.dto.response.client.ClientFollowItemDto;
import com.inngke.bp.content.dto.request.ContentGetListRequest;
import com.inngke.bp.content.dto.request.ProductGetListRequest;
import com.inngke.bp.content.dto.response.ContentDto;
import com.inngke.bp.content.dto.response.product.ProductDto;
import com.inngke.bp.content.enums.ContentTypeEnum;
import com.inngke.bp.content.service.ContentGetService;
import com.inngke.bp.content.service.ProductService;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.core.utils.LeadsChannelUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.entity.LeadsPushBackLog;
import com.inngke.bp.leads.db.leads.manager.*;
import com.inngke.bp.leads.dto.LeadsFollowBatchCountDTO;
import com.inngke.bp.leads.dto.request.LeadsChannelListRequest;
import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import com.inngke.bp.leads.enums.LeadsPreFollowStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsChannelService;
import com.inngke.bp.leads.service.LeadsGetService;
import com.inngke.bp.leads.service.LeadsListPermissionsContext;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.service.AgentService;
import com.inngke.bp.organize.utils.AgentUtil;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@DubboService(version = "1.0.0")
public class LeadsGetServiceImpl implements LeadsGetService {

    private static final String INDEX_ALIAS = "leads";

    public static final Integer TRANSFER_CLIENT = 100;

    /**
     * 未分配
     */
    private static final Integer STATUS_GROUP_UNASSIGNED = 4;

    /**
     * 已分配
     */
    private static final Integer STATUS_GROUP_ASSIGNED = 5;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private LeadsListPermissionsContext leadsListPermissionsContext;

    @Autowired
    private LeadsService leadsService;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;

    @Autowired
    private ClientGetClientFollowForLeads clientGetClientFollowForLeads;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private AgentService agentService;

    @DubboReference(version = "1.0.0", timeout = 6000, url = "${inngke.dubbo.url.content_bp_yk:}")
    private ContentGetService contentGetService;

    @Autowired
    private ClientLevelClientForLeads clientLevelClientForLeads;

    @Autowired
    private LeadsCallLogManager leadsCallLogManager;

    @Autowired
    private LeadsChannelService leadsChannelService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Resource
    private LeadsPushBackLogManager leadsPushBackLogManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.content_bp_yk:}")
    private ProductService productService;

    @Override
    public BaseResponse<LeadsListVo> search(SearchLeadsRequest request) {
        //获取员工当前所有角色的code
        Set<String> userRoleCodes = rbacClientForLeads.getUserRoleCode(request.getBid(), request.getSid());

        LeadsListVo leadsListVo = new LeadsListVo();
        leadsListVo.setList(Lists.newArrayList());
        leadsListVo.setTotal(0);

        //查询Es 注：只是为了拼接数据，主数据体是是Mysql
        SearchResponse searchResponse = searchLeadsFromEs(request, userRoleCodes);
        if (ObjectUtils.isEmpty(searchResponse)) {
            return BaseResponse.success(leadsListVo);
        }

        // 判断跟进提醒按钮是否可以按、一天内通知点击次数
        searchExtInfo(leadsListVo, request, userRoleCodes);


        List<LeadsEsDto> esDocList = getEsDocList(searchResponse);

        if (CollectionUtils.isEmpty(esDocList)) {
            return BaseResponse.success(leadsListVo);
        }
        int total = (int) searchResponse.getHits().getTotalHits().value;

        Set<Long> leadsIds = Sets.newHashSet();
        Set<Long> staffIds = Sets.newHashSet();
        Set<Integer> levelIds = Sets.newHashSet();
        Set<Long> productIds = Sets.newHashSet();

        Map<Long, LeadsEsDto> leadsDocMap = esDocList.stream()
                .peek(leadsEsDto -> {
                    leadsIds.add(leadsEsDto.getLeadsId());
                    staffIds.add(leadsEsDto.getPreFollowStaffId());
                    staffIds.add(leadsEsDto.getPushBackStaffId());
                    staffIds.add(leadsEsDto.getCreateStaffId());
                    levelIds.add(leadsEsDto.getLevelId());
                    if (!CollectionUtils.isEmpty(leadsEsDto.getProductIds())) {
                        productIds.addAll(leadsEsDto.getProductIds());
                    }
                })
                .collect(Collectors.toMap(LeadsEsDto::getLeadsId, Function.identity()));
        boolean isPreList = Objects.nonNull(request.getStatusSet()) && request.getStatusSet().contains(LeadsStatusEnum.PRE_FOLLOW.getStatus());

        CompletableFuture<List<Leads>> leadsListFromDbFuture = AsyncUtils.supplyTraceAsync(() -> getLeadsByIds(request.getBid(), leadsIds, request.getSortType(), isPreList));
        CompletableFuture<List<LeadsExtInformation>> leadsExtFuture = AsyncUtils.supplyTraceAsync(() -> leadsExtInformationManager.getByIds(request.getBid(), leadsIds));
        CompletableFuture<Map<Integer, String>> levelMapFuture = AsyncUtils.supplyTraceAsync(() -> clientLevelClientForLeads.getNameMapByIds(request.getBid(), levelIds));
        CompletableFuture<Map<Long, LocalDateTime>> leadsFirstCallTimeMapFuture = AsyncUtils.supplyTraceAsync(() -> leadsFollowManager.getLeadsFirstCallTime(request.getBid(), leadsIds));
//        CompletableFuture<List<ContentDto>> goodsFuture = AsyncUtils.supplyTraceAsync(() -> getContentList(ContentTypeEnum.SHOP_PRODUCT, request.getBid(), Lists.newArrayList(productIds)));
        CompletableFuture<List<ProductDto>> productFuture = AsyncUtils.supplyTraceAsync(() -> {
            if (CollectionUtils.isEmpty(productIds)) {
                return Lists.newArrayList();
            }
            ProductGetListRequest productRequest = new ProductGetListRequest();
            productRequest.setBid(request.getBid());
            productRequest.setProductIds(Lists.newArrayList(productIds));
            BaseResponse<List<ProductDto>> response = productService.list(productRequest);
            return response.getData();
        });

        //从数据库中获取线索
        List<Leads> leadsListFromDb = AsyncUtils.getFutureData(leadsListFromDbFuture);
        //获取线索扩展信息
        List<LeadsExtInformation> leadsExt = AsyncUtils.getFutureData(leadsExtFuture);
        Map<Long, LeadsExtInformation> leadsExtMap = leadsExt.stream().collect(Collectors.toMap(LeadsExtInformation::getId, Function.identity()));
        //等级
        Map<Integer, String> levelMap = AsyncUtils.getFutureData(levelMapFuture);
        //获取线索第一次跟进时间
        Map<Long, LocalDateTime> leadsFirstCallTimeMap = AsyncUtils.getFutureData(leadsFirstCallTimeMapFuture);

        //历史回收id Map
        Map<Long, List<Long>> leadsIdRecoveryFromIdMap = leadsListFromDb.stream()
                .collect(Collectors.toMap(Leads::getId, leads ->
                        !StringUtils.isEmpty(leads.getRecoveryFromIds()) ?
                                Arrays.stream(leads.getRecoveryFromIds().split(InngkeAppConst.COMMA_STR))
                                        .map(Long::valueOf)
                                        .collect(Collectors.toList()) :
                                Lists.newArrayList()
                ));

        CompletableFuture<Map<Long, Integer>> leadsFollowMapCompletableFuture =
                AsyncUtils.supplyTraceAsync(() -> getLeadsFollowMap(leadsIdRecoveryFromIdMap));

        CompletableFuture<Map<Long, List<ClientFollowItemDto>>> clientLeadsFollowMapCompletableFuture =
                AsyncUtils.supplyTraceAsync(() -> getClientLeadsFollowMap(request.getBid(), leadsListFromDb));

        CompletableFuture<Map<Long, String>> staffNameMapCompletableFuture = AsyncUtils.supplyTraceAsync(() -> {
            Map<Long, StaffDto> staffMap = staffClientForLeads.getStaffByIds(request.getBid(), Sets.newHashSet(staffIds));
            return staffMap.keySet().stream()
                    .collect(Collectors.toMap(Function.identity(), staffId -> staffMap.get(staffId).getName()));
        });

        //跟进记录条数
        Map<Long, Integer> leadsFollowMap = AsyncUtils.getFutureData(leadsFollowMapCompletableFuture);

        //转客户后的根进数量
        Map<Long, List<ClientFollowItemDto>> clientIdFollowListMap = AsyncUtils.getFutureData(clientLeadsFollowMapCompletableFuture);

        //员工名称
        Map<Long, String> staffNameMap = AsyncUtils.getFutureData(staffNameMapCompletableFuture);

        // 商品信息
        List<ProductDto> productList = AsyncUtils.getFutureData(productFuture);
        Map<Long, ProductDto> productMap =  CollectionUtils.isEmpty(productList) ? Maps.newHashMap() : productList.stream().collect(Collectors.toMap(ProductDto::getId, Function.identity()));

        leadsListVo.setList(leadsListFromDb
                .stream()
                .map(this::toLeadsListItem)
                .filter(Objects::nonNull)
                .peek(leadsVo -> {
                    //跟进记录
                    leadsVo.setFollowCount(Optional.ofNullable(leadsFollowMap.get(leadsVo.getId())).orElse(0));
                    //已分配员工/部门名称
                    LeadsEsDto leadsEsDto = leadsDocMap.get(leadsVo.getId());
                    if (!ObjectUtils.isEmpty(leadsEsDto)) {
                        leadsVo.setDistributeStaffName(leadsEsDto.getDistributeStaffName());
                        leadsVo.setDistributeStaffDepartment(leadsEsDto.getDepartmentName());
                    }
                    leadsVo.setDistributeAgentId(leadsEsDto.getDistributeAgentId());
                    leadsVo.setDistributeAgentName(leadsEsDto.getDistributeAgentName());
                    //客服员工名称
                    leadsVo.setPreFollowStaffName(staffNameMap.getOrDefault(leadsEsDto.getPreFollowStaffId(), InngkeAppConst.EMPTY_STR));
                    //退回员工名称
                    leadsVo.setPushBackStaffName(staffNameMap.getOrDefault(leadsEsDto.getPushBackStaffId(), InngkeAppConst.EMPTY_STR));
                    if (LeadsStatusEnum.PUSH_BACK.getStatus() == leadsEsDto.getStatus() && leadsEsDto.getPushBackStaffId() > 0) {
                        DepartmentDto departmentDto = departmentClientForLeads.getDepartmentByStaffId(leadsEsDto.getBid(), leadsEsDto.getPushBackStaffId());
                        leadsVo.setDistributeStaffDepartment(departmentDto.getName());
                        leadsVo.setDistributeAgentId(departmentDto.getAgentId());
                    }
                    // 创建人名称,线索导出需要
                    leadsVo.setCreateStaffName(staffNameMap.getOrDefault(leadsEsDto.getCreateStaffId(), InngkeAppConst.EMPTY_STR));
                    if (leadsEsDto.getCreateStaffId().equals(0L)) {
                        leadsVo.setCreateStaffName("系统对接");
                    }
                    if (!Long.valueOf(0).equals(leadsVo.getClientId()) && !ObjectUtils.isEmpty(leadsVo.getRelationClientTime())) {
                        System.out.println(leadsVo.getClientId());
                        List<ClientFollowItemDto> clientFollowItemDtoList = clientIdFollowListMap.get(leadsVo.getClientId());
                        if (!ObjectUtils.isEmpty(clientFollowItemDtoList)) {
                            long count = clientFollowItemDtoList.stream().filter(i -> i.getCreateTime() >= LocalDateTimeUtil.toEpochMilli(leadsVo.getRelationClientTime())).count();
                            leadsVo.setClientFollowCount(Optional.ofNullable(leadsVo.getFollowCount()).orElse(0) + (int) count);
                        }
                    } else {
                        leadsVo.setClientFollowCount(leadsVo.getFollowCount());
                    }

                    if (Objects.nonNull(leadsVo.getLevelId()) && leadsVo.getLevelId() > 0) {
                        Optional.ofNullable(levelMap.get(leadsVo.getLevelId())).ifPresent(leadsVo::setLevelText);
                    }
                    Optional.ofNullable(leadsFirstCallTimeMap.get(leadsVo.getId()))
                            .ifPresent(firstContactTime -> leadsVo.setFirstContactTime(DateTimeUtils.getMilli(firstContactTime)));
                    //留资时间
                    Optional.ofNullable(leadsExtMap.get(leadsVo.getId())).map(LeadsExtInformation::getSubmitTime)
                            .ifPresent(submitTime -> leadsVo.setSubmitTime(DateTimeUtils.getMilli(submitTime)));

                    if (!CollectionUtils.isEmpty(leadsVo.getProductIds())) {
                        List<ProductDto> products = leadsVo.getProductIds().stream().map(productMap::get).collect(Collectors.toList());

                        if (!CollectionUtils.isEmpty(products)) {
                            leadsVo.setProducts(products.stream().map(this::toProductDto).collect(Collectors.toList()));
                        }
                    }
                })
                .collect(Collectors.toList()));

        //设置线索扩展数据
        setExtInfo(request.getBid(), leadsIds, leadsListVo.getList());

        AgentUtil.translateName(agentService, request.getBid(), leadsListVo.getList());
        leadsListVo.setTotal(total);
        // 设置退回信息
        Set<Long> pushBackLeadsIds = leadsListVo.getList().stream().filter(e -> e.getStatus() == -1).map(LeadsListItemDto::getId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(pushBackLeadsIds)) {
            List<LeadsPushBackLog> leadsPushBackLogs = leadsPushBackLogManager.batchFindLastOneLeadsPushBackLogByLeadsId(request.getBid(), pushBackLeadsIds, 0);
            Map<Long, LeadsPushBackLog> pushBackLogMap = leadsPushBackLogs.stream().collect(Collectors.toMap(LeadsPushBackLog::getLeadsId, Function.identity()));
            for (LeadsListItemDto leadsListItemDto : leadsListVo.getList()) {
                LeadsPushBackLog pushBackLog = pushBackLogMap.get(leadsListItemDto.getId());
                if (Objects.nonNull(pushBackLog)) {
                    leadsListItemDto.setPushBackId(pushBackLog.getId());
                    leadsListItemDto.setPushBackImages(JsonUtil.jsonToList(pushBackLog.getPushBackReasonImages(), String.class));
                }
            }
        }

        return BaseResponse.success(leadsListVo);
    }

    private List<ContentDto> getContentList(ContentTypeEnum typeEnum, Integer bid, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ContentGetListRequest request = new ContentGetListRequest();
        request.setIds(Sets.newHashSet(ids));
        request.setBid(bid);
        request.setPageSize(1000);
        request.setPageNo(1);
        request.setType(Sets.newHashSet(typeEnum.getId()));

        return Optional.ofNullable(contentGetService.getList(request)).map(BaseResponse::getData).map(BasePaginationResponse::getList).orElse(Lists.newArrayList());
    }

    private com.inngke.bp.leads.dto.response.ProductDto toProductDto(ProductDto productDto) {
        if (Objects.isNull(productDto)) {
            return null;
        }
        com.inngke.bp.leads.dto.response.ProductDto leadsProduct = new com.inngke.bp.leads.dto.response.ProductDto();
        leadsProduct.setId(productDto.getId());
        leadsProduct.setBid(productDto.getBid());
        leadsProduct.setTitle(productDto.getTitle());
        leadsProduct.setDescription(productDto.getDescription());
        leadsProduct.setSpecDescription(productDto.getSpecDescription());
        leadsProduct.setCoverUrl(productDto.getCoverUrl());
        leadsProduct.setCoverImages(productDto.getCoverImages());
        leadsProduct.setVideoUrl(productDto.getVideoUrl());
        leadsProduct.setProductNo(productDto.getProductNo());
        leadsProduct.setSort(productDto.getSort());
        leadsProduct.setCategoryIds(productDto.getCategoryIds());
        leadsProduct.setCateGoryNames(productDto.getCateGoryNames());
        leadsProduct.setChannelIds(productDto.getChannelIds());
        leadsProduct.setDepartmentIds(productDto.getDepartmentIds());
        return leadsProduct;

    }

    private void searchExtInfo(LeadsListVo leadsListVo, SearchLeadsRequest request, Set<String> userRoleCodes) {
        if (request.getStatusGroup() == null || !request.getStatusGroup().equals(5)) {
            return;
        }
        Object o = redisTemplate.opsForValue().get(LeadsServiceImpl.LEADS_NOTIFY_INC + request.getBid());
        Long inc = o == null ? 0L : Long.parseLong(String.valueOf(o));
        leadsListVo.setLeadsNotifyTime(inc);
        // 已分配页待联系的线索数据
        SearchLeadsRequest searchLeadsRequest = new SearchLeadsRequest();
        searchLeadsRequest.setBid(request.getBid());
        searchLeadsRequest.setSid(request.getSid());
        searchLeadsRequest.setPageNo(1);
        searchLeadsRequest.setPageSize(1);
        searchLeadsRequest.setSortType(request.getSortType());
        searchLeadsRequest.setStatusSet(Sets.newHashSet(LeadsStatusEnum.DISTRIBUTED.getStatus()));
        searchLeadsRequest.setStatusGroup(request.getStatusGroup());
        SearchResponse searchResponse = searchLeadsFromEs(searchLeadsRequest, userRoleCodes);
        int total = (int) searchResponse.getHits().getTotalHits().value;
        if (total > 0) {
            leadsListVo.setHashDistribute(true);
        }
    }

    @Override
    public BaseResponse<List<LeadsEsDto>> searchByEs(SearchLeadsRequest request) {
        List<LeadsEsDto> esDocList = Lists.newArrayList();
        //查询Es 注：只是为了拼接数据，主数据体是是Mysql
        SearchResponse searchResponse = searchLeadsFromEs(request);
        if (ObjectUtils.isEmpty(searchResponse)) {
            return BaseResponse.success(esDocList);
        }

        esDocList = getEsDocList(searchResponse);

        if (CollectionUtils.isEmpty(esDocList)) {
            return BaseResponse.success(esDocList);
        }
        return BaseResponse.success(esDocList);
    }

    @Override
    public BaseResponse<List<LeadsEsDto>> batchGetLeads(BaseIdRequest request) {
        Long lastId = Optional.ofNullable(request.getId()).orElse(0L);

        return BaseResponse.success(
                leadsManager.list(Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq("client_id", 0)
                        .isNotNull("client_id")
                        .gt(Leads.STATUS, 0)
                        .lt(Leads.STATUS, 20)
                        .gt(Leads.ID, lastId)
                        .orderByAsc(Leads.ID)
                        .last("limit 5000")
                ).stream().map(LeadsConverter::toLeadsEsDto).collect(Collectors.toList())
        );
    }

    private void setExtInfo(Integer bid, Set<Long> leadsIds, List<LeadsListItemDto> list) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return;
        }
        Map<Long, LeadsExtInformation> leadsExtInformationMap = leadsExtInformationManager.list(
                Wrappers.<LeadsExtInformation>query()
                        .eq(LeadsExtInformation.BID, bid)
                        .in(LeadsExtInformation.ID, leadsIds)
        ).stream().collect(Collectors.toMap(LeadsExtInformation::getId, Function.identity()));

        if (CollectionUtils.isEmpty(leadsExtInformationMap)) {
            return;
        }

        list.forEach(
                leadsListItemDto -> {
                    LeadsExtInformation leadsExtInformation = leadsExtInformationMap.get(leadsListItemDto.getId());
                    if (Objects.isNull(leadsExtInformation)) {
                        return;
                    }
                    leadsListItemDto.setCampaignName(leadsExtInformation.getCampaignName());
                }
        );
    }

    private LeadsVo toLeadsListItem(Leads leads) {
        // 判空
        if (ObjectUtils.isEmpty(leads)) {
            return null;
        }
        LeadsVo leadsvo = new LeadsVo();
        leadsvo.setTpId(leads.getTpId());
        leadsvo.setId(leads.getId());
        leadsvo.setClientId(leads.getClientId());
        leadsvo.setCreateTime(leads.getCreateTime() != null ? leads.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : System.currentTimeMillis());
        leadsvo.setBid(leads.getBid());
        leadsvo.setName(leads.getName());
        leadsvo.setMobile(leads.getMobile());
        leadsvo.setCustomerId(leads.getCustomerId());
        leadsvo.setCustomerUid(leads.getCustomerUid());
        leadsvo.setStatus(leads.getStatus());
        leadsvo.setProvinceId(leads.getProvinceId());
        leadsvo.setWeChat(leads.getWeChat());
        leadsvo.setGoodsLink(leads.getGoodsLink());
        leadsvo.setChannelId(Optional.ofNullable(leads.getChannelId()).orElse(0L));
        leadsvo.setProvinceName(leads.getProvinceName());
        leadsvo.setCityId(leads.getCityId());
        leadsvo.setCityName(leads.getCityName());
        leadsvo.setAreaId(leads.getAreaId());
        leadsvo.setAreaName(leads.getAreaName());
        leadsvo.setAddress(leads.getAddress());
        leadsvo.setChannel(leads.getChannel());
        leadsvo.setChannelType(leads.getChannelType());
        leadsvo.setChannelSource(leads.getChannelSource());
        leadsvo.setOrderAccount(leads.getOrderAccount());
        leadsvo.setOrderSn(leads.getOrderSn());
        leadsvo.setGoodsName(leads.getGoodsName());
        leadsvo.setGoodsNum(leads.getGoodsNum());
        LocalDateTime payTime = leads.getPayTime();
        if (payTime != null) {
            leadsvo.setPayTime(leads.getPayTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setPayAmount(leads.getPayAmount());
        leadsvo.setOrderMessage(leads.getOrderMessage());
        leadsvo.setRemark(leads.getRemark());
        leadsvo.setTpLeadsId(leads.getTpLeadsId());
        leadsvo.setPromotionName(leads.getPromotionName());
        LocalDateTime registryTime = leads.getRegistryTime();
        if (registryTime != null) {
            leadsvo.setRegistryTime(registryTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        leadsvo.setExpectIn(leads.getExpectIn());
        leadsvo.setShowPhone(leads.getShowPhone());
        leadsvo.setStyle(leads.getStyle());
        leadsvo.setBatchId(leads.getBatchId());

        Long distributeStaffId = leads.getDistributeStaffId();
        leadsvo.setDistributeStaffId(distributeStaffId);

        leadsvo.setErrorMsg(leads.getErrorMsg());
        LeadsStatusEnum statusEnum = LeadsStatusEnum.parse(leads.getStatus());
        if (Objects.nonNull(leads.getClientId()) && !Objects.equals(leads.getClientId(), 0L)) {
            leadsvo.setClientId(leads.getClientId());
            leadsvo.setStatusText("已转客户");
            leadsvo.setRelationClientTime(leads.getRelationClientTime());
        } else {
            leadsvo.setStatusText(statusEnum == null ? InngkeAppConst.EMPTY_STR : statusEnum.getName());
        }
        leadsvo.setChannelText(LeadsChannelUtil.getName(leads.getBid(), leads.getChannel()));
        LocalDateTime distributeTime = leads.getDistributeTime();
        if (distributeTime != null) {
            leadsvo.setDistributeTime(distributeTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        LocalDateTime distributeFollowTime = leads.getDistributeFollowTime();
        if (distributeFollowTime != null) {
            leadsvo.setDistributeFollowTime(String.valueOf(distributeFollowTime.toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        }
        LocalDateTime updateTime = leads.getUpdateTime();
        if (updateTime != null) {
            leadsvo.setUpdateTime(updateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }

        LocalDateTime pushBackTime = leads.getPushBackTime();
        if (pushBackTime != null) {
            leadsvo.setPushBackTime(pushBackTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        //新增和修改时如果有自动分配员工则返回自动分配状态给前段
        if (distributeStaffId != null || leads.getPreFollowStaffId() != null) {
            leadsvo.setDistributeStatus(1);
        }
        leadsvo.setExtData(leads.getExtData());
        leadsvo.setChannelTypeText(LeadsDataSourceEnum.parse(leads.getChannelType()).getName());
        if (!com.inngke.common.utils.StringUtils.isEmpty(leads.getTags())) {
            leadsvo.setTags(Lists.newArrayList(leads.getTags().split(",")));
            leadsvo.setTagsSize(leadsvo.getTags().size());
        } else {
            leadsvo.setTagsSize(0);
        }
        leadsvo.setPreFollowStaffId(leads.getPreFollowStaffId());
        leadsvo.setPreFollowStatus(leads.getPreFollowStatus());
        LeadsPreFollowStatusEnum preFollowStatusEnum = LeadsPreFollowStatusEnum.parse(leads.getPreFollowStatus());
        leadsvo.setPreFollowStatusText(preFollowStatusEnum == null ? InngkeAppConst.EMPTY_STR : preFollowStatusEnum.getName());
        leadsvo.setLevel(leads.getLevel());
        leadsvo.setType(leads.getType());
        leadsvo.setCreateStaffId(leads.getCreateStaffId());
        leadsvo.setPushBackStaffId(leads.getPushBackStaffId());
        leadsvo.setLeadsSourceText(Objects.isNull(LeadsInputSourceEnum.parse(leads.getChannelSource())) ? null : LeadsInputSourceEnum.parse(leads.getChannelSource()).getName());
        if (!StringUtils.isEmpty(leads.getFollowStatuses())) {
            leadsvo.setFollowStatuses(jsonService.toObjectList(leads.getFollowStatuses(), LeadsFollowStatusDto.class));
        }
        if (!StringUtils.isEmpty(leads.getKfFollowStatuses())) {
            leadsvo.setKfFollowStatuses(jsonService.toObjectList(leads.getKfFollowStatuses(), LeadsFollowStatusDto.class));
        }
        leadsvo.setDemandProduct(leads.getDemandProduct());
        leadsvo.setLevelId(leads.getLevelId());

        if (Objects.nonNull(leads.getClientId()) && leads.getClientId() > 0) {
            leadsvo.setStatus(leads.getClientStatus());
            Optional.ofNullable(LeadsStatusEnum.parse(leads.getClientStatus())).map(LeadsStatusEnum::getName)
                    .ifPresent(leadsvo::setStatusText);
        }

        leadsvo.setProductIds(org.apache.commons.lang3.StringUtils.isBlank(leads.getProductIds()) ? Lists.newArrayList() : Splitter.on(InngkeAppConst.COMMA_STR).splitToList(leads.getProductIds()).stream().map(Long::valueOf).collect(Collectors.toList()));

        Optional.ofNullable(leads.getLastFollowTime()).map(DateTimeUtils::getMilli).ifPresent(leadsvo::setLastFollowTime);

        return leadsvo;
    }


    /**
     * 获取线索跟进记录Map
     *
     * @param leadsIdRecoveryFromIdMap leadsIdRecoveryFromIdMap
     */
    private Map<Long, Integer> getLeadsFollowMap(Map<Long, List<Long>> leadsIdRecoveryFromIdMap) {

        Set<Long> leadsIds = Sets.newHashSet(leadsIdRecoveryFromIdMap.keySet());
        leadsIds.addAll(leadsIdRecoveryFromIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet()));

        List<LeadsFollowBatchCountDTO> leadsFollowBatchCountDTOS = leadsFollowManager.countBatch(leadsIds);
        Map<Long, Integer> leadsFollowCountMap = leadsFollowBatchCountDTOS.stream()
                .collect(Collectors.toMap(LeadsFollowBatchCountDTO::getLeadsId, LeadsFollowBatchCountDTO::getFollowCount));

        return leadsIdRecoveryFromIdMap.keySet().stream().collect(Collectors.toMap(Function.identity(), leadsId -> {
            List<Long> recoveryIds = leadsIdRecoveryFromIdMap.get(leadsId);
            int count = Optional.ofNullable(leadsFollowCountMap.get(leadsId)).orElse(0);
            for (Long recoveryId : recoveryIds) {
                count += Optional.ofNullable(leadsFollowCountMap.get(recoveryId)).orElse(0);
            }
            return count;
        }));
    }

    /**
     * 获取线索跟进记录Map(转客户)
     */
    private Map<Long, List<ClientFollowItemDto>> getClientLeadsFollowMap(Integer bid, List<Leads> leadsListFromDb) {
        Set<Long> clientIds = leadsListFromDb.stream().filter(i -> i.getClientId() > 0).map(Leads::getClientId).collect(Collectors.toSet());
        if (ObjectUtils.isEmpty(clientIds)) {
            Maps.newHashMap();
        }
        Map<Long, List<ClientFollowItemDto>> clientIdFollowListMap = clientGetClientFollowForLeads.getClientFollowListByClientIds(bid, Lists.newArrayList(clientIds))
                .stream().collect(Collectors.groupingBy(ClientFollowItemDto::getClientId, Collectors.mapping(Function.identity(), Collectors.toList())));

        return clientIdFollowListMap;
    }

    /**
     * 通过ID 从数据库获取线索
     *
     * @param bid       bid
     * @param leadsIds  leadsIds
     * @param sortType  sortType
     * @param isPreList
     */
    private List<Leads> getLeadsByIds(int bid, Set<Long> leadsIds, Integer sortType, boolean isPreList) {
        return leadsManager.list(buildSort(sortType, Wrappers.<Leads>query().eq(Leads.BID, bid).in(Leads.ID, leadsIds), isPreList));
    }

    /**
     * 解析Es返回
     *
     * @param searchResponse searchResponse
     */
    private List<LeadsEsDto> getEsDocList(SearchResponse searchResponse) {
        if (!ObjectUtils.isEmpty(searchResponse.getHits()) && !NumberUtil.equals(searchResponse.getHits().getTotalHits().value, 0L)) {
            return Arrays.stream(searchResponse.getHits().getHits())
                    .map(dataStr -> jsonService.toObject(dataStr.getSourceAsString(), LeadsEsDto.class))
                    .collect(Collectors.toList());
        }
        return null;
    }

    /**
     * Es 搜索
     *
     * @param request       request
     * @param userRoleCodes userRoleCodes
     * @return SearchResponse
     */
    private SearchResponse searchLeadsFromEs(SearchLeadsRequest request, Set<String> userRoleCodes) {
        //构建Es查询条件
        BoolQueryBuilder queryBuilder = toSearchQueryBuilder(request);

        //不同角色不同列表的数据权限设置
        setListPermissionsParams(request, queryBuilder, userRoleCodes);
        //执行Es查询
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder)
                .from((request.getPageNo() - 1) * request.getPageSize()).size(request.getPageSize()).trackTotalHits(true);


        buildSort(request.getSortType(), searchSourceBuilder,
                Objects.nonNull(request.getStatusSet()) &&
                        request.getStatusSet().contains(LeadsStatusEnum.PRE_FOLLOW.getStatus()));

        SearchRequest searchRequest = new SearchRequest().indices(INDEX_ALIAS).source(searchSourceBuilder);

        try {
            return restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private SearchResponse searchLeadsFromEs(SearchLeadsRequest request) {

        BoolQueryBuilder queryBuilder = toSearchQueryBuilder(request);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder)
                .from((request.getPageNo() - 1) * request.getPageSize()).size(request.getPageSize()).trackTotalHits(true);

        if (Boolean.TRUE.equals(request.getAuthority())) {
            Set<String> userRoleCodes = rbacClientForLeads.getUserRoleCode(request.getBid(), request.getSid());
            //不同角色不同列表的数据权限设置
            setListPermissionsParams(request, queryBuilder, userRoleCodes);
        }

        buildSort(request.getSortType(), searchSourceBuilder);

        SearchRequest searchRequest = new SearchRequest().indices(INDEX_ALIAS).source(searchSourceBuilder);

        try {
            return restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 设置列表权限过滤参数
     *
     * @param request       request
     * @param queryBuilder  queryBuilder
     * @param userRoleCodes userRoleCodes
     */
    private void setListPermissionsParams(SearchLeadsRequest request, BoolQueryBuilder queryBuilder, Set<String> userRoleCodes) {
        leadsListPermissionsContext.handle(request, queryBuilder, userRoleCodes);
    }

    /**
     * 构造ES查询条件
     *
     * @param request SearchLeadsRequest
     * @return BoolQueryBuilder
     */
    private BoolQueryBuilder toSearchQueryBuilder(SearchLeadsRequest request) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("bid", request.getBid()));

        //手机号,名称,微信号
        if (!StringUtils.isEmpty(request.getKeyword())) {
            queryBuilder.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.MOBILE_KEYWORD, getWildcardValue(request.getKeyword())))
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.WECHATID_KEYWORD, getWildcardValue(request.getKeyword())))
            );
        }
        if (!StringUtils.isEmpty(request.getAddress())){
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.ADDRESS_KEYWORD,getWildcardValue(request.getAddress())));
        }
        if (!StringUtils.isEmpty(request.getName())) {
            queryBuilder.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.NAME_KEYWORD, getWildcardValue(request.getName()))));
        }

        if (Objects.nonNull(request.getClientId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CLIENT_ID, request.getClientId()));
        }

        //数据来源
        if (!CollectionUtils.isEmpty(request.getChannelTypeSet())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.CHANNEL_TYPE, request.getChannelTypeSet()));
        }

        //线索来源
        if (!CollectionUtils.isEmpty(request.getChannelSourceSet())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.CHANNEL_SOURCE, request.getChannelSourceSet()));
        }

        //渠道来源
        if (!CollectionUtils.isEmpty(request.getChannelSet())) {
            Set<Integer> channelSet = getChannelSet(request.getBid(), request.getChannelSet());
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.CHANNEL, channelSet));
        }

        //省市区
        if (!ObjectUtils.isEmpty(request.getProvinceId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PROVINCE_ID, request.getProvinceId()));
        }
        if (!ObjectUtils.isEmpty(request.getCityId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CITY_ID, request.getCityId()));
        }
        if (!ObjectUtils.isEmpty(request.getAreaId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.AREA_ID, request.getAreaId()));
        }

        //客户等级
        if (!CollectionUtils.isEmpty(request.getLevelSet())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.LEVEL_KEYWORD, request.getLevelSet()));
        }

        // 客户等级
        if (!CollectionUtils.isEmpty(request.getLevelIdSet())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.LEVEL_ID, request.getLevelIdSet()));
        }
        //线索标签
        if (!StringUtils.isEmpty(request.getTag())) {
            List<String> tags = Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(request.getTag()));
            BoolQueryBuilder tagsQuery = QueryBuilders.boolQuery();
            tags.forEach(tag-> tagsQuery.should(QueryBuilders.wildcardQuery(LeadsEsDto.TAGS_LIST_KEYWORD,InngkeAppConst.STAR_STR + tag + InngkeAppConst.STAR_STR))
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.ENTERPRISE_TAGS_KEYWORD,InngkeAppConst.STAR_STR + tag + InngkeAppConst.STAR_STR)));
            queryBuilder.must(tagsQuery);
        }

        //企业标签
        if (!StringUtils.isEmpty(request.getEnterpriseTags())) {
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.ENTERPRISE_TAGS_KEYWORD,
                    InngkeAppConst.STAR_STR + request.getTag() + InngkeAppConst.STAR_STR
            ));
        }

        //需求产品渠道
        if (!CollectionUtils.isEmpty(request.getShopChannelIds())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.CHANNEL_ID, request.getShopChannelIds()));
        }

        //计划名称
        String campaignKeyword = request.getCampaignKeyword();
        if (!StringUtils.isEmpty(campaignKeyword)) {
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.CAMPAIGN_NAME, getWildcardValue(campaignKeyword)));
        }

        //创建人
        if (!ObjectUtils.isEmpty(request.getCreateStaffId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CREATE_STAFF_ID, request.getCreateStaffId()));
        }

        //导入时间
        buildTimeQuery(LeadsEsDto.CREATE_TIME, DateTimeUtils.toLocalDateTime(request.getImportTimeStart()),
                DateTimeUtils.toLocalDateTime(request.getImportTimeEnd()), queryBuilder);

        //线索状态-优先使用status
        if (!CollectionUtils.isEmpty(request.getStatusSet())) {
            queryBuilder.must(
                    QueryBuilders.boolQuery().should(
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, request.getStatusSet()))
                                    .must(QueryBuilders.termQuery(LeadsEsDto.CLIENT_ID, 0))
                    ).should(
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery(LeadsEsDto.CLIENT_STATUS, request.getStatusSet()))
                                    .mustNot(QueryBuilders.termQuery(LeadsEsDto.CLIENT_ID, 0))
                    )
            );
        }
        //线索状态-已分配
        if (STATUS_GROUP_ASSIGNED.equals(request.getStatusGroup())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.STATUS, LeadsStatusEnum.getAllocatedLeadsStatus()));
        }
        //线索状态-未分配
        if (STATUS_GROUP_UNASSIGNED.equals(request.getStatusGroup())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.TO_DISTRIBUTE.getStatus()));
        }

        //跟进客服
        if (!ObjectUtils.isEmpty(request.getPreFollowStaffId()) && !request.getPreFollowStaffId().equals(0L)) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, request.getPreFollowStaffId()));
        }

        //订单编号
        if (!StringUtils.isEmpty(request.getOrderNum())) {
            //queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.ORDER_SN, request.getOrderNum()));
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.ORDER_SN + ".keyword", getWildcardValue(request.getOrderNum())));
        }

        //负责人
        if (!ObjectUtils.isEmpty(request.getStaffId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, request.getStaffId()));
        }


        //退回员工ID
        if (!CollectionUtils.isEmpty(request.getPushBackStaffIds())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.PUSH_BACK_STAFF_ID, request.getPushBackStaffIds()));
        }

        //退回原因
        if (!StringUtils.isEmpty(request.getPushBackContent())) {
            queryBuilder.must(QueryBuilders.wildcardQuery(LeadsEsDto.ERROR_MSG_KEYWORD, getWildcardValue(request.getPushBackContent())));
        }

        //退回原因
        if (!ObjectUtils.isEmpty(request.getLastReasonId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.LAST_REASON_ID, request.getLastReasonId()));
        }

        //退回时间
        buildTimeQuery(LeadsEsDto.PUSH_BACK_TIME, DateTimeUtils.toLocalDateTime(request.getPushBackStartTime()),
                DateTimeUtils.toLocalDateTime(request.getPushBackEndTime()), queryBuilder);

        //分配时间
        buildTimeQuery(LeadsEsDto.DISTRIBUTE_TIME, DateTimeUtils.toLocalDateTime(request.getDistributeTimeStart()),
                DateTimeUtils.toLocalDateTime(request.getDistributeTimeEnd()), queryBuilder);

        // 分配给客服的分配时间
        buildTimeQuery(LeadsEsDto.DISTRIBUTE_FOLLOW_TIME, DateTimeUtils.toLocalDateTime(request.getDistributeFollowTimeStart()),
                DateTimeUtils.toLocalDateTime(request.getDistributeFollowTimeEnd()), queryBuilder);

        //最后跟进时间
        buildTimeQuery(LeadsEsDto.LAST_FOLLOW_TIME, DateTimeUtils.toLocalDateTime(request.getLastFollowTimeStart()),
                DateTimeUtils.toLocalDateTime(request.getLastFollowTimeEnd()), queryBuilder);

        //更新时间
        if (!StringUtils.isEmpty(request.getLastUpdateTime())) {
            queryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.UPDATE_TIME).gte(request.getLastUpdateTime()));
        }

        // 客服状态
        if (!CollectionUtils.isEmpty(request.getPreFollowStatusSet())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.PRE_FOLLOW_STATUS, request.getPreFollowStatusSet()));
        }

        // 所在部门
        if (!CollectionUtils.isEmpty(request.getDepartmentIds())) {
            queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, request.getDepartmentIds()));
        }

        // 所在部门（并集）
        List<Set<Long>> departmentIdsCollection = request.getDepartmentIdsCollection();
        if (!CollectionUtils.isEmpty(departmentIdsCollection)) {
            departmentIdsCollection.forEach(departmentIds -> {
                queryBuilder.must(QueryBuilders.termsQuery(LeadsEsDto.DEPT_IDS, departmentIds));
            });
        }


        //客户手机号,名称模糊搜索
        if (!StringUtils.isEmpty(request.getCustomerKeyword())) {
            queryBuilder.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.NAME_KEYWORD, getWildcardValue(request.getCustomerKeyword())))
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.MOBILE_KEYWORD, getWildcardValue(request.getCustomerKeyword())))
                    .should(QueryBuilders.wildcardQuery(LeadsEsDto.WECHATID_KEYWORD, getWildcardValue(request.getCustomerKeyword())))
            );
            queryBuilder.mustNot(QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, -4))
                    .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, -5))
            );
        }


        // 经销商
        if (Objects.nonNull(request.getDistributeAgentId())
                && !CollectionUtils.isEmpty(request.getStatusSet())
                && request.getStatusSet().size() == 1
                && request.getStatusSet().contains(LeadsStatusEnum.PUSH_BACK.getStatus())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.PUSH_BACK_AGENT_ID, request.getDistributeAgentId()));
        } else if (Objects.nonNull(request.getDistributeAgentId())) {
            queryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_AGENT_ID, request.getDistributeAgentId()));
        }

        if (!CollectionUtils.isEmpty(request.getProductIds())) {
            BoolQueryBuilder productQuery = QueryBuilders.boolQuery();
            List<Long> productIds = request.getProductIds();

            productIds.forEach(product -> {
                productQuery.must(QueryBuilders.termQuery("productIds", product));
            });
            queryBuilder.must(productQuery);
        }

        return queryBuilder;
    }

    /**
     * 查询渠道及其子渠道
     */
    private Set<Integer> getChannelSet(int bid, Set<Integer> channelSet) {
        Set<Integer> result = new HashSet<>(channelSet.size());
        LeadsChannelListRequest leadsChannelListRequest = new LeadsChannelListRequest();
        leadsChannelListRequest.setBid(bid);
        BaseResponse<List<LeadsChannelDto>> list = leadsChannelService.getList(leadsChannelListRequest);
        List<LeadsChannelDto> data = list.getData();
        if (CollectionUtils.isEmpty(data)) {
            return channelSet;
        }
        result.addAll(channelSet);
        data.forEach(item -> {
            if (channelSet.contains(item.getValue())) {
                if (CollectionUtils.isEmpty(item.getChildren())) {
                    return;
                }
                result.addAll(item.getChildren().stream().map(LeadsChannelDto::getValue).collect(Collectors.toSet()));
            }
        });
        return result;
    }

    /**
     * Es 排序
     *
     * @param sortType            sortType
     * @param searchSourceBuilder searchSourceBuilder
     */
    private void buildSort(Integer sortType, SearchSourceBuilder searchSourceBuilder) {
        //根据排序类型对结果进行排序,0=按导入时间倒序 1=按分配时间倒序 2=更新时间，即最新的修改的线索时间排序
        if (sortType != null) {
            switch (sortType) {
                case 0:
                    searchSourceBuilder.sort(LeadsEsDto.CREATE_TIME, SortOrder.DESC).sort("leadsId", SortOrder.DESC);
                    break;
                case 1:
                    searchSourceBuilder.sort(LeadsEsDto.DISTRIBUTE_TIME, SortOrder.DESC).sort("leadsId", SortOrder.DESC);
                    break;
                case 2:
                    searchSourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.DESC).sort("leadsId", SortOrder.DESC);
                    break;
                case 3:
                    searchSourceBuilder.sort(LeadsEsDto.UPDATE_TIME, SortOrder.ASC).sort("leadsId", SortOrder.DESC);
                    break;
                default:
                    searchSourceBuilder.sort("leadsId", SortOrder.DESC);
            }
        }
    }

    private void buildSort(Integer sortType, SearchSourceBuilder searchSourceBuilder, Boolean isPreFollowList) {
        if (isPreFollowList) {
            searchSourceBuilder.sort(LeadsEsDto.DISTRIBUTE_FOLLOW_TIME, SortOrder.DESC).sort("leadsId", SortOrder.DESC);
        } else {
            buildSort(sortType, searchSourceBuilder);
        }
    }

    /**
     * Db 排序
     *
     * @param sortType     sortType
     * @param queryWrapper queryWrapper
     */
    private QueryWrapper<Leads> buildSort(Integer sortType, QueryWrapper<Leads> queryWrapper, boolean isPreList) {
        //根据排序类型对结果进行排序,0=按导入时间倒序 1=按分配时间倒序 2=更新时间，即最新的修改的线索时间排序
        if (isPreList) {
            queryWrapper.orderByDesc(Leads.DISTRIBUTE_FOLLOW_TIME).orderByDesc(Leads.ID);
            return queryWrapper;
        }
        if (sortType != null) {
            switch (sortType) {
                case 0:
                    queryWrapper.orderByDesc(Leads.CREATE_TIME).orderByDesc(Leads.ID);
                    break;
                case 1:
                    queryWrapper.orderByDesc(Leads.DISTRIBUTE_TIME).orderByDesc(Leads.ID);
                    break;
                case 2:
                    queryWrapper.orderByDesc(Leads.UPDATE_TIME).orderByDesc(Leads.ID);
                    break;
                case 3:
                    queryWrapper.orderByAsc(Leads.UPDATE_TIME).orderByDesc(Leads.ID);
                    break;
                default:
                    queryWrapper.orderByDesc(Leads.ID);
            }
        }
        return queryWrapper;
    }

    /**
     * Es 模糊查询
     *
     * @param value value
     */
    private String getWildcardValue(String value) {
        return InngkeAppConst.STAR_STR + value + InngkeAppConst.STAR_STR;
    }

    /**
     * Es 时间查询
     *
     * @param field            field
     * @param startTime        startTime
     * @param endTime          endTime
     * @param boolQueryBuilder boolQueryBuilder
     */
    private void buildTimeQuery(String field, LocalDateTime startTime, LocalDateTime endTime, BoolQueryBuilder boolQueryBuilder) {
        if (!ObjectUtils.isEmpty(startTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(field).gte(DateTimeUtils.getMilli(startTime)));
        }
        if (!ObjectUtils.isEmpty(endTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(field).lte(DateTimeUtils.getMilli(endTime)));
        }
    }
}

package com.inngke.bp.leads.mq.message.client;

import com.inngke.common.dto.request.BaseBidOptRequest;

public class ClientChangeMessage extends BaseBidOptRequest {

    /**
     * ID
     */
    private Long id;

    /**
     * 变更类型
     *
     */
    private Integer event;

    /**
     * 业务类型
     */
    private String business;

    /**
     * 事件发生时间
     */
    private Long timestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
}

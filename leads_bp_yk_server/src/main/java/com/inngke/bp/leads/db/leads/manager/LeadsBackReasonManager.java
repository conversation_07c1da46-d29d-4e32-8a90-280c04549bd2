/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsBackReason;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface LeadsBackReasonManager extends IService<LeadsBackReason> {

    void moveSort(Integer bid, LeadsBackReason prevLeadsBackReason, LeadsBackReason nextLeadsBackReason);

    LeadsBackReason getById(Integer bid,Long id);
}

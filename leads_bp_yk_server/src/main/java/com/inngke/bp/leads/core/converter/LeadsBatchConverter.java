package com.inngke.bp.leads.core.converter;

import com.inngke.bp.leads.db.leads.entity.LeadsBatch;
import com.inngke.bp.leads.dto.response.LeadsBatchDto;

import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @since 2021/9/10 9:41 AM
 */
public class LeadsBatchConverter {

    private LeadsBatchConverter(){}
    public static LeadsBatchDto toLeadsBatchDto(LeadsBatch leadsBatch) {
        LeadsBatchDto leadsBatchDto = new LeadsBatchDto();

        leadsBatchDto.setId(leadsBatch.getId());
        leadsBatchDto.setBid(leadsBatch.getBid());
        leadsBatchDto.setChannel(leadsBatch.getChannel());
        leadsBatchDto.setFileUrl(leadsBatch.getFileUrl());
        leadsBatchDto.setFileType(leadsBatch.getFileType());
        leadsBatchDto.setProcessStatus(leadsBatch.getProcessStatus());
        leadsBatchDto.setSuccessCount(leadsBatch.getSuccessCount());
        leadsBatchDto.setErrorCount(leadsBatch.getErrorCount());
        leadsBatchDto.setStaffId(leadsBatch.getStaffId());
        leadsBatchDto.setErrorFileUrl(leadsBatch.getErrorFileUrl());
        leadsBatchDto.setCreateTime(leadsBatch.getCreateTime() != null ? leadsBatch.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);
        leadsBatchDto.setUpdateTime(leadsBatch.getUpdateTime() != null ? leadsBatch.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);

        return leadsBatchDto;
    }
}

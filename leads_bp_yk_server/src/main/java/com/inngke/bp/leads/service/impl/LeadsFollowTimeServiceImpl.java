package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.*;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowManager;
import com.inngke.bp.leads.db.leads.manager.LeadsFollowTimeManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.dto.response.LeadsOrganizationPreFollowDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsFollowTimeService;
import com.inngke.bp.leads.service.LeadsListPermissionsContext;
import com.inngke.bp.leads.service.LeadsSearchService;
import com.inngke.bp.leads.service.enums.LeadsReportListDetailTypeEnum;
import com.inngke.bp.leads.service.strategy.leadsFollowTime.LeadsFollowTimeContext;
import com.inngke.bp.leads.utils.DecimalUtils;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.LoginDto;
import com.inngke.bp.organize.dto.response.StaffDepartmentAgentSimpleDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.DateUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: moqinglong
 * @since 2022/4/7 10:13
 */
@Service
public class LeadsFollowTimeServiceImpl implements LeadsFollowTimeService {
    private static final Logger log = LoggerFactory.getLogger(LeadsFollowTimeServiceImpl.class);

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsFollowTimeManager leadsFollowTimeManager;

    @Autowired
    private LeadsFollowTimeContext leadsFollowTimeContext;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private CustomerGetServiceClientForLeads customerGetServiceClient;

    @Autowired
    private RestHighLevelClient esClient;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private LeadsListPermissionsContext leadsListPermissionsContext;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private LoginClientForLeads loginClientForLeads;

    @Autowired
    private LeadsSearchService leadsSearchService;

    @Autowired
    private StoreOrderClientForLeads storeOrderClientForLeads;

    @Override
    public BaseResponse<Void> allSync(int bid) {
        int limit = 1000;
        Long leadsFollowsId = null;
        while (true) {
            QueryWrapper<LeadsFollow> query = Wrappers.query();
            if (Objects.nonNull(leadsFollowsId)) {
                query.gt(LeadsFollow.ID, leadsFollowsId);
            }
            List<LeadsFollow> leadsFollows = leadsFollowManager.list(query.last("Limit " + limit));
            if (CollectionUtils.isEmpty(leadsFollows)) {
                break;
            }

            try {
                updateOrSaveLeadsFollowTime(leadsFollows);
            } catch (Exception e) {
                log.warn("save LeadsFollowTime fail", e);
            }

            leadsFollowsId = leadsFollows.get(leadsFollows.size() - 1).getId();
        }

        return BaseResponse.success();
    }

    @Override
    public void updateOrSaveLeadsFollowTime(List<LeadsFollow> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<Long, LeadsFollowTime> updateMap = Maps.newHashMap();
        Map<Long, LeadsFollowTime> saveMap = Maps.newHashMap();
        Set<Long> leadsIds = list.stream().map(LeadsFollow::getLeadsId).collect(Collectors.toSet());
        Map<Long, LeadsFollowTime> leadsFollowTimeMap = leadsFollowTimeManager.list(
                Wrappers.<LeadsFollowTime>query().in(LeadsFollowTime.ID, leadsIds)
        )
                .stream()
                .collect(Collectors.toMap(LeadsFollowTime::getId, Function.identity()));
        long time = DateTimeUtils.getMilli(LocalDateTime.now());
        for (LeadsFollow leadsFollow : list) {
            Long leadsId = leadsFollow.getLeadsId();

            LeadsFollowTime leadsFollowTime = leadsFollowTimeMap.get(leadsId);
            if (Objects.isNull(leadsFollowTime)) {
                if (!saveMap.containsKey(leadsId)) {
                    leadsFollowTime = leadsFollowTimeBeanProcess(leadsFollow);
                } else {
                    leadsFollowTime = saveMap.get(leadsId);
                }

                if (Objects.isNull(leadsFollowTime)) {
                    continue;
                }
                leadsFollowTimeContext.process(leadsFollow, leadsFollowTime);
                saveMap.put(leadsId, leadsFollowTime);
                continue;
            }

            //update
            leadsFollowTimeContext.process(leadsFollow, leadsFollowTime);
            updateMap.put(leadsId, leadsFollowTime);
        }

        if (!updateMap.isEmpty()) {
            leadsFollowTimeManager.updateBatchById(updateMap.values());
        }

        if (!saveMap.isEmpty()) {
            leadsFollowTimeManager.saveBatch(saveMap.values());
        }
    }

    private BoolQueryBuilder listByStaffBoolQueryBuilder(LeadsFollowTimeByStaffRequest request) {
        //查询员工管理权限
        //List<Long> mangerDeptIds = getMangerDeptIds(request.getBid(), request.getOperatorId());
        //qb.must(QueryBuilders.termsQuery("belongingDepartmentIds",mangerDeptIds));

        BoolQueryBuilder qb = QueryBuilders.boolQuery();
        qb.must(QueryBuilders.termQuery("bid", request.getBid()));
        qb.must(QueryBuilders.termQuery("staffIsDel", 1));

        qb.mustNot(QueryBuilders.termQuery("distributeStaffId", 0));
        if (request.getDepartmentId() != null) {
            //qb.must(QueryBuilders.termQuery("departmentId", request.getDepartmentId()));
            qb.must(QueryBuilders.termQuery(LeadsEsDto.DEPT_IDS, request.getDepartmentId()));
        }
        if (request.getStaffId() != null) {
            qb.must(QueryBuilders.termQuery("distributeStaffId", request.getStaffId()));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeStart())) {
            qb.must(QueryBuilders.rangeQuery("distributeTime").gte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeEnd())) {
            qb.must(QueryBuilders.rangeQuery("distributeTime").lte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeEnd())));
        }
        if (!StringUtils.isEmpty(request.getCreateTimeStart())) {
            qb.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).gte(DateTimeUtils.dateTimeStrToMilli(request.getCreateTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getCreateTimeEnd())) {
            qb.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).lte(DateTimeUtils.dateTimeStrToMilli(request.getCreateTimeEnd())));
        }

        qb.mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(0, -4, -5, 21)));

        // 权限控制
        Set<String> userRoleCode = rbacClientForLeads.getUserRoleCode(request.getBid(), request.getSId());
        setListPermissionsParams(request, qb, userRoleCode);

        return qb;
    }

    @Override
    public BaseResponse<BasePaginationResponse<LeadsFollowTimeByStaffResponse>> listByStaff(LeadsFollowTimeByStaffRequest request) {
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = 20;
        }
        pageNo = (pageNo - 1) * pageSize;

        //构建分页返回结果
        BasePaginationResponse<LeadsFollowTimeByStaffResponse> result = new BasePaginationResponse<>();
        List<LeadsFollowTimeByStaffResponse> dtoList = Lists.newArrayList();

        Script script = new Script("doc['distributeStaffId']");

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder countQb = listByStaffBoolQueryBuilder(request);
        BoolQueryBuilder qb = listByStaffBoolQueryBuilder(request);

        sourceBuilder.query(qb);
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("groupFiled").script(script)
                .subAggregation(
                        AggregationBuilders.terms("statusCount").field("status")
                );
        leadsFollowTimeContext.process(aggregation);

        SearchRequest counthRequest = new SearchRequest();
        counthRequest.indices("leads");
        String key = "distributeStaffId";
        //指定count(distinct)字段名,cardinality为指定字段的别名,field为指定字段
        CardinalityAggregationBuilder aggregationBuilder = AggregationBuilders.cardinality(key).field("distributeStaffId");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(countQb).aggregation(aggregationBuilder);
        //执行查询
        counthRequest.source(searchSourceBuilder);

        aggregation.subAggregation(AggregationBuilders.topHits("record"))
                .size(10000)
                .subAggregation(new BucketSortPipelineAggregationBuilder("bucket_field", null).from(pageNo).size(pageSize));

//        aggregation.size(pageSize);
        sourceBuilder.aggregation(aggregation);
        sourceBuilder.size(pageSize)
                .from(pageNo);
        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(sourceBuilder);
        SearchResponse response = null;
        long total = 0L;
        List<? extends Terms.Bucket> buckets = Lists.newArrayList();
        try {
            log.info("========>{}" + sourceBuilder.toString());
            SearchResponse countResponse = esClient.search(counthRequest, RequestOptions.DEFAULT);
            ParsedCardinality parsedCardinality = countResponse.getAggregations().get("distributeStaffId");
            total = parsedCardinality.getValue();
            response = esClient.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = response.getAggregations();
            ParsedStringTerms parsedStringTerms = aggregations.get("groupFiled");
            buckets = parsedStringTerms.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                LeadsFollowTimeByStaffResponse dto = new LeadsFollowTimeByStaffResponse();
                //获取总线索数
                long docCount = bucket.getDocCount();
                dto.setLeadsCount((int) docCount);
                Aggregations bucketAggregations = bucket.getAggregations();
                //获取doc
                ParsedTopHits record = bucketAggregations.get("record");
                SearchHit[] hits = record.getHits().getHits();
                List<SearchHit> searchHits = Lists.newArrayList(hits);
                List<LeadsEsDto> esDtoList = searchHits.stream().map(SearchHit::getSourceAsString).map(item -> jsonService.toObject(item, LeadsEsDto.class)).collect(Collectors.toList());

//                if(Objects.isNull(esDtoList.get(0).getDistributeStaffId())){
//                    continue;
//                }
                dto.setAgentId(esDtoList.get(0).getDistributeAgentId());
                dto.setStaffId(esDtoList.get(0).getDistributeStaffId());
                dto.setBid(esDtoList.get(0).getBid());


                StaffListRequest staffListRequest = new StaffListRequest();
                staffListRequest.setBid(request.getBid());
                staffListRequest.setId(esDtoList.get(0).getDistributeStaffId());
                List<StaffDto> staffInfoList = staffClientForLeads.getStaffList(staffListRequest);
                if (Objects.isNull(staffInfoList) || staffInfoList.isEmpty()) {
                    continue;
                }

                StaffDto staffDto = staffInfoList.get(0);
                dto.setStaffName(staffDto.getName());
                dto.setMobile(staffDto.getMobile());
                Long departmentId = staffDto.getDepartmentId();

                if (!Objects.isNull(departmentId)) {
                    BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
                    baseIdsRequest.setIds(Collections.singletonList(esDtoList.get(0).getDistributeStaffId()));
                    baseIdsRequest.setBid(esDtoList.get(0).getBid());
                    List<StaffDepartmentAgentSimpleDto> staffDepartmentAgentSimpleList = staffClientForLeads.getStaffDepartmentAgentSimple(baseIdsRequest);
                    StaffDepartmentAgentSimpleDto staffDepartmentAgentSimple = staffDepartmentAgentSimpleList.get(0);
                    dto.setDepartmentName(staffDepartmentAgentSimple.getDepartmentName());
                    if (StringUtils.isEmpty(dto.getDepartmentName())) {
                        dto.setDepartmentName(esDtoList.get(0).getDepartmentName());
                    }
                } else {
                    dto.setDepartmentName(esDtoList.get(0).getDepartmentName());
                }

                // 通过员工编号拿经销商名称
                BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
                baseIdsRequest.setBid(request.getBid());
                baseIdsRequest.setIds(Collections.singletonList(staffDto.getId()));
                List<StaffDepartmentAgentSimpleDto> staffDepartmentAgentSimpleList = staffClientForLeads.getStaffDepartmentAgentSimple(baseIdsRequest);
                StaffDepartmentAgentSimpleDto staffDepartmentAgentSimpleDto = staffDepartmentAgentSimpleList.get(0);

                // 经销商名称
                dto.setAgentName(staffDepartmentAgentSimpleDto.getAgentName());
                leadsFollowTimeContext.process(bucketAggregations, dto);
                dtoList.add(dto);
            }

        } catch (IOException exception) {
            log.error("获取ES数据失败:", exception);
        }

        List<LeadsFollowTimeByStaffResponse> resp = converterFirstCallIntervalTime(dtoList);
        result.setList(resp);
        result.setTotal((int) total);
        return BaseResponse.success(result);
    }


    @Override
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByStaffDetail(LeadsFollowTimeByStaffRequest request) {
        if (request.getStaffId() == null) {
            return BaseResponse.error("staffId不能为空");
        }
        if (Objects.isNull(request.getType())) {
            return BaseResponse.error("type不能为空");
        }
        BoolQueryBuilder queryBuilder = listByStaffBoolQueryBuilder(request);
        setQueryType(queryBuilder, request.getType());

        int from = (request.getPageNo()-1) * request.getPageSize();

        try {
            BasePaginationResponse<LeadsBillingIndicatorsDto> leadsByEs = leadsSearchService.getLeadsByEs(request.getBid(),queryBuilder, from, request.getPageSize());
            // 设置下单时间、成交时间
            setStoreTIme(request.getBid(),leadsByEs.getList());
            return BaseResponse.success(leadsByEs);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResponse.error("查询数据异常");
        }
    }

    private void setStoreTIme(Integer bid, List<LeadsBillingIndicatorsDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Set<Long> leadsIds = new HashSet<>(list.size());
        list.forEach(item -> leadsIds.add(item.getId()));

        List<StoreOrderDto> leadsOrder = storeOrderClientForLeads.getLeadsOrder(bid, leadsIds, Sets.newHashSet());

        if (CollectionUtils.isEmpty(leadsOrder)) {
            return;
        }

        Map<Long, List<StoreOrderDto>> collect = leadsOrder.stream().collect(Collectors.groupingBy(StoreOrderDto::getLeadsId));

        list.forEach(item -> {
            List<StoreOrderDto> storeOrders = collect.get(item.getId());
                setStoreTime0(storeOrders, item);
        });
    }

    private void setStoreTime0(List<StoreOrderDto> storeOrders, LeadsBillingIndicatorsDto leadsBillingIndicatorsDto) {
        if (CollectionUtils.isEmpty(storeOrders)) {
            return;
        }
        storeOrders.sort(Comparator.comparingLong(StoreOrderDto::getUpdateTime));

        // 防止Es数据错误
        if(leadsBillingIndicatorsDto.getFullPayOrderCount() == null || leadsBillingIndicatorsDto.getFullPayOrderCount().equals(0)){
            leadsBillingIndicatorsDto.setFullPayOrderCount(storeOrders.size());
        }

        Map<Integer, List<StoreOrderDto>> collect = storeOrders.stream().collect(Collectors.groupingBy(StoreOrderDto::getType));

        // '交易类型 1=定金 2=全款',
        List<StoreOrderDto> storeOrderDtos = collect.get(1);
        if (!CollectionUtils.isEmpty(storeOrderDtos) && Objects.isNull(leadsBillingIndicatorsDto.getStateDepositTimeStr())) {
            StoreOrderDto storeOrderDto = storeOrderDtos.get(0);
            leadsBillingIndicatorsDto.setStateDeposit(storeOrderDto.getCreateTime());
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(storeOrderDto.getCreateTime()));
            leadsBillingIndicatorsDto.setStateDepositTimeStr(s);
        }
        storeOrderDtos = collect.get(2);
        if (!CollectionUtils.isEmpty(storeOrderDtos) && Objects.isNull(leadsBillingIndicatorsDto.getStateOrderSuccessTimeStr())) {
            StoreOrderDto storeOrderDto = storeOrderDtos.get(0);
            leadsBillingIndicatorsDto.setStateOrderSuccess(storeOrderDto.getUpdateTime());
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date(storeOrderDto.getUpdateTime()));
            leadsBillingIndicatorsDto.setStateOrderSuccessTimeStr(s);
        }
    }

    private void setQueryType(BoolQueryBuilder qb, Integer type) {
        Map<Integer, QueryBuilder> transTypeMap = new HashMap<>();
        // 总客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.TOTAL_CUSTOMER.getCode(), QueryBuilders.boolQuery());
        // 量尺数
        transTypeMap.put(LeadsReportListDetailTypeEnum.MEASURED.getCode(), QueryBuilders.boolQuery().should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.MEASURED.getStatus()))
                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.MEASURED.getStatus())));
        // 定金数
        transTypeMap.put(LeadsReportListDetailTypeEnum.ORDERED.getCode(), QueryBuilders.boolQuery().should(QueryBuilders.rangeQuery("depositAmount").gt(0))
                .should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.ORDERED.getStatus()))
                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.ORDERED.getStatus())));
        // 到店数
        transTypeMap.put(LeadsReportListDetailTypeEnum.STORED.getCode(), QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.STORED.getStatus()))
                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.STORED.getStatus())));
        // 成交数
        transTypeMap.put(LeadsReportListDetailTypeEnum.TRADED.getCode(),   QueryBuilders.boolQuery().should(QueryBuilders.rangeQuery("orderAmount").gt(0))
                .should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.TRADED.getStatus()))
                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.TRADED.getStatus())));
        // 已流失
        transTypeMap.put(LeadsReportListDetailTypeEnum.LOST.getCode(), QueryBuilders.termQuery("status", LeadsStatusEnum.LOST.getStatus()));
        // 有效客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.EFFECTIVE_CUSTOMER.getCode(),
                QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery("stateContact").gt(0))
                        .mustNot(QueryBuilders.termsQuery("status",
                                Lists.newArrayList(LeadsStatusEnum.DISTRIBUTED.getStatus(),LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.LOST.getStatus()))));
        // 无效客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.INVALID_CUSTOMER.getCode(), QueryBuilders.boolQuery()
                .should(QueryBuilders.termsQuery("status", Arrays.asList(LeadsStatusEnum.LOST.getStatus(), LeadsStatusEnum.INVALID.getStatus()))));
        // 待联系
        transTypeMap.put(LeadsReportListDetailTypeEnum.WAIT_CONTRACT.getCode(), QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus()));
        // 联系数
        transTypeMap.put(LeadsReportListDetailTypeEnum.CONTRACTED.getCode(),
                QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery("stateContact").gt(0))
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus()))
        );
        // 24小时联系数
        transTypeMap.put(LeadsReportListDetailTypeEnum.HOUR_24_CONTRACTED.getCode(), QueryBuilders.termQuery("contactIn24", 1));
        // 成功联系数
        transTypeMap.put(LeadsReportListDetailTypeEnum.SUCCESS_CONTRACT.getCode(), QueryBuilders.rangeQuery("stateContactSuccess").gt(0));
        // 已跟进数
        transTypeMap.put(LeadsReportListDetailTypeEnum.FOLLOW_UP.getCode(), QueryBuilders.boolQuery().must(QueryBuilders.termQuery("preFollowLeadsUp", "1")));
        // 待跟进数
        transTypeMap.put(LeadsReportListDetailTypeEnum.NOT_FOLLOW_UP.getCode(), QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery("preFollowLeadsUp", "1")));
        // 下发数
        transTypeMap.put(LeadsReportListDetailTypeEnum.DISTRIBUTE.getCode(),
                QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, "0"))
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.PRE_FOLLOW.getStatus()))
        );
        // 待下发数
        transTypeMap.put(LeadsReportListDetailTypeEnum.NOT_DISTRIBUTE.getCode(),
                QueryBuilders.boolQuery()
                        .should(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, "0"))
                        .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.PRE_FOLLOW.getStatus()))
        );


        QueryBuilder queryBuilder = transTypeMap.get(type);

        if (Objects.isNull(queryBuilder)) {
            throw new InngkeServiceException("type不正确");
        }

        qb.must(queryBuilder);
    }

    private void setQueryTypeChannel(BoolQueryBuilder qb, Integer type) {
        Map<Integer, QueryBuilder> transTypeMap = new HashMap<>();
        // 总客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.TOTAL_CUSTOMER.getCode(), QueryBuilders.boolQuery());
        // 下发客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.DISTRIBUTE.getCode(),
                QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, Lists.newArrayList(
                                LeadsStatusEnum.RECOVERY.getStatus(),
                                LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                                LeadsStatusEnum.PUSH_BACK.getStatus(),
                                LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                                LeadsStatusEnum.DELETED.getStatus()
                        ))));
        // 有效客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.EFFECTIVE_CUSTOMER.getCode(),
                QueryBuilders.boolQuery()
                        //.should(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(LeadsStatusEnum.CONTACTED.getStatus())))
                        .mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, Lists.newArrayList(
                                LeadsStatusEnum.LOST.getStatus(),
                                LeadsStatusEnum.RECOVERY.getStatus(),
                                LeadsStatusEnum.DELETED.getStatus(),
                                LeadsStatusEnum.INVALID.getStatus(),
                                LeadsStatusEnum.PUSH_BACK.getStatus(),
                                LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                                LeadsStatusEnum.DISTRIBUTED.getStatus()
                        ))));
        // 无效客户数
        transTypeMap.put(LeadsReportListDetailTypeEnum.INVALID_CUSTOMER.getCode(), QueryBuilders.termsQuery(LeadsEsDto.STATUS,
                Lists.newArrayList(LeadsStatusEnum.INVALID.getStatus(), LeadsStatusEnum.LOST.getStatus())));
        // 量尺数
        transTypeMap.put(LeadsReportListDetailTypeEnum.MEASURED.getCode(),
                QueryBuilders.boolQuery()
                        .should(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(LeadsStatusEnum.MEASURED.getStatus())))
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS,LeadsStatusEnum.LOST.getStatus()))
        );
        // 到店数
        transTypeMap.put(LeadsReportListDetailTypeEnum.STORED.getCode(),
                QueryBuilders.boolQuery()
                        .should(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(LeadsStatusEnum.STORED.getStatus())))
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS,LeadsStatusEnum.LOST.getStatus())));
        // 定金数
        transTypeMap.put(LeadsReportListDetailTypeEnum.ORDERED.getCode(),
                QueryBuilders.boolQuery()
                        .should(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(LeadsStatusEnum.ORDERED.getStatus())))
                        //.mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS,LeadsStatusEnum.LOST.getStatus()))
        );
        // 成交数
        transTypeMap.put(LeadsReportListDetailTypeEnum.TRADED.getCode(),
                QueryBuilders.boolQuery()
                        .should(QueryBuilders.termsQuery("statusLog", Lists.newArrayList(LeadsStatusEnum.TRADED.getStatus())))
                        .mustNot(QueryBuilders.termsQuery(LeadsEsDto.STATUS, Lists.newArrayList(
                                LeadsStatusEnum.LOST.getStatus(),
                                LeadsStatusEnum.DELETED.getStatus(),
                                LeadsStatusEnum.RECOVERY.getStatus(),
                                LeadsStatusEnum.PUSH_BACK.getStatus()
                        ))));
        // 流失数
        transTypeMap.put(LeadsReportListDetailTypeEnum.LOST.getCode(), QueryBuilders.termsQuery(LeadsEsDto.STATUS,
                Lists.newArrayList(LeadsStatusEnum.LOST.getStatus())));


        QueryBuilder queryBuilder = transTypeMap.get(type);

        if (Objects.isNull(queryBuilder)) {
            throw new InngkeServiceException("type不正确");
        }

        qb.must(queryBuilder);
    }


    private String customerUnitPrice(BigDecimal orderAmount,Integer stateOrderSuccess) {
        if(orderAmount == null || stateOrderSuccess == null){
            return "0%";
        }
        BigDecimal divide = orderAmount.divide(new BigDecimal(stateOrderSuccess == 0 ? 1 : stateOrderSuccess), 2, RoundingMode.HALF_DOWN);
        return DecimalUtils.decimalFormat(divide);
    }

    private String lostRate(Integer lostNum,Integer leadsCount,Integer stateNoAvail) {
        if(lostNum==null || leadsCount == null || stateNoAvail == null){
            return "0%";
        }
        int stateCount = leadsCount - stateNoAvail;
        if(stateCount == 0){
            return "0%";
        }
        BigDecimal bigDecimal = new BigDecimal(lostNum).divide(new BigDecimal(stateCount), 4, RoundingMode.HALF_DOWN);
        BigDecimal rate = bigDecimal.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN);
        return new BigDecimal(0).compareTo(rate) == 0 ? "0%" : rate.toString() + "%";
    }

    @Override
    public BaseResponse<List<LeadsFollowTimeByStaffResponse>> listByDepartment(LeadsFollowTimeByDepartmentRequest request) {

        return listByDepartment(request, getDepartmentNameAll(request.getBid()));
    }

    @Override
    public BaseResponse<List<LeadsFollowTimeByStaffResponse>> listByDepartment(LeadsFollowTimeByDepartmentRequest request, HashMap<Long, String> departIdAndNameAll) {

        Set<Long> staffManageDepartmentIds = staffClientForLeads.getStaffManageDepartmentIds(request.getBid(), request.getsId());

        Long departmentId = request.getDepartmentId();
        if (Objects.isNull(departmentId) || departmentId < 0 || CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            return BaseResponse.success(new ArrayList<>());
        }
        //查询员工管理权限
        //List<Long> mangerDeptIds = getMangerDeptIds(request.getBid(),request.getOperatorId());
        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setBid(request.getBid());
        getDepartmentRequest.setDepartmentId(departmentId);
        DepartmentDto department = departmentClientForLeads.getDepartment(getDepartmentRequest);
        List<DepartmentDto> children = department.getChildren();

        if (children == null) {
            children = Lists.newArrayList();
        }
        List<LeadsFollowTimeByStaffResponse> dtoList = Lists.newArrayList();

        Script script = new Script("doc['departmentId']");
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 查询条件
        BoolQueryBuilder qb = listByDepartmentBoolQueryBuilder(request, staffManageDepartmentIds, children);
        sourceBuilder.query(qb);
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("groupFiled").script(script)
                .subAggregation(
                        AggregationBuilders.terms("statusCount").field("status")
                );
        leadsFollowTimeContext.process(aggregation);

        aggregation.subAggregation(AggregationBuilders.topHits("record"))
                .size(10000);

        sourceBuilder.aggregation(aggregation);

        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(sourceBuilder);
        SearchResponse response = null;
        List<? extends Terms.Bucket> buckets;
        try {
            log.info("LeadsFollowTime，sourceBuilder={}",sourceBuilder);
            response = esClient.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = response.getAggregations();
            ParsedStringTerms parsedStringTerms = aggregations.get("groupFiled");
            buckets = parsedStringTerms.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                LeadsFollowTimeByStaffResponse dto = new LeadsFollowTimeByStaffResponse();
                //获取总线索数
                long docCount = bucket.getDocCount();
                dto.setLeadsCount((int) docCount);
                Aggregations bucketAggregations = bucket.getAggregations();
                //获取doc
                ParsedTopHits record = bucketAggregations.get("record");
                SearchHit[] hits = record.getHits().getHits();
                List<SearchHit> searchHits = Lists.newArrayList(hits);
                List<LeadsEsDto> esDtoList = searchHits.stream().map(SearchHit::getSourceAsString).map(item -> jsonService.toObject(item, LeadsEsDto.class)).collect(Collectors.toList());
                List<Long> departmentIdList = esDtoList.get(0).getBelongingDepartmentIds();
                if (!CollectionUtils.isEmpty(departmentIdList)) {
                    Long newDepId = departmentIdList.get(0);

                    GetDepartmentRequest getDepartmentRequest1 = new GetDepartmentRequest();
                    getDepartmentRequest1.setBid(request.getBid());
                    getDepartmentRequest1.setDepartmentId(newDepId);
                    getDepartmentRequest1.setChildrenLevel(0);
                    DepartmentDto depart = departmentClientForLeads.getDepartment(getDepartmentRequest1);

                    if (Objects.nonNull(depart)) {
                        dto.setDepartmentId(newDepId);
                        dto.setDepartmentName(depart.getName());
                        dto.setParentId(depart.getParentId());
                    } else {
                        dto.setParentId(-1L);
                    }
                } else {
                    dto.setParentId(-1L);
                }

                dto.setDeptIds(esDtoList.get(0).getDeptIds());
                leadsFollowTimeContext.process(bucketAggregations, dto);
                dtoList.add(dto);
            }

            //如果父部门不存在
            addChildren(department, dtoList);
            //数据叠加
            sumValToParentDepartment(dtoList, departmentId, children);
            if(!request.getNeedDetail()) {
                //去掉不需要的部门
                dtoList = filterNoNeedDepartment(dtoList, departmentId, children);
            }
            if (!CollectionUtils.isEmpty(dtoList)) {
                dtoList = dtoList.stream()
                        .sorted(Comparator.comparing(LeadsFollowTimeByStaffResponse::getLeadsCount).reversed())
                        .collect(Collectors.toList());
            }
        } catch (IOException exception) {
            log.error("获取ES数据失败:", exception);
        }
        List<LeadsFollowTimeByStaffResponse> resp = converterFirstCallIntervalTime(dtoList);

        dtoList.forEach(item->{
            String s = departIdAndNameAll.get(item.getDepartmentId());
            item.setDepartmentNameAll(s);
        });

        // 调整部门列表
        resp = listByDepartmentChangePosition(resp,request.getDepartmentId());


        return BaseResponse.success(resp);
    }

    private void listByDepartmentRemove(List<LeadsFollowTimeByStaffResponse> dtoList){

        List<LeadsFollowTimeByStaffResponse> remove = new ArrayList<>(dtoList.size());

        for (LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse : dtoList) {
            Integer leadsCount = leadsFollowTimeByStaffResponse.getLeadsCount();
            if( leadsCount == null || leadsCount.equals(0)){
                remove.add(leadsFollowTimeByStaffResponse);
            }
        }

        dtoList.removeAll(remove);
    }

    private BoolQueryBuilder listByDepartmentBoolQueryBuilder(LeadsFollowTimeByDepartmentRequest request, Set<Long> staffManageDepartmentIds,List<DepartmentDto> children){
        Long departmentId = request.getDepartmentId();

        BoolQueryBuilder qb = QueryBuilders.boolQuery();
        qb.must(QueryBuilders.termQuery("bid", request.getBid()));
        qb.must(QueryBuilders.termQuery("staffIsDel", 1));
        //qb.must(QueryBuilders.termsQuery("belongingDepartmentIds",mangerDeptIds));
        // 查询指定客服跟进
        if(request.getPreFollowStaffId() != null){
            qb.must(QueryBuilders.termQuery("preFollowStaffId", request.getPreFollowStaffId()));
            qb.mustNot(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, 0));
        }
        qb.mustNot(QueryBuilders.termQuery("departmentId", -1));

        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            qb.must(QueryBuilders.termsQuery(LeadsEsDto.DEPARTMENT_ID, staffManageDepartmentIds));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeStart())) {
            qb.must(QueryBuilders.rangeQuery("distributeTime").gte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeEnd())) {
            qb.must(QueryBuilders.rangeQuery("distributeTime").lte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeEnd())));
        }
        if (!StringUtils.isEmpty(request.getCreateTimeStart())) {
            qb.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).gte(DateTimeUtils.dateTimeStrToMilli(request.getCreateTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getCreateTimeEnd())) {
            qb.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).lte(DateTimeUtils.dateTimeStrToMilli(request.getCreateTimeEnd())));
        }
        // 指定部门查询
        BoolQueryBuilder queryEs = QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery("parentId", departmentId))
                .should(QueryBuilders.termQuery("departmentId", departmentId));
        if (!CollectionUtils.isEmpty(children)) {
            children.stream().forEach(departmentDto -> {
                queryEs.should(QueryBuilders.termQuery("deptIds", departmentDto.getId()));
            });
        }
        qb.must(queryEs);

//        List<Long> departmentIds = children.stream().map(DepartmentDto::getId).collect(Collectors.toList());
//        departmentIds.add(departmentId);
//        qb.must(QueryBuilders.boolQuery().should(QueryBuilders.termsQuery(LeadsEsDto.DEPARTMENT_IDS, departmentIds)));

        qb.mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(0, -4, -5, 21)));

        // 权限控制
        SearchLeadsRequest searchLeadsRequest = new SearchLeadsRequest();
        searchLeadsRequest.setSid(request.getsId());
        searchLeadsRequest.setBid(request.getBid());
        searchLeadsRequest.setStatusGroup(5);
        setListPermissionsParams(searchLeadsRequest, qb);

        return qb;
    }

    @Override
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByDepartmentDetail(LeadsFollowTimeByDepartmentRequest request) {
        if (Objects.isNull(request.getDepartmentId())) {
            return BaseResponse.error("departmentId不能为空");
        }
        if (Objects.isNull(request.getType())) {
            return BaseResponse.error("type不能为空");
        }
        Set<Long> staffManageDepartmentIds = staffClientForLeads.getStaffManageDepartmentIds(request.getBid(), request.getsId());

        Long departmentId = request.getDepartmentId();
        if (Objects.isNull(departmentId) || departmentId < 0 || CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            BasePaginationResponse<LeadsBillingIndicatorsDto> response = new BasePaginationResponse<>();
            response.setTotal(0);
            response.setList(new ArrayList<>());
            return BaseResponse.success(response);
        }

        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setBid(request.getBid());
        getDepartmentRequest.setDepartmentId(departmentId);
        DepartmentDto department = departmentClientForLeads.getDepartment(getDepartmentRequest);
        List<DepartmentDto> children = department.getChildren();

        if (children == null) {
            children = Lists.newArrayList();
        }

        BoolQueryBuilder queryBuilder = listByDepartmentBoolQueryBuilder(request, staffManageDepartmentIds, children);
        setQueryType(queryBuilder, request.getType());

        int from = (request.getPageNo()-1) * request.getPageSize();

        try {
            BasePaginationResponse<LeadsBillingIndicatorsDto> leadsByEs = leadsSearchService.getLeadsByEs(request.getBid(),queryBuilder, from, request.getPageSize());
            // 设置下单时间、成交时间
            setStoreTIme(request.getBid(),leadsByEs.getList());
            return BaseResponse.success(leadsByEs);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResponse.error("查询部门数据异常");
        }
    }

    private List<LeadsFollowTimeByStaffResponse> listByDepartmentChangePosition(List<LeadsFollowTimeByStaffResponse> resp, Long departmentId) {
        if (CollectionUtils.isEmpty(resp)) {
            return resp;
        }
        LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse = resp.get(0);
        if (departmentId.equals(leadsFollowTimeByStaffResponse.getDepartmentId())) {
            return resp;
        }
        List<LeadsFollowTimeByStaffResponse> result = new ArrayList<>(resp.size());
        List<LeadsFollowTimeByStaffResponse> temp = new ArrayList<>(resp.size() - 1);
        resp.forEach(item -> {
            if (departmentId.equals(item.getDepartmentId())) {
                result.add(item);
            } else {
                temp.add(item);
            }
        });
        result.addAll(temp);
        return result;
    }

    @Override
    public BaseResponse<List<LeadsFollowTimeByStaffResponse>> exportDepartment(LeadsFollowTimeByDepartmentRequest query) {

        HashMap<Long, String> departIdAndNameAll = getDepartmentNameAll(query.getBid());

        BaseResponse<List<LeadsFollowTimeByStaffResponse>> listBaseResponse = listByDepartment(query, departIdAndNameAll);
        List<LeadsFollowTimeByStaffResponse> result = listBaseResponse.getData();
        if(CollectionUtils.isEmpty(result)){
            return listBaseResponse;
        }
        List<LeadsFollowTimeByStaffResponse> leadsFollowTimeByStaffResponses = exportDepartment(result, query,1,query.getDepartmentId(),departIdAndNameAll);

        return BaseResponse.success(leadsFollowTimeByStaffResponses);
    }

    @Override
    public BaseResponse<List<LeadsFollowTimeByStaffResponse>> exportDepartmentV2(LeadsFollowTimeByDepartmentRequest request) {
        request.setNeedDetail(true);
        BaseResponse<List<LeadsFollowTimeByStaffResponse>> listBaseResponse = listByDepartment(request);
        if (!BaseResponse.responseSuccess(listBaseResponse)) {
            return listBaseResponse;
        }

        List<LeadsFollowTimeByStaffResponse> data = listBaseResponse.getData();
        if (CollectionUtils.isEmpty(data)) {
            return listBaseResponse;
        }

        log.info("转化数据部门数据导出条数：{}", data.size());

        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setDepartmentId(0L);
        getDepartmentRequest.setBid(request.getBid());
        DepartmentDto department = departmentClientForLeads.getDepartment(getDepartmentRequest);

        Long requestDepartmentId = request.getDepartmentId();

        // 获取父部门
        Map<Long, Long> departIdToParentId = new HashMap<>();
        departIdToParentId(Lists.newArrayList(department), departIdToParentId);
        // 添加部门存在列表中不存在的子部门
        Map<Long, LeadsFollowTimeByStaffResponse> collectData = data.stream().collect(Collectors.toMap(LeadsFollowTimeByStaffResponse::getDepartmentId, l -> l));
        addDepartmentChildren(Lists.newArrayList(department), collectData, data);

        // 列表转成Map,部门父级Id对应的子类
        Map<Long, List<LeadsFollowTimeByStaffResponse>> parentDtoMap = exportDepartmentV2ToTree(data, departIdToParentId);
        log.info("转化数据部门数据导出父类条数：{}", parentDtoMap.size());
        // 处理数据叠加
        List<LeadsFollowTimeByStaffResponse> list = parentDtoMap.getOrDefault(requestDepartmentId, new ArrayList<>());
        log.info("转化数据部门数据导出处理数据叠加开始：{}", list.size());
        exportDepartmentV2Sum(parentDtoMap, departIdToParentId, list, null, requestDepartmentId);
        // 转换成列表
        List<LeadsFollowTimeByStaffResponse> result = new ArrayList<>(parentDtoMap.size());
        // 获取请求部门的父部门Id
        Long requestDepartParentId = departIdToParentId.get(requestDepartmentId);
        // 添加请求部门到返回参数中
        List<LeadsFollowTimeByStaffResponse> requestDepartmentList = Optional.ofNullable(parentDtoMap.get(requestDepartParentId)).orElse(new ArrayList<>());
        result.addAll(requestDepartmentList);
        List<LeadsFollowTimeByStaffResponse> listTemp = parentDtoMap.get(requestDepartmentId);
        exportDepartmentV2ToList(result, parentDtoMap, listTemp);
        // 去除线索条数为0的
        result = result.stream().filter(item -> item.getLeadsCount() != null && !item.getLeadsCount().equals(0)).collect(Collectors.toList());
        log.info("转化数据部门数据导出去除多余条数后：{}", result.size());
        // 计算百分比
        exportDepartmentV2Rate(result);
        // 重新设置部门全名称
        HashMap<Long, String> departmentNameAll = getDepartmentNameAll(request.getBid());
        result.forEach(item->{
            String s = departmentNameAll.get(item.getDepartmentId());
            item.setDepartmentNameAll(s);
        });


        return BaseResponse.success(result);
    }

    private void departIdToParentId(List<DepartmentDto> departments, Map<Long, Long> departIdToParentId) {
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }
        for (DepartmentDto department : departments) {
            departIdToParentId.put(department.getId(), department.getParentId());
            departIdToParentId(department.getChildren(),departIdToParentId);
        }
    }

    private void addDepartmentChildren(List<DepartmentDto> departments, Map<Long, LeadsFollowTimeByStaffResponse> collectData,
                                       List<LeadsFollowTimeByStaffResponse> data) {
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }
        for (DepartmentDto department : departments) {
            if (!collectData.containsKey(department.getId())) {
                LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse = new LeadsFollowTimeByStaffResponse();
                leadsFollowTimeByStaffResponse.setDepartmentId(department.getId());
                leadsFollowTimeByStaffResponse.setDepartmentName(department.getName());
                data.add(leadsFollowTimeByStaffResponse);
            }
            addDepartmentChildren(department.getChildren(), collectData, data);
        }
    }

    private  Map<Long,List<LeadsFollowTimeByStaffResponse>> exportDepartmentV2ToTree(List<LeadsFollowTimeByStaffResponse> data,Map<Long,Long> departIdToParentId){
        Map<Long,List<LeadsFollowTimeByStaffResponse>> parentDtoMap = new HashMap<>();
        data.forEach(item->{
            Long departParentId = departIdToParentId.get(item.getDepartmentId());
            if (Objects.nonNull(departParentId)) {
                parentDtoMap.computeIfAbsent(departParentId, list -> new ArrayList<>()).add(item);
            }
        });
        return parentDtoMap;
    }

    private void exportDepartmentV2Sum(Map<Long, List<LeadsFollowTimeByStaffResponse>> parentDtoMap,
                                       Map<Long, Long> departIdToParentId,
                                       List<LeadsFollowTimeByStaffResponse> list,
                                       LeadsFollowTimeByStaffResponse parent,
                                       Long requestDepartId) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse : list) {
            List<LeadsFollowTimeByStaffResponse> temp = parentDtoMap.get(leadsFollowTimeByStaffResponse.getDepartmentId());
            exportDepartmentV2Sum(parentDtoMap, departIdToParentId, temp, leadsFollowTimeByStaffResponse, requestDepartId);

            if (shouldSkip(parent, departIdToParentId, requestDepartId)) {
                continue;
            }
            exportDepartmentV2Sum(parent, leadsFollowTimeByStaffResponse);
        }
    }

    private void exportDepartmentV2ToList(List<LeadsFollowTimeByStaffResponse> result,
                                          Map<Long, List<LeadsFollowTimeByStaffResponse>> parentDtoMap,
                                          List<LeadsFollowTimeByStaffResponse> list) {

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse : list) {
            result.add(leadsFollowTimeByStaffResponse);
            List<LeadsFollowTimeByStaffResponse> listTemp = parentDtoMap.get(leadsFollowTimeByStaffResponse.getDepartmentId());
            exportDepartmentV2ToList(result, parentDtoMap, listTemp);
        }
    }

    private void exportDepartmentV2Rate(List<LeadsFollowTimeByStaffResponse> result) {
        result.forEach(dto -> {
            // 平均联系时长
            Long firstCallIntervalTime = dto.getFirstCallIntervalTime();
            int stateAvail = dto.getStateAvail();
            if (0 != stateAvail) {
                firstCallIntervalTime = firstCallIntervalTime / stateAvail;
                long hour = firstCallIntervalTime / 3600;
                long minute = (firstCallIntervalTime % 3600) / 60;
                dto.setFirstCallIntervalTimeStr(hour + "小时" + minute + "分");
            } else {
                dto.setFirstCallIntervalTimeStr("0小时");
            }
            // 客单价
            String customerUnitPrice = customerUnitPrice(dto.getOrderAmount(), dto.getStateOrderSuccess());
            dto.setCustomerUnitPrice(customerUnitPrice);
            // 联系率
            dto.setContactRate(rateCalculation(dto.getStateContact(), dto.getLeadsCount()));
            // 24小时联系率
            dto.setContactIn24Rate(rateCalculation(dto.getContactIn24Count(), dto.getLeadsCount()));
            // 成功联系率
            dto.setContactSuccessRate(rateCalculation(dto.getStateContactSuccess(), dto.getLeadsCount()));
            // 量尺率
            dto.setMeasuringRate(rateCalculation(dto.getStateMeasuring(), dto.getStateAvail()));
            // 到店率
            dto.setArrivalStoreRate(rateCalculation(dto.getStateArrivalStore(), dto.getStateAvail()));
            // 定金转化率
            dto.setDepositRate(rateCalculation(dto.getStateDeposit(), dto.getStateAvail()));
            // 成交转化率
            dto.setOrderSuccessRate(rateCalculation(dto.getStateOrderSuccess(), dto.getStateAvail()));
            // 流失率
            String lostRate = lostRate(dto.getLostNum(), dto.getLeadsCount(), dto.getStateNoAvail());
            dto.setLostRate(lostRate);
        });
    }

    private String rateCalculation(Integer numerator, Integer denominator) {
        if (denominator == null || denominator.equals(0)) {
            return "0.00%";
        }
        return BigDecimal.valueOf(numerator)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP).toString() + "%";
    }

    private boolean shouldSkip(LeadsFollowTimeByStaffResponse department, Map<Long, Long> departIdToParentId, Long requestDepartId) {
        if (department == null) {
            return true;
        }
        Long departParentId = departIdToParentId.get(department.getDepartmentId());
        return requestDepartId.equals(department.getDepartmentId()) || requestDepartId.equals(departParentId);
    }

    private void exportDepartmentV2Sum(LeadsFollowTimeByStaffResponse parent, LeadsFollowTimeByStaffResponse child) {
        // 总客户数
        parent.setLeadsCount(parent.getLeadsCount() + child.getLeadsCount());
        // 有效客户数
        parent.setStateAvail(parent.getStateAvail() + child.getStateAvail());
        // 无效客户数
        parent.setStateNoAvail(parent.getStateNoAvail() + child.getStateNoAvail());
        // 待联系数
        parent.setNotContactCount(parent.getNotContactCount() + child.getNotContactCount());
        // 联系数
        parent.setStateContact(parent.getStateContact() + child.getStateContact());
        // 24小时联系数
        parent.setContactIn24Count(parent.getContactIn24Count() + child.getContactIn24Count());
        // 成功联系数
        parent.setStateContactSuccess(parent.getStateContactSuccess() + child.getStateContactSuccess());
        // 量尺数
        parent.setStateMeasuring(parent.getStateMeasuring() + child.getStateMeasuring());
        // 到店数
        parent.setStateArrivalStore(parent.getStateArrivalStore() + child.getStateArrivalStore());
        // 定金数
        parent.setStateDeposit(parent.getStateDeposit() + child.getStateDeposit());
        // 成交数
        parent.setStateOrderSuccess(parent.getStateOrderSuccess() + child.getStateOrderSuccess());
        // 定金总金额
        parent.setDepositAmount(parent.getDepositAmount().add(child.getDepositAmount()));
        // 成交总金额
        parent.setOrderAmount(parent.getOrderAmount().add(child.getOrderAmount()));
        // 流失数
        parent.setLostNum(parent.getLostNum() + child.getLostNum());

    }

    @Override
    public BaseResponse<List<LeadsOrganizationPreFollowDto>> listByPreFollow(LeadsOrganizationPreFollowRequest request) {
        Set<Long> staffIds = rbacClientForLeads.listCustomerRoleUserIds(request.getBid());
        if (CollectionUtils.isEmpty(staffIds)) {
            return BaseResponse.success(new ArrayList<>());
        }
        // 聚合查询
        TermsAggregationBuilder terms = listByPreFollowSubAgg();
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = listByPreFollowBoolQueryBuilder(request, staffIds);
        // 权限控制
        listByPreFollowAuth(request.getBid(), request.getSid(), boolQueryBuilder);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().size(0).aggregation(terms).query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest().indices("leads").source(sourceBuilder);

        List<LeadsOrganizationPreFollowDto> result = new ArrayList<>();

        try {
            SearchResponse response = esClient.search(searchRequest, RequestOptions.DEFAULT);

            // 获取已跟进数
            //Map<Long, Long> hasFollowNumMap = listByPreFollowHasFollowNum(response, staffId);

            Aggregations aggregations = response.getAggregations();
            ParsedStringTerms parsedStringTerms = aggregations.get("preFollowAgg");
            for (Terms.Bucket bucket : parsedStringTerms.getBuckets()) {

                LeadsOrganizationPreFollowDto leadsOrganizationPreFollowDto = listByPreFollowAggResult(bucket);

                if(leadsOrganizationPreFollowDto != null) {
                    result.add(leadsOrganizationPreFollowDto);
                }
            }

            // 设置手机号.部门
            listByPreFollowSetDepartment(request.getBid(),staffIds,result);

        } catch (IOException e) {
            e.printStackTrace();
        }

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByPreFollowDetail(LeadsOrganizationPreFollowRequest request) {
        if (Objects.isNull(request.getStaffId())) {
            return BaseResponse.error("staffId不能为空");
        }
        // 查询条件
        BoolQueryBuilder queryBuilder = listByPreFollowBoolQueryBuilder(request, Sets.newHashSet(request.getStaffId()));
        // 权限控制
        listByPreFollowAuth(request.getBid(), request.getSid(), queryBuilder);

        setQueryType(queryBuilder, request.getType());

        int from = (request.getPageNo() - 1) * request.getPageSize();

        try {
            BasePaginationResponse<LeadsBillingIndicatorsDto> leadsByEs = leadsSearchService.getLeadsByEs(request.getBid(),queryBuilder, from, request.getPageSize());
            // 设置下单时间、成交时间
            setStoreTIme(request.getBid(),leadsByEs.getList());
            return BaseResponse.success(leadsByEs);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResponse.error("查询客服数据异常");
        }
    }

    @Override
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> listByChannelDetail(LeadsOrganizationChannelRequest request) {

        BoolQueryBuilder queryBuilder = listByChannelDetailBoolQueryBuilder(request);

        setQueryTypeChannel(queryBuilder, request.getType());

        int from = (request.getPageNo() - 1) * request.getPageSize();

        try {
            BasePaginationResponse<LeadsBillingIndicatorsDto> leadsByEs = leadsSearchService.getLeadsByEs(request.getBid(),queryBuilder, from, request.getPageSize());
            // 设置下单时间、成交时间
            setStoreTIme(request.getBid(),leadsByEs.getList());
            return BaseResponse.success(leadsByEs);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResponse.error("查询客服数据异常");
        }
    }

    private BoolQueryBuilder listByChannelDetailBoolQueryBuilder(LeadsOrganizationChannelRequest request) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 设置渠道
        if (request.getChannel().equals(0)) {
        } else {
            boolQueryBuilder.must(QueryBuilders.termQuery(LeadsEsDto.CHANNEL, request.getChannel()));
        }

        boolQueryBuilder.must(QueryBuilders.termQuery("bid",request.getBid()));

        // 创建时间
        if (!StringUtils.isEmpty(request.getStartTime())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).gte(DateTimeUtils.dateTimeStrToMilli(request.getStartTime())));
        }
        if (!StringUtils.isEmpty(request.getEndTime())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).lte(DateTimeUtils.dateTimeStrToMilli(request.getEndTime())));
        }
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(
                LeadsStatusEnum.DELETED.getStatus(),
                LeadsStatusEnum.RECOVERY.getStatus(),
                //LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                LeadsStatusEnum.PUSH_BACK.getStatus())));

        return boolQueryBuilder;
    }

    private void listByPreFollowAuth(Integer bid, Long sid, BoolQueryBuilder boolQueryBuilder) {
        // 权限控制
        Set<Long> staffManageDepartmentIds = staffClientForLeads.getStaffManageDepartmentIds(bid, sid);
        //自己所管理部门的员工创建的,自己所管理部门员工[[跟进的]]
        if (!CollectionUtils.isEmpty(staffManageDepartmentIds)) {
            boolQueryBuilder.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.termsQuery(LeadsEsDto.PRE_FOLLOW_DEPARTMENT_IDS, staffManageDepartmentIds)));
        }
    }

    private void listByPreFollowSetDepartment(Integer bid, Set<Long> staffIds, List<LeadsOrganizationPreFollowDto> result) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return;
        }
        Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffIds);
        Set<Long> departmentIds = new HashSet<>(staffByIds.size());
        staffByIds.values().forEach(item -> departmentIds.add(item.getDepartmentId()));
        List<DepartmentDto> departmentByIds = departmentClientForLeads.getDepartmentByIds(bid, new ArrayList<>(departmentIds));
        Map<Long, DepartmentDto> collect = departmentByIds.stream().collect(Collectors.toMap(DepartmentDto::getId, item -> item));

        result.forEach(item -> {
            Long staffId = item.getStaffId();
            StaffDto staffDto = staffByIds.get(staffId);
            if (staffDto != null) {
                item.setStaffName(staffDto.getName());
                item.setMobile(staffDto.getMobile());
                DepartmentDto departmentDto = collect.getOrDefault(staffDto.getDepartmentId(), new DepartmentDto());
                item.setDepartmentName(departmentDto.getName());
            }
        });
    }

    private TermsAggregationBuilder listByPreFollowSubAgg() {
        // 聚合查询
        Script script = new Script("doc['preFollowStaffId']");
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("preFollowAgg").script(script);
        // 总客户数
        aggregation.subAggregation(
                AggregationBuilders.count("customerCount").field(LeadsEsDto.PRE_FOLLOW_STAFF_ID));
        //  下发数
        aggregation.subAggregation(AggregationBuilders.filter("hasDistributeNum",
                QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.DISTRIBUTE_STAFF_ID, "0"))
                        .mustNot(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.PRE_FOLLOW.getStatus()))
        ));
        // 已跟进数
        aggregation.subAggregation(AggregationBuilders.filter("preFollowLeadsUp",
                QueryBuilders.boolQuery().must(QueryBuilders.termQuery("preFollowLeadsUp", "1"))));
        return aggregation;
    }

    private BoolQueryBuilder listByPreFollowBoolQueryBuilder(LeadsOrganizationPreFollowRequest request, Set<Long> staffIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.boolQuery()
                .should(QueryBuilders.termsQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, staffIds)))
                .mustNot(QueryBuilders.termQuery(LeadsEsDto.PRE_FOLLOW_STAFF_ID, 0L))
                .mustNot(QueryBuilders.termsQuery("status", Lists.newArrayList(
                        LeadsStatusEnum.RECOVERY.getStatus(),
                        LeadsStatusEnum.DELETED.getStatus(),
                        LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                        LeadsStatusEnum.PUSH_BACK.getStatus())));
        // 分配时间
        if (!StringUtils.isEmpty(request.getDistributeTimeStart())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.DISTRIBUTE_FOLLOW_TIME).gte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getDistributeTimeEnd())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.DISTRIBUTE_FOLLOW_TIME).lte(DateTimeUtils.dateTimeStrToMilli(request.getDistributeTimeEnd())));
        }
        // 创建时间
        if (!StringUtils.isEmpty(request.getCreateTimeStart())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).gte(DateTimeUtils.dateTimeStrToMilli(request.getCreateTimeStart())));
        }
        if (!StringUtils.isEmpty(request.getCreateTimeEnd())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(LeadsEsDto.CREATE_TIME).lte(DateTimeUtils.dateTimeStrToMilli(request.getCreateTimeEnd())));
        }
        //

        return boolQueryBuilder;
    }

    private LeadsOrganizationPreFollowDto listByPreFollowAggResult(Terms.Bucket bucket) {
        LeadsOrganizationPreFollowDto dto = new LeadsOrganizationPreFollowDto();

        Aggregations aggregations = bucket.getAggregations();
        // 总客户数
        ParsedValueCount customerCount = aggregations.get("customerCount");
        dto.setCustomerCount(customerCount.getValue());
        if(dto.getCustomerCount().equals(0L)){
            return null;
        }
        // 下发数
        ParsedFilter hasDistributeNum = aggregations.get("hasDistributeNum");
        dto.setHasDistributeNum(hasDistributeNum.getDocCount());
        // 待下发数
        dto.setWaitDistributeNum(dto.getCustomerCount() - dto.getHasDistributeNum());
        // 下发率
        if (!dto.getCustomerCount().equals(0L) && !dto.getHasDistributeNum().equals(0L)) {
            BigDecimal divide = new BigDecimal(dto.getHasDistributeNum()).divide(new BigDecimal(dto.getCustomerCount()), 4, RoundingMode.HALF_DOWN);
            BigDecimal rate = divide.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN);
            String rateStr = rate.compareTo(new BigDecimal(0)) == 0 ? "0%" : rate.toString() + "%";
            dto.setDistributeRate(rateStr);
        }
        // staffId
        String key = (String) bucket.getKey();
        dto.setStaffId(Long.valueOf(key));
        // 已跟进数
        ParsedFilter preFollowLeadsUp = aggregations.get("preFollowLeadsUp");
        dto.setHasFollowNum(preFollowLeadsUp.getDocCount());
        // 待跟进数
        dto.setHasNotFollowNum(dto.getCustomerCount() - dto.getHasFollowNum());
        return dto;
    }



    /**
     * 设置列表权限过滤参数
     *
     * @param request       request
     * @param queryBuilder  queryBuilder
     */
    private void setListPermissionsParams(SearchLeadsRequest request, BoolQueryBuilder queryBuilder) {
        Set<String> userRoleCodes = rbacClientForLeads.getUserRoleCode(request.getBid(), request.getSid());
        leadsListPermissionsContext.handle(request, queryBuilder, userRoleCodes);
    }

    private void setListPermissionsParams(LeadsFollowTimeByStaffRequest request, BoolQueryBuilder queryBuilder, Set<String> userRoleCodes) {
        SearchLeadsRequest searchLeadsRequest = new SearchLeadsRequest();
        searchLeadsRequest.setSid(request.getSId());
        searchLeadsRequest.setBid(request.getBid());
        searchLeadsRequest.setStatusGroup(5);
        leadsListPermissionsContext.handle(searchLeadsRequest, queryBuilder, userRoleCodes);
    }

    private List<LeadsFollowTimeByStaffResponse> exportDepartment(List<LeadsFollowTimeByStaffResponse> list,
                                                                  LeadsFollowTimeByDepartmentRequest query, int depth, Long departmentId,
                                                                  HashMap<Long, String> departIdAndNameAll) {
        List<LeadsFollowTimeByStaffResponse> result = new ArrayList<>();
        if (depth == 1) {
            result.add(list.get(0));
        }
        if (depth > 10) {
            return result;
        }
        for (int i = 0; i < list.size(); i++) {
            LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse = list.get(i);
            if (departmentId.equals(leadsFollowTimeByStaffResponse.getDepartmentId())) {
                continue;
            }
            result.add(leadsFollowTimeByStaffResponse);

            query.setDepartmentId(leadsFollowTimeByStaffResponse.getDepartmentId());
            BaseResponse<List<LeadsFollowTimeByStaffResponse>> listBaseResponse = listByDepartment(query, departIdAndNameAll);
            List<LeadsFollowTimeByStaffResponse> data = listBaseResponse.getData();
            if (CollectionUtils.isEmpty(data)) {
                continue;
            }

            List<LeadsFollowTimeByStaffResponse> leadsFollowTimeByStaffResponses =
                    exportDepartment(data, query, depth + 1, leadsFollowTimeByStaffResponse.getDepartmentId(), departIdAndNameAll);

            result.addAll(leadsFollowTimeByStaffResponses);
        }

        return result;
    }

    private HashMap<Long, String> getDepartmentNameAll(Integer bid){
        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setBid(bid);
        getDepartmentRequest.setDepartmentId(0L);
        DepartmentDto department = departmentClientForLeads.getDepartment(getDepartmentRequest);

        HashMap<Long, String> departIdAndNameAll = new HashMap<>(36);
        getDepartmentNameAll(Lists.newArrayList(department), "", departIdAndNameAll);
        return departIdAndNameAll;
    }

    /**
     * 部门Id对于的全部门名称
     */
    private void getDepartmentNameAll(List<DepartmentDto> department,String parentName,Map<Long,String> result){
        if(CollectionUtils.isEmpty(department)){
            return;
        }
        for (DepartmentDto departmentDto : department) {
            String departmentName = setDepartmentName(parentName, departmentDto.getName());
            result.put(departmentDto.getId(),departmentName);
            getDepartmentNameAll(departmentDto.getChildren(),departmentName,result);
        }
    }
    private String setDepartmentName(String parentName,String departmentName){
        if(StringUtils.isEmpty(parentName)){
            return departmentName;
        }
        return parentName + "/" + departmentName;
    }

    private List<Long> getMangerDeptIds(Integer bid,Long cid) {
        LoginDto loginDto = loginClientForLeads.getLoginDtoByCustomerId(bid, cid);
        if (Objects.nonNull(loginDto)
                && !CollectionUtils.isEmpty(loginDto.getManageDepartmentIds())) {
            return Lists.newArrayList(loginDto.getManageDepartmentIds());
        }
        throw new InngkeServiceException("获取管理权限失败");
    }

    /**
     * 添加下级的子部门信息
     *
     * @param department
     * @param dtoList
     * @return void
     */
    private void addChildren(DepartmentDto department, List<LeadsFollowTimeByStaffResponse> dtoList) {
        Long rootDpId = department.getId();
        List<String> haveDpIds = dtoList.stream().map(LeadsFollowTimeByStaffResponse::getDepartmentId).map(String::valueOf).collect(Collectors.toList());
        if (!haveDpIds.contains(String.valueOf(rootDpId))) {
            LeadsFollowTimeByStaffResponse newLeadsFollowTime = new LeadsFollowTimeByStaffResponse();
            newLeadsFollowTime.setDepartmentId(rootDpId);
            newLeadsFollowTime.setDepartmentName(department.getName());

            GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
            getDepartmentRequest.setBid(department.getBid());
            getDepartmentRequest.setDepartmentId(rootDpId);
            List<DepartmentDto> departmentWithParents = departmentClientForLeads.getDepartmentWithParents(getDepartmentRequest);

            if (!CollectionUtils.isEmpty(departmentWithParents)) {
                List<Long> departmentIds = departmentWithParents.stream().map(DepartmentDto::getId).collect(Collectors.toList());
                newLeadsFollowTime.setDeptIds(departmentIds);
            }

            dtoList.add(newLeadsFollowTime);
        }
    }

    /**
     * 平均联系时间转换
     *
     * @param list
     * @return
     */
    private List<LeadsFollowTimeByStaffResponse> converterFirstCallIntervalTime(List<LeadsFollowTimeByStaffResponse> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        for (LeadsFollowTimeByStaffResponse leadsFollowTimeByStaffResponse : list) {
            Long firstCallIntervalTime = leadsFollowTimeByStaffResponse.getFirstCallIntervalTime();
            int stateAvail = leadsFollowTimeByStaffResponse.getStateAvail();
            if (0 != stateAvail) {
                firstCallIntervalTime = firstCallIntervalTime / stateAvail;
                long hour = firstCallIntervalTime / 3600;
                long minute = (firstCallIntervalTime % 3600) / 60;
                leadsFollowTimeByStaffResponse.setFirstCallIntervalTimeStr(hour + "小时" + minute + "分");
            } else {
                leadsFollowTimeByStaffResponse.setFirstCallIntervalTimeStr("0小时");
            }
            // 客单价：成交总金额/成交数，不显示小数，金额每三位用逗号隔开
            String customerUnitPrice = customerUnitPrice(leadsFollowTimeByStaffResponse.getOrderAmount(), leadsFollowTimeByStaffResponse.getStateOrderSuccess());
            leadsFollowTimeByStaffResponse.setCustomerUnitPrice(customerUnitPrice);
            // 流失率：流失数/（总客户数-无效客户数）
            String lostRate = lostRate(leadsFollowTimeByStaffResponse.getLostNum(), leadsFollowTimeByStaffResponse.getLeadsCount(), leadsFollowTimeByStaffResponse.getStateNoAvail());
            leadsFollowTimeByStaffResponse.setLostRate(lostRate);
        }
        return list;
    }

    /**
     * 过滤不需要显示的记录
     *
     * @param dtoList
     * @param departmentId
     * @param children
     * @return java.util.List<com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse>
     */
    private List<LeadsFollowTimeByStaffResponse> filterNoNeedDepartment(List<LeadsFollowTimeByStaffResponse> dtoList, Long departmentId, List<DepartmentDto> children) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return dtoList;
        }
        List<Long> ids = new ArrayList<>();
        ids.add(departmentId);

        if (!CollectionUtils.isEmpty(children)) {
            List<Long> childrenIds = children.stream().map(DepartmentDto::getId).collect(Collectors.toList());
            ids.addAll(childrenIds);
        }

        if (CollectionUtils.isEmpty(ids)) {
            return dtoList;
        }

        return dtoList.stream().filter(t -> Objects.nonNull(t.getDepartmentId()) && t.getLeadsCount() > 0 && ids.contains(t.getDepartmentId())).collect(Collectors.toList());
    }

    /**
     * 累加数据
     *
     * @param dtoList
     * @param departmentId
     * @param children
     * @return void
     */
    private void sumValToParentDepartment(List<LeadsFollowTimeByStaffResponse> dtoList, Long departmentId, List<DepartmentDto> children) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        //is have parentId==departmentId
        long count = dtoList.stream().filter(t -> !CollectionUtils.isEmpty(t.getDeptIds()) && t.getDeptIds().contains(departmentId)).count();
        if (count == 0) {
            return;
        }

        dpToSumDepartmentVal(dtoList, departmentId, children);
    }

    //dp to sum value
    private void dpToSumDepartmentVal(List<LeadsFollowTimeByStaffResponse> dtoList, Long departmentId, List<DepartmentDto> children) {
        //存在的部门id
        List<String> haveDpIds = dtoList.stream().map(LeadsFollowTimeByStaffResponse::getDepartmentId).map(String::valueOf).collect(Collectors.toList());
        for (DepartmentDto departmentDto : children) {
            Long childernDpId = departmentDto.getId();
            boolean contains = haveDpIds.contains(String.valueOf(childernDpId));
            if (!contains) {
                LeadsFollowTimeByStaffResponse newLeadsFollowTime = new LeadsFollowTimeByStaffResponse();
                newLeadsFollowTime.setDepartmentId(childernDpId);
                newLeadsFollowTime.setDepartmentName(departmentDto.getName());

                GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
                getDepartmentRequest.setBid(departmentDto.getBid());
                getDepartmentRequest.setDepartmentId(childernDpId);
                List<DepartmentDto> departmentWithParents = departmentClientForLeads.getDepartmentWithParents(getDepartmentRequest);

                if (!CollectionUtils.isEmpty(departmentWithParents)) {
                    List<Long> departmentIds = departmentWithParents.stream().map(DepartmentDto::getId).collect(Collectors.toList());
                    newLeadsFollowTime.setDeptIds(departmentIds);
                }

                dtoList.add(newLeadsFollowTime);
            }
        }

        LeadsFollowTimeByStaffResponse rootDepartment = new LeadsFollowTimeByStaffResponse();
        List<LeadsFollowTimeByStaffResponse> list = dtoList.stream().filter(t -> !CollectionUtils.isEmpty(t.getDeptIds()) && t.getDeptIds().contains(departmentId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Integer leadsCount = list.stream().map(LeadsFollowTimeByStaffResponse::getLeadsCount).reduce(Integer::sum).get();
        rootDepartment.setLeadsCount(leadsCount);
        leadsFollowTimeContext.process(list, rootDepartment);

        dtoList.forEach(item -> {
            if (Objects.equals(item.getDepartmentId(), departmentId)) {
                rootDepartment.setDepartmentName(item.getDepartmentName());
                rootDepartment.setDepartmentId(item.getDepartmentId());
                BeanUtils.copyProperties(rootDepartment, item);
            }
        });

        if (CollectionUtils.isEmpty(children)) {
            return;
        }

        //遍历第二层
        for (DepartmentDto departmentDto : children) {
            Long childernDpId = departmentDto.getId();
            dpToSumDepartmentVal(dtoList, childernDpId, new ArrayList<>());
        }
    }

    /**
     * 组装bean
     *
     * @param leadsFollow
     * @return com.inngke.bp.leads.db.leads.entity.LeadsFollowTime
     */
    private LeadsFollowTime leadsFollowTimeBeanProcess(LeadsFollow leadsFollow) {
        LeadsFollowTime leadsFollowTime = new LeadsFollowTime();
        leadsFollowTime.setId(leadsFollow.getLeadsId());
        Leads leads = leadsManager.getOne(Wrappers.<Leads>query()
                .eq(Leads.ID, leadsFollow.getLeadsId()));
        if (Objects.isNull(leads)) {
            return null;
        }
        Long staffId = leadsFollow.getStaffId();
        if (Objects.isNull(staffId) || staffId == 0) {
            //绑定导购
            staffId = leads.getDistributeStaffId();
        }

        LocalDateTime distributeTime = leads.getDistributeTime();
        if (Objects.isNull(distributeTime)) {
            distributeTime = LocalDateTime.now();
        }

        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(leads.getBid());
        baseIdsRequest.setIds(Collections.singletonList(staffId));
        List<StaffDepartmentAgentSimpleDto> staffDepartmentAgentDtoList= staffClientForLeads.getStaffDepartmentAgentSimple(baseIdsRequest);
        if (CollectionUtils.isEmpty(staffDepartmentAgentDtoList)){
            return null;
        }

        StaffDepartmentAgentSimpleDto staffDepartmentAgentSimpleDto = staffDepartmentAgentDtoList.get(0);

        leadsFollowTime.setAgentId(staffDepartmentAgentSimpleDto.getAgentId());
        leadsFollowTime.setDepartmentId(staffDepartmentAgentSimpleDto.getDepartmentId());
        leadsFollowTime.setBid(leads.getBid());
        leadsFollowTime.setStaffId(staffId);
        leadsFollowTime.setDistributeTime(distributeTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        return leadsFollowTime;
    }
}

package com.inngke.bp.leads.mq.process.organize;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.entity.LeadsDistributeConfPermissions;
import com.inngke.bp.leads.db.leads.entity.LeadsPreFollowConfig;
import com.inngke.bp.leads.db.leads.manager.LeadsDistributeConfPermissionsManager;
import com.inngke.bp.leads.db.leads.manager.LeadsPreFollowConfigManager;
import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.bp.leads.service.LeadsDistributeRegionPermissionsService;
import com.inngke.bp.organize.enums.EntityChangeTypeEnum;
import com.inngke.common.core.InngkeAppConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StaffDeleteProcess extends StaffChangeAbstract implements StaffChangeProcess {

    private static final Logger log = LoggerFactory.getLogger(StaffDeleteProcess.class);

    @Autowired
    private LeadsPreFollowConfigManager leadsPreFollowConfigManager;
    @Autowired
    private LeadsDistributeConfPermissionsManager leadsDistributeConfPermissionsManager;

    @Override
    public void handle(EntityChangeMessageDto entityChangeMessageDto) {
        deleteDistributeConf(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());

        // 删除客服配置
        removePreFollowConfig(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());

        //删除区域接收人权限配置
        leadsDistributeConfPermissionsManager.removeDistributeConfPermissions(entityChangeMessageDto.getBid(), entityChangeMessageDto.getId());
    }

    private void removePreFollowConfig(int bid, Long staffId) {
        log.info("删除客服配置接收到员工Mq:{},{}", bid, staffId);
        if (staffId != null) {
            return;
        }
        boolean remove = leadsPreFollowConfigManager.remove(new QueryWrapper<LeadsPreFollowConfig>()
                .eq(LeadsPreFollowConfig.BID, bid)
                .eq(LeadsPreFollowConfig.STAFF_ID, staffId));
        log.info("删除客服配置接收到员工Mq,删除完成:{},{}", staffId, remove);
    }

    @Override
    public EntityChangeTypeEnum getHandleEntityChangeType() {
        return EntityChangeTypeEnum.DELETE;
    }
}

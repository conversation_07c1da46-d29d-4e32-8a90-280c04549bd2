package com.inngke.bp.leads.mq.message.leads;

import com.inngke.common.mq.dto.BaseMessage;

import java.util.List;

/**
 * 线索变更mq'消息体
 *
 * <AUTHOR>
 * @since 2021/12/18 8:17 PM
 */
public class LeadsChangeMessage extends BaseMessage {

    private List<Long> leadsId;

    private Boolean notifyPartner;

    private String leadsName;

    /**
     * 事件（新增=1 修改=2 删除=3 分配=4 转交=5）
     */
    private Integer event;

    public List<Long> getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(List<Long> leadsId) {
        this.leadsId = leadsId;
    }

    public Boolean getNotifyPartner() {
        return notifyPartner;
    }

    public void setNotifyPartner(Boolean notifyPartner) {
        this.notifyPartner = notifyPartner;
    }

    public String getLeadsName() {
        return leadsName;
    }

    public void setLeadsName(String leadsName) {
        this.leadsName = leadsName;
    }

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }
}

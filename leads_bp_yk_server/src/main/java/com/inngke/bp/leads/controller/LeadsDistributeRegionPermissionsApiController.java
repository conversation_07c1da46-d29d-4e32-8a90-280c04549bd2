package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.SaveRegionPermissionsRequest;
import com.inngke.bp.leads.dto.response.RegionConfDto;
import com.inngke.bp.leads.service.LeadsDistributeRegionPermissionsService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @chapter 接收人配置
 * @section 区域权限配置
 * @since 2021/9/7 3:49 PM
 */
@RestController
@RequestMapping("/api/leads/distribute-region-conf")
public class LeadsDistributeRegionPermissionsApiController {

    @Resource
    private LeadsDistributeRegionPermissionsService leadsDistributeRegionPermissionsService;

    /**
     * 获取区域权限配置
     */
    @GetMapping
    public BaseResponse<List<RegionConfDto>> getRegionPermissions(@RequestAttribute JwtPayload jwtPayload){
        BaseBidRequest request = new BaseBidRequest();
        request.setBid(jwtPayload.getBid());

        return leadsDistributeRegionPermissionsService.getRegionPermissions(request);
    }

    /**
     * 清除配置
     */
    @DeleteMapping("/{regionId:\\d+}")
    public BaseResponse<Boolean> removeRegionPermissions(
            @RequestAttribute JwtPayload jwtPayload,@PathVariable Long regionId){
        BaseIdRequest request = new BaseIdRequest();
        request.setBid(jwtPayload.getBid());
        request.setId(regionId);

        return leadsDistributeRegionPermissionsService.removeRegionPermissions(request);
    }

    /**
     * 保存区域权限配置
     */
    @PostMapping
    public BaseResponse<Boolean> saveRegionPermissions(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody SaveRegionPermissionsRequest request){
        request.setBid(jwtPayload.getBid());

        return leadsDistributeRegionPermissionsService.saveRegionPermissions(request);
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsTransferRollbackTodo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.bp.organize.dto.response.StaffDto;

import java.util.List;

/**
 * <p>
 * 线索转交撤回代办 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
public interface LeadsTransferRollbackTodoManager extends IService<LeadsTransferRollbackTodo> {

    void add(Integer bid, Long id, Long forwardStaffId, Long distributeStaffId);

    List<LeadsTransferRollbackTodo> getRollbackList(Integer bid);

    boolean rollback(Leads leads, StaffDto staff, LeadsTransferRollbackTodo todo);
}

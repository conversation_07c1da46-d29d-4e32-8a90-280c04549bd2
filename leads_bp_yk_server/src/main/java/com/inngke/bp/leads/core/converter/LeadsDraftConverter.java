package com.inngke.bp.leads.core.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inngke.bp.leads.core.utils.LeadsChannelUtil;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsDraft;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.entity.LeadsInformationExtend;
import com.inngke.bp.leads.dto.LeadsDraftExcelDto;
import com.inngke.bp.leads.dto.LeadsErrorMessageDto;
import com.inngke.bp.leads.dto.response.LeadsDraftDto;
import com.inngke.bp.leads.enums.LeadsChannelEnum;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.enums.LeadsInputSourceEnum;
import com.inngke.bp.leads.enums.LeadsTypeEnum;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 *<AUTHOR>
 * @since 2021/9/10 11:15 PM
 */
public class LeadsDraftConverter {

    private static final Logger logger = LoggerFactory.getLogger(LeadsDraftConverter.class);
    private static final Map<String, Integer> GENDER_STRING_MAP = new HashMap<String, Integer>() {{
        put("男", 1);
        put("女", 2);
    }};

    public static LeadsDraftDto toLeadsDraftDto(LeadsDraft leadsDraft) {
        LeadsDraftDto leadsDraftDto = new LeadsDraftDto();
        leadsDraftDto.setId(leadsDraft.getId());
        leadsDraftDto.setBid(leadsDraft.getBid());
        leadsDraftDto.setCustomerId(leadsDraft.getCustomerId());
        leadsDraftDto.setName(leadsDraft.getName());
        leadsDraftDto.setMobile(leadsDraft.getMobile());
        leadsDraftDto.setProvinceId(leadsDraft.getProvinceId());
        leadsDraftDto.setProvinceName(leadsDraft.getProvinceName());
        leadsDraftDto.setCityId(leadsDraft.getCityId());
        leadsDraftDto.setCityName(leadsDraft.getCityName());
        leadsDraftDto.setAreaId(leadsDraft.getAreaId());
        leadsDraftDto.setAreaName(leadsDraft.getAreaName());
        leadsDraftDto.setAddress(leadsDraft.getAddress());
        leadsDraftDto.setChannel(leadsDraft.getChannel());
        leadsDraftDto.setOrderAccount(leadsDraft.getOrderAccount());
        leadsDraftDto.setOrderSn(leadsDraft.getOrderSn());
        leadsDraftDto.setGoodsName(leadsDraft.getGoodsName());
        leadsDraftDto.setGoodsNum(leadsDraft.getGoodsNum());
        leadsDraftDto.setPayTime(leadsDraft.getPayTime() != null ? DateTimeUtils.getMilli(leadsDraft.getPayTime()) : null);
        leadsDraftDto.setPayAmount(leadsDraft.getPayAmount());
        leadsDraftDto.setOrderMessage(leadsDraft.getOrderMessage());
        leadsDraftDto.setRemark(leadsDraft.getRemark());
        leadsDraftDto.setTpLeadsId(leadsDraft.getTpLeadsId());
        leadsDraftDto.setPromotionName(leadsDraft.getPromotionName());
        leadsDraftDto.setRegistryTime(leadsDraft.getRegistryTime() != null ? leadsDraft.getRegistryTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);
        leadsDraftDto.setExpectIn(leadsDraft.getExpectIn());
        leadsDraftDto.setStyle(leadsDraft.getStyle());
        leadsDraftDto.setBatchId(leadsDraft.getBatchId());
        leadsDraftDto.setErrorMsg(leadsDraft.getErrorMsg());
        leadsDraftDto.setCreateTime(leadsDraft.getCreateTime() != null ? leadsDraft.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);
        leadsDraftDto.setUpdateTime(leadsDraft.getUpdateTime() != null ? leadsDraft.getUpdateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli() : null);
        return leadsDraftDto;
    }

    public static Leads toLeads(LeadsDraft leadsDraft) {
        Leads leads = new Leads();
        leads.setId(leadsDraft.getId());
        leads.setBid(leadsDraft.getBid());
        leads.setCustomerId(leadsDraft.getCustomerId());
        leads.setName(leadsDraft.getName());
        leads.setMobile(leadsDraft.getMobile());
        //leads.setStatus();
        leads.setProvinceId(leadsDraft.getProvinceId());
        leads.setProvinceName(leadsDraft.getProvinceName());
        leads.setCityId(leadsDraft.getCityId());
        leads.setCityName(leadsDraft.getCityName());
        leads.setAreaId(leadsDraft.getAreaId());
        leads.setAreaName(leadsDraft.getAreaName());
        leads.setAddress(leadsDraft.getAddress());
        leads.setChannel(leadsDraft.getChannel());
        leads.setOrderAccount(leadsDraft.getOrderAccount());
        leads.setOrderSn(leadsDraft.getOrderSn());
        leads.setGoodsName(leadsDraft.getGoodsName());
        leads.setGoodsNum(leadsDraft.getGoodsNum());
        leads.setPayTime(leadsDraft.getPayTime());
        leads.setPayAmount(leadsDraft.getPayAmount());
        leads.setOrderMessage(leadsDraft.getOrderMessage());
        leads.setRemark(leadsDraft.getRemark());
        leads.setTpLeadsId(leadsDraft.getTpLeadsId());
        leads.setPromotionName(leadsDraft.getPromotionName());
        leads.setRegistryTime(leadsDraft.getRegistryTime());
        leads.setExpectIn(leadsDraft.getExpectIn());
        leads.setStyle(leadsDraft.getStyle());
        leads.setBatchId(leadsDraft.getBatchId());
        //leads.setDistributeAgentId();
        //leads.setDistributeStaffId();
        //leads.setDistributeTime();
        leads.setErrorMsg(leadsDraft.getErrorMsg());
        //leads.setLastFollowId();
        leads.setCreateTime(leadsDraft.getCreateTime());
        leads.setUpdateTime(leadsDraft.getUpdateTime());
        return leads;
    }

    public static LeadsDraft toLeadsDraft(LeadsDraftExcelDto dto, Map<String, Integer> channelNameToIdMap) {
        LeadsDraft leadsDraft = new LeadsDraft();
        leadsDraft.setBid(dto.getBid());
        leadsDraft.setCustomerId(dto.getCustomerId());
        leadsDraft.setName(dto.getName());
        leadsDraft.setChannelId(dto.getShopChannelId());
        leadsDraft.setMobile(dto.getMobile());
        leadsDraft.setProvinceId(dto.getProvinceId());
        leadsDraft.setProvinceName(dto.getProvinceName());
        leadsDraft.setCityId(dto.getCityId());
        leadsDraft.setCityName(dto.getCityName());
        leadsDraft.setAreaId(dto.getAreaId());
        leadsDraft.setAreaName(dto.getAreaName());
        leadsDraft.setAddress(dto.getAddress());
        leadsDraft.setChannel(channelNameToIdMap.get(dto.getChannelName()));
        leadsDraft.setOrderAccount(dto.getOrderAccount());
        leadsDraft.setOrderSn(dto.getOrderSn());
        leadsDraft.setGoodsName(dto.getGoodsName());
        leadsDraft.setGoodsNum(dto.getGoodsNum());
        leadsDraft.setPayAmount(dto.getPayAmount());
        leadsDraft.setOrderMessage(dto.getOrderMessage());
        leadsDraft.setRemark(dto.getRemark());
        leadsDraft.setTpLeadsId(dto.getTpLeadsId());
        LeadsInputSourceEnum leadsInputSourceEnum = LeadsInputSourceEnum.parse(dto.getChannelSource());
        leadsDraft.setChannelSource(leadsInputSourceEnum == null ? null : leadsInputSourceEnum.getCode());
        leadsDraft.setGender(GENDER_STRING_MAP.getOrDefault(dto.getGender(), 0));
        leadsDraft.setAge(dto.getAge());
        leadsDraft.setQq(dto.getQq());
        leadsDraft.setEmail(dto.getEmail());
        leadsDraft.setCampaignId(dto.getCampaignId());
        leadsDraft.setCampaignName(dto.getCampaignName());
        leadsDraft.setAccountId(dto.getAccountId());
        leadsDraft.setAccountName(dto.getAccountName());
        if (!StringUtils.isBlank(dto.getSubmitTime())){
            leadsDraft.setSubmitTime(DateTimeUtils.toLocalDateTime(dto.getSubmitTime().trim()));
        }
        leadsDraft.setWeChat(dto.getWeChat());

        if (!StringUtils.isBlank(dto.getRegistryTime())) {
            leadsDraft.setRegistryTime(DateTimeUtils.toLocalDateTime(dto.getRegistryTime().trim()));
        }
        leadsDraft.setPromotionName(dto.getPromotionName());
        leadsDraft.setCreateTime(LocalDateTime.now());
        leadsDraft.setGoodsLink(dto.getGoodsLink());
        try {
            if (!StringUtils.isBlank(dto.getPayTime())) {
                LocalDateTime time = DateTimeUtils.toLocalDateTime(dto.getPayTime().trim());
                leadsDraft.setPayTime(time);
            }
        } catch (Exception e) {
            try {
                LeadsErrorMessageDto errorDto = new LeadsErrorMessageDto();
                errorDto.setErrorData(dto);
                errorDto.setErrorMessage(e.getCause().getMessage());
                leadsDraft.setErrorMsg(new ObjectMapper().writeValueAsString(errorDto));
            } catch (Exception jsonException) {
                logger.error("leadsDraft json序列化失败" + jsonException);
            }
        }

        leadsDraft.setLevel(dto.getLevel());
        leadsDraft.setType(LeadsTypeEnum.parse(dto.getType()).getCode());
        leadsDraft.setTags(dto.getTags());
        leadsDraft.setEnterpriseTags(dto.getEnterpriseTags());
        leadsDraft.setDemandProduct(dto.getDemandProduct());
        leadsDraft.setTpId(dto.getTpId());
        return leadsDraft;

    }

    public static LeadsDraftExcelDto toLeadsDraftExcelDto(LeadsDraftDto leads){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LeadsDraftExcelDto leadsDraftExcelDto = new LeadsDraftExcelDto();
        leadsDraftExcelDto.setBid(leads.getBid());
        leadsDraftExcelDto.setCustomerId(leads.getCustomerId());
        leadsDraftExcelDto.setName(leads.getName());
        leadsDraftExcelDto.setMobile(leads.getMobile());
        leadsDraftExcelDto.setProvinceId(leads.getProvinceId());
        leadsDraftExcelDto.setProvinceName(leads.getProvinceName());
        leadsDraftExcelDto.setCityId(leads.getCityId());
        leadsDraftExcelDto.setCityName(leads.getCityName());
        leadsDraftExcelDto.setAreaId(leads.getAreaId());
        leadsDraftExcelDto.setAreaName(leads.getAreaName());
        leadsDraftExcelDto.setAddress(leads.getAddress());
        leadsDraftExcelDto.setChannelName(LeadsChannelUtil.getName(leads.getBid(), leads.getChannel()));
        leadsDraftExcelDto.setOrderAccount(leads.getOrderAccount());
        leadsDraftExcelDto.setOrderSn(leads.getOrderSn());
        leadsDraftExcelDto.setGoodsName(leads.getGoodsName());
        leadsDraftExcelDto.setGoodsNum(leads.getGoodsNum());
        if(null != leads.getPayTime()) {
                LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(leads.getPayTime() / 1000 , 0, ZoneOffset.ofHours(8));
                String format = DateTimeUtils.format(localDateTime, DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
                leadsDraftExcelDto.setPayTime(format);
        }
        leadsDraftExcelDto.setPayAmount(leads.getPayAmount());
        leadsDraftExcelDto.setOrderMessage(leads.getOrderMessage());
        leadsDraftExcelDto.setRemark(leads.getRemark());
        leadsDraftExcelDto.setTpLeadsId(leads.getTpLeadsId());
        leadsDraftExcelDto.setPromotionName(leads.getPromotionName());

        return leadsDraftExcelDto;
    }

    public static LeadsInformationExtend toLeadsInformationExt(LeadsDraft leadsDraft){
        LeadsInformationExtend leadsInformationExtend = new LeadsInformationExtend();
        leadsInformationExtend.setChannelId(Optional.ofNullable(leadsDraft.getChannelId()).orElse(0L));
        leadsInformationExtend.setCreateTime(leadsDraft.getCreateTime());
        leadsInformationExtend.setUpdateTime(leadsDraft.getUpdateTime());
        leadsInformationExtend.setId(leadsDraft.getId());
        leadsInformationExtend.setBid(leadsDraft.getBid());
        leadsInformationExtend.setCustomerId(leadsDraft.getCustomerId());
        leadsInformationExtend.setName(leadsDraft.getName());
        leadsInformationExtend.setMobile(leadsDraft.getMobile());
        leadsInformationExtend.setProvinceId(leadsDraft.getProvinceId());
        leadsInformationExtend.setProvinceName(leadsDraft.getProvinceName());
        leadsInformationExtend.setCityId(leadsDraft.getCityId());
        leadsInformationExtend.setCityName(leadsDraft.getCityName());
        leadsInformationExtend.setAreaId(leadsDraft.getAreaId());
        leadsInformationExtend.setAreaName(leadsDraft.getAreaName());
        leadsInformationExtend.setAddress(leadsDraft.getAddress());
        leadsInformationExtend.setChannel(leadsDraft.getChannel());
        leadsInformationExtend.setChannelSource(leadsDraft.getChannelSource());
        leadsInformationExtend.setOrderAccount(leadsDraft.getOrderAccount());
        leadsInformationExtend.setOrderSn(leadsDraft.getOrderSn());
        leadsInformationExtend.setGoodsName(leadsDraft.getGoodsName());
        leadsInformationExtend.setGoodsNum(leadsDraft.getGoodsNum());
        leadsInformationExtend.setPayTime(leadsDraft.getPayTime());
        leadsInformationExtend.setPayAmount(leadsDraft.getPayAmount());
        leadsInformationExtend.setOrderMessage(leadsDraft.getOrderMessage());
        leadsInformationExtend.setRemark(leadsDraft.getRemark());
        leadsInformationExtend.setTpLeadsId(leadsDraft.getTpLeadsId());
        leadsInformationExtend.setPromotionName(leadsDraft.getPromotionName());
        leadsInformationExtend.setRegistryTime(leadsDraft.getSubmitTime());
        leadsInformationExtend.setExpectIn(leadsDraft.getExpectIn());
        leadsInformationExtend.setStyle(leadsDraft.getStyle());
        leadsInformationExtend.setBatchId(leadsDraft.getBatchId());
        leadsInformationExtend.setErrorMsg(leadsDraft.getErrorMsg());
        leadsInformationExtend.setCreateTime(leadsDraft.getCreateTime());
        leadsInformationExtend.setUpdateTime(leadsDraft.getUpdateTime());
        leadsInformationExtend.setChannelType(LeadsDataSourceEnum.MANUAL_IMPORT.getCode());
        leadsInformationExtend.setSubmitTime(leadsDraft.getSubmitTime());
        leadsInformationExtend.setGender(leadsDraft.getGender());
        leadsInformationExtend.setAge(leadsDraft.getAge());
        leadsInformationExtend.setQq(leadsDraft.getQq());
        leadsInformationExtend.setEmail(leadsDraft.getEmail());
        leadsInformationExtend.setCampaignId(leadsDraft.getCampaignId());
        leadsInformationExtend.setCampaignName(leadsDraft.getCampaignName());
        leadsInformationExtend.setAccountId(leadsDraft.getAccountId());
        leadsInformationExtend.setAccountName(leadsDraft.getAccountName());
        leadsInformationExtend.setGoodsLink(leadsDraft.getGoodsLink());
//        leadsInformationExtend.setWeChat();
        leadsInformationExtend.setCreateStaffId(leadsDraft.getCreateStaffId());
        leadsInformationExtend.setType(leadsDraft.getType());
        leadsInformationExtend.setLevel(leadsDraft.getLevel());
        leadsInformationExtend.setLevelId(leadsDraft.getLevelId());
        leadsInformationExtend.setTags(leadsDraft.getTags());
        leadsInformationExtend.setEnterpriseTags(leadsDraft.getEnterpriseTags());
        leadsInformationExtend.setWeChat(leadsDraft.getWeChat());
        leadsInformationExtend.setDemandProduct(leadsDraft.getDemandProduct());
        leadsInformationExtend.setTpId(leadsDraft.getTpId());
        return leadsInformationExtend;

    }

    public static Leads toLeads(LeadsInformationExtend leadsInformationExtend){
        Leads leads = new Leads();
        leads.setId(leadsInformationExtend.getId());
        leads.setChannelId(Optional.ofNullable(leadsInformationExtend.getChannelId()).orElse(0L));
        leads.setBid(leadsInformationExtend.getBid());
        leads.setCustomerId(leadsInformationExtend.getCustomerId());
        leads.setCustomerUid(leadsInformationExtend.getCustomerUid());
        leads.setName(leadsInformationExtend.getName());
        leads.setMobile(leadsInformationExtend.getMobile());
        leads.setWeChat(leadsInformationExtend.getWeChat());
        leads.setStatus(leadsInformationExtend.getStatus());
        leads.setContactIn24(leadsInformationExtend.getContactIn24());
        leads.setProvinceId(leadsInformationExtend.getProvinceId());
        leads.setProvinceName(leadsInformationExtend.getProvinceName());
        leads.setCityId(leadsInformationExtend.getCityId());
        leads.setCityName(leadsInformationExtend.getCityName());
        leads.setAreaId(leadsInformationExtend.getAreaId());
        leads.setAreaName(leadsInformationExtend.getAreaName());
        leads.setAddress(leadsInformationExtend.getAddress());
        leads.setChannel(leadsInformationExtend.getChannel());
        leads.setChannelType(leadsInformationExtend.getChannelType());
        leads.setChannelSource(leadsInformationExtend.getChannelSource());
        leads.setOrderAccount(leadsInformationExtend.getOrderAccount());
        leads.setOrderSn(leadsInformationExtend.getOrderSn());
        leads.setGoodsName(leadsInformationExtend.getGoodsName());
        leads.setGoodsNum(leadsInformationExtend.getGoodsNum());
        leads.setPayTime(leadsInformationExtend.getPayTime());
        leads.setPayAmount(leadsInformationExtend.getPayAmount());
        leads.setOrderMessage(leadsInformationExtend.getOrderMessage());
        leads.setRemark(leadsInformationExtend.getRemark());
        leads.setTpLeadsId(leadsInformationExtend.getTpLeadsId());
        leads.setPromotionName(leadsInformationExtend.getPromotionName());
        leads.setRegistryTime(leadsInformationExtend.getRegistryTime());
        leads.setExpectIn(leadsInformationExtend.getExpectIn());
        leads.setStyle(leadsInformationExtend.getStyle());
        leads.setBatchId(leadsInformationExtend.getBatchId());
        // leads.setDistributeAgentId(leadsInformationExtend.getDistributeAgentId());
        leads.setDistributeStaffId(leadsInformationExtend.getDistributeStaffId());
        leads.setDistributeTime(leadsInformationExtend.getDistributeTime());
        leads.setPushBackTime(leadsInformationExtend.getPushBackTime());
        leads.setErrorMsg(leadsInformationExtend.getErrorMsg());
        leads.setLastFollowId(leadsInformationExtend.getLastFollowId());
        leads.setExtData(leadsInformationExtend.getExtData());
        leads.setGoodsLink(leadsInformationExtend.getGoodsLink());
        leads.setCreateTime(leadsInformationExtend.getCreateTime());
        leads.setUpdateTime(leadsInformationExtend.getUpdateTime());
        leads.setTags(leadsInformationExtend.getTags());
        leads.setEnterpriseTags(leadsInformationExtend.getEnterpriseTags());
        leads.setType(leadsInformationExtend.getType());
//        leads.setLevel(leadsInformationExtend.getLevel());
        leads.setLevelId(leadsInformationExtend.getLevelId());
        leads.setCreateStaffId(leadsInformationExtend.getCreateStaffId());
        leads.setDemandProduct(leadsInformationExtend.getDemandProduct());
        leads.setTpId(leadsInformationExtend.getTpId());
        return leads;
    }

    public static LeadsExtInformation toLeadsExtInformation(LeadsInformationExtend leadsInformationExtend){
        LeadsExtInformation leadsExtInformation = new LeadsExtInformation();
        leadsExtInformation.setId(leadsInformationExtend.getId());
        leadsExtInformation.setBid(leadsInformationExtend.getBid());
        leadsExtInformation.setExternalId(leadsInformationExtend.getExternalId());
        leadsExtInformation.setGender(leadsInformationExtend.getGender());
        leadsExtInformation.setCampaignId(leadsInformationExtend.getCampaignId());
        leadsExtInformation.setCampaignName(leadsInformationExtend.getCampaignName());
        leadsExtInformation.setAccountId(leadsInformationExtend.getAccountId());
        leadsExtInformation.setAccountName(leadsInformationExtend.getAccountName());
        leadsExtInformation.setSubmitTime(leadsInformationExtend.getSubmitTime());
        leadsExtInformation.setCreateTime(leadsInformationExtend.getCreateTime());
        leadsExtInformation.setUpdateTime(leadsInformationExtend.getUpdateTime());
        return leadsExtInformation;
    }
}

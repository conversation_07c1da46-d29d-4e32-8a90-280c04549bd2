package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.PullAiCustomerLeadsRequest;
import com.inngke.bp.leads.dto.response.tp.TpAiCustomerServiceConfigDto;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/3/3 10:04
 */
public interface AiCustomerServiceLeadsService {

    /**
     * @param request
     * @return
     */
    BaseResponse<Boolean> pullLeads(PullAiCustomerLeadsRequest request);

    BaseResponse<Boolean> checkConfig(TpAiCustomerServiceConfigDto tpAiCustomerServiceConfigDto);
}

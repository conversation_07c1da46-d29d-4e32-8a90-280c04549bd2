package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsCheckService;
import com.inngke.common.exception.YkIllegalArgumentException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * LeadsCheckServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/16 17:00
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LeadsCheckServiceImpl implements LeadsCheckService {

    private final LeadsManager leadsManager;

    private final LeadsServiceV2Impl leadsServiceV2Impl;

    @Override
    public List<Leads> hasAnyMobileOrWechatLeadsOfDistributorStaff(Integer bid, Long distributorStaffId, String mobile, String wx) {
        if (Objects.isNull(bid) || bid == 0) {
            throw new YkIllegalArgumentException("租户id不能为空");
        }
        if (Objects.isNull(distributorStaffId) || distributorStaffId.equals(0L)) {
            throw new YkIllegalArgumentException("跟进导购id不能为空");
        }
        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(wx)) {
            throw new YkIllegalArgumentException("手机号与微信不能同时为空");
        }


        return leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.DISTRIBUTE_STAFF_ID, distributorStaffId)
                        .notIn(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.DELETED.getStatus(), LeadsStatusEnum.RECOVERY.getStatus()))
                        .and(wrapper -> wrapper.or(
                                        orWra -> orWra
                                                .eq(StringUtils.isNotBlank(mobile), Leads.MOBILE, mobile)
                                                .eq(StringUtils.isNotBlank(wx), Leads.WE_CHAT, wx)
                                )
                        )
        );
    }

    @Override
    public List<Leads> hasAnyMobileOrWechatLeadsOfReportStaff(Integer bid, Long reportStaffId, String mobile, String wx) {
        if (Objects.isNull(bid) || bid == 0) {
            throw new YkIllegalArgumentException("租户id不能为空");
        }
        if (Objects.isNull(reportStaffId) || reportStaffId.equals(0L)) {
            throw new YkIllegalArgumentException("上报导购id不能为空");
        }
        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(wx)) {
            throw new YkIllegalArgumentException("手机号与微信不能同时为空");
        }


        return leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.REPORT_STAFF_ID, reportStaffId)
                        .and(wrapper -> wrapper.or(
                                        orWra -> orWra
                                                .eq(StringUtils.isNotBlank(mobile), Leads.MOBILE, mobile)
                                                .eq(StringUtils.isNotBlank(wx), Leads.WE_CHAT, wx)
                                )
                        )
        );
    }

    @Override
    public void checkRepeatLeads(Integer bid, String mobile, String weChat, Set<Long> excludeIds) {
        leadsServiceV2Impl.checkRequest(bid, mobile, weChat, excludeIds);
    }
}

package com.inngke.bp.leads.client;

import com.google.common.collect.Sets;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.auth.rbac.dto.request.UserListByRoleRequest;
import com.inngke.ip.auth.rbac.dto.request.UserRolesRequest;
import com.inngke.ip.auth.rbac.dto.response.RoleDto;
import com.inngke.ip.auth.rbac.service.RbacUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/10 17:52
 **/
@Component
@Slf4j
public class RbacClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.auth_ip_yk:}")
    RbacUserService rbacUserService;

    private static final String ROLE_CODE = "agent_manager";
    private static final String APP_CODE = "yk-pc";

    /**
     * 客服角色
     */
    private static final String ROLE_CUSTOMER = "customer";

    private static final String ROLE_CUSTOMER_MANAGER = "customer_manager";

    public static final String MERCHANT_MANAGER = "merchant_manager";

    /**
     * 导购
     */
    private static final String GUIDE = "guide";

    public Set<Long> listUserIdsByRole(BaseBidOptRequest request) {
        UserListByRoleRequest userListByRoleRequest = new UserListByRoleRequest();
        userListByRoleRequest.setBid(request.getBid());
        userListByRoleRequest.setRoleCode(ROLE_CODE);
        userListByRoleRequest.setAppCode(APP_CODE);
        BaseResponse<Set<Long>> response = rbacUserService.listUserIdsByRole(userListByRoleRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    /**
     * 通过roleCode获取员工Ids
     *
     * @param bid      bid
     * @return 员工IDs
     */
    public Set<Long> listCustomerRoleUserIds(Integer bid) {
        UserListByRoleRequest request = new UserListByRoleRequest();
        request.setRoleCode(ROLE_CUSTOMER);
        request.setAppCode(APP_CODE);
        request.setBid(bid);

        Set<Long> result = Sets.newHashSet();

        BaseResponse<Set<Long>> response = rbacUserService.listUserIdsByRole(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("未获取到customer的userIds");
        }
        result.addAll(response.getData());

        request.setRoleCode(ROLE_CUSTOMER_MANAGER);
        BaseResponse<Set<Long>> managerResponse = rbacUserService.listUserIdsByRole(request);

        if (!BaseResponse.responseSuccessWithNonNullData(managerResponse)) {
            log.info("未获取到customer_manager的userIds");
        }
        result.addAll(managerResponse.getData());

        return result;
    }

    public Set<Long> listGuideRoleUserIds(Integer bid) {
        UserListByRoleRequest request = new UserListByRoleRequest();
        request.setRoleCode(GUIDE);
        request.setAppCode(APP_CODE);
        request.setBid(bid);

        BaseResponse<Set<Long>> response = rbacUserService.listUserIdsByRole(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return Sets.newHashSet();
        }

        return response.getData();
    }

    /**
     * 获取员工PC角色
     *
     * @param bid
     * @param userId
     * @return
     */
    public Set<RoleDto> getUserRole(int bid, Long userId){
        UserRolesRequest request = new UserRolesRequest();
        request.setUserId(userId);
        request.setAppCode(APP_CODE);
        request.setOperatorId(0L);
        request.setBid(bid);
        //获取用户角色列表
        BaseResponse<Set<RoleDto>> response = rbacUserService.getUserRoles(request);
        if (BaseResponse.responseErrorOrNullData(response)){
            return Sets.newHashSet();
        }
        return response.getData();
    }

    public Set<String> getUserRoleCode(int bid,Long userId){
        return getUserRole(bid, userId).stream().map(RoleDto::getCode).collect(Collectors.toSet());
    }

}

package com.inngke.bp.leads.notify.builder;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.notify.context.RejectLeadsPushBackNotifyContext;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * RejectLeadsPushBackNotifyBuilder
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/10 18:57
 */
@Component(value = "reject_leads_push_back")
@Slf4j
public class RejectLeadsPushBackNotifyBuilder extends TemplateMessageContentBuilder<RejectLeadsPushBackNotifyContext> {
    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Override
    public TemplateMessageSendRequestBuilder builderMessageContent(RejectLeadsPushBackNotifyContext ctx) {
        TemplateMessageSendRequestBuilder builder = super.builderNewMessageRequest(ctx);
        String rejectReason = ctx.getRejectReason();
        String followType = "线索退回失败," + "[" + rejectReason + "]";
        return builder
                .setTitle("客户跟进结果通知")
                .setVar("name", ctx.getName())
                .setVar("mobile", ctx.getMobile())
                .setVar("followType", "退回失败")
                .setVar("followContent", ctx.getRejectReason().length() > 20 ? (ctx.getRejectReason().substring(16) + "...") : ctx.getRejectReason());
    }
}

package com.inngke.bp.leads.mq.message.organize;

import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.mq.dto.BaseMessage;

public class EntityChangeMessageDto extends BaseMessage {

    /**
     * 实体ID
     */
    private Long id;

    /**
     * 事件类型
     */
    private Integer event;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEvent() {
        return event;
    }

    public void setEvent(Integer event) {
        this.event = event;
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线索事件
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsEventLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer bid;

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 当前跟进人
     */
    private Long followStaffId;

    /**
     * 当前跟进人所在部门包含父级
     */
    private String followDepartmentIds;

    /**
     * 跟进人所在部门
     */
    private Long followDepartmentId;

    /**
     * 线索客服id
     */
    @TableField("pre_followStaff_id")
    private Long preFollowstaffId;

    /**
     * 客服所在部门包含父级
     */
    private String preFollowDepartmentIds;

    /**
     * 客服所在部门
     */
    private Long preFollowDepartmentId;

    /**
     * 事件ID
     */
    private Integer eventId;

    /**
     * 事件时间
     */
    private LocalDateTime createTime;

    /**
     * 线索创建时间
     */
    private LocalDateTime leadsCreateTime;

    /**
     * 线索分配时间
     */
    private LocalDateTime leadsDistributeTime;

    /**
     * 额外信息，JSON格式
     */
    private String ext;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String LEADS_ID = "leads_id";

    public static final String FOLLOW_STAFF_ID = "follow_staff_id";

    public static final String FOLLOW_DEPARTMENT_IDS = "follow_department_ids";

    public static final String FOLLOW_DEPARTMENT_ID = "follow_department_id";

    public static final String PRE_FOLLOWSTAFF_ID = "pre_followStaff_id";

    public static final String PRE_FOLLOW_DEPARTMENT_IDS = "pre_follow_department_ids";

    public static final String PRE_FOLLOW_DEPARTMENT_ID = "pre_follow_department_id";

    public static final String EVENT_ID = "event_id";

    public static final String CREATE_TIME = "create_time";

    public static final String LEADS_CREATE_TIME = "leads_create_time";

    public static final String LEADS_DISTRIBUTE_TIME = "leads_distribute_time";

    public static final String EXT = "ext";

}

package com.inngke.bp.leads.service.impl.contact.status;

import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.dto.request.PrivateVoiceRecordDTO;
import com.inngke.bp.leads.service.enums.LeadsContactStatusEnum;
import com.inngke.bp.leads.service.impl.contact.AbstractLeadsContactFollowService;
import org.springframework.stereotype.Service;

/**
 * 虚拟号 首次拨打 接通 10s以下
 * 第一次拨打客户电话，通话时长5秒，系统将线索状态由【待联系】修改为【已联系】
 *
 * <AUTHOR>
 * @date 2022/4/2 14:23
 */
@Service
public class LeadsContactFollowFakeFirstConnectMinServiceImpl extends AbstractLeadsContactFollowService {

    @Override
    protected String generateContactFollowContent(Leads leads, PrivateVoiceRecordDTO dto) {
        return "第一次拨打客户电话，通话时长" + getTalkTimeText(dto.getVoiceTime()) + "，系统将线索状态由【待联系】修改为【已联系】";
    }

    @Override
    public LeadsContactStatusEnum getStatusEnum() {
        return LeadsContactStatusEnum.FAKE_FIRST_CONNECT_MIN;
    }
}


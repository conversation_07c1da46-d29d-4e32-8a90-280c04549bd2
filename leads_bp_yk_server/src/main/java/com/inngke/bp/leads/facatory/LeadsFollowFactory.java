package com.inngke.bp.leads.facatory;

import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import com.inngke.bp.leads.enums.LeadsTransferEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * LeadsFollowFacatory
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 16:45
 */
@Component
public class LeadsFollowFactory {

    @Autowired
    private Map<String, LeadsFollowHandler> leadsFollowHandlerList;

    public LeadsFollow createFollow(LeadsTransferEnum transferEnum) {
        if (Objects.isNull(transferEnum) || Objects.isNull(leadsFollowHandlerList.get(transferEnum.getHandlerName()))) {
            return new LeadsFollow();
        }
        return leadsFollowHandlerList.get(transferEnum.getHandlerName()).createFollow();
    }
}

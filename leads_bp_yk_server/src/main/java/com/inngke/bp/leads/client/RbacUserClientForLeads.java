package com.inngke.bp.leads.client;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.ip.auth.rbac.dto.request.*;
import com.inngke.ip.auth.rbac.dto.response.RoleDto;
import com.inngke.ip.auth.rbac.service.PermissionCalculationService;
import com.inngke.ip.auth.rbac.service.RbacUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * RbacUserClient
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/3 21:31
 */
@Component
@Slf4j
public class RbacUserClientForLeads {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.auth_ip_yk:}")
    private RbacUserService rbacUserService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.auth_ip_yk:}")
    private PermissionCalculationService permissionCalculationService;

    public static final String ROLE_GUIDE = "guide";
    public static final String APP_CODE_YK_PC = "yk-pc";

    public static final String APP_CODE_YK_MP = "yk-mp";

    @Autowired
    private JsonService jsonService;

    public String getUserPermission(int bid, Long staffId) {
        UserPermissionRequest userPermissionRequest = new UserPermissionRequest();
        userPermissionRequest.setBid(bid);
        userPermissionRequest.setAppId(4);
        userPermissionRequest.setUserId(staffId);
        BaseResponse<String> userPermission = rbacUserService.getUserPermission(userPermissionRequest);
        if (!BaseResponse.responseSuccess(userPermission)) {
            throw new InngkeServiceException("获取用户权限失败！");
        }
        return userPermission.getData();
    }

    public Boolean setUserRole(Integer bid, Set<String> appCodes, Long staffId, Set<String> roleCodes, Long operatorId) {
        SetUserRoleRequest request = new SetUserRoleRequest();
        request.setAppCodes(appCodes);
        request.setUserId(staffId);
        request.setBid(bid);
        request.setRoleCodes(roleCodes);
        request.setOperatorId(Optional.ofNullable(operatorId).orElse(0L));
        BaseResponse<Boolean> response = rbacUserService.setUserRole(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.error("远程调用发生错误:{}", jsonService.toJson(response));
            throw new InngkeServiceException("网络异常,添加角色失败");
        }
        return response.getData();
    }

    /**
     * 通过roleCode获取员工Ids
     *
     * @param bid      bid
     * @param roleCode 角色代码
     * @return 员工IDs
     */
    public Set<Long> listUserIdsByRole(Integer bid, String roleCode) {
        if (StringUtils.isEmpty(roleCode)) {
            return Sets.newHashSet();
        }
        UserListByRoleRequest request = new UserListByRoleRequest();
        request.setRoleCode(roleCode);
        request.setAppCode(APP_CODE_YK_PC);
        request.setBid(bid);

        BaseResponse<Set<Long>> response = rbacUserService.listUserIdsByRole(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            return Sets.newHashSet();
        }

        return response.getData();
    }

    /**
     * 设置用户角色
     *
     * @param bid         bid
     * @param roleCodeSet 角色Set
     * @param staffId     员工ID
     * @return true | false
     */
    public boolean setAgentManagerRole(Integer bid, Set<String> roleCodeSet, Long staffId) {
        SetUserRoleRequest setUserRoleRequest = new SetUserRoleRequest();
        setUserRoleRequest.setBid(bid);
        setUserRoleRequest.setUserId(staffId);
        setUserRoleRequest.setAppCodes(Sets.newHashSet(APP_CODE_YK_PC, APP_CODE_YK_MP));
        setUserRoleRequest.setRoleCodes(roleCodeSet);
        BaseResponse<Boolean> booleanBaseResponse = rbacUserService.setUserRole(setUserRoleRequest);
        if (!BaseResponse.responseSuccess(booleanBaseResponse)) {
            log.warn("添加员工成功RPC调用绑定角色失败staffId={},roleCode={}", staffId, roleCodeSet);
            return false;
        }
        return true;
    }


    public Set<RoleDto> getUserRole(int bid, Long operatorId, Long staffId) {
        UserRolesRequest userRolesRequest = new UserRolesRequest();
        userRolesRequest.setUserId(staffId);
        userRolesRequest.setBid(bid);
        userRolesRequest.setOperatorId(operatorId);
        userRolesRequest.setAppCode(APP_CODE_YK_PC);
        BaseResponse<Set<RoleDto>> userRoles = rbacUserService.getUserRoles(userRolesRequest);
        if (!BaseResponse.responseSuccess(userRoles)) {
            throw new InngkeServiceException("网络异常!");
        }
        return Optional.ofNullable(userRoles.getData()).orElse(Sets.newHashSet());
    }

    public String getUserRoleCode(int bid, Long operatorId, Long staffId) {
        Set<RoleDto> userRole = getUserRole(bid, operatorId, staffId);
        if (CollectionUtils.isEmpty(userRole)) {
            throw new InngkeServiceException("获取用户角色失败");
        }

        RoleDto roleDto = userRole.stream().findFirst().orElse(null);
        if (ObjectUtils.isEmpty(roleDto)) {
            throw new InngkeServiceException("获取用户角色失败");
        }
        return roleDto.getCode();
    }

    public Map<Long, Set<RoleDto>> batchGetUserRole(int bid, Long operatorId, Set<Long> staffIds) {
        UserRolesBatchRequest request = new UserRolesBatchRequest();
        request.setUserIds(staffIds);
        request.setAppCode(APP_CODE_YK_PC);
        request.setOperatorId(operatorId);
        request.setBid(bid);

        BaseResponse<Map<Long, Set<RoleDto>>> mapBaseResponse = rbacUserService.batchGetUserRoles(request);
        if (!BaseResponse.responseSuccessWithNonNullData(mapBaseResponse)) {
            return Maps.newHashMap();
        }

        return mapBaseResponse.getData();
    }

    public void batchSetUserRole(int bid, Long operatorId, Set<Long> staffIds, Set<String> roleCodes) {
        UserRolesBatchSetRequest request = new UserRolesBatchSetRequest();
        request.setUserIds(staffIds);
        request.setAppCodes(Sets.newHashSet(APP_CODE_YK_PC, APP_CODE_YK_MP));
        request.setRoleCodes(roleCodes);
        request.setOperatorId(Optional.ofNullable(operatorId).orElse(0L));
        request.setBid(bid);

        rbacUserService.batchSetUserRoles(request);
    }

    public boolean matchAllPermission(Integer bid, String permissionVal, String matchPermissionVal) {
        HasAllPermissionRequest request = new HasAllPermissionRequest();
        request.setBid(bid);
        request.setPermissionValue(permissionVal);
        request.setToMatchPermission(matchPermissionVal);
        BaseResponse<Boolean> response = permissionCalculationService.hasAllPermission(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.info("hasAllPermissionRequest调用异常：{}", jsonService.toJson(response));
            return Boolean.FALSE;
        }

        return Boolean.TRUE.equals(response.getData());
    }
}

package com.inngke.bp.leads.service.strategy.permissions.list;

import com.inngke.bp.leads.dto.request.SearchLeadsRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.Set;

public interface ListPermissionsStrategy {

    /**
     * 处理权限参数
     *
     * @param request       查询参数
     * @param queryBuilder  Es查询条件
     * @param userRoleCodes 用户角色
     */
    void handle(SearchLeadsRequest request, BoolQueryBuilder queryBuilder, Set<String> userRoleCodes);
}

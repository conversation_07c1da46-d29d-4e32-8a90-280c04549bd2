package com.inngke.bp.leads.facatory;

import com.inngke.bp.leads.db.leads.entity.LeadsFollow;
import org.springframework.stereotype.Component;

/**
 * ClientTransferHandler
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 16:54
 */
@Component
public class ClientTransferHandler implements LeadsFollowHandler {
    @Override
    public LeadsFollow createFollow() {
        return LeadsFollow.builder().followContent("转交客户数据时将线索转交给【{0}】").build();
    }
}

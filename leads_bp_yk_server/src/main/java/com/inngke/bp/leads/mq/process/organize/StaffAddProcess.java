package com.inngke.bp.leads.mq.process.organize;

import com.inngke.bp.leads.mq.message.organize.EntityChangeMessageDto;
import com.inngke.bp.organize.enums.EntityChangeTypeEnum;
import org.springframework.stereotype.Service;

@Service
public class StaffAddProcess extends StaffChangeAbstract implements StaffChangeProcess {

    @Override
    public void handle(EntityChangeMessageDto entityChangeMessageDto) {
    }

    @Override
    public EntityChangeTypeEnum getHandleEntityChangeType() {
        return EntityChangeTypeEnum.ADD;
    }
}

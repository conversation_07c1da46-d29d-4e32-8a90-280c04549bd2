package com.inngke.bp.leads.notify.context;

import com.google.common.collect.Maps;
import com.inngke.bp.leads.client.CustomerGetServiceClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.utils.ApplicationContextGetBeanHelper;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.service.enums.LeadsNotifyMessageEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.common.notify.builder.TemplateMessageContentBuilder;
import com.inngke.common.notify.context.NotifyMessageContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * AdminNotifyNotContactGuidesContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/8/21 15:34
 */
public class AdminNotifyNotContactGuidesContext implements NotifyMessageContext {

    private int bid;

    private List<LeadsEsDto> leadsList;

    private final LeadsNotifyMessageEnum notifyEnum = LeadsNotifyMessageEnum.ADMIN_NOTIFY_NOT_CONTACT_GUIDES;

    private String qyWxUserId;

    private String wxPubOpenId;


    public int getSize() {
        return leadsList.size();
    }

    public List<LeadsEsDto> getLeadsList() {
        return leadsList;
    }

    @Override
    public int getBid() {
        return this.bid;
    }

    @Override
    public String getMsgTypeCode() {
        return notifyEnum.getTemplateCode();
    }

    @Override
    public String getPath() {
        return notifyEnum.getPath();
    }

    @Override
    public Long getTargetStaffId() {
        if (CollectionUtils.isEmpty(leadsList)) {
            return 0L;
        }
        return leadsList.get(0).getDistributeStaffId();
    }

    @Override
    public Map<String, String> getPathParams() {
        int size = this.getSize();

        Map<String, String> params = Maps.newHashMap();
        params.put("umaEvent", "clueRemind");
        params.put("staffId", String.valueOf(this.getTargetStaffId()));

        if (size == 1) {
            params.put("leadsId", this.getLeadsList().get(0).getId());
            params.put("id", this.getLeadsList().get(0).getId());
        } else {
            params.put("secondStatus", "1");
        }

        return params;
    }

    @Override
    public String getQyUserId() {
        return this.qyWxUserId;
    }

    @Override
    public String getWxPubOpenId() {
        return this.wxPubOpenId;
    }

    @Override
    public TemplateMessageContentBuilder<? extends NotifyMessageContext> getMessageBuilder() {
        return null;
    }


    public static AdminNotifyNotContactGuidesContext init(int bid, List<LeadsEsDto> leadsEsDtoList) {
        AdminNotifyNotContactGuidesContext context = new AdminNotifyNotContactGuidesContext();
        context.bid = bid;
        context.leadsList = leadsEsDtoList;

        StaffClientForLeads staffClient = ApplicationContextGetBeanHelper.getBean(StaffClientForLeads.class);
        StaffDto staff = staffClient.getStaffById(bid, context.getTargetStaffId());
        if (Objects.nonNull(staff) && staff.getStatus().equals(StaffStatusEnum.OPENED.getCode())) {
            if (StringUtils.isNotBlank(staff.getQyUserId())) {
                context.qyWxUserId = staff.getQyUserId();
            }

            CustomerGetServiceClientForLeads customerClient = ApplicationContextGetBeanHelper.getBean(CustomerGetServiceClientForLeads.class);
            CustomerDto customer = customerClient.getCustomerById(bid, staff.getCustomerId());
            if (Objects.nonNull(customer)) {
                context.wxPubOpenId = customer.getWxPubOpenId();
            }
        }

        return context;
    }
}

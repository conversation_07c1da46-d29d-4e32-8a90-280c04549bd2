package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.bp.client.dto.request.level.AddClientLevelByIdRequest;
import com.inngke.bp.client.dto.request.level.EditClientLevelByIdRequest;
import com.inngke.bp.client.dto.response.common.ClientLevelItemDto;
import com.inngke.bp.client.dto.response.level.ClientLevelListDto;
import com.inngke.bp.client.service.ClientCommonService;
import com.inngke.bp.client.service.ClientLevelService;
import com.inngke.common.dto.request.BaseBidOptPageRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/16 11:25
 */
@Service
public class ClientLevelClientForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientLevelService clientLevelService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.client_bp_yk:}")
    private ClientCommonService clientCommonService;




    public List<ClientLevelItemDto> getClientLevelList(int bid){
        AddClientLevelByIdRequest request = new AddClientLevelByIdRequest();
        request.setBid(bid);
        BaseResponse<List<ClientLevelItemDto>> response = clientCommonService.getClientLevelList(request);
        if(!BaseResponse.responseSuccess(response)){
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }

    public List<ClientLevelListDto> getByIds(int bid, List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        BaseIdsRequest request = new BaseIdsRequest();
        List<Long> list = new ArrayList<>();
        ids.forEach(item -> list.add(item.longValue()));
        request.setBid(bid);
        request.setIds(list);
        BaseResponse<List<ClientLevelListDto>> response = clientLevelService.getByIds(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }

        return response.getData();
    }

    public Map<Integer,String> getNameMapByIds(int bid, Set<Integer> ids) {
        try {
             return getByIds(bid, Lists.newArrayList(ids)).stream()
                     .collect(Collectors.toMap(ClientLevelListDto::getId,level-> level.getTitle() + "：" + level.getExplainInfo()));
        }catch (InngkeServiceException e){
            return Maps.newHashMap();
        }
    }
}

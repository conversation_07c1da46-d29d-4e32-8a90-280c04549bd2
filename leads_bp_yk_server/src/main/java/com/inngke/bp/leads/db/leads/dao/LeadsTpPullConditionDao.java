/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.dao;

import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpPullCondition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.common.ds.annotation.DS;

/**
 * <p>
 * 拉取第三方平台数据条件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public interface LeadsTpPullConditionDao extends BaseMapper<LeadsTpPullCondition> {

}

package com.inngke.bp.leads.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/9/17 8:11 PM
 */
public class StaffLeadsCountDto implements Serializable {
    /**
     * 员工ID
     *
     * @demo 1234
     */
    private Long staffId;

    /**
     * 员工新下发线索数量
     *
     * @demo 32
     */
    private Integer count;

    private String messageName;

    private String mobile;

    /**
     * 当需要跳转线索详情的此字段才有值
     */
    private Long leadsId;

    /**
     * 0：总部下发 1:报备
     */
    private Integer leadsChannelType;

    public Integer getLeadsChannelType() {
        return leadsChannelType;
    }

    public void setLeadsChannelType(Integer leadsChannelType) {
        this.leadsChannelType = leadsChannelType;
    }

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 广告平台授权用户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsTpAccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 类型 1:腾讯 2:飞鱼
     */
    private Integer type;

    /**
     * 账号ID
     */
    private String accountId;

    /**
     * 主账号ID
     */
    private String parentAccountId;

    /**
     * 企业名称-腾讯,账户名-飞鱼
     */
    private String accountName;

    /**
     * 商务管家账号类型-枚举-腾讯
     */
    private String accountType;

    /**
     * 角色-飞鱼
     */
    private String accountRole;

    /**
     * 商户管家账号-腾讯
     */
    private String businessId;

    /**
     * 商户管家账号-腾讯
     */
    private String businessRole;

    private Boolean enable;

    private Long operatorStaffId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String ACCOUNT_ID = "account_id";

    public static final String PARENT_ACCOUNT_ID = "parent_account_id";

    public static final String ACCOUNT_NAME = "account_name";

    public static final String ACCOUNT_TYPE = "account_type";

    public static final String ACCOUNT_ROLE = "account_role";

    public static final String BUSINESS_ID = "business_id";

    public static final String BUSINESS_ROLE = "business_role";

    public static final String OPERATOR_STAFF_ID = "operator_staff_id";

    public static final String ENABLE = "enable";

    public static final String TYPE = "type";

}

package com.inngke.bp.leads.api.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/21 16:18
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class GetFlyFishTokenByCodeRequest extends GetFlyFishAccessTokenRequest{

    /**
     * 必填 string
     */
    @JsonProperty("auth_code")
    private String authCode;
}

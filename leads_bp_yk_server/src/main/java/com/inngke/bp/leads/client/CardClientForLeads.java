package com.inngke.bp.leads.client;

import com.google.common.collect.Maps;
import com.inngke.bp.organize.dto.request.card.GetCardByStaffIdRequest;
import com.inngke.bp.organize.dto.request.card.GetCardByStaffIdsRequest;
import com.inngke.bp.organize.dto.response.card.CardDto;
import com.inngke.bp.organize.service.CardService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeErrorException;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/10 11:23
 **/
@Component
public class CardClientForLeads {

    private static final Logger LOG = LoggerFactory.getLogger(CardClientForLeads.class);

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private CardService cardService;

    @Resource
    private JsonService jsonService;

    /**
     * 通过员工编号查询名片数据
     * @param getCardByStaffIdsRequest
     * bid 商户号
     * staffIds 员工编号列表
     * Fields 查询的字段
     */
    public CardDto getCardByStaffId(GetCardByStaffIdRequest getCardByStaffIdsRequest) {
        BaseResponse<CardDto> response = cardService.getByStaffId(getCardByStaffIdsRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException("查询名片请求异常: " + response.getMsg());
        }
        return response.getData();
    }

    public Map<Long, CardDto> getCardInfos(GetCardByStaffIdsRequest request) {
        BaseResponse<List<CardDto>> response = cardService.getByStaffIds(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            LOG.warn("查询名片请求异常: {}",jsonService.toJson(response.getMsg()));
            return Maps.newHashMap();
        }
        return response.getData().stream().collect(Collectors.toMap(CardDto::getStaffId, Function.identity()));
    }

    public Map<Long, CardDto> getCardDtoListByStaffIds(Integer bid,List<Long> staffIds) {
        GetCardByStaffIdsRequest getCardByStaffIdsRequest=new GetCardByStaffIdsRequest();
        getCardByStaffIdsRequest.setBid(bid);
        getCardByStaffIdsRequest.setIds(staffIds);
        BaseResponse<List<CardDto>> baseResponse = cardService.getByStaffIds(getCardByStaffIdsRequest);
        if (!BaseResponse.responseSuccess(baseResponse)) {
            LOG.warn("获取名片数据失败！request={}，response={}",jsonService.toJson(getCardByStaffIdsRequest),jsonService.toJson(baseResponse));
            throw new InngkeErrorException("获取名片数据失败！");
        }
        if(CollectionUtils.isEmpty(baseResponse.getData())){
            return Maps.newHashMap();
        }
        return baseResponse.getData().stream().collect(Collectors.toMap(CardDto::getStaffId, Function.identity()));
    }

}

package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.bp.leads.api.AiCustomerService;
import com.inngke.bp.leads.api.dto.GetLeadsListRequest;
import com.inngke.bp.leads.db.leads.dao.LeadsExtInformationDao;
import com.inngke.bp.leads.db.leads.entity.Leads;
import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.manager.LeadsExtInformationManager;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.platform.CustomerServiceLeadsDto;
import com.inngke.bp.leads.dto.platform.CustomerServiceResponse;
import com.inngke.bp.leads.dto.platform.CustomerServiceResponseDataDto;
import com.inngke.bp.leads.dto.request.PullAiCustomerLeadsRequest;
import com.inngke.bp.leads.dto.response.tp.TpAiCustomerServiceConfigDto;
import com.inngke.bp.leads.dto.response.tp.TpConfigDto;
import com.inngke.bp.leads.enums.LeadsDataSourceEnum;
import com.inngke.bp.leads.service.AiCustomerServiceLeadsService;
import com.inngke.bp.leads.service.LeadsTpConserveService;
import com.inngke.bp.leads.service.tp.LeadsTpConfigService;
import com.inngke.bp.organize.dto.request.merchant.GetMerchantConfigRequest;
import com.inngke.bp.organize.dto.response.merchant.MerchantConfigDto;
import com.inngke.bp.organize.service.MerchantConfigService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/3 10:05
 */
@Service
public class AiCustomerServiceLeadsServiceImpl implements AiCustomerServiceLeadsService {

    private static final Logger logger = LoggerFactory.getLogger(AiCustomerServiceLeadsServiceImpl.class);

    private static final String APP_ID_KEY = "aiCustomerService.appId";

    @Autowired
    private LeadsTpConfigService tpConfigService;

    @Autowired
    private AiCustomerService aiCustomerService;

    @Autowired
    private LeadsTpConserveService leadsTpConserveService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.merchant_bp_yk:}")
    private MerchantConfigService merchantConfigService;

    /**
     * 同步Ai客服线索数据
     *
     * @param request bid
     * @return boolean
     */
    @Override
    public BaseResponse<Boolean> pullLeads(PullAiCustomerLeadsRequest request) {
        Integer bid = request.getBid();
        //获取配置
        BaseResponse<TpAiCustomerServiceConfigDto> configResponse = getConfig(request);
        if (!configResponse.getCode().equals(0)) {
            return BaseResponse.error(configResponse.getMsg());
        }
        TpAiCustomerServiceConfigDto config = configResponse.getData();
        if (!config.getUseSwitch()){
            return BaseResponse.error("未开启Ai客服同步");
        }

        //请求接口
        Integer page = 1;
        String startTime = request.getStartTime();
        if (StringUtils.isEmpty(startTime)){
            startTime = getLastUpdateTime(request.getBid());
        }
        LocalDateTime firstOpeningTime = getFirstOpeningTime(bid);
        while (true) {
            CustomerServiceResponseDataDto customerServiceResponseDataDto = null;
            try{
                CustomerServiceResponse listResponse = getList(config.getAppId(), config.getSecret(), page,startTime);
                if (!listResponse.getCode().equals(0)) {
                    logger.error("获取列表异常->code:{},msg:{}", listResponse.getCode(), listResponse.getMsg());
                    break;
                }
                customerServiceResponseDataDto = listResponse.getData();
            }catch (Exception e){
                logger.error("获取列表异常",e);
            }

            if (ObjectUtils.isEmpty(customerServiceResponseDataDto) ||
                    ObjectUtils.isEmpty(customerServiceResponseDataDto.getUsers())) {
                break;
            }
            List<CustomerServiceLeadsDto> users = customerServiceResponseDataDto.getUsers();

            List<CustomerServiceLeadsDto> finalUsers = firstOpeningTime != null ?
                    users.stream().filter(user -> DateTimeUtils.toLocalDateTime(user.getCreatedAt())
                            .isAfter(firstOpeningTime)).collect(Collectors.toList()) :
                    users;

            //保存入库
            AsyncUtils.runAsync(() -> finalUsers.forEach((user) -> leadsTpConserveService.conserve(bid, user)));
            page++;
        }

        return BaseResponse.success(true);
    }

    private CustomerServiceResponse getList(String appId, String secret, Integer page,String startTime) {
        GetLeadsListRequest getLeadsListRequest = new GetLeadsListRequest();

        LocalDateTime now = LocalDateTime.now();

        getLeadsListRequest.setAppId(appId);
        getLeadsListRequest.setStartTime(startTime);
        getLeadsListRequest.setEndTime(DateTimeUtils.format(now,DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        getLeadsListRequest.setPage(page);
        getLeadsListRequest.setPageSize(100);
        getLeadsListRequest.setTime(DateTimeUtils.getMilli(now) / 1000);
        fillSignature(getLeadsListRequest,secret);

        return aiCustomerService.getLeadsList(getLeadsListRequest);
    }

    private String getLastUpdateTime(Integer bid){
        LeadsExtInformation lastUpdate = leadsExtInformationManager.getLastUpdate(bid, LeadsDataSourceEnum.AI_CUSTOMER_SERVICE.getCode());
        LocalDateTime lastUpdateTime;
        if (ObjectUtils.isEmpty(lastUpdate)){
            lastUpdateTime = LocalDateTime.now();
        }else {
            lastUpdateTime = lastUpdate.getUpdateTime();
        }
        //拉取七天前的数据
        lastUpdateTime = lastUpdateTime.minusDays(7);

        return DateTimeUtils.format(lastUpdateTime, DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
    }

    private void fillSignature(GetLeadsListRequest request, String secret) {
        String jsonString = jsonService.toJson(request);
        Map map = jsonService.toObject(jsonString, Map.class);

        List<String> keyList = new ArrayList<>();

        for (Object key : map.keySet()) {
            keyList.add(key.toString());
        }
        Collections.sort(keyList);

        StringBuilder paramsString = new StringBuilder();

        keyList.forEach((key)-> paramsString.append(key).append(map.get(key)));

        paramsString.append(secret);

        request.setToken(DigestUtils.md5DigestAsHex(paramsString.toString().getBytes()));
    }

    private BaseResponse<TpAiCustomerServiceConfigDto> getConfig(BaseBidRequest request) {
        BaseResponse<TpAiCustomerServiceConfigDto> aiCustomerServiceConfig =
                tpConfigService.getAiCustomerServiceConfig(request);

        if (!BaseResponse.responseSuccessWithNonNullData(aiCustomerServiceConfig)) {
            logger.error("获取ai客服配置失败{}", aiCustomerServiceConfig.getMsg());
        }
        TpAiCustomerServiceConfigDto aiCustomerServiceConfigData = aiCustomerServiceConfig.getData();

        String appId = aiCustomerServiceConfigData.getAppId();
        if (StringUtils.isEmpty(appId)) {
            return BaseResponse.error("ai客服appID为空");
        }
        String secret = aiCustomerServiceConfigData.getSecret();
        if (StringUtils.isEmpty(secret)) {
            logger.error("ai客服secret为空");
            return BaseResponse.error("ai客服token为空");
        }

        return BaseResponse.success(aiCustomerServiceConfigData);
    }

    @Override
    public BaseResponse<Boolean> checkConfig(TpAiCustomerServiceConfigDto tpAiCustomerServiceConfigDto){
        String appId = tpAiCustomerServiceConfigDto.getAppId();
        String secret = tpAiCustomerServiceConfigDto.getSecret();
        CustomerServiceResponse list = getList(appId, secret, 1, DateTimeUtils.format(LocalDateTime.now().minusYears(2), DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        if (!list.getCode().equals(0)){
            return BaseResponse.error(list.getMsg());
        }

        return BaseResponse.success(true);
    }

    private LocalDateTime getFirstOpeningTime(int bid){
        GetMerchantConfigRequest request = new GetMerchantConfigRequest();
        request.setBid(bid);
        request.setCode(APP_ID_KEY);
        BaseResponse<MerchantConfigDto> response = merchantConfigService.get(request);

        if (!BaseResponse.responseSuccessWithNonNullData(response)){
            return null;
        }

        return response.getData().getCreateTime();
    }

}

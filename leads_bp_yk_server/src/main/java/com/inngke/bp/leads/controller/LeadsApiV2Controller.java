package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.annotation.LeadsIdValidator;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsInformationDto;
import com.inngke.bp.leads.dto.response.LeadsInformationVo;
import com.inngke.bp.leads.dto.response.MallOrderDto;
import com.inngke.bp.leads.dto.response.LeadsLostReasonDto;
import com.inngke.bp.leads.service.AiCustomerServiceLeadsService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 客户线索-v2
 * @date 2022/3/8 16:30
 */
@RestController
@RequestMapping("/api/leads/v2")
@Validated
public class LeadsApiV2Controller {

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @Autowired
    private AiCustomerServiceLeadsService aiCustomerServiceLeadsService;

    /**
     * 获取线索详情（后端修改）
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param id         线索ID
     * @return 线索详情
     */
    @GetMapping("/{id:\\d+}")
    public BaseResponse<LeadsInformationDto> getLeads(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long id
    ) {
        LeadsGetRequest request = new LeadsGetRequest();
        request.setId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsServiceV2.getLeads(request);
    }


    /**
     * 获取线索详情（供前端）
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param id         线索ID
     * @param request    线索详情请求
     * @return 线索详情
     */
    @GetMapping("/{id:\\d+}/detail")
    public BaseResponse<LeadsInformationVo> getLeadsDetail(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long id,
            LeadsGetRequest request
    ) {
        request.setId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);
        return leadsServiceV2.getLeadsDetail(request);
    }


    /**
     * 修改线索信息v2
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request
     * @return
     */
    @PutMapping()
    public BaseResponse<Boolean> updateLeads(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsInformationUpdateRequest request,
            @RequestAttribute JwtPayload jwtPayload) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setOperatorStaffId(jwtPayload.getSid());
        return leadsServiceV2.updateLeads(request);
    }

    /**
     * 添加线索
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request    线索信息
     * @return 保存成功后的线索信息
     */
    @PostMapping
    public BaseResponse<LeadsInformationDto> add(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody LeadsInformationAddRequest request,
            @RequestAttribute JwtPayload jwtPayload
    ) {
        request.setOperatorId(operatorId);
        request.setBid(bid);
        request.setCreateStaffId(jwtPayload.getSid());
        request.setTpLeadsId(request.getExternalId());

        return leadsServiceV2.add(request);
    }

    /**
     * 拉取ai客服线索
     *
     * @param bid
     * @param request
     * @return
     */
    @PostMapping("/pull/ai")
    public BaseResponse<Boolean> pullAiCustomer(
            @RequestHeader(InngkeApiConst.STR_BID) int bid,
            @RequestBody PullAiCustomerLeadsRequest request){
        request.setBid(bid);

        return aiCustomerServiceLeadsService.pullLeads(request);
    }



    /**
     * 小程序修改线索 订单类
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request
     * @return
     */
    @PostMapping("/edit-by-order")
    public BaseResponse<Boolean> editByOrder(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Valid LeadsEditByOrderRequest request) {
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsServiceV2.editByOrder(request);
    }

    /**
     * 小程序修改线索 信息类
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @param request
     * @return
     */
    @PostMapping("/edit-by-info")
    public BaseResponse<Boolean> editByInfo(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @RequestBody @Valid LeadsEditByInforRequest request) {
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsServiceV2.editByInfo(request);
    }

    /**
     * 根据 线索id 查询成交订单
     *
     * @param bid        商户ID
     * @param operatorId 操作者ID
     * @return
     */
    @GetMapping("/getMallOrderList/{id:\\d+}")
    public BaseResponse<List<MallOrderDto>> getMallOrderList(
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            @PathVariable long id) {
        LeadsGetRequest request = new LeadsGetRequest();
        request.setId(id);
        request.setOperatorId(operatorId);
        request.setBid(bid);

        return leadsServiceV2.getMallOrderList(request);
    }

    /**
     * 获取线索流失原因列表
     *
     * @return
     */
    @GetMapping("/getLeadsLostReasonList")
    public BaseResponse<List<LeadsLostReasonDto>> getLeadsLostReasonList() {
        return leadsServiceV2.getLeadsLostReasonList();
    }
}

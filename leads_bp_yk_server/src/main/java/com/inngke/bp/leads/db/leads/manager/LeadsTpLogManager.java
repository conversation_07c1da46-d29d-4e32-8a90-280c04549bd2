/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsTpLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface LeadsTpLogManager extends IService<LeadsTpLog> {

    void saveOrUpdateBatch(Integer bid,List<LeadsTpLog> leadsTpLogList);
}

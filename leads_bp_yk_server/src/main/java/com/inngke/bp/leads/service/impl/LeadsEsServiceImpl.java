package com.inngke.bp.leads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.core.utils.StaffToAgentUtil;
import com.inngke.bp.leads.db.leads.entity.*;
import com.inngke.bp.leads.db.leads.manager.*;
import com.inngke.bp.leads.dto.leadsMobileAndIdStoreOrderMapDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.enums.LeadsPreFollowStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.mq.listener.LeadsChangeListener;
import com.inngke.bp.leads.mq.message.leads.LeadsChangeMessage;
import com.inngke.bp.leads.service.LeadsDepartmentCacheDisposable;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.bp.organize.dto.response.StaffDepartmentAgentSimpleDto;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.organize.enums.StaffStatusEnum;
import com.inngke.bp.organize.service.DepartmentService;
import com.inngke.bp.store.dto.request.GetLeadsStoreOrderRequest;
import com.inngke.bp.store.dto.request.StoreOrderMobileQuery;
import com.inngke.bp.store.dto.response.StoreOrderDto;
import com.inngke.bp.store.service.StoreOrderService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.es.dto.IndexDocDto;
import com.inngke.common.es.service.EsDocService;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.StringUtils;
import com.inngke.ip.common.dto.request.EsIndexRebuildRequest;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.dto.response.EsDocsResponse;
import com.inngke.ip.common.service.EsService;
import com.inngke.ip.common.service.MqService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.elasticsearch.action.admin.indices.flush.FlushRequest;
import org.elasticsearch.action.admin.indices.flush.FlushResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 构建ES索引服务类
 *
 * <AUTHOR>
 */
@Service
public class LeadsEsServiceImpl implements LeadsEsService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsEsServiceImpl.class);
    private static final String LEADS_INDEX_NAME = "leads";

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsLogManager leadsLogManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsCallLogManager leadsCallLogManager;

    @Autowired
    private EsDocService esDocService;

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsHistoryDistributeManager leadsHistoryDistributeManager;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private EsService esService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.store+bp_yk:}")
    private StoreOrderService storeOrderService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private DepartmentService departmentService;

    @Autowired
    private LeadsFollowTimeManager leadsFollowTimeManager;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Autowired
    private StaffToAgentUtil staffToAgentUtil;

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;

    @Override
    public BaseResponse<EsDocsResponse<LeadsEsDto>> getBatchDoc(LeadsEsBatchRequest request) {
        //构建返回体
        EsDocsResponse<LeadsEsDto> response = new EsDocsResponse<>();

        //构建查询线索的条件，默认从入参的lastLeadsId之后取一千条
        QueryWrapper<Leads> wrapper = Wrappers.<Leads>query()
                .orderByAsc(Leads.ID)
                .last("LIMIT " + request.getPageSize());

        Long lastLeadsId = request.getLastLeadsId();
        if (lastLeadsId != null && lastLeadsId > 0) {
            wrapper.gt(Leads.ID, lastLeadsId);
        } else if (request.getLeadsId() != null && request.getLeadsId() > 0L) {
            wrapper.eq(Leads.ID, request.getLeadsId());
        }

        List<Leads> leadList = leadsManager.list(wrapper);

        Map<Integer, List<Leads>> leadsBidMap = leadList.stream().collect(Collectors.groupingBy(Leads::getBid));

        Map<Long, List<StoreOrderDto>> orderResponse = Maps.newHashMap();
        leadsBidMap.forEach((bid, leads) -> {
            GetLeadsStoreOrderRequest orderRequest = new GetLeadsStoreOrderRequest();
            orderRequest.setBid(bid);
            orderRequest.setLeadsIds(leadList.stream().map(Leads::getId).collect(Collectors.toSet()));
            Map<Long, List<StoreOrderDto>> data = storeOrderService.getStoreOrderListByLeads(orderRequest).getData();
            if (Objects.nonNull(data)) {
                orderResponse.putAll(data);
            }
        });


        if (CollectionUtils.isEmpty(leadList)) {
            return BaseResponse.success(response);
        }
        List<Long> leadsIds = leadList.stream().map(Leads::getId).collect(Collectors.toList());
        List<Long> distributeStaffIdList = leadList.stream().map(Leads::getDistributeStaffId).collect(Collectors.toList());
        distributeStaffIdList.addAll(leadList.stream().map(Leads::getPushBackStaffId).collect(Collectors.toList()));
        List<LeadsEsDto> list = leadList.stream().map(LeadsConverter::toLeadsEsDto).collect(Collectors.toList());
        list.forEach(item -> {
            if (Objects.nonNull(orderResponse) && !CollectionUtils.isEmpty(orderResponse.get(item.getLeadsId()))) {
                List<StoreOrderDto> storeOrderDtos = orderResponse.get(item.getLeadsId());
                List<StoreOrderDto> orderList = storeOrderDtos.stream().filter(order -> order.getType() == 2).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(orderList)) {
                    item.setHasStoreOrder(1);
                }
            } else {
                item.setHasStoreOrder(0);
            }
        });
        //查询出所有的线索历史状态，后根据leadsEsDto的id去匹配
        List<LeadsFollow> allLeadsStatusLog = leadsFollowManager.list(
                Wrappers.<LeadsFollow>query()
                        .in(LeadsFollow.LEADS_ID,leadsIds)
        );

        Map<Long, List<LeadsFollow>> followMap = allLeadsStatusLog.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));
        Map<Long, List<Long>> historyDistributeStaffIdListMap = leadsHistoryDistributeManager.list(
                Wrappers.<LeadsHistoryDistribute>query()
                        .in(LeadsHistoryDistribute.LEADS_ID, leadsIds)
                ).stream().collect(Collectors.groupingBy(LeadsHistoryDistribute::getLeadsId,
                        Collectors.mapping(LeadsHistoryDistribute::getDistributeStaffId, Collectors.toList())));

        for (LeadsEsDto leadsEsDto : list) {
            List<LeadsFollow> leadsFollows = followMap.getOrDefault(leadsEsDto.getLeadsId(), new ArrayList<>());
            //Set<Integer> staffLogByLeadsId = allLeadsStatusLog.stream().filter(leadsFollow -> leadsFollow.getLeadsId().equals(leadsEsDto.getLeadsId())).map(LeadsFollow::getLeadsStatus).collect(Collectors.toSet());
            Integer preFollowLeadsUp = preFollowLeadsUp(leadsEsDto);
            Set<Integer> staffLogByLeadsId = leadsFollows.stream().map(LeadsFollow::getLeadsStatus).collect(Collectors.toSet());
            leadsEsDto.setStatusLog(staffLogByLeadsId);
            leadsEsDto.setPreFollowLeadsUp(preFollowLeadsUp);
            leadsEsDto.setHasMeasure(staffLogByLeadsId.contains(LeadsStatusEnum.MEASURED.getStatus()) ? 1 : 0);
        }

        //为空则直接返回空数据
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success(response);
        }
        Map<Integer, List<LeadsEsDto>> bidLeadsMap = Maps.newHashMap();
        list.forEach(leadsEsDto -> {
            if (!bidLeadsMap.containsKey(leadsEsDto.getBid())) {
                bidLeadsMap.put(leadsEsDto.getBid(), Lists.newArrayList());
            }
            bidLeadsMap.get(leadsEsDto.getBid()).add(leadsEsDto);
        });
        Map<Long, Long> bigStaffAndAgentMap = Maps.newHashMap();
        LeadsDepartmentCacheDisposable departmentCache = new LeadsDepartmentCacheDisposable(departmentService);
        for (Integer bid : bidLeadsMap.keySet()) {
            buildLeadsEsDocs(bidLeadsMap.get(bid), bid,departmentCache,followMap,historyDistributeStaffIdListMap);
            Map<Long, Long> staffAndAgentMap = staffToAgentUtil.getStaffAndAgentByStaffIds(distributeStaffIdList, bid);
            bigStaffAndAgentMap.putAll(staffAndAgentMap);
        }

        //获取线索扩展信息
        Map<Long, LeadsExtInformation> leadsExtMap = getLeadsExtMap(bidLeadsMap);


        list = list.stream().map(item -> LeadsConverter.toLeadsEsDto(item, bigStaffAndAgentMap,leadsExtMap)).collect(Collectors.toList());
        Long maxId = list.stream().map(LeadsEsDto::getLeadsId).max(Long::compare).orElse(0L);
        response.setList(list);
        response.setMaxId(maxId);

        return BaseResponse.success(response);
    }

    private Map<Long, LeadsExtInformation> getLeadsExtMap(Map<Integer, List<LeadsEsDto>> bidLeadsMap) {
        Map<Long, LeadsExtInformation> campaignMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(bidLeadsMap)) {
            return campaignMap;
        }
        bidLeadsMap.forEach(
                (bid, bidlist) -> {
                    Set<String> leadsIds = bidlist.stream().map(LeadsEsDto::getId).collect(Collectors.toSet());
                    Map<Long, LeadsExtInformation> bidcampiganMap = leadsExtInformationManager.list(
                            Wrappers.<LeadsExtInformation>query()
                                    .eq(LeadsExtInformation.BID, bid)
                                    .in(LeadsExtInformation.ID, leadsIds)
                                    .select(LeadsExtInformation.ID, LeadsExtInformation.CAMPAIGN_NAME)
                    ).stream().collect(Collectors.toMap(LeadsExtInformation::getId, Function.identity()));
                    campaignMap.putAll(bidcampiganMap);
                }
        );
        return campaignMap;
    }

    /**
     * 判断客服是否添加了跟进记录，1添加了跟进记录
     */
    private Integer preFollowLeadsUp(Long preFollowStaffId, List<LeadsFollow> leadsFollows) {
        if (preFollowStaffId == null || preFollowStaffId.equals(0L)) {
            return 0;
        }
        for (LeadsFollow leadsFollow : leadsFollows) {
            if (leadsFollow.getStaffId() != null && leadsFollow.getStaffId().equals(preFollowStaffId)) {
                return 1;
            }
        }
        return 0;
    }

    private Integer preFollowLeadsUp(LeadsEsDto leadsEsDto) {
        Integer status = Optional.ofNullable(leadsEsDto.getStatus()).orElse(0);
        Integer preFollowStatus = Optional.ofNullable(leadsEsDto.getPreFollowStatus()).orElse(0);
        if (status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus()) && !preFollowStatus.equals(LeadsPreFollowStatusEnum.DISTRIBUTED.getStatus())) {
            return 1;
        }
        if (!status.equals(LeadsStatusEnum.RECOVERY.getStatus()) &&
                !status.equals(LeadsStatusEnum.DELETED.getStatus()) &&
                !status.equals(LeadsStatusEnum.TO_DISTRIBUTE.getStatus()) &&
                !status.equals(LeadsStatusEnum.PRE_FOLLOW.getStatus())) {
            return 1;
        }
        return 0;
    }

    @Override
    public BaseResponse<Boolean> updateDocs(LeadsUpdateRequest request) {
        Integer bid = request.getBid();
        List<Long> ids = request.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            logger.warn("ids is empty");
            return BaseResponse.error("ids is empty");
        }
        Boolean notifyPartner = request.getNotifyPartner();
        String leadsName = request.getLeadsName();
        //发送mq
        if(request.getNeedSendMQ()){
            AsyncUtils.runAsync(() -> sendLeadsChangeMq(bid, ids, notifyPartner, leadsName, 2));
        }
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .in(Leads.ID, ids)
                        .eq(Leads.BID, request.getBid())
        );

        List<Leads> tempList = leadsList.stream().map(leads -> {
            Leads l = new Leads();
            l.setId(leads.getId());
            l.setStatus(leads.getStatus());
            return l;
        }).collect(Collectors.toList());
        logger.info("更新单个Doc，ids={}, data={}", ids, jsonService.toJson((Serializable) tempList));

        if (CollectionUtils.isEmpty(leadsList)) {
            throw new InngkeServiceException("找不到要更新的线索【" + request.getIds() + "】");
        }

        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(leadsList, bid);
        List<LeadsEsDto> esDtoList = leadsList.stream().map(leads -> LeadsConverter.toLeadsEsDto(leads, staffAndAgent)).collect(Collectors.toList());

        esDtoList = this.buildLeadsEsDocs(esDtoList, bid);

        // 打印日志
        printEsStatus(esDtoList);

        if (!Boolean.TRUE.equals(request.getRefreshEs())) {
            for (LeadsEsDto leadsEsDto : esDtoList) {
                esDocService.upsertDoc(LEADS_INDEX_NAME, leadsEsDto.getBid() + InngkeAppConst.UNDERLINE_STR + leadsEsDto.getId(), jsonService.toJson(leadsEsDto));
            }
            logger.info("线索更新Es完成1：{}", ids);
        } else {
            ArrayList<IndexDocDto> dataList = new ArrayList<>();
            for (LeadsEsDto leadsEsDto : esDtoList) {
                IndexDocDto data = new IndexDocDto();
                data.setDocId(leadsEsDto.getBid() + InngkeAppConst.UNDERLINE_STR + leadsEsDto.getId());
                data.setDocJson(jsonService.toJson(leadsEsDto));
                dataList.add(data);
            }
            esDocService.updateDocs(LEADS_INDEX_NAME, dataList);
            logger.info("线索更新Es完成2：{}", ids);
        }

        FlushRequest flushRequest = new FlushRequest(LEADS_INDEX_NAME);
        try {
            client.indices().flush(flushRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("ES刷新失败：", e);
        }
        return BaseResponse.success(true);
    }

    private void printEsStatus(List<LeadsEsDto> esDtoList) {
        List<LeadsEsDto> tempList = esDtoList.stream().map(leads -> {
            LeadsEsDto l = new LeadsEsDto();
            l.setId(leads.getId());
            l.setStatus(leads.getStatus());
            return l;
        }).collect(Collectors.toList());
        logger.info("Es最终更新单个Doc，data={}", jsonService.toJson((Serializable) tempList));
    }

    /**
     * 发送线索修改mq
     */
    @Override
    public void sendLeadsChangeMq(Integer bid, List<Long> leadsIds, Boolean notifyPartner, String leadsName, Integer event) {
        if (CollectionUtils.isEmpty(leadsIds)) {
            return;
        }
        LeadsChangeMessage leadsChangeMessage = new LeadsChangeMessage();
        leadsChangeMessage.setLeadsId(leadsIds);
        leadsChangeMessage.setBid(bid);
        leadsChangeMessage.setOperatorId(0L);
        leadsChangeMessage.setNotifyPartner(notifyPartner);
        leadsChangeMessage.setLeadsName(leadsName);
        leadsChangeMessage.setEvent(event);

        MqSendRequest sendRequest = new MqSendRequest();
        sendRequest.setBid(bid);
        sendRequest.setTopic(LeadsChangeListener.TOPIC_LEADS_CHANGE);
        sendRequest.setPayload(jsonService.toJson(leadsChangeMessage));
        BaseResponse<Boolean> resp = mqService.send(sendRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(resp)) {
            logger.error("线索变更MQ发送失败：{}", resp == null ? "--" : resp.getMsg());
        }
    }

    @Override
    public BaseResponse<Boolean> createLeadsDocs(LeadsAddRequest request) {
        List<Long> ids = request.getIds();
        Integer bid = request.getBid();
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .in(Leads.ID, ids)
                        .eq(Leads.BID, bid)
        );
        if (CollectionUtils.isEmpty(leadsList)) {
            throw new InngkeServiceException("找不到要更新的线索【" + request.getIds() + "】");
        }

        List<Leads> tempList = leadsList.stream().map(leads -> {
            Leads l = new Leads();
            l.setId(leads.getId());
            l.setStatus(leads.getStatus());
            return l;
        }).collect(Collectors.toList());
        logger.info("更新单个Doc，ids={}, data={}", ids, jsonService.toJson((Serializable) tempList));

        Map<Long, Long> staffAndAgent = staffToAgentUtil.getStaffAndAgent(leadsList, bid);
        List<LeadsEsDto> esDtoList = leadsList.stream().map(leadsEsDto -> LeadsConverter.toLeadsEsDto(leadsEsDto, staffAndAgent)).collect(Collectors.toList());
        esDtoList = this.buildLeadsEsDocs(esDtoList, bid);

        List<IndexDocDto> docDtos = new ArrayList<>();
        for (LeadsEsDto leadsEsDto : esDtoList) {
            IndexDocDto indexDocDto = new IndexDocDto();
            indexDocDto.setDocId(leadsEsDto.getBid() + InngkeAppConst.UNDERLINE_STR + leadsEsDto.getId());
            indexDocDto.setDocJson(jsonService.toJson(leadsEsDto));

            docDtos.add(indexDocDto);
        }
        //先获取索引，如果还没有索引，则先构建索引，如果索引存在，则添加doc
        GetIndexRequest indexRequest = new GetIndexRequest(LEADS_INDEX_NAME);
        try {
            GetIndexResponse indexResponse = client.indices().get(indexRequest, RequestOptions.DEFAULT);
            if (null != indexResponse.getIndices() && indexResponse.getIndices().length > 0) {
                esDocService.addDocs(LEADS_INDEX_NAME, docDtos);
            } else {
                EsIndexRebuildRequest rebuildRequest = new EsIndexRebuildRequest();
                rebuildRequest.setIndexName(LEADS_INDEX_NAME);
                esService.rebuild(rebuildRequest);
            }
            FlushRequest flushRequest = new FlushRequest(LEADS_INDEX_NAME);
            FlushResponse flush = client.indices().flush(flushRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("更新线索ES发生错误");
        }
        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<List<LeadsEsDto>> searchLeads(LeadsSearchRequest request) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        Long id = request.getId();
        Integer bid = request.getBid();
        Long distributeAgentId = request.getDistributeAgentId();
        String name = request.getName();

        queryBuilder.must(QueryBuilders.termQuery("bid", bid));
        if (Objects.nonNull(id)) {
            queryBuilder.must(QueryBuilders.termQuery("id", id));
        }
        if (Objects.nonNull(distributeAgentId)) {
            queryBuilder.must(QueryBuilders.termQuery("distributeAgentId", distributeAgentId));
        }
        if (!StringUtils.isEmpty(name)) {
            queryBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + name + "*").caseInsensitive(true));
        }
        if (!CollectionUtils.isEmpty(request.getStaffId())) {
            queryBuilder.must(QueryBuilders.termsQuery("staffId", request.getStaffId()));
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder);

        SearchRequest searchRequest = new SearchRequest()
                .indices("leads")
                .source(searchSourceBuilder);
        logger.info("searchLeads searchSourceBuilder {}", searchSourceBuilder);
        SearchResponse resp = esDocService.search(searchRequest);

        if (resp == null || resp.getHits() == null) {
            throw new InngkeServiceException("获取线索数据异常，query=" + searchSourceBuilder + "，response=" + resp);
        }

        List<LeadsEsDto> leadsEsList = Lists.newArrayList();
        resp.getHits().forEach(
                hit -> {
                    LeadsEsDto leadsEsDto = jsonService.toObject(hit.getSourceAsString(), LeadsEsDto.class);
                    leadsEsList.add(leadsEsDto);
                }
        );
        return BaseResponse.success(leadsEsList);
    }

    @Override
    public BaseResponse updateByStaffId(LeadsUpdateByStaffIdRequest request) {
        List<Leads> leadsList = leadsManager.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, request.getBid())
                        .eq(Leads.DISTRIBUTE_STAFF_ID, request.getStaffId())
                        .select(Leads.ID, Leads.DISTRIBUTE_STAFF_ID, Leads.BID, Leads.STATUS)
        );
        if (CollectionUtils.isEmpty(leadsList)) {
            return BaseResponse.success();
        }
        List<Long> leadsIds = leadsList.stream().map(Leads::getId).collect(Collectors.toList());
        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setBid(request.getBid());
        leadsUpdateRequest.setIds(leadsIds);
        leadsUpdateRequest.setNeedSendMQ(false);
        updateDocs(leadsUpdateRequest);
        return BaseResponse.success();
    }

    private List<DepartmentDto> getStaffParentDepartment(Long departmentId, Integer bid, LeadsDepartmentCacheDisposable departmentCache) {
        if (Objects.nonNull(departmentCache)){
            return departmentCache.getParentDepartment(bid, departmentId);
        }
        GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
        getDepartmentRequest.setBid(bid == 0 ? 1 : bid);
        getDepartmentRequest.setDepartmentId(departmentId);
        return departmentClientForLeads.getDepartmentWithParents(getDepartmentRequest);
    }

    /**
     * 获取Leads -> staffIds缓存
     *
     * @param ids
     * @return
     */
    private Map<Long, List<Long>> getStaffIdsLogCache(List<Long> ids) {
        //先把当前leadsId和所有经手员工的id存储成key->value结构，之后在循环里去匹配
        List<LeadsLog> leadsLogs = leadsLogManager.list(
                Wrappers.<LeadsLog>query()
                        .in(LeadsLog.LEADS_ID, ids)
                        .ne(LeadsLog.DISTRIBUTE_STAFF_ID, 0)
                        .select(LeadsLog.LEADS_ID, LeadsLog.DISTRIBUTE_STAFF_ID)
        );
        //缓存的leadsId -> staffIds
        Map<Long, List<Long>> leadsLogMap = new HashMap<>(ids.size());
        for (LeadsLog leadsLog : leadsLogs) {
            if (leadsLogMap.containsKey(leadsLog.getLeadsId())) {
                List<Long> staffLogIds = leadsLogMap.get(leadsLog.getLeadsId());
                staffLogIds.add(leadsLog.getDistributeStaffId());
                leadsLogMap.put(leadsLog.getLeadsId(), staffLogIds);
            } else {
                leadsLogMap.put(leadsLog.getLeadsId(), Lists.newArrayList(leadsLog.getDistributeStaffId()));
            }
        }
        return leadsLogMap;
    }

    private List<LeadsEsDto> buildLeadsEsDocs(List<LeadsEsDto> list, Integer bid) {
        return buildLeadsEsDocs(list,bid,null,null,null);
    }

    private List<LeadsEsDto> buildLeadsEsDocs(
            List<LeadsEsDto> list, Integer bid,
            LeadsDepartmentCacheDisposable departmentCache,
            Map<Long, List<LeadsFollow>> followMap,
            Map<Long, List<Long>> historyDistributeStaffIdListMap) {
        // leadsId
        Set<Long> ids = list.stream().map(LeadsEsDto::getLeadsId).collect(Collectors.toSet());
        // leadsMobileList
        Set<String> leadsMobileSet = list.stream().map(LeadsEsDto::getMobile).collect(Collectors.toSet());
        // 构建leadsFollowTimeMap: leadsId -> leadsFollowTimePOJO
        Map<Long, LeadsFollowTime> leadsFollowTimeMap = buildLeadsFollowTimeMap(bid, ids);
        // 构建首次联系时间Map: leadsId -> firstCallTime
        Map<Long, LocalDateTime> firstCallTimeMap = buildFirstCallTimeMap(ids, leadsFollowTimeMap);

        // 构建门店订单Map: leadsMobile -> StoreOrder
        leadsMobileAndIdStoreOrderMapDto leadsMobileAndIdStoreOrderMapDto = buildStoreOrderMap(bid, leadsMobileSet, ids);

        if (CollectionUtils.isEmpty(historyDistributeStaffIdListMap)){
            historyDistributeStaffIdListMap = leadsHistoryDistributeManager.list(
                    Wrappers.<LeadsHistoryDistribute>query()
                            .eq(LeadsHistoryDistribute.BID, bid)
                            .in(LeadsHistoryDistribute.LEADS_ID, ids)
            ).stream().collect(Collectors
                    .groupingBy(LeadsHistoryDistribute::getLeadsId, Collectors.mapping(LeadsHistoryDistribute::getDistributeStaffId, Collectors.toList())));
        }
        //staffIds
        Set<Long> staffIds = list.stream().map(leadsEsDto -> Sets.newHashSet(
                leadsEsDto.getDistributeStaffId(),
                leadsEsDto.getCreateStaffId(),
                leadsEsDto.getPushBackStaffId(),
                leadsEsDto.getPreFollowStaffId())).flatMap(Collection::stream).collect(Collectors.toSet());
        Map<Long, StaffDto> staffDtoMap = staffClientForLeads.getStaffByIds(bid, staffIds);

        //获取leadsId -> staffIds缓存
        Map<Long, List<Long>> leadsLogMap = getStaffIdsLogCache(Lists.newArrayList(ids));
        StaffDto emptyStaff = new StaffDto();
        //查询经销商Map
        Map<Long, StaffDepartmentAgentSimpleDto> agentNameStaffIdMap = getAgentNameByStaffIds(bid, staffIds);

        // 线索跟进记录
        if (Objects.isNull(followMap) || CollectionUtils.isEmpty(followMap)){
            List<LeadsFollow> allLeadsStatusLog = leadsFollowManager.list(
                    Wrappers.<LeadsFollow>query()
                            .eq(LeadsFollow.BID, bid)
                            .in(LeadsFollow.LEADS_ID, ids)
            );
            followMap = allLeadsStatusLog.stream().collect(Collectors.groupingBy(LeadsFollow::getLeadsId));
        }

        //获取扩展信息
        Map<Integer,List<LeadsEsDto>> extMap = Maps.newHashMap();
        extMap.put(bid,list);
        Map<Long, LeadsExtInformation> leadsExtMap = getLeadsExtMap(extMap);
        for (LeadsEsDto leadsEsDto : list) {
            //设置扩展信息
            if (!CollectionUtils.isEmpty(leadsExtMap)){
                LeadsExtInformation leadsExtInformation = leadsExtMap.get(leadsEsDto.getLeadsId());
                leadsEsDto.setCampaignName(Objects.nonNull(leadsExtInformation) ? leadsExtInformation.getCampaignName():null);
            }
            // 线索历史状态
            List<LeadsFollow> leadsFollows = followMap.getOrDefault(leadsEsDto.getLeadsId(), new ArrayList<>());
            Set<Integer> statufLogByLeadsId = leadsFollows.stream().map(LeadsFollow::getLeadsStatus).collect(Collectors.toSet());
            List<LeadsFollow> leadsFollowList = leadsFollows.stream().filter(i -> !Long.valueOf(0).equals(i.getReasonId())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(leadsFollowList)) {
                LeadsFollow leadsFollow = leadsFollowList.stream().sorted(Comparator.comparing(LeadsFollow::getCreateTime).reversed()).collect(Collectors.toList()).get(0);
                leadsEsDto.setLastReasonId(leadsFollow.getReasonId());
            }
            leadsEsDto.setStatusLog(statufLogByLeadsId);
            // 客服是否添加跟进记录
            Integer preFollowLeadsUp = preFollowLeadsUp(leadsEsDto);
            leadsEsDto.setPreFollowLeadsUp(preFollowLeadsUp);

            //创建线索的部门
            StaffDto createStaff = Optional.ofNullable(staffDtoMap.get(leadsEsDto.getCreateStaffId())).orElse(emptyStaff);
            Long createDepartmentId = Optional.ofNullable(createStaff.getDepartmentId()).orElse(0L);
            leadsEsDto.setCreateDepartmentId(createDepartmentId.equals(0L) ? Sets.newHashSet(0L) :
                    getStaffParentDepartment(createDepartmentId, leadsEsDto.getBid(),departmentCache)
                            .stream().map(DepartmentDto::getId).collect(Collectors.toSet()));

            //退回线索的部门
            StaffDto pushBackStaff = Optional.ofNullable(staffDtoMap.get(leadsEsDto.getPushBackStaffId())).orElse(emptyStaff);
            Long pushbackDepartmentId = Optional.ofNullable(pushBackStaff.getDepartmentId()).orElse(0L);
            leadsEsDto.setPushbackDepartmentId(pushbackDepartmentId.equals(0L) ? Sets.newHashSet(0L) :
                    getStaffParentDepartment(pushbackDepartmentId, leadsEsDto.getBid(), departmentCache)
                            .stream().map(DepartmentDto::getId).collect(Collectors.toSet()));

            //客服部门
            StaffDto preFollowStaff = Optional.ofNullable(staffDtoMap.get(leadsEsDto.getPreFollowStaffId())).orElse(emptyStaff);
            Long preFollowDepartmentId = Optional.ofNullable(preFollowStaff.getDepartmentId()).orElse(0L);
            leadsEsDto.setPreFollowDepartmentIds(preFollowDepartmentId.equals(0L) ? Sets.newHashSet(0L) :
                    getStaffParentDepartment(preFollowDepartmentId, leadsEsDto.getBid(), departmentCache)
                            .stream().map(DepartmentDto::getId).collect(Collectors.toSet()));

            //查询员工姓名
            StaffDto distributeStaff = staffDtoMap.get(leadsEsDto.getDistributeStaffId());
            if (ObjectUtils.isEmpty(distributeStaff)) {
                leadsEsDto.setDistributeStaffName("未分配员工");
                leadsEsDto.setStaffIsDel(0);
                continue;
            }

            // 构建员工相关信息
            leadsEsDto.setDistributeStaffName(distributeStaff.getName());
            leadsEsDto.setPosition(StringUtils.isNotBlank(distributeStaff.getPosition()) ? distributeStaff.getPosition() : "无职位");
            leadsEsDto.setStaffIsDel(distributeStaff.getStatus().equals(StaffStatusEnum.DELETE.getCode()) ? 0 : 1);
            //查询员工所在的所有上级部门id
            List<Long> parentDepartmentIds = getStaffParentDepartment(distributeStaff.getDepartmentId(), leadsEsDto.getBid(), departmentCache).stream().map(DepartmentDto::getId).collect(Collectors.toList());
            leadsEsDto.setDeptIds(parentDepartmentIds);
            //查询经销商
            if (Objects.nonNull(agentNameStaffIdMap.get(distributeStaff.getId()))) {
                leadsEsDto.setAgentName(agentNameStaffIdMap.get(distributeStaff.getId()).getAgentName());
                leadsEsDto.setDistributeAgentId(agentNameStaffIdMap.get(distributeStaff.getId()).getAgentId());
                leadsEsDto.setDistributeAgentName(agentNameStaffIdMap.get(distributeStaff.getId()).getAgentName());
            }
            //组装员工所在部门集合信息，不包括上级部门
            Long departmentId = distributeStaff.getDepartmentId();
            leadsEsDto.setBelongingDepartmentIds(Collections.singletonList(departmentId));

            if (!Objects.isNull(departmentId)) {
                DepartmentDto department1;
                if (Objects.nonNull(departmentCache)){
                    department1 = departmentCache.getDepartment(bid, departmentId);
                }else {
                    GetDepartmentRequest getDepartmentRequest = new GetDepartmentRequest();
                    getDepartmentRequest.setBid(bid);
                    getDepartmentRequest.setDepartmentId(departmentId);
                    getDepartmentRequest.setChildrenLevel(0);
                    department1 = departmentClientForLeads.getDepartment(getDepartmentRequest);
                }
                if (Objects.nonNull(department1)) {
                    leadsEsDto.setDepartmentId(departmentId);
                    leadsEsDto.setDepartmentName(department1.getName());
                    leadsEsDto.setParentId(department1.getParentId());
                } else {
                    leadsEsDto.setDepartmentId(-1L);
                    leadsEsDto.setDepartmentName("无部门");
                    leadsEsDto.setParentId(-1L);
                }
            } else {
                leadsEsDto.setDepartmentId(-1L);
                leadsEsDto.setDepartmentName("无部门");
                leadsEsDto.setParentId(-1L);
            }
            // 设置联系时间间隔
            leadsEsDto = buildLeadsEsFirstCallIntervalTimeProperty(firstCallTimeMap, leadsEsDto);

            //设置门店订单金额及定金
            leadsEsDto = buildLeadsEsStoreOrderProperty(leadsMobileAndIdStoreOrderMapDto, leadsEsDto);

            //查询所经手的员工Id
            leadsEsDto.setStaffIds(leadsLogMap.get(leadsEsDto.getId()));


            List<Long> historyDistributeStaffIdList = historyDistributeStaffIdListMap.get(leadsEsDto.getLeadsId());
            if (Objects.nonNull(historyDistributeStaffIdList)) {
                leadsEsDto.setHistoryDistributeStaffIds(historyDistributeStaffIdList);
            }

            //查询leadsFollowTime
            LeadsFollowTime leadsFollowTime = leadsFollowTimeMap.get(leadsEsDto.getLeadsId());
            if (Objects.nonNull(leadsFollowTime)) {
                leadsFollowTimeToLeadsEsDto(leadsEsDto, leadsFollowTime, distributeStaff);
            }
        }
        return list;
    }

    private Map<Long, StaffDepartmentAgentSimpleDto> getAgentNameByStaffIds(Integer bid, Set<Long> staffIds) {
        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setBid(bid);
        baseIdsRequest.setIds(Lists.newArrayList(staffIds));
        List<StaffDepartmentAgentSimpleDto> staffDepartmentAgentSimpleList = staffClientForLeads.getStaffDepartmentAgentSimple(baseIdsRequest);

        return staffDepartmentAgentSimpleList.stream()
                .filter(i -> Objects.nonNull(i.getAgentName()))
                .collect(Collectors.toMap(StaffDepartmentAgentSimpleDto::getId, Function.identity(), (i1, i2) -> i1));
    }

    /**
     * 构建leadsId -> LeadsFollowTime POJO map
     *
     * @param bid
     * @param leadsIds
     * @return
     */
    private Map<Long, LeadsFollowTime> buildLeadsFollowTimeMap(Integer bid, Set<Long> leadsIds) {
        Map<Long, LeadsFollowTime> leadsFollowTimeMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(leadsIds)) {
            return leadsFollowTimeMap;
        }

        List<LeadsFollowTime> leadsFollowTimes = leadsFollowTimeManager.list(
                Wrappers.<LeadsFollowTime>query()
                        .eq(LeadsFollowTime.BID, bid)
                        .in(LeadsFollowTime.ID, leadsIds)
        );
        if (!CollectionUtils.isEmpty(leadsFollowTimes)) {
            leadsFollowTimeMap = leadsFollowTimes.stream().collect(Collectors.toMap(
                    LeadsFollowTime::getId, item -> item
            ));
        }
        return leadsFollowTimeMap;
    }

    /**
     * 构建门店订单Map
     *
     * @return
     */
    private leadsMobileAndIdStoreOrderMapDto buildStoreOrderMap(Integer bid, Set<String> leadsMobileSet, Set<Long> leadsIds) {
        leadsMobileAndIdStoreOrderMapDto result = new leadsMobileAndIdStoreOrderMapDto();

        List<StoreOrderDto> storeOrderList = getStoreOrder(bid, leadsMobileSet, leadsIds);

        Map<String, List<StoreOrderDto>> storeOrderMap = new HashMap<>(storeOrderList.size());
        Map<Long, List<StoreOrderDto>> leadsIdOrderMap = new HashMap<>(storeOrderList.size());

        storeOrderList.forEach(store -> {
            // 获取根据手机号获取门店订单
            if (store.getLeadsId() != null && !store.getLeadsId().equals(0L)) {
                leadsIdOrderMap.computeIfAbsent(store.getLeadsId(), item -> new ArrayList<>()).add(store);
            } else {
                storeOrderMap.computeIfAbsent(store.getCustomerMobile(), item -> new ArrayList<>()).add(store);
            }
        });

        result.setMobileStoreOrder(storeOrderMap);
        result.setLeadsIdStoreOrder(leadsIdOrderMap);

        return result;
    }

    private List<StoreOrderDto> getStoreOrder(Integer bid, Set<String> leadsMobileSet, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsMobileSet) && CollectionUtils.isEmpty(leadsIds)) {
            return new ArrayList<>(0);
        }
        // 获取根据手机号获取门店订单
        StoreOrderMobileQuery storeOrderMobileQuery = new StoreOrderMobileQuery();
        storeOrderMobileQuery.setBid(bid);
        storeOrderMobileQuery.setMobiles(leadsMobileSet);
        storeOrderMobileQuery.setLeadsIds(leadsIds);
        BaseResponse<List<StoreOrderDto>> storeOrderListByMobile = storeOrderService.getStoreOrderListByMobile(storeOrderMobileQuery);
        if (BaseResponse.responseSuccessWithNonNullData(storeOrderListByMobile)) {
            return storeOrderListByMobile.getData();
        }
        return new ArrayList<>(0);
    }

    /**
     * 构建LeadsEsDto门店订单相关的属性
     *
     * @param leadsEsDto
     * @return
     */
    public LeadsEsDto buildLeadsEsStoreOrderProperty(leadsMobileAndIdStoreOrderMapDto leadsMobileAndIdStoreOrderMapDto, LeadsEsDto leadsEsDto) {
        Map<String, List<StoreOrderDto>> storeOrderMap = leadsMobileAndIdStoreOrderMapDto.getMobileStoreOrder();
        Map<Long, List<StoreOrderDto>> leadsIdStoreOrder = leadsMobileAndIdStoreOrderMapDto.getLeadsIdStoreOrder();
        List<StoreOrderDto> storeOrderList = new ArrayList<>();
        // 获取手机号对应的门店订单列表
        if (!CollectionUtils.isEmpty(storeOrderMap.get(leadsEsDto.getMobile()))) {
            List<StoreOrderDto> storeOrders = storeOrderMap.get(leadsEsDto.getMobile());
            // 2022-10-21 09:50:00后不通过手机号码关联线索，通过LeadsId进行关联
            storeOrderList.addAll(storeOrders.stream().filter(storeOrderDto -> storeOrderDto.getCreateTime().compareTo(1666317000000L) <= 0).collect(Collectors.toList()));
        }
        // 获取线索Id对应的门店订单列表
        if (!CollectionUtils.isEmpty(leadsIdStoreOrder.get(leadsEsDto.getLeadsId()))) {
            storeOrderList.addAll(leadsIdStoreOrder.get(leadsEsDto.getLeadsId()));
        }
        if (!CollectionUtils.isEmpty(storeOrderList)) {
            // 初始化各属性
            // 定金金额
            BigDecimal depositAmount = BigDecimal.valueOf(0);
            // 全款金额
            BigDecimal orderAmount = BigDecimal.valueOf(0);
            // 定金订单数量
            Integer depositCount = 0;
            // 全款订单数量
            Integer fullPayCount = 0;

            for (StoreOrderDto storeOrderDto : storeOrderList) {
                // 订单创建时间在线索分配时间之后，累加金额
                if (storeOrderDto.getCreateTime() >= leadsEsDto.getDistributeTime()) {
                    //累加定金
                    BigDecimal orderDeposit = StringUtils.isNotBlank(storeOrderDto.getOrderDeposit())
                            ? new BigDecimal(storeOrderDto.getOrderDeposit()) : BigDecimal.valueOf(0);
                    depositAmount = depositAmount.add(orderDeposit);
                    BigDecimal amount = StringUtils.isNotBlank(storeOrderDto.getOrderAmount())
                            ? new BigDecimal(storeOrderDto.getOrderAmount()) : BigDecimal.valueOf(0);
                    orderAmount = orderAmount.add(amount);
                    // 累加定金订单的数量和全款订单的数量
                    if (storeOrderDto.getType() == 1) {
                        depositCount += 1;
                    } else if (storeOrderDto.getType() == 2) {
                        fullPayCount += 1;
                    }
                }

            }
            leadsEsDto.setDepositAmount(depositAmount);
            leadsEsDto.setOrderAmount(orderAmount);
            leadsEsDto.setDepositOrderCount(depositCount);
            leadsEsDto.setFullPayOrderCount(fullPayCount);
        }
        return leadsEsDto;
    }

    /**
     * 构建线索分配后首次联系时间Map
     *
     * @param leadsIds
     * @return
     */
    private Map<Long, LocalDateTime> buildFirstCallTimeMap(Set<Long> leadsIds, Map<Long, LeadsFollowTime> leadsFollowTimeMap) {
        Map<Long, LocalDateTime> firstCallTimeMap = Maps.newHashMap();
        leadsIds.forEach(item -> {
            LeadsFollowTime leadsFollowTime = leadsFollowTimeMap.get(item);
            if (!Objects.isNull(leadsFollowTime) && leadsFollowTime.getStateContact() != 0L) {
                firstCallTimeMap.put(item, DateTimeUtils.MillisToLocalDateTime(leadsFollowTime.getStateContact()));
            }
        });
        return firstCallTimeMap;
    }

    /**
     * 设置ESDto的首次联系时间属性
     *
     * @param firstCallTimeMap
     * @param leadsEsDto
     * @return
     */
    private LeadsEsDto buildLeadsEsFirstCallIntervalTimeProperty(Map<Long, LocalDateTime> firstCallTimeMap, LeadsEsDto leadsEsDto) {
        // 设置首次联系间隔时间
        LocalDateTime firstCallTime = firstCallTimeMap.get(leadsEsDto.getLeadsId());
        if (null != firstCallTime) {
            // 分配时间-首次联系时间=间隔时间
            Long distributeTimeMillis = leadsEsDto.getDistributeTime();
            LocalDateTime distributeTime = DateTimeUtils.MillisToLocalDateTime(distributeTimeMillis);
            long seconds = Math.abs(firstCallTime.until(distributeTime, ChronoUnit.SECONDS));
            leadsEsDto.setFirstCallIntervalTime(seconds);
        }
        return leadsEsDto;
    }

    private void leadsFollowTimeToLeadsEsDto(LeadsEsDto leadsEsDto, LeadsFollowTime leadsFollowTime, StaffDto staffDto) {
        Long staffId = leadsFollowTime.getStaffId();
        if (Objects.nonNull(staffDto)) {
            leadsEsDto.setStaffName(staffDto.getName());
        }
        leadsEsDto.setStaffId(staffId);
        leadsEsDto.setDistributeTimeL(leadsFollowTime.getDistributeTime());
        leadsEsDto.setStateArrivalStore(leadsFollowTime.getStateArrivalStore());
        leadsEsDto.setStateContact(leadsFollowTime.getStateContact());
        leadsEsDto.setStateContactSuccess(leadsFollowTime.getStateContactSuccess());
        leadsEsDto.setStateDeposit(leadsFollowTime.getStateDeposit());
        leadsEsDto.setStateInstall(leadsFollowTime.getStateInstall());
        leadsEsDto.setStateMeasuring(leadsFollowTime.getStateMeasuring());
        leadsEsDto.setStateNoAvail(leadsFollowTime.getStateNoAvail());
        leadsEsDto.setStateOfferPrice(leadsFollowTime.getStateOfferPrice());
        leadsEsDto.setStateOrderSuccess(leadsFollowTime.getStateOrderSuccess());
    }
}

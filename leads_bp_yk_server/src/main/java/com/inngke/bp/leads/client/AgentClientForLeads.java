package com.inngke.bp.leads.client;

import com.google.common.collect.Lists;
import com.inngke.bp.organize.dto.request.GetAgentRequest;
import com.inngke.bp.organize.dto.response.AgentDto;
import com.inngke.bp.organize.service.AgentService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/8/10 11:19
 **/
@Component
@Slf4j
public class AgentClientForLeads {

    @Autowired
    private JsonService jsonService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.organize_bp_yk:}")
    private AgentService agentService;

    public List<AgentDto> getAgentById(GetAgentRequest getAgentRequest) {
        BaseResponse<List<AgentDto>> response = agentService.getAgentList(getAgentRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException("经销商数据查询失败");
        }
        return response.getData();
    }

    public List<AgentDto> getAgentByIds(Integer bid, Set<Long> agentIds) {
        GetAgentRequest request = new GetAgentRequest();
        request.setBid(bid);
        request.setIds(Lists.newArrayList(agentIds));

        BaseResponse<List<AgentDto>> response = agentService.getAgentList(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("获取经销商数据异常：{}", jsonService.toJson(response));
            return Lists.newArrayList();
        }
        return response.getData();
    }

}

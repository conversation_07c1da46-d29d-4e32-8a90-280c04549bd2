package com.inngke.bp.leads.client;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.request.GetAppConfByIdRequest;
import com.inngke.ip.common.dto.request.GetAppConfRequest;
import com.inngke.ip.common.dto.response.WxAppConfDto;
import com.inngke.ip.common.service.wx.WxThirdPlatformService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-07-10 18:10
 **/
@Component
public class WxThirdPlatformServiceForLeads {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private WxThirdPlatformService wxThirdPlatformService;

    public WxAppConfDto getWxAppConf(int bid, Integer type) {
        GetAppConfRequest request = new GetAppConfRequest();
        request.setBid(bid);
        request.setType(type);
        BaseResponse<WxAppConfDto> response = wxThirdPlatformService.getWxAppConf(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException(response.getMsg());
        }
        return response.getData();
    }



}

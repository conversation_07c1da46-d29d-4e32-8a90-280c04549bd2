package com.inngke.bp.leads.service.impl.tp.oauth;

import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.bp.leads.service.LeadsTpOauthTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 15:36
 * factory
 */
@Service
public class LeadsTpOauthFactory {

    @Autowired
    private List<LeadsTpOauthTypeService> leadsTpOauthTypeServiceList;

    /**
     * 获取第三方平台实例
     *
     * @param type
     * @return
     */
    public LeadsTpOauthTypeService getInstance(Integer type){
        LeadsTpTypeEnum leadsTpTypeEnum = LeadsTpTypeEnum.parse(type);

        if (leadsTpTypeEnum == null){
            return null;
        }

        for (LeadsTpOauthTypeService leadsTpOauthTypeService : leadsTpOauthTypeServiceList) {
            if (leadsTpOauthTypeService.getType().equals(leadsTpTypeEnum)){
                return leadsTpOauthTypeService;
            }
        }

        return null;
    }
}

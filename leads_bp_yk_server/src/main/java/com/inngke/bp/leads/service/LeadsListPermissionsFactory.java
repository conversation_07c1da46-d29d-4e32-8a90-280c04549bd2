package com.inngke.bp.leads.service;

import com.inngke.bp.leads.service.enums.ListPermissionsEnums;
import com.inngke.bp.leads.service.strategy.permissions.list.ListPermissionsStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Set;

@Component
@Slf4j
public class LeadsListPermissionsFactory {

    //使用策略模式,里面都是接口实现类
    @Autowired
    private List<ListPermissionsStrategy> listPermissionsStrategyList;

    public ListPermissionsStrategy analysisGetListPermissionsStrategy(Set<Integer> status, Integer statusGroup) {
        //返回一个枚举类
        ListPermissionsEnums parse = ListPermissionsEnums.parse(status, statusGroup);
        if (ObjectUtils.isEmpty(parse)) {
            log.error("获取列表权限处理器失败status:{},statusGroup:{}", status, statusGroup);
            return null;
        }
        //判断枚举类里面返回的策略和策略模式里面进行匹配返回策略实例
        for (ListPermissionsStrategy listPermissionsStrategy : listPermissionsStrategyList) {
            if (listPermissionsStrategy.getClass().getName().equals(parse.getStrategyClazz().getName())) {
                return listPermissionsStrategy;
            }
        }

        log.error("获取列表权限处理器失败 class.name:{}", parse.getStrategyClazz().getName());
        return null;
    }
}

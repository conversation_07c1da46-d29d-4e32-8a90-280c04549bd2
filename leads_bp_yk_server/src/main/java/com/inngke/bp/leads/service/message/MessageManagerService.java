package com.inngke.bp.leads.service.message;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.service.message.context.MessageContext;
import com.inngke.bp.leads.service.message.sender.MessageSenderService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.ip.common.dto.request.MqDelaySendRequest;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.dto.request.NotifyMessageRequest;
import com.inngke.ip.common.service.MqService;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import com.inngke.ip.reach.service.TemplateMessageSendService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Service
public class MessageManagerService {

    private static final String STR_NOTIFY_SEND = "notify_send";

    private static final Logger logger = LoggerFactory.getLogger(MessageManagerService.class);

    @Autowired
    private List<MessageSenderService> messageSenderService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @Autowired
    private JsonService jsonService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    protected TemplateMessageSendService templateMessageSendService;

    public TemplateMessageSendRequest templateMessageSendRequest(MessageContext ctx) {
        return findSender(ctx).sendMessage(ctx);
    }


    public void send(MessageContext ctx) {
        TemplateMessageSendRequest request = templateMessageSendRequest(ctx);
        if (Objects.isNull(request)) {
            logger.info("消息发送失败构建请求失败, ctx={}", jsonService.toJson(ctx));
            return;
        }

        //延迟,定时发送
        if (Objects.nonNull(ctx.getDeliverAt()) || Objects.nonNull(ctx.getDeliverAfter())) {
            MqDelaySendRequest mqDelaySendRequest = new MqDelaySendRequest();
            mqDelaySendRequest.setBid(ctx.getBid());
            mqDelaySendRequest.setTopic(STR_NOTIFY_SEND);
            mqDelaySendRequest.setOperatorId(0L);
            NotifyMessageRequest notifyMessageRequest = new NotifyMessageRequest();
            notifyMessageRequest.setContent(jsonService.toJson(request));
            notifyMessageRequest.setType(Lists.newArrayList("template"));
            mqDelaySendRequest.setPayload(jsonService.toJson(notifyMessageRequest));

            Optional.ofNullable(ctx.getDeliverAfter()).ifPresent(mqDelaySendRequest::setDeliverAfter);
            Optional.ofNullable(ctx.getDeliverAt()).ifPresent(mqDelaySendRequest::setDeliverAt);
            BaseResponse<Boolean> response = mqService.sendDelay(mqDelaySendRequest);
            logger.info("发送模版消息延迟mq返回:{}", jsonService.toJson(response));
            return;
        }

        //直接发送
        templateMessageSendService.send(request);
    }


    private MessageSenderService findSender(MessageContext ctx) {
        for (MessageSenderService senderService : messageSenderService) {
            if (senderService.getMessageType().equals(ctx.getMessageType())) {
                return senderService;
            }
        }

        throw new InngkeServiceException("未获取到消息类型【" + ctx.getMessageType().getName() + "】的处理器");
    }
}

package com.inngke.bp.leads.controller;

import com.inngke.bp.leads.dto.request.GuideCreateLeadsRequest;
import com.inngke.bp.leads.dto.request.LeadsQuery;
import com.inngke.bp.leads.dto.response.LeadsListVo;
import com.inngke.bp.leads.service.LeadsDocService;
import com.inngke.bp.leads.service.LeadsGuideService;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 导购-线索控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/17 13:42
 */
@RestController
@RequestMapping("/api/leads-guide")
@RequiredArgsConstructor
public class LeadsGuideController {

    private final LeadsGuideService leadsGuideService;

    private final LeadsDocService leadsDocService;

    /**
     * 导购创建线索
     * @param jwtPayload 会话信息
     * @param request 请求实体
     * @return 线索id
     */
    @PostMapping
    public BaseResponse<Long> guideCreateLeads(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody @Validated GuideCreateLeadsRequest request
    ) {
        request.setBid(jwtPayload.getBid());
        request.setOperatorId(jwtPayload.getCid());
        request.setOperatorStaffId(jwtPayload.getSid());

        return leadsGuideService.guideCreateLeads(request);
    }


    /**
     * 查询上报线索列表
     *
     * @param jwtPayload 会话信息
     * @param bid 商户id
     * @param operatorId 操作者id
     * @param query 查询实体
     * @return 列表
     */
    @GetMapping("/report-list")
    public BaseResponse<LeadsListVo> findReportList(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestHeader int bid,
            @RequestHeader(InngkeApiConst.STR_OPERATOR_ID) long operatorId,
            LeadsQuery query
    ) {
        query.setBid(jwtPayload.getBid());
        query.setReportStaffId(jwtPayload.getSid());
        query.setOperatorId(jwtPayload.getCid());
        query.setOperatorStaffId(jwtPayload.getSid());
        query.setSortType(0);
        return leadsDocService.searchLeads(query);
    }
}

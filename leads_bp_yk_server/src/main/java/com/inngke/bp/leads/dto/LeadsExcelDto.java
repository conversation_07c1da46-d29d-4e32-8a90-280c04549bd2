package com.inngke.bp.leads.dto;

import com.inngke.common.utils.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10 AM 12:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class  LeadsExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 商户ID
     */
    private Integer bid;


    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 手机号码
     */
    @Excel(name = "手机号")
    private String mobile;

    @Excel(name = "线索状态")
    private String status;

    @Excel(name = "线索标签")
    private String tags;

    @Excel(name = "负责人")
    private String staffName;

    @Excel(name = "负责人所属部门")
    private String staffDepartment;

    @Excel(name = "所属经销商")
    private String agentName;

    /**
     * 省份ID，0表示未匹配
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    @Excel(name = "省")
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    @Excel(name = "市")
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    @Excel(name = "区/县")
    private String areaName;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称")
    private String goodsName;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderSn;


    @Excel(name = "下单账号")
    private String orderAccount;

    @Excel(name = "是否退款")
    private String isRefund;

    @Excel(name = "退款备注")
    private String refundRemark;

    /**
     * 付款金额
     */
    @Excel(name = "付款金额")
    private String price;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    @Excel(name = "渠道来源")
    private String channelName;

    @Excel(name = "导入时间")
    private String createTime;

    @Excel(name = "分配时间")
    private String distributeTime;


}

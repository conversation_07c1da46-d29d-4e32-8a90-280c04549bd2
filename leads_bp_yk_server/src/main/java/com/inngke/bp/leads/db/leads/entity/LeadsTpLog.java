/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LeadsTpLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer bid;

    /**
     * 1:腾讯 2:飞鱼 3:ai客服
     */
    private Integer type;

    /**
     * 数据
     */
    private String content;

    /**
     * 第三方平台ID
     */
    private String tpId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String BID = "bid";

    public static final String TYPE = "type";

    public static final String CONTENT = "content";

    public static final String TP_ID = "tp_id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}

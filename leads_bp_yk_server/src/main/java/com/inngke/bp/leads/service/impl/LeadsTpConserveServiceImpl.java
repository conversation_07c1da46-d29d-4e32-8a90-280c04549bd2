package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.db.leads.entity.LeadsExtInformation;
import com.inngke.bp.leads.db.leads.manager.LeadsExtInformationManager;
import com.inngke.bp.leads.dto.platform.CustomerServiceLeadsDto;
import com.inngke.bp.leads.dto.request.LeadsAddRequest;
import com.inngke.bp.leads.dto.request.LeadsUpdateRequest;
import com.inngke.bp.leads.dto.request.tp.FeiYuLeadsPushDto;
import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.dto.response.LeadsInformationDto;
import com.inngke.bp.leads.service.LeadsChangeService;
import com.inngke.bp.leads.service.LeadsTpConserveService;
import com.inngke.bp.leads.service.LeadsTpDtoTransformService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.BidUtils;
import com.inngke.common.utils.DateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/2/28 10:44
 */
@Service
public class LeadsTpConserveServiceImpl implements LeadsTpConserveService {

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpConserveServiceImpl.class);

    @Autowired
    private LeadsTpDtoTransformService leadsTpDtoTransformService;

    @Autowired
    private LeadsChangeService leadsChangeService;

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;


    @Override
    public BaseResponse<Long> conserve(Integer bid, TencentLeadsPushDto tencentLeadsPushDto) {
        LeadsInformationDto transform = leadsTpDtoTransformService.transform(tencentLeadsPushDto);
        transform.setBid(bid);
        return addLeadsBasicAndInformation(transform);
    }

    @Override
    public BaseResponse<Long> conserve(Integer bid, FeiYuLeadsPushDto feiYuLeadsPushDto) {
        LeadsInformationDto transform = leadsTpDtoTransformService.transform(feiYuLeadsPushDto);
        transform.setBid(bid);
        return addLeadsBasicAndInformation(transform);
    }

    @Override
    public BaseResponse<Long> conserve(Integer bid, CustomerServiceLeadsDto customerServiceLeadsDto) {
        BidUtils.setBid(bid);
        LeadsInformationDto transform = leadsTpDtoTransformService.transform(customerServiceLeadsDto);
        transform.setBid(bid);
        return addLeadsBasicAndInformation(transform);
    }


    /**
     * 保存信息类线索
     *
     * @return
     */
    private BaseResponse<Long> addLeadsBasicAndInformation(LeadsInformationDto leadsInformationDto) {
        //1.调用原有接口保存线索基本信息
        Long leadsExtInformationId = getLeadsExtInformationId(leadsInformationDto.getBid(), leadsInformationDto.getExternalId());
        if (Objects.nonNull(leadsExtInformationId)){
            return BaseResponse.success();
        }

        Long leadsId = saveBasicInfo(leadsInformationDto);

        //保存扩展数据
        leadsInformationDto.setId(leadsId);
        Boolean extSaved = addLeadsInformation(leadsInformationDto);
        if (extSaved.equals(false)) {
            logger.error("保存线索扩展信息失败");
            return BaseResponse.error("保存线索扩展信息失败");
        }

        return BaseResponse.success(leadsId);
    }

    /**
     * 查看是否已经有信息扩展信息
     * 有则更新线索, 无则新增线索
     * @param leadsInformationDto 线索信息对象
     * @return 线索扩展id
     */
    private Long saveBasicInfo(LeadsInformationDto leadsInformationDto) {
        BaseResponse<LeadsDto> leadsBasicInfoResponse = leadsChangeService.add(createAddLeadsRequest(leadsInformationDto));
        if (!BaseResponse.responseSuccessWithNonNullData(leadsBasicInfoResponse)) {
            logger.error("保存线索基本信息失败->{}", leadsBasicInfoResponse.getMsg());
            throw new InngkeServiceException(leadsBasicInfoResponse.getMsg());
        }
        return leadsBasicInfoResponse.getData().getId();
    }

    private LeadsAddRequest createAddLeadsRequest(LeadsInformationDto leadsInformationDto) {
        LeadsAddRequest leadsAddRequest = new LeadsAddRequest();
        leadsAddRequest.setName(leadsInformationDto.getName());
        leadsAddRequest.setMobile(leadsInformationDto.getMobile());
        leadsAddRequest.setProvinceId(leadsInformationDto.getProvinceId());
        leadsAddRequest.setProvinceName(leadsInformationDto.getProvinceName());
        leadsAddRequest.setCityId(leadsInformationDto.getCityId());
        leadsAddRequest.setCityName(leadsInformationDto.getCityName());
        leadsAddRequest.setAreaId(leadsInformationDto.getAreaId());
        leadsAddRequest.setAreaName(leadsInformationDto.getAreaName());
        leadsAddRequest.setAddress(leadsInformationDto.getAddress());
        leadsAddRequest.setChannel(leadsInformationDto.getChannel());
        leadsAddRequest.setChannelType(leadsInformationDto.getChannelType());
        leadsAddRequest.setChannelSource(leadsInformationDto.getChannelSource());
        leadsAddRequest.setOrderAccount(leadsInformationDto.getOrderAccount());
        leadsAddRequest.setOrderSn(leadsInformationDto.getOrderSn());
        leadsAddRequest.setGoodsName(leadsInformationDto.getGoodsName());
        leadsAddRequest.setGoodsNum(leadsInformationDto.getGoodsNum());
        leadsAddRequest.setPayTime(leadsInformationDto.getPayTime());
        leadsAddRequest.setPayAmount(leadsInformationDto.getPayAmount());
        leadsAddRequest.setOrderMessage(leadsInformationDto.getOrderMessage());
        leadsAddRequest.setRemark(leadsInformationDto.getRemark());
        leadsAddRequest.setTpLeadsId(leadsInformationDto.getTpLeadsId());
        leadsAddRequest.setPromotionName(leadsInformationDto.getPromotionName());
        leadsAddRequest.setRegistryTime(leadsInformationDto.getRegistryTime());
        leadsAddRequest.setExpectIn(leadsInformationDto.getExpectIn());
        leadsAddRequest.setStyle(leadsInformationDto.getStyle());
        leadsAddRequest.setBatchId(leadsInformationDto.getBatchId());
        leadsAddRequest.setExtData(leadsInformationDto.getExtData());
        leadsAddRequest.setCreateStaffId(leadsInformationDto.getOperatorStaffId());
        leadsAddRequest.setOperatorId(0L);
        leadsAddRequest.setBid(leadsInformationDto.getBid());
        leadsAddRequest.setTags(leadsInformationDto.getTags());
        leadsAddRequest.setWeChat(leadsInformationDto.getWeChat());
        leadsAddRequest.setExternalTags(leadsInformationDto.getExternalTags());
        return leadsAddRequest;
    }

    private LeadsUpdateRequest createUpdateLeadsRequest(LeadsInformationDto leadsInformationDto) {
        LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
        leadsUpdateRequest.setTags(leadsInformationDto.getTags());
        leadsUpdateRequest.setStatus(leadsInformationDto.getStatus());
        leadsUpdateRequest.setOperatorId(0L);
        leadsUpdateRequest.setId(leadsInformationDto.getId());
        leadsUpdateRequest.setName(leadsInformationDto.getName());
        leadsUpdateRequest.setMobile(leadsInformationDto.getMobile());
        leadsUpdateRequest.setProvinceId(leadsInformationDto.getProvinceId());
        leadsUpdateRequest.setProvinceName(leadsInformationDto.getProvinceName());
        leadsUpdateRequest.setCityId(leadsInformationDto.getCityId());
        leadsUpdateRequest.setCityName(leadsInformationDto.getCityName());
        leadsUpdateRequest.setAreaId(leadsInformationDto.getAreaId());
        leadsUpdateRequest.setAreaName(leadsInformationDto.getAreaName());
        leadsUpdateRequest.setAddress(leadsInformationDto.getAddress());
        leadsUpdateRequest.setChannel(leadsInformationDto.getChannel());
        leadsUpdateRequest.setChannelType(leadsInformationDto.getChannelType());
        leadsUpdateRequest.setChannelSource(leadsInformationDto.getChannelSource());
        leadsUpdateRequest.setOrderAccount(leadsInformationDto.getOrderAccount());
        leadsUpdateRequest.setOrderSn(leadsInformationDto.getOrderSn());
        leadsUpdateRequest.setGoodsName(leadsInformationDto.getGoodsName());
        leadsUpdateRequest.setGoodsNum(leadsInformationDto.getGoodsNum());
        leadsUpdateRequest.setPayTime(leadsInformationDto.getPayTime());
        leadsUpdateRequest.setPayAmount(leadsInformationDto.getPayAmount());
        leadsUpdateRequest.setOrderMessage(leadsInformationDto.getOrderMessage());
        leadsUpdateRequest.setRemark(leadsInformationDto.getRemark());
        leadsUpdateRequest.setTpLeadsId(leadsInformationDto.getTpLeadsId());
        leadsUpdateRequest.setPromotionName(leadsInformationDto.getPromotionName());
        leadsUpdateRequest.setRegistryTime(leadsInformationDto.getRegistryTime());
        leadsUpdateRequest.setExpectIn(leadsInformationDto.getExpectIn());
        leadsUpdateRequest.setStyle(leadsInformationDto.getStyle());
        leadsUpdateRequest.setBatchId(leadsInformationDto.getBatchId());
        leadsUpdateRequest.setExtData(leadsInformationDto.getExtData());
        leadsUpdateRequest.setBid(leadsInformationDto.getBid());
        leadsUpdateRequest.setWeChat(leadsInformationDto.getWeChat());
        leadsUpdateRequest.setExternalTags(leadsInformationDto.getExternalTags());
        return leadsUpdateRequest;

    }

    private Boolean addLeadsInformation(LeadsInformationDto leadsInformationDto) {
        LeadsExtInformation leadsExtInformation = createLeadsExtInformation(leadsInformationDto);
        return leadsExtInformationManager.saveOrUpdate(leadsExtInformation);
    }

    private Long getLeadsExtInformationId(Integer bid, String externalId) {
        LeadsExtInformation leadsExtInfo = leadsExtInformationManager.getLeadsExtInformationSelectId(bid, externalId);
        return leadsExtInfo == null ? null : leadsExtInfo.getId();
    }

    private LeadsExtInformation createLeadsExtInformation(LeadsInformationDto leadsInformationDto) {
        LeadsExtInformation leadsExtInformation = new LeadsExtInformation();
        LocalDateTime now = LocalDateTime.now();
        leadsExtInformation.setId(leadsInformationDto.getId());
        leadsExtInformation.setBid(leadsInformationDto.getBid());
        leadsExtInformation.setExternalId(leadsInformationDto.getExternalId());
        leadsExtInformation.setOpenId(leadsInformationDto.getOpenId());
        leadsExtInformation.setGender(leadsInformationDto.getGender());
        leadsExtInformation.setCampaignId(leadsInformationDto.getCampaignId());
        leadsExtInformation.setCampaignName(leadsInformationDto.getCampaignName());
        leadsExtInformation.setAccountId(leadsInformationDto.getAccountId());
        leadsExtInformation.setAccountName(leadsInformationDto.getAccountName());
        leadsExtInformation.setCreateTime(Optional.ofNullable(
                DateTimeUtils.MillisToLocalDateTime(leadsInformationDto.getCreateTime())).orElse(now));
        leadsExtInformation.setUpdateTime(Optional.ofNullable(
                DateTimeUtils.MillisToLocalDateTime(leadsInformationDto.getUpdateTime())).orElse(now));
        LocalDateTime submitTime = leadsInformationDto.getSubmitTime() != null ?
                DateTimeUtils.MillisToLocalDateTime(leadsInformationDto.getSubmitTime()) : null;
        leadsExtInformation.setSubmitTime(submitTime);

        return leadsExtInformation;
    }
}

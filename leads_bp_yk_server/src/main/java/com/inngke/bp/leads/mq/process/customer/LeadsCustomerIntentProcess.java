package com.inngke.bp.leads.mq.process.customer;

import com.inngke.bp.leads.mq.message.customer.CusromerChangeMessageDto;
import org.springframework.stereotype.Service;

@Service
public class LeadsCustomerIntentProcess extends BaseCustomerProcess {

    /**
     * 是否需要处理,只有当手机号码授权时才进行线索状态变更，将客户的状态变更为有意向
     *
     * @param message 消息体
     * @return 是否需要继续处理
     */
    @Override
    public boolean match(CusromerChangeMessageDto message) {
        return message.getEvent() == 4;
    }


    /**
     * 处理逻辑
     *
     * @param message 消息体
     */
    @Override
    public void process(CusromerChangeMessageDto message) {
        changeLeadsStatus(message);
    }
}

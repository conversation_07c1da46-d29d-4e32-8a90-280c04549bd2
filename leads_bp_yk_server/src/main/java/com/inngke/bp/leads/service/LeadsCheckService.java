package com.inngke.bp.leads.service;

import com.inngke.bp.leads.db.leads.entity.Leads;

import java.util.List;
import java.util.Set;

/**
 * LeadsCheckService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/16 17:00
 */
public interface LeadsCheckService {

    /**
     * 校验是distributorStaff下是否已有手机号或微信号相同的线索
     * @param bid 租户id
     * @param distributorStaffId 跟进员工id
     * @param mobile 线索手机号
     * @param wx 线索微信号
     * @return
     */
    List<Leads> hasAnyMobileOrWechatLeadsOfDistributorStaff(Integer bid, Long distributorStaffId, String mobile, String wx);

    /**
     * 校验是否有相同手机号或微信号的线索，where reportStaff = ？
     * @param bid 商户id
     * @param reportStaffId 线索上报人
     * @param mobile 手机号
     * @param wx 微信号
     * @return true OR false
     */
    List<Leads> hasAnyMobileOrWechatLeadsOfReportStaff(Integer bid, Long reportStaffId, String mobile, String wx);


    /**
     * 根据配置校验重复线索
     * @param bid 商户id
     * @param mobile 手机号
     * @param weChat 微信号
     * @param excludeIds 需要排除的ids
     */
    void checkRepeatLeads(Integer bid, String mobile, String weChat, Set<Long> excludeIds);
}

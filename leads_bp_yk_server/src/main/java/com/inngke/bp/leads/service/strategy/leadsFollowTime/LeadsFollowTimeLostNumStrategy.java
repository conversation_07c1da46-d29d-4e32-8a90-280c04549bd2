package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.db.leads.manager.LeadsManager;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Stream;

/**
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeLostNumStrategy")
public class LeadsFollowTimeLostNumStrategy implements LeadsFollowTimeStrategy {


    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime,long time) {
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "lostNum",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.termQuery("status", LeadsStatusEnum.LOST.getStatus()))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateNoAvail = bucketAggregations.get("lostNum");
        long count = stateNoAvail.getDocCount();
        dto.setLostNum((int)count);
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Stream<Integer> integerStream = dtos.stream().map(LeadsFollowTimeByStaffResponse::getLostNum);
        Integer count = integerStream.reduce(Integer::sum).get();
        dto.setLostNum(count);
    }
}

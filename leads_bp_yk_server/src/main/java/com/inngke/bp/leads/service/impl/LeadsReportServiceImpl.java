package com.inngke.bp.leads.service.impl;

import com.inngke.bp.leads.dto.request.LeadsAchievementReportDetailRequest;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.DepartmentClientForLeads;
import com.inngke.bp.leads.dto.request.LeadsAchievementReportRequest;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.service.LeadsReportService;
import com.inngke.bp.leads.service.template.detail.LeadsAchievementDetail;
import com.inngke.bp.leads.service.template.leadsAchievement.AbstractLeadsAchievement;
import com.inngke.bp.leads.service.template.leadsAchievement.LeadsAchievementReportContext;
import com.inngke.bp.organize.dto.request.department.GetDepartmentRequest;
import com.inngke.bp.organize.dto.response.DepartmentDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * LeadsReportServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/13 20:54
 */
@DubboService(version = "1.0.0", timeout = 3000)
@Slf4j
public class LeadsReportServiceImpl implements LeadsReportService {

    @Autowired
    private List<AbstractLeadsAchievement> leadsAchievementList;

    @Autowired
    private List<LeadsAchievementDetail> leadsAchievementDetailList;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private DepartmentClientForLeads departmentClientForLeads;

    @Override
    public BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> getReportList(LeadsAchievementReportRequest request) {
        List<AbstractLeadsAchievement> achievements = leadsAchievementList.stream()
                .filter(item -> item.getDimensionCode().equals(request.getDimensionCode()))
                .collect(Collectors.toList());
        AbstractLeadsAchievement abstractLeadsAchievement = achievements.get(0);
        if (abstractLeadsAchievement.getDimensionCode().equalsIgnoreCase("department")) {
            request.setPageSize(1000000);
        }
        LeadsAchievementReportContext context = toContext(request);
        List<LeadsAchievementReportResponse> reportData = abstractLeadsAchievement.getReportData(context);
        BasePaginationResponse<LeadsAchievementReportResponse> result = new BasePaginationResponse<>();
        result.setList(reportData);
        result.setTotal(context.getResultCount());

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<List<LeadsAchievementReportResponse>> exportList(LeadsAchievementReportRequest request) {
        BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> reportList = this.getReportList(request);
        List<LeadsAchievementReportResponse> result = reportList.getData().getList();
        if(CollectionUtils.isEmpty(result)){
            return BaseResponse.success(result);
        }
        DepartmentDto department = getDepartment(request.getBid(), 0L);

        List<LeadsAchievementReportResponse> responseList = recursionGetExportList(result, request,1, request.getSelectDeptId());

        HashMap<Long, String> departIdAndNameAll = new HashMap<>(36);

        getDepartmentNameAll(Lists.newArrayList(department), "", departIdAndNameAll);

        doDepartmentChainName(responseList, departIdAndNameAll);
        return BaseResponse.success(responseList);
    }

    private void doDepartmentChainName(List<LeadsAchievementReportResponse> responseList, HashMap<Long, String> departIdAndNameAll) {
        if (!CollectionUtils.isEmpty(responseList)) {
            responseList.forEach(item -> {
                String chainName = departIdAndNameAll.get(item.getDeptId());
                item.setDeptChainName(chainName);
            });
        }
    }

    private DepartmentDto getDepartment(Integer bid, Long deptId) {
        GetDepartmentRequest request = new GetDepartmentRequest();
        request.setBid(bid);
        request.setDepartmentId(deptId);
        request.setChildrenLevel(9);
        return departmentClientForLeads.getDepartment(request);
    }

    /**
     * 部门Id对于的全部门名称
     */
    private void getDepartmentNameAll(List<DepartmentDto> department, String parentName, Map<Long,String> result){
        if(CollectionUtils.isEmpty(department)){
            return;
        }
        for (DepartmentDto departmentDto : department) {
            String departmentName = setDepartmentName(parentName, departmentDto.getName());
            result.put(departmentDto.getId(),departmentName);
            getDepartmentNameAll(departmentDto.getChildren(),departmentName,result);
        }
    }

    private String setDepartmentName(String parentName,String departmentName){
        if(StringUtils.isEmpty(parentName)){
            return departmentName;
        }
        return parentName + "/" + departmentName;
    }

    private List<LeadsAchievementReportResponse> recursionGetExportList(List<LeadsAchievementReportResponse> list,
                                                                        LeadsAchievementReportRequest request,
                                                                        int depth,
                                                                        Long departmentId) {
        List<LeadsAchievementReportResponse> result = Lists.newArrayList();
        if (depth == 1) {
            result.add(list.get(0));
        }
        if (depth > 10) {
            return result;
        }
        request.setPageNo(1);
        request.setPageSize(1000);

        for (int i = 0; i < list.size(); i++) {
            LeadsAchievementReportResponse reportDto = list.get(i);
            if (departmentId.equals(reportDto.getDeptId())) {
                continue;
            }
            result.add(reportDto);

            request.setSelectDeptId(reportDto.getDeptId());
            BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> listBaseResponse = getReportList(request);
            List<LeadsAchievementReportResponse> data = listBaseResponse.getData().getList();
            if (CollectionUtils.isEmpty(data)) {
                continue;
            }

            List<LeadsAchievementReportResponse> rowResponse =
                    recursionGetExportList(data, request, depth + 1, reportDto.getDeptId());

            result.addAll(rowResponse);
        }
        return result;
    }

    private LeadsAchievementReportContext toContext(LeadsAchievementReportRequest request) {
        LeadsAchievementReportContext leadsAchievementReportContext = new LeadsAchievementReportContext();
        leadsAchievementReportContext.setPageNo(request.getPageNo());
        leadsAchievementReportContext.setPageSize(request.getPageSize());
        leadsAchievementReportContext.setBid(request.getBid());
        leadsAchievementReportContext.setStartEventTime(request.getStartEventTime());
        leadsAchievementReportContext.setEndEventTime(request.getEndEventTime());
        leadsAchievementReportContext.setStartDistributeTime(request.getStartDistributeTime());
        leadsAchievementReportContext.setEndDistributeTime(request.getEndDistributeTime());
        leadsAchievementReportContext.setStartCreateTime(request.getStartCreateTime());
        leadsAchievementReportContext.setEndCreateTime(request.getEndCreateTime());
        leadsAchievementReportContext.setSelectStaffId(request.getSelectStaffId());
        leadsAchievementReportContext.setSelectDeptId(request.getSelectDeptId());
        leadsAchievementReportContext.setCurrentStaffId(request.getCurrentStaffId());
        return leadsAchievementReportContext;
    }


    @Override
    public BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> getReportListDetail(LeadsAchievementReportDetailRequest request) {
        String dimensionCode = request.getDimensionCode() + "Detail";
        LeadsAchievementDetail abstractLeadsAchievement = null;
        for (LeadsAchievementDetail leadsAchievementDetail : leadsAchievementDetailList) {
            if (dimensionCode.equals(leadsAchievementDetail.getDimensionCode())) {
                abstractLeadsAchievement = leadsAchievementDetail;
                break;
            }
        }
        if (Objects.isNull(abstractLeadsAchievement)) {
            return BaseResponse.error("dimensionCode不正确");
        }

        LeadsAchievementReportContext context = toContext(request);

        LeadsAchievementDetail leadsAchievementDetail = (LeadsAchievementDetail) abstractLeadsAchievement;

        return leadsAchievementDetail.getReportListDetail(context, request.getType(), request.getTotal());
    }


}

package com.inngke.bp.leads.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.entity.LeadsTpOauth;
import com.inngke.bp.leads.db.leads.manager.LeadsTpOauthManager;
import com.inngke.bp.leads.enums.LeadsTpTypeEnum;
import com.inngke.bp.leads.service.LeadsPullService;
import com.inngke.bp.leads.service.impl.tp.oauth.LeadsTpOauthFlyFishServiceImpl;
import com.inngke.bp.leads.service.impl.tp.pull.LeadsPullServiceFactory;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.service.DatabasePrivatizationService;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/24 11:50
 */
@Component
public class LeadsTpSchedule {

    private static final String REFRESH_TOKEN = "refresh_token";
    private static final String PULL_FY = "pull_fy";
    private static final String PULL_TENCENT = "pull_tencent";

    private static final Logger logger = LoggerFactory.getLogger(LeadsTpSchedule.class);

    @Autowired
    private LeadsTpOauthFlyFishServiceImpl leadsTpOauthFlyFishService;

    @Autowired
    private DatabasePrivatizationService databasePrivatizationService;

    @Autowired
    private LeadsPullServiceFactory leadsPullServiceFactory;

    @Autowired
    private LeadsTpOauthManager leadsTpOauthManager;

    @Autowired
    private LockService lockService;


    /**
     * 定时刷新飞鱼token 每12小时
     */
    @Scheduled(cron = "0 0 */12 * * *")
    public void refreshFlyFishToken() {
        Lock lock = lock(REFRESH_TOKEN, 60);
        if (lock == null) {
            return;
        }
        databasePrivatizationService.accept(privatizationDb -> {
            Integer bid = privatizationDb.getBids().iterator().next();
            BaseBidOptRequest request = new BaseBidOptRequest();
            request.setBid(bid);
            request.setOperatorId(0L);
            leadsTpOauthFlyFishService.refreshToken(request);
        });
    }

    /**
     * 拉取腾讯广告线索 每五分钟一次
     */
    @Scheduled(fixedDelay = 60000 * 15,initialDelay = 60000 * 15)
    public void pullTencentLeads() {
        Lock lock = lock(PULL_TENCENT, 60 * 15);
        if (lock == null) {
            return;
        }

        LeadsPullService leadsPullService = leadsPullServiceFactory.getInstance(LeadsTpTypeEnum.TENCENT.getCode());
        databasePrivatizationService.accept(privatizationDb -> {
            privatizationDb.getBids().forEach((item) -> {
                Set<Integer> allBids = getAllBids(LeadsTpTypeEnum.TENCENT.getCode(), item);
                if (!CollectionUtils.isEmpty(allBids)) {
                    allBids.forEach(bid -> {
                        try {
                            BaseBidOptRequest request = new BaseBidOptRequest();
                            request.setOperatorId(0L);
                            request.setBid(bid);
                            leadsPullService.pull(request);
                        } catch (Exception e) {
                            logger.warn("获取腾讯线索失败", e);
                        }
                    });
                }
            });
        });

        LeadsPullService leadsPullServiceFy = leadsPullServiceFactory.getInstance(LeadsTpTypeEnum.FEI_YU.getCode());
        databasePrivatizationService.accept(privatizationDb -> {
            privatizationDb.getBids().forEach((item) -> {
                Set<Integer> allBids = getAllBids(LeadsTpTypeEnum.FEI_YU.getCode(), item);
                allBids.forEach(bid -> {
                    try {
                        BaseBidOptRequest request = new BaseBidOptRequest();
                        request.setOperatorId(0L);
                        request.setBid(bid);
                        leadsPullServiceFy.pull(request);
                    } catch (Exception e) {
                        logger.warn("拉取飞鱼线索失败", e);
                    }
                });
            });

        });
    }

    private Set<Integer> getAllBids(Integer type, Integer bid) {
        return leadsTpOauthManager.list(Wrappers.<LeadsTpOauth>query().eq(LeadsTpOauth.TYPE, type).select(LeadsTpOauth.BID))
                .stream()
                .map(LeadsTpOauth::getBid).collect(Collectors.toSet());
    }

    private Lock lock(String method, Integer expiration) {
        String key = LeadsServiceConsts.APP_ID + ":lock:" + method;
        Lock lock = lockService.getLock(key, expiration);
        if (lock == null) {
            logger.info("线索任务拿锁失败:{}", method);
            return null;
        }
        return lock;
    }
}

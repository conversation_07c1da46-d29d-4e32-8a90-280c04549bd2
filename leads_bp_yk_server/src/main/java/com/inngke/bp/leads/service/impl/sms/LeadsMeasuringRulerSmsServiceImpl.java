package com.inngke.bp.leads.service.impl.sms;

import com.inngke.bp.leads.dto.request.LeadsSmsSendRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.enums.LeadsSmsTemplateEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import org.springframework.stereotype.Service;

/**
 * 量尺短信
 *
 * <AUTHOR>
 * @date 2022/4/6 11:16
 */
@Service
public class LeadsMeasuringRulerSmsServiceImpl extends AbstractLeadsSmsService {
    @Override
    public LeadsSmsTemplateEnum getLeadsSmsTemplateEnum() {
        return LeadsSmsTemplateEnum.MEASURING_RULER;
    }

    /**
     * 发送短信后应该更新到的线索状态
     *
     * @return
     */
    @Override
    protected LeadsStatusEnum getNewStatusEnum() {
        return LeadsStatusEnum.MEASURED;
    }

    @Override
    protected String getSmsTemplateContent() {
        return "尊敬的{1}，我是{2}销售顾问{3}，您已成功预约量尺服务，我们的服务人员{4}将会在{5}上门量尺，" +
                "如时间有变或有其他问题可随时与我联系{6}{7}";
    }

    @Override
    protected String getFollowContent(LeadsDto leads, LeadsSmsSendRequest request) {
        LeadsStatusEnum leadsStatusEnum = LeadsStatusEnum.parse(leads.getStatus());
        //如果线索状态大于量尺
        if (leadsStatusEnum.getStatus() >= getNewStatusEnum().getStatus()) {
            return "给客户发送量尺短信\n短信内容：" + getSmsContentInfo(request.getLeadsSmsVarList());
        }

        return "给客户发送量尺短信，系统自动将线索状态由【" +
                leadsStatusEnum.getName() +
                "】变更为【" + LeadsStatusEnum.MEASURED.getName() + "】\n短信内容：" +
                getSmsContentInfo(request.getLeadsSmsVarList());
    }
}

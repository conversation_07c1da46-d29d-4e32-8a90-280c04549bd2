package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.service.enums.LeadsFollowTimeStatusStrategyEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: moqing<PERSON>
 * @chapter
 * @section
 * @since 2022/4/7 13:37
 */
@Component
public class LeadsFollowTimeFactory {
    @Autowired
    private Map<String, LeadsFollowTimeStrategy> leadsFollowTimeStrategyMap;

    public LeadsFollowTimeStrategy getLeadsFollowTimeStrategy(int status){
         return leadsFollowTimeStrategyMap.get(LeadsFollowTimeStatusStrategyEnum.parse(status));
    }

    public List<LeadsFollowTimeStrategy> getAllStrategy(){
        return new ArrayList<>(leadsFollowTimeStrategyMap.values());
    }
}

/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.inngke.bp.leads.client.MerchantConfigClientForLeads;
import com.inngke.bp.leads.client.RbacClientForLeads;
import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.core.converter.LeadsConverter;
import com.inngke.bp.leads.core.converter.LeadsDraftConverter;
import com.inngke.bp.leads.db.leads.dao.LeadsDao;
import com.inngke.bp.leads.db.leads.entity.*;
import com.inngke.bp.leads.db.leads.manager.*;
import com.inngke.bp.leads.dto.CommitLeadsPushBackLogDto;
import com.inngke.bp.leads.dto.LeadsSnapshotDto;
import com.inngke.bp.leads.dto.LeadsTransferResultDto;
import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsStatisticsDto;
import com.inngke.bp.leads.dto.response.PreFollowStaffReportDto;
import com.inngke.bp.leads.enums.LeadsBatchStatusEnum;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.enums.LeadsTransferEnum;
import com.inngke.bp.leads.enums.LeadsTypeEnum;
import com.inngke.bp.leads.facatory.LeadsFollowFactory;
import com.inngke.bp.leads.service.LeadsEsService;
import com.inngke.bp.leads.service.LeadsFollowCacheService;
import com.inngke.bp.leads.service.LeadsServiceV2;
import com.inngke.bp.leads.service.enums.LeadsFollowTypeEnum;
import com.inngke.bp.leads.service.enums.LeadsHistoryDistributeTypeEnum;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.ds.annotation.DS;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsManagerImpl extends ServiceImpl<LeadsDao, Leads> implements LeadsManager {

    private static final Logger logger = LoggerFactory.getLogger(LeadsManagerImpl.class);

    @Autowired
    private JsonService jsonService;

    @Autowired
    private LeadsLogManager leadsLogManager;

    @Autowired
    private MerchantConfigClientForLeads merchantConfigClientForLeads;

    @Autowired
    private LeadsTransferRollbackTodoManager leadsTransferRollbackTodoManager;

    @Autowired
    private LeadsBatchManager leadsBatchManager;

    @Autowired
    private LeadsDraftManager leadsDraftManager;

    @Autowired
    private LeadsDao leadsDao;

    @Autowired
    private LeadsFollowManager leadsFollowManager;

    @Autowired
    private LeadsHistoryDistributeManager leadsHistoryDistributeManager;

    @Autowired
    private LeadsExtInformationManager leadsExtInformationManager;

    @Autowired
    private RbacClientForLeads rbacClientForLeads;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    @Autowired
    private LeadsServiceV2 leadsServiceV2;

    @Autowired
    private LeadsEsService leadsEsService;

    @Autowired
    private LeadsFollowFactory leadsFollowFactory;

    @Autowired
    private LeadsManager leadsManager;

    @Autowired
    private LeadsBackReasonManager leadsBackReasonManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private LeadsFollowCacheService leadsFollowCacheService;

    @Resource
    private LeadsPushBackLogManager leadsPushBackLogManager;


    /**
     * 线索状态变更为：-2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系
     * 3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交 12=无效线索
     */

    private LeadsLog toLeadsLog(Leads leads, Long operatorId) {
        LeadsLog leadsLog = new LeadsLog();
        leadsLog.setCreateTime(LocalDateTime.now());
        leadsLog.setDistributeStaffId(leads.getDistributeStaffId());
        //0为待分配
        leadsLog.setStatusChange(leads.getStatus());
        leadsLog.setLogContent(jsonService.toJson(leads));
        leadsLog.setOperatorId(operatorId);
        leadsLog.setBid(leads.getBid());
        leadsLog.setLeadsId(leads.getId());
        return leadsLog;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addLeads(Leads leads, Long operatorId) {
        //补充默认是否在24小时内联系 0否 1是 数据 默认为未在24小时内联系
        Integer contact = Optional.ofNullable(leads.getContactIn24()).orElse(0);
        leads.setContactIn24(contact);
        saveOrUpdate(leads);
        LeadsLog leadsLog = toLeadsLog(leads, operatorId);
        leadsLog.setCreateTime(LocalDateTime.now());
        leadsLogManager.save(leadsLog);
        return leads.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDistributeLeads(List<Leads> leadsList, Long operatorId) {
        //每个员工对应的所有的线索，根据员工id分组是因为每个员工对应一个经销商，那么就可以分批的进行修改了，避免使用batchSave中生成多条更新的sql
        Map<Long, List<Leads>> staffAndListMap = leadsList.stream().collect(Collectors.groupingBy(Leads::getDistributeStaffId));
        LocalDateTime now = LocalDateTime.now();
        //对每个员工对应的线索进行分批批量分配线索的数据库更新操作，也包括分配异常的线索
        staffAndListMap.forEach((staffId, groupedLeadsList) -> {
            Integer bid = groupedLeadsList.get(0).getBid();
            List<Long> leadsIds = groupedLeadsList.stream().map(Leads::getId).collect(Collectors.toList());
            if (staffId > 0) {
                //配置成功的线索
                update(
                        Wrappers.<Leads>update()
                                .eq(Leads.BID, bid)
                                .in(Leads.ID, leadsIds)
                                .set(Leads.DISTRIBUTE_STAFF_ID, staffId)
                                // .set(Leads.DISTRIBUTE_AGENT_ID, agentId)
                                .set(Leads.STATUS, LeadsStatusEnum.DISTRIBUTED.getStatus())
                                .set(Leads.DISTRIBUTE_TIME, now)
                );
            } else {
                //设置失败
                groupedLeadsList.forEach(leads ->
                        update(
                                Wrappers.<Leads>update()
                                        .eq(Leads.BID, bid)
                                        .eq(Leads.ID, leads.getId())
                                        .set(Leads.STATUS, leads.getStatus())
                                        .set(Leads.ERROR_MSG, leads.getErrorMsg())
                                        .set(Leads.DISTRIBUTE_TIME, now)
                        )
                );
            }
        });

        List<LeadsLog> leadsLogs = leadsList.stream().map(leads -> {
            Leads editLeads = new Leads();
            editLeads.setBid(leads.getBid());
            editLeads.setDistributeStaffId(leads.getDistributeStaffId());
            editLeads.setDistributeTime(now);
            editLeads.setErrorMsg(leads.getErrorMsg());
            editLeads.setId(leads.getId());
            editLeads.setStatus(leads.getStatus());
            return toLeadsLog(editLeads, operatorId);
        }).collect(Collectors.toList());
        leadsLogManager.saveBatch(leadsLogs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLeads(Leads leads, Long operatorId) {
        addLeads(leads, operatorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeadsFollow pushBack(Leads leads, Long operatorId, Long operatorUserId, String reason, Long reasonId, List<String> pushBackImages) {
        reason = Optional.ofNullable(reason).orElse("");
        Integer bid = leads.getBid();
        Long id = leads.getId();

        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(bid);
        leadsFollow.setLeadsStatus(LeadsStatusEnum.PUSH_BACK.getStatus());
        leadsFollow.setLeadsId(id);
        String followContent = "将线索退回";
        if (!ObjectUtils.isEmpty(leads.getErrorMsg())) {
            followContent +=  "\n详细说明："+leads.getErrorMsg();
        }

        leadsFollow.setFollowContent(followContent);
        //手工跟进
        leadsFollow.setFollowType(LeadsFollowTypeEnum.MANUAL.getCode());
        if (!CollectionUtils.isEmpty(pushBackImages)) {
            leadsFollow.setFollowImages(Joiner.on(InngkeAppConst.COMMA_STR).skipNulls().join(pushBackImages));
        }
        leadsFollow.setCreateTime(LocalDateTime.now());
        StaffDto staffDto = staffClientForLeads.getStaffByCid(bid, operatorUserId);
        leadsFollow.setUserId(operatorUserId);
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setStaffId(staffDto.getId());
        }
        if (!ObjectUtils.isEmpty(reason) || !ObjectUtils.isEmpty(reasonId)) {
            leadsFollow.setReason(ObjectUtils.isEmpty(reason) ? leadsBackReasonManager.getById(bid,reasonId).getReason() : reason);
            leadsFollow.setReasonId(reasonId);
        }

        leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        leadsFollowManager.save(leadsFollow);

        CommitLeadsPushBackLogDto commitLeadsPushBack = new CommitLeadsPushBackLogDto();
        commitLeadsPushBack.setId(SnowflakeHelper.getId());
        commitLeadsPushBack.setBid(bid);
        commitLeadsPushBack.setLeadsId(leads.getId());
        commitLeadsPushBack.setPushBackReasonId(reasonId);
        commitLeadsPushBack.setPushBackReason(reason);
        commitLeadsPushBack.setPushBackReasonImages(pushBackImages);
        commitLeadsPushBack.setLeadsStatus(leads.getStatus());
        commitLeadsPushBack.setPushBackBy(staffDto.getId());
        commitLeadsPushBack.setPushBackTime(DateTimeUtils.getMilli(LocalDateTime.now()));
        commitLeadsPushBack.setCreateTime(DateTimeUtils.getMilli(LocalDateTime.now()));
        commitLeadsPushBack.setLeadsSnapshot(leads.converterLeadsSnapshot());
        leadsPushBackLogManager.commitPushBackLog(commitLeadsPushBack);

        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
        update(null, new UpdateWrapper<Leads>()
                .eq(Leads.BID, bid)
                .eq(Leads.ID, id)
                // .set(Leads.DISTRIBUTE_AGENT_ID, 0)
                .set(Leads.DISTRIBUTE_STAFF_ID, 0)
                .set(Leads.STATUS, -1)
                .set(Leads.PUSH_BACK_TIME, LocalDateTime.now())
                .set(Leads.ERROR_MSG, reason)
                .set(Leads.LAST_FOLLOW_ID, 0L)
                .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
                .set(Leads.PUSH_BACK_STAFF_ID, operatorId)
        );


        Leads editLeads = new Leads();
        editLeads.setStatus(leads.getStatus());
        editLeads.setDistributeStaffId(leads.getDistributeStaffId());
        editLeads.setDistributeTime(leads.getDistributeTime());
        editLeads.setErrorMsg(leads.getErrorMsg());
        editLeads.setBid(leads.getBid());
        editLeads.setId(leads.getId());
        leadsLogManager.save(toLeadsLog(editLeads, operatorId));

        return leadsFollow;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(Integer bId, List<Long> leadsIds, Long operatorId) {
        update(Wrappers.<Leads>update().eq(Leads.BID, bId).in(Leads.ID, leadsIds).set(Leads.STATUS, -4));
        LocalDateTime now = LocalDateTime.now();
        List<LeadsLog> leadsLogs = leadsIds.stream().map(item -> {
            LeadsLog leadsLog = new LeadsLog();
            leadsLog.setCreateTime(now);
            leadsLog.setStatusChange(LeadsStatusEnum.DELETED.getStatus());
            leadsLog.setLeadsId(item);
            leadsLog.setBid(bId);
            leadsLog.setOperatorId(operatorId);
            return leadsLog;
        }).collect(Collectors.toList());
        leadsLogManager.saveBatch(leadsLogs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeadsFollow forward(Leads leads, LeadsForwardRequest request, Long forwardStaffId, String forwardStaffName, String acceptStaffName) {
        Integer bid = leads.getBid();
        Long id = leads.getId();
        String followContent = "【" + forwardStaffName + "】将线索转交给【" + acceptStaffName + "】";

        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(bid);
        leadsFollow.setLeadsStatus(leads.getStatus());
        leadsFollow.setLeadsId(id);
        leadsFollow.setFollowContent(followContent);
        //手工跟进
        leadsFollow.setFollowType(LeadsFollowTypeEnum.MANUAL.getCode());
        leadsFollow.setCreateTime(LocalDateTime.now());
        leadsFollow.setStaffId(request.getOperatorStaffId());
        StaffDto staffDto = staffClientForLeads.getStaffById(bid, request.getOperatorStaffId());
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setUserId(staffDto.getCustomerId());
        }
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());

        //转发时添加跟进记录
        leadsFollowManager.save(leadsFollow);
        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
        LeadsHistoryDistribute leadsHistoryDistribute = leadsHistoryDistributeManager.getOne(Wrappers.<LeadsHistoryDistribute>query()
                .eq(LeadsHistoryDistribute.BID, bid).eq(LeadsHistoryDistribute.LEADS_ID, id)
                .eq(LeadsHistoryDistribute.DISTRIBUTE_STAFF_ID, forwardStaffId)
                .last("limit 1")
        );

        if (leadsHistoryDistribute == null) {
            LeadsHistoryDistribute newModel = new LeadsHistoryDistribute();
            newModel.setBid(bid);
            newModel.setLeadsId(id);
            newModel.setDistributeStaffId(forwardStaffId);
            newModel.setCreateTime(LocalDateTime.now());
            leadsHistoryDistributeManager.save(newModel);
        }

        update(new UpdateWrapper<Leads>()
                        .eq(Leads.BID, leads.getBid())
                        .eq(Leads.ID, leads.getId())
                        .set(Leads.DISTRIBUTE_TIME, leads.getDistributeTime())
                        // .set(Leads.DISTRIBUTE_AGENT_ID, leads.getDistributeAgentId())
                        .set(Leads.DISTRIBUTE_STAFF_ID, leads.getDistributeStaffId())
                        .set(Leads.STATUS,LeadsStatusEnum.DISTRIBUTED.getStatus())
                        .set(Leads.LAST_FOLLOW_ID, 0L)
                        .set(Leads.LAST_FOLLOW_TIME, LocalDateTime.now())
        );
        AsyncUtils.runAsync(() -> leadsEsService.sendLeadsChangeMq(request.getBid(), Lists.newArrayList(leads.getId()), null , null, 5));
        leadsLogManager.save(toLeadsLog(leads, request.getOperatorId()));
        if (merchantConfigClientForLeads.isOpenForwardRollback(bid)){
            leadsTransferRollbackTodoManager.add(bid,leads.getId(),forwardStaffId,leads.getDistributeStaffId());
        }
        return leadsFollow;
    }

    /**
     * 查询分组后的数据量
     *
     * @param queryWrapper 查询条件
     * @return 愤俗后的数据量
     */
    @Override
    public Integer getGroupCount(QueryWrapper<Leads> queryWrapper) {

        return leadsDao.getGroupCount(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> recoveryLeads(List<Long> leadsIds, Long operatorId, Integer bid, Long staffId) {
        List<Leads> leadsList = list(Wrappers.<Leads>query()
                .eq(Leads.BID, bid)
                .in(Leads.ID, leadsIds)
                .notIn(Leads.STATUS, Lists.newArrayList(LeadsStatusEnum.RECOVERY.getStatus()))
        );
        for (Leads leads : leadsList) {
            if (!leads.getClientId().equals(0L)) {
                throw new InngkeServiceException("回收失败，已转客户的线索不允许回收");
            }
        }
        if (!CollectionUtils.isEmpty(leadsList)) {
            //修改之前的线索数据状态为-5，已回收
            update(Wrappers.<Leads>update().eq(Leads.BID, bid).in(Leads.ID, leadsIds).set(Leads.STATUS, -5));
            //保存被回收的线索的日志
            List<LeadsLog> recoveryLeadsLogs = leadsList.stream().map(item -> {
                LeadsLog leadsLog = toLeadsLog(item, operatorId);
                leadsLog.setStatusChange(LeadsStatusEnum.RECOVERY.getStatus());
                return leadsLog;
            }).collect(Collectors.toList());
            leadsLogManager.saveBatch(recoveryLeadsLogs);

            Map<Long, LeadsFollow> needSaveFollow = new HashMap<>(leadsList.size());

            //根据回收的线索新增待分配的线索
            List<Leads> saveLeadsList = leadsList.stream().map(item -> {

                LeadsFollow leadsFollow = new LeadsFollow();
                leadsFollow.setLeadsId(item.getId());
                leadsFollow.setUserId(operatorId);
                leadsFollow.setStaffId(staffId);
                leadsFollow.setBid(bid);
                leadsFollow.setLeadsStatus(item.getStatus());
                leadsFollow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
                leadsFollow.setCreateTime(LocalDateTime.now());
                leadsFollow.setBeforeLeadsStatus(item.getStatus());
                if (item.getDistributeStaffId() != null && !item.getDistributeStaffId().equals(0L)) {
                    needSaveFollow.put(item.getDistributeStaffId(), leadsFollow);
                }

                if (Objects.nonNull(item.getPreFollowStaffId()) && item.getPreFollowStaffId() > 0
                        && item.getStatus().equals(LeadsStatusEnum.PRE_FOLLOW.getStatus())) {
                    needSaveFollow.put(item.getPreFollowStaffId(), leadsFollow);
                }


                recoveryLeads(item);
                return item;
            }).collect(Collectors.toList());
            saveBatch(saveLeadsList);
            saveRecoveryLeadsFollow(bid, needSaveFollow, staffId);

            Map<Long, Long> recoveryIdMap = saveLeadsList.stream().collect(Collectors.toMap(Leads::getRecoveryFrom, Leads::getId));

            List<LeadsExtInformation> leadsExtList = leadsExtInformationManager.list(Wrappers.<LeadsExtInformation>query()
                    .eq(LeadsExtInformation.BID, bid)
                    .in(LeadsExtInformation.ID, leadsIds)
            );

            leadsExtList.stream()
                    .filter(leadsExtInformation -> recoveryIdMap.containsKey(leadsExtInformation.getId()))
                    .forEach(leadsExtInformation -> leadsExtInformation.setId(recoveryIdMap.get(leadsExtInformation.getId())));

            if (!CollectionUtils.isEmpty(leadsExtList)) {
                leadsExtInformationManager.saveBatch(leadsExtList);
            }

            List<LeadsLog> logs = saveLeadsList.stream().map(item -> toLeadsLog(item, operatorId)).collect(Collectors.toList());
            //报错回收后新增的线索数据
            leadsLogManager.saveBatch(logs);

            //更新线索状态记录
//            handelLeadsStatusRecord(bid, saveLeadsList);
            return saveLeadsList.stream().map(Leads::getId).collect(Collectors.toList());
        } else {
            String error = Arrays.toString(leadsIds.toArray());
            logger.error("需要回收的线索编号：{},线索数据缺失", error);
            return Lists.newArrayList();
        }
    }

    /**
     * 更新线索状态记录
     *
     * @param bid
     * @param leadsList
     */
    private void handelLeadsStatusRecord(Integer bid, List<Leads> leadsList) {
        Set<Leads> kfLeadsList = leadsList.stream().filter(leads -> leads.getStatus().equals(LeadsStatusEnum.PRE_FOLLOW.getStatus())).collect(Collectors.toSet());
        Set<Long> kfLeadsIds = kfLeadsList.stream().map(Leads::getId).collect(Collectors.toSet());
        Set<Long> leadsIds = leadsList.stream().filter(leads -> !kfLeadsIds.contains(leads.getId()))
                .collect(Collectors.toSet()).stream().map(Leads::getId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(kfLeadsList)) {
            ClearLeadsStatusRecordRequest kfClearLeadsStatusRecordRequest = new ClearLeadsStatusRecordRequest();
            kfClearLeadsStatusRecordRequest.setBid(bid);
            kfClearLeadsStatusRecordRequest.setLeadsIds(kfLeadsIds);
            kfClearLeadsStatusRecordRequest.setStatus(LeadsStatusEnum.RECOVERY.getStatus());
            kfClearLeadsStatusRecordRequest.setHasKf(true);
            leadsServiceV2.clearLeadsStatusRecord(kfClearLeadsStatusRecordRequest);
        }

        if (!CollectionUtils.isEmpty(leadsIds)) {
            ClearLeadsStatusRecordRequest kfClearLeadsStatusRecordRequest = new ClearLeadsStatusRecordRequest();
            kfClearLeadsStatusRecordRequest.setBid(bid);
            kfClearLeadsStatusRecordRequest.setLeadsIds(leadsIds);
            kfClearLeadsStatusRecordRequest.setStatus(LeadsStatusEnum.RECOVERY.getStatus());
            kfClearLeadsStatusRecordRequest.setHasKf(false);
            leadsServiceV2.clearLeadsStatusRecord(kfClearLeadsStatusRecordRequest);
        }
    }

    private void saveRecoveryLeadsFollow(Integer bid, Map<Long, LeadsFollow> needSaveFollow, Long operatorStaffId) {
        if (CollectionUtils.isEmpty(needSaveFollow)) {
            return;
        }
        try {
            Set<Long> staffIds = new HashSet<>(needSaveFollow.keySet());
            staffIds.add(operatorStaffId);
            Map<Long, StaffDto> staffByIds = staffClientForLeads.getStaffByIds(bid, staffIds);

            StaffDto staffDto = staffByIds.get(operatorStaffId);
            String operatorStaffName = staffDto != null ? staffDto.getName() : "";

            needSaveFollow.forEach((staffId, leadsFollow) -> {
                StaffDto temp = staffByIds.get(staffId);
                String content = recoveryContent(operatorStaffName, temp == null ? "" : temp.getName());
                leadsFollow.setFollowContent(content);
            });

            leadsFollowManager.saveBatch(needSaveFollow.values());
            needSaveFollow.forEach((staffId, leadsFollow) -> {
                leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
            });
        } catch (Exception e) {
            logger.info("线索回收保存跟进记录失败：", e);
        }
    }

    private String recoveryContent(String operatorStaffName, String guideStaffName) {
        return "【" + operatorStaffName + "】" + "将线索从跟进人" + "【" + guideStaffName + "】" + "处回收";
    }

    private Leads recoveryLeads(Leads item) {
        Integer status = item.getStatus();
        item.setRecoveryFrom(item.getId());
        // 设置被回收的线索Id
        List<String> ids = strToList(item.getRecoveryFromIds());
        ids.add(String.valueOf(item.getId()));
        item.setRecoveryFromIds(listToStr(ids));
        item.setId(null);
        //重新设置为待分配状态
        item.setStatus(LeadsStatusEnum.TO_DISTRIBUTE.getStatus());
        item.setPayTime(null);
        item.setDistributeTime(null);
        item.setDistributeFollowTime(null);
        // 线索回收不更改导入时间
        item.setUpdateTime(LocalDateTime.now());
        item.setErrorMsg(null);
        item.setDistributeStaffId(0L);
        // 线索有跟进客服：回收后去到“客服接待”列表，对应负责的客服可以看到
        if (item.getPreFollowStaffId() != null && !item.getPreFollowStaffId().equals(0L) && LeadsStatusEnum.getAllocatedLeadsStatus().contains(status)) {
            item.setStatus(LeadsStatusEnum.PRE_FOLLOW.getStatus());
            item.setPreFollowStatus(LeadsStatusEnum.DISTRIBUTED.getStatus());
            item.setDistributeTime(LocalDateTime.now());
            item.setDistributeFollowTime(LocalDateTime.now());
            item.setKfFollowStatuses(InngkeAppConst.EMPTY_STR);
        } else {
            item.setPreFollowStaffId(null);
            item.setPreFollowStatus(null);
            item.setFollowStatuses(InngkeAppConst.EMPTY_STR);
        }
        item.setContactIn24(0);
        item.setBatchId(0L);
        item.setLastFollowId(0L);
        item.setLastFollowTime(null);
        return item;
    }

    private List<String> strToList(String str) {
        if (StringUtils.isEmpty(str)) {
            return new ArrayList<>(0);
        }
        return Lists.newArrayList(str.split(InngkeAppConst.COMMA_STR));
    }

    private String listToStr(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return String.join(",", list);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateLeads(Leads leads, LeadsFollow leadsFollow, LeadsLog leadsLog) {
        boolean updateFlag = this.updateById(leads);
        if (null != leadsFollow) {
            leadsFollowManager.save(leadsFollow);
            leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());
        }
        if (null != leadsLog) {
            leadsLogManager.save(leadsLog);
        }
        return updateFlag;
    }

    @Override
    public Map<String, LocalDateTime> getMobileAndDistributeTime() {
        return leadsDao.getMobileAndDistributeTime();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean editByInfo(LeadsEditByInforRequest request) {
        Long id = request.getId();
        Integer bid = request.getBid();

        UpdateWrapper<Leads> updateWrapper = Wrappers.<Leads>update()
                .eq(Leads.BID, bid).eq(Leads.ID, id)
                .set(Leads.NAME, request.getName());

        Leads newLeads = new Leads();
        BeanUtils.copyProperties(request, newLeads);
        LeadsConverter.updateWrapperSetLeadsInfo(updateWrapper, newLeads);
        //update leads
        boolean isUpdateLeadsSuccess = update(updateWrapper);
        //updateleads ext data
        boolean isUpdateLeadsExtSuccess = true;
        Integer age = request.getAge();
        boolean isHaveNonNullVal = Objects.nonNull(request.getGender()) || Objects.nonNull(age) || com.inngke.common.utils.StringUtils.isNotEmpty(request.getQq()) || com.inngke.common.utils.StringUtils.isNotEmpty(request.getEmail());
        if (isHaveNonNullVal) {
            isUpdateLeadsExtSuccess = leadsExtInformationManager.update(Wrappers.<LeadsExtInformation>update()
                    .eq(LeadsExtInformation.BID, bid).eq(LeadsExtInformation.ID, id)
                    .set(LeadsExtInformation.GENDER, request.getGender()));
        }
        //更新es索引
        AsyncUtils.runAsync(() -> {
            LeadsUpdateRequest leadsUpdateRequest = new LeadsUpdateRequest();
            leadsUpdateRequest.setBid(bid);
            leadsUpdateRequest.setIds(Lists.newArrayList(request.getId()));
            leadsEsService.updateDocs(leadsUpdateRequest);
        });
        return isUpdateLeadsSuccess && isUpdateLeadsExtSuccess;
    }

    @Override
    public Leads getById(Integer bid, Long id) {
        return this.getOne(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, id)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer transferLeads(List<Leads> leadsList, List<Leads> preFollowList, List<LeadsHistoryDistribute> historyDistributes, Long targetStaffId, String targetStaffName, Long agentId) {
        if (CollectionUtils.isEmpty(leadsList)) {
            logger.info("员工转交线索数据leads is empty");
            return 0;
        }
        List<LeadsFollow> leadsFollowList = Lists.newArrayList();

        leadsList.forEach(leads -> {
            leads.setDistributeStaffId(targetStaffId);
            leads.setLastFollowId(0L);
            leads.setLastFollowTime(null);

            String followContent = "转交员工数据时将线索转交给【" + targetStaffName + "】";
            LeadsFollow follow = new LeadsFollow();
            follow.setBid(leads.getBid());
            follow.setLeadsStatus(leads.getStatus());
            follow.setLeadsId(leads.getId());
            follow.setFollowContent(followContent);
            follow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
            follow.setCreateTime(LocalDateTime.now());
            follow.setStaffId(0L);
            follow.setUserId(0L);
            follow.setBeforeLeadsStatus(leads.getStatus());
            leadsFollowList.add(follow);
        });
        List<LeadsHistoryDistribute> preFollowLeadsHistory = new ArrayList<>(preFollowList.size());
        preFollowList.forEach(leads -> {
            leads.setPreFollowStaffId(targetStaffId);
            leads.setLastFollowId(0L);
            leads.setLastFollowTime(null);

            LeadsHistoryDistribute leadsHistoryDistribute = new LeadsHistoryDistribute();
            leadsHistoryDistribute.setBid(leads.getBid());
            leadsHistoryDistribute.setLeadsId(leads.getId());
            leadsHistoryDistribute.setDistributeStaffId(targetStaffId);
            leadsHistoryDistribute.setCreateTime(LocalDateTime.now());
            leadsHistoryDistribute.setType(LeadsHistoryDistributeTypeEnum.FOLLOW_LEADS.getCode());
            preFollowLeadsHistory.add(leadsHistoryDistribute);

            leads.setBid(null);
        });
        historyDistributes.addAll(preFollowLeadsHistory);
        if (!CollectionUtils.isEmpty(preFollowList)) {
            this.updateBatchById(preFollowList);
        }

        if (!CollectionUtils.isEmpty(leadsList)) {
            this.saveOrUpdateBatch(leadsList);
        }

        if (!CollectionUtils.isEmpty(leadsFollowList)) {
            leadsFollowManager.saveBatch(leadsFollowList);
        }
        if (!CollectionUtils.isEmpty(historyDistributes)) {
            leadsHistoryDistributeManager.saveBatch(historyDistributes);
        }

        updateLeadsLastFollowTime(leadsFollowList);

        return leadsList.size();
    }

    @Override
    public LeadsTransferResultDto transferLeads(List<Leads> leadsList, StaffDto targetStaff, LeadsTransferEnum transferEnum) {

        if (CollectionUtils.isEmpty(leadsList)) {
            logger.info("转交线索数据leads is empty");
            return null;
        }
        List<LeadsFollow> leadsFollowList = Lists.newArrayList();
        List<LeadsHistoryDistribute> leadsHistoryDistributeList = Lists.newArrayList();

        List<Leads> transferLeadsList = leadsList.stream().peek(leads -> {
            Long historyDistributeStaffId = leads.getDistributeStaffId();
            // transfer lead
            leads.setDistributeStaffId(targetStaff.getId());

            // builder leadsHistoryDistribute
            LeadsHistoryDistribute historyDistribute = new LeadsHistoryDistribute();
            historyDistribute.setBid(leads.getBid());
            historyDistribute.setLeadsId(leads.getId());
            historyDistribute.setDistributeStaffId(historyDistributeStaffId);
            historyDistribute.setCreateTime(LocalDateTime.now());
            leadsHistoryDistributeList.add(historyDistribute);

            // builder leadsFollow
            LeadsFollow follow = leadsFollowFactory.createFollow(transferEnum);
            follow.setBid(leads.getBid());
            follow.setFollowContent(MessageFormat.format(follow.getFollowContent(), targetStaff.getName()));
            follow.setLeadsStatus(leads.getStatus());
            follow.setLeadsId(leads.getId());
            follow.setFollowType(LeadsFollowTypeEnum.SYSTEM.getCode());
            follow.setCreateTime(LocalDateTime.now());
            follow.setStaffId(0L);
            follow.setUserId(0L);
            follow.setBeforeLeadsStatus(leads.getStatus());
            leadsFollowList.add(follow);
        }).collect(Collectors.toList());

        LeadsTransferResultDto resultDto = new LeadsTransferResultDto();
        resultDto.setTransferLeadsList(transferLeadsList);
        resultDto.setTransferLeadsFollowList(leadsFollowList);
        resultDto.setTransferLeadsHistoryDistributeList(leadsHistoryDistributeList);
        updateLeadsLastFollowTime(leadsFollowList);
        return resultDto;
    }

    private void  updateLeadsLastFollowTime(List<LeadsFollow> leadsFollowList){
        if (CollectionUtils.isEmpty(leadsFollowList)){
            return;
        }
        leadsManager.updateBatchById(
                leadsFollowList.stream().map(leadsFollow->{
                    Leads leads = new Leads();
                    leads.setBid(leadsFollow.getBid());
                    leads.setId(leadsFollow.getLeadsId());
                    leads.setLastFollowTime(leadsFollow.getCreateTime());
                    leads.setLastFollowId(leadsFollow.getId());
                    return leads;
                }).collect(Collectors.toList())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean doTransferLeads(LeadsTransferResultDto leadsTransferResultDto) {
        if (Objects.isNull(leadsTransferResultDto)) {
            return false;
        }
        if (!CollectionUtils.isEmpty(leadsTransferResultDto.getTransferLeadsList())) {
            this.saveOrUpdateBatch(leadsTransferResultDto.getTransferLeadsList());
        }
        if (!CollectionUtils.isEmpty(leadsTransferResultDto.getTransferLeadsFollowList())) {
            leadsFollowManager.saveBatch(leadsTransferResultDto.getTransferLeadsFollowList());
        }
        if (!CollectionUtils.isEmpty(leadsTransferResultDto.getTransferLeadsHistoryDistributeList())) {
            leadsHistoryDistributeManager.saveBatch(leadsTransferResultDto.getTransferLeadsHistoryDistributeList());
        }
        return true;
    }

    @Override
    public List<PreFollowStaffReportDto> listByPreFollowStaffIds(GetPreFollowStaffReportRequest request, List<Long> staffIds) {
        return this.baseMapper.getStatisticsByPreFollowStaffIds(request, staffIds);
    }


    /**
     * 导入线索数据并更新批次号状态
     *
     * @param leadsInformationExtends 线索列表
     * @param leadsBatch              批次信息
     * @param operator                操作员编号
     * @return 批量导入后线索的编号集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Set<Long> importLeadsAndUpdateLeadsBatch(List<LeadsInformationExtend> leadsInformationExtends, LeadsBatch leadsBatch, Long operator) {
        //设置临时externalId
        Map<String, Leads> leadsMap = new HashMap<>(leadsInformationExtends.size());
        for (int i = 0; i < leadsInformationExtends.size(); i++) {
            leadsInformationExtends.get(i).setExternalId(Integer.toString(i));
            leadsMap.put(Integer.toString(i), LeadsDraftConverter.toLeads(leadsInformationExtends.get(i)));
        }
        //插入leads
        ArrayList<Leads> leadsList = Lists.newArrayList(leadsMap.values());
        this.batchSaveLeads(leadsList, operator);

        List<LeadsExtInformation> leadsExtInformationList = leadsInformationExtends.stream()
                .filter((leadsInformationExtend -> !LeadsTypeEnum.ORDER.getCode().equals(leadsInformationExtend.getType())))
                .map(LeadsDraftConverter::toLeadsExtInformation).collect(Collectors.toList());
        leadsExtInformationList.forEach(leadsExtInformation -> {
            leadsExtInformation.setId(leadsMap.get(leadsExtInformation.getExternalId()).getId());
            leadsExtInformation.setExternalId(leadsMap.get(leadsExtInformation.getExternalId()).getTpLeadsId());
        });
        if (!CollectionUtils.isEmpty(leadsExtInformationList)) {
            leadsExtInformationManager.saveBatch(leadsExtInformationList);
        }

        leadsBatch.setProcessStatus(LeadsBatchStatusEnum.IMPORTED.getStatus());
        //修改批次状态
        leadsBatchManager.updateById(leadsBatch);
        return leadsList.stream().map(Leads::getId).collect(Collectors.toSet());
    }

    /**
     * 批量保存
     *
     * @param leadsList 线索集合
     */
    @Override
    public int batchSaveLeads(List<Leads> leadsList, Long operator) {
        //记录成功导入的记录数
        int successRow = 0;
        if (CollectionUtils.isEmpty(leadsList)) {
            return successRow;
        }
        LocalDateTime now = LocalDateTime.now();
        for (Leads leads : leadsList) {
            if (!StringUtils.isEmpty(leads.getErrorMsg())) {
                continue;
            }

            Long draftId = leads.getId();
            leads.setId(null);
            try {
                //补充默认的是否在24小时内联系 0否 1是 默认为0,未在24小时联系
                leads.setContactIn24(0);
                boolean save = this.save(leads);
                if (save) {
                    successRow += 1;
                    //插入日志
                    saveLeadsLog(leads, operator, now);
                }
            } catch (Exception e) {
                logger.error("导入线索失败，draft表id是{}", draftId);
                LeadsDraft draft = leadsDraftManager.getById(draftId);
                draft.setErrorMsg(draft.getErrorMsg() + e.getCause().getMessage());
                leadsDraftManager.updateById(draft);
            }

        }
        return successRow;
    }

    /**
     * 修改线索状态
     *
     * @param leads      线索数据
     * @param operatorId 操作员编号
     * @param followInfo 跟进信息
     * @param status     状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Leads leads, Long operatorId, String followInfo, Integer status) {
        Integer bid = leads.getBid();
        Long id = leads.getId();
        UpdateWrapper<Leads> leadsSet = Wrappers.<Leads>update()
                .eq(Leads.BID, bid)
                .eq(Leads.ID, id)
                .set(Leads.STATUS, status)
                .set(Objects.nonNull(leads.getClientId()), Leads.CLIENT_ID, leads.getClientId())
                .set(Objects.nonNull(leads.getRelationClientTime()), Leads.RELATION_CLIENT_TIME, leads.getRelationClientTime());
        //如果修改成已成交的状态则将支付时间填充为后台操作员修改为已成交状态的时间
        if (LeadsStatusEnum.TRADED.getStatus() == status) {
            leadsSet.set(Leads.PAY_TIME, LocalDateTime.now());
        }
        //记录首次联系时间
        if (LeadsStatusEnum.getAllCONTACT().contains(status) && Objects.isNull(leads.getFirstContactTime())) {
            leadsSet.set(Leads.FIRST_CONTACT_TIME,LocalDateTime.now());
        }

        update(leadsSet);
        LeadsFollow leadsFollow = new LeadsFollow();
        leadsFollow.setBid(bid);
        leadsFollow.setFollowType(LeadsFollowTypeEnum.MANUAL.getCode());
        leadsFollow.setLeadsStatus(status);
        leadsFollow.setFollowContent(followInfo);
        leadsFollow.setLeadsId(id);
        StaffDto staffDto = staffClientForLeads.getStaffByCid(bid, operatorId);
        leadsFollow.setUserId(operatorId);
        if (Objects.nonNull(staffDto)) {
            leadsFollow.setStaffId(staffDto.getId());
        }
        leadsFollow.setBeforeLeadsStatus(leads.getStatus());
        leadsFollow.setCreateTime(LocalDateTime.now());
        leadsFollowManager.save(leadsFollow);
        leadsFollowCacheService.add(leadsFollow.getBid(),leadsFollow.getId());

        LeadsLog leadsLog = new LeadsLog();
        leadsLog.setCreateTime(LocalDateTime.now());
        //状态修改，为0时代表管理人员
        leadsLog.setDistributeStaffId(leads.getDistributeStaffId());
        //0为待分配
        leadsLog.setStatusChange(status);
        leadsLog.setLogContent(followInfo);
        leadsLog.setOperatorId(operatorId);
        leadsLog.setBid(bid);
        leadsLog.setLeadsId(id);

        leadsLogManager.save(leadsLog);
    }


    private void saveLeadsLog(Leads leads, Long operator, LocalDateTime time) {
        LeadsLog log = new LeadsLog();
        log.setBid(leads.getBid());
        log.setLeadsId(leads.getId());
        log.setOperatorId(operator);
        log.setStatusChange(LeadsStatusEnum.TO_DISTRIBUTE.getStatus());
        log.setCreateTime(time);
        leadsLogManager.save(log);
    }

    /**
     * 分页统计线索
     *
     * @param wrapper 查询条件
     * @return 线索分页集合数据
     */
    @Override
    public List<LeadsStatisticsDto> pageStatistics(QueryWrapper<Leads> wrapper) {
        return baseMapper.pageStatistics(wrapper);
    }


    @Override
    public List<Leads> selectBadCustomerLeads(Integer bid) {
        return leadsDao.selectBadCustomerLeads();
    }

    @Override
    public List<Leads> listByKeyword(ListByMobileOrWeChatRequest request) {
        if (StringUtils.isBlank(request.getMobile()) && StringUtils.isBlank(request.getWeChat())){
            return Lists.newArrayList();
        }

        QueryWrapper<Leads> queryWrapper = Wrappers.<Leads>query()
                .eq(Leads.BID, request.getBid())
                .ne(Objects.nonNull(request.getId()), Leads.ID, request.getId())
                .in(Leads.STATUS, LeadsStatusEnum.getUsedLeadsStatus())
                .notIn(!ObjectUtils.isEmpty(request.getExcludeIds()), Leads.ID, request.getExcludeIds())
                .select(Leads.ID, Leads.NAME, Leads.MOBILE, Leads.WE_CHAT, Leads.CREATE_TIME, Leads.DISTRIBUTE_STAFF_ID, Leads.PRE_FOLLOW_STAFF_ID)
                .and(query -> {
                    if (StringUtils.isNotBlank(request.getMobile())) {
                        query.eq(Leads.MOBILE, request.getMobile()).or().eq(Leads.WE_CHAT, request.getMobile());
                    }
                    if (StringUtils.isNotBlank(request.getWeChat())) {
                        if (StringUtils.isNotBlank(request.getMobile())){
                            query.or();
                        }
                        query.eq(Leads.MOBILE, request.getWeChat()).or().eq(Leads.WE_CHAT, request.getWeChat());
                    }
                    return query;
                });
        return list(queryWrapper);
    }

    @Override
    public List<Leads> findLeadsByClient(Integer bid, Long clientId) {
        return this.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .eq(Leads.CLIENT_ID, clientId)
        );
    }

    @Override
    public List<Leads> findLeadsByClientIds(Integer bid, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return this.list(
                Wrappers.<Leads>query()
                        .eq(Leads.BID, bid)
                        .in(Leads.CLIENT_ID, ids)
        );
    }

    @Override
    public boolean updateClientStatus(Integer bid, Long id, Integer status) {
        return this.update(
                Wrappers.<Leads>update()
                        .eq(Leads.BID, bid)
                        .eq(Leads.ID, id)
                        .set(Leads.CLIENT_STATUS, status)
        );
    }

    @Override
    public boolean updateLeadsFollowTimeByClientId(Integer bid, Long clientId, LocalDateTime createTime) {
        boolean update = this.update(Wrappers.<Leads>update()
                .eq(Leads.BID, bid)
                .eq(Leads.CLIENT_ID, clientId)
                .set(Leads.LAST_FOLLOW_TIME, createTime)
        );

        if (update){
            List<Leads> leadsList = this.findLeadsByClient(bid, clientId);
            List<Long> leadsIds = leadsList.stream().map(Leads::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leadsIds)){
                return update;
            }

            LeadsUpdateRequest request = new LeadsUpdateRequest();
            request.setNotifyPartner(false);
            request.setRefreshEs(true);
            request.setIds(leadsIds);
            request.setBid(bid);

            leadsEsService.updateDocs(request);
        }
        return update;
    }

    @Override
    public List<Leads> findRelationClientAndNotContactLeads(Integer bid) {
        return leadsDao.findRelationClientAndNotContactLeads(bid);
    }

    @Override
    public List<Leads> getByIds(Set<Long> leadsIds) {
        return this.list(Wrappers.<Leads>query().in(Leads.ID, leadsIds));
    }


    /**
     * 拒绝线索退回，重构退回前的线索信息
     *
     * @return 线索信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long rejectPushBackRollbackLeads(LeadsPushBackLog leadsPushBackLog) {
        if (Objects.isNull(leadsPushBackLog)) {
            return null;
        }
        LeadsSnapshotDto leadsSnapshot = JsonUtil.jsonToObject(leadsPushBackLog.getLeadsSnapshot(), LeadsSnapshotDto.class);
        if (Objects.isNull(leadsSnapshot)) {
            return null;
        }

        LeadsFollow leadsFollow = LeadsFollow.builder()
                .id(SnowflakeHelper.getId())
                .bid(leadsPushBackLog.getBid())
                .leadsId(leadsPushBackLog.getLeadsId())
                .userId(leadsPushBackLog.getRejectBy())
                .staffId(leadsPushBackLog.getRejectBy())
                .operatorRole(2)
                .followType(1)
                .followContent("线索退回拒绝\n退回原因：" + leadsPushBackLog.getRejectReason())
                .beforeLeadsStatus(LeadsStatusEnum.PUSH_BACK.getStatus())
                .leadsStatus(leadsPushBackLog.getLeadsStatus())
                .createTime(LocalDateTime.now())
                .build();

        boolean update = leadsManager.update(
                Wrappers.<Leads>update()
                        .eq(Leads.ID, leadsPushBackLog.getLeadsId())
                        .eq(Leads.BID, leadsPushBackLog.getBid())
                        .set(Leads.STATUS, leadsPushBackLog.getLeadsStatus())
                        .set(Leads.DISTRIBUTE_STAFF_ID, leadsSnapshot.getDistributeStaffId())
                        .set(Leads.PUSH_BACK_TIME, null)
                        .set(Leads.PUSH_BACK_STAFF_ID, 0)
                        .set(Leads.ERROR_MSG, "")
                        .set(Leads.LAST_FOLLOW_ID, leadsFollow.getId())
                        .set(Leads.LAST_FOLLOW_TIME, leadsFollow.getCreateTime())
        );


        boolean leadsFollowFlag = leadsFollowManager.save(leadsFollow);

        return (update && leadsFollowFlag) ? leadsPushBackLog.getLeadsId() : null;
    }

}

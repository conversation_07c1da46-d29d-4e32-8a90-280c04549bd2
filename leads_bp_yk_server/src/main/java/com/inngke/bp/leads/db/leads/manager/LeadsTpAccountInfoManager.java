/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.bp.leads.db.leads.manager;

import com.inngke.bp.leads.db.leads.entity.LeadsTpAccountInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 广告平台授权用户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
public interface LeadsTpAccountInfoManager extends IService<LeadsTpAccountInfo> {

}

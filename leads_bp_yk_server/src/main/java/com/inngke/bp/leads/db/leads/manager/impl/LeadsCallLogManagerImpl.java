package com.inngke.bp.leads.db.leads.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.inngke.bp.leads.consts.LeadsServiceConsts;
import com.inngke.bp.leads.db.leads.dao.LeadsCallLogDao;
import com.inngke.bp.leads.db.leads.entity.LeadsCallLog;
import com.inngke.bp.leads.db.leads.manager.LeadsCallLogManager;
import com.inngke.common.ds.annotation.DS;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@DS(LeadsServiceConsts.DS_APP_ID_SELECTOR)
public class LeadsCallLogManagerImpl extends ServiceImpl<LeadsCallLogDao, LeadsCallLog> implements LeadsCallLogManager {

    @Override
    public Map<Long, LocalDateTime> getLeadsFirstCallTime(Integer bid, Set<Long> leadsIds) {
        if (CollectionUtils.isEmpty(leadsIds)){
            return Maps.newHashMap();
        }

        List<LeadsCallLog> list = list(Wrappers.<LeadsCallLog>query().eq(LeadsCallLog.BID, bid)
                .in(LeadsCallLog.LEADS_ID, leadsIds)
                .groupBy(LeadsCallLog.LEADS_ID).orderByAsc(LeadsCallLog.CREATE_TIME)
        );

        return list.stream().collect(Collectors.toMap(LeadsCallLog::getLeadsId,LeadsCallLog::getCreateTime));
    }

    @Override
    public LocalDateTime getLeadsFirstCallTime(Integer bid, Long leadsId) {
        return Optional.ofNullable(getOne(Wrappers.<LeadsCallLog>query().eq(LeadsCallLog.BID,bid)
                .eq(LeadsCallLog.LEADS_ID,leadsId)
                .orderByDesc(LeadsCallLog.CREATE_TIME).last("limit 1").select(LeadsCallLog.CREATE_TIME))
        ).map(LeadsCallLog::getCreateTime).orElse(null);
    }

    @Override
    public List<LeadsCallLog> getLeadsCallLog(Integer bid, Long id,Integer type) {
        return list(
                Wrappers.<LeadsCallLog>query()
                        .eq(LeadsCallLog.BID, bid)
                        .eq(LeadsCallLog.LEADS_ID, id)
                        .eq(LeadsCallLog.TYPE, type)
        );
    }
}

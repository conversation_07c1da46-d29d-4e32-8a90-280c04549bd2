package com.inngke.bp.leads.core.utils;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.dto.request.LeadsIdsRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.bp.organize.dto.request.staff.StaffListRequest;
import com.inngke.bp.organize.dto.response.StaffDto;
import com.inngke.bp.user.dto.request.customer.CustomerInfoGetByStaffIdRequest;
import com.inngke.bp.user.dto.request.staff.QyUserTokenGetRequest;
import com.inngke.bp.user.dto.response.CustomerDto;
import com.inngke.bp.user.service.CustomerGetService;
import com.inngke.bp.user.service.QyWxSelfAppUserService;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.wx.core.utils.QyWxMessageBuilder;
import com.inngke.ip.common.dto.request.MqSendRequest;
import com.inngke.ip.common.dto.response.WxMpSimpleInfoDto;
import com.inngke.ip.common.service.MqService;
import com.inngke.ip.common.service.wx.WxMpCommonService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class LeadsQyWxTemplateMessageUtils {

    private Logger logger = LoggerFactory.getLogger(LeadsQyWxTemplateMessageUtils.class);

    private static final String STR_NOTIFY_SEND = "notify_send";

    private static final String STR_TEMPLATEID_REFUND = "OPENTM412319518";

    private static final String STR_TEMPLATEPATH_REFUND = "Clue/Clue/myClue/myClue?id=%s";

    @Autowired
    private JsonService jsonService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private MqService mqService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private CustomerGetService customerGetService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private WxMpCommonService wxMpCommonService;

    @Autowired
    private LeadsService leadsService;

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.user_bp_yk:}")
    private QyWxSelfAppUserService qyWxSelfAppUserService;

    @Autowired
    private StaffClientForLeads staffClientForLeads;

    /**
     * 发送给分销员客户成交消息
     * @param bid
     * @param leadsId
     * @return
     */
    public BaseResponse sendLeadsRefundMessage(Integer bid,Long leadsId) {
        String appId = getWxMpAppId(bid);
        LeadsDto leadsDto = getLeads(bid, leadsId);
        String wxPubOpenId = getWxPubOpenId(bid, leadsDto.getDistributeStaffId());
        if(StringUtils.isEmpty(wxPubOpenId)){
            return BaseResponse.error("qyUserId空");
        }

        QyUserTokenGetRequest qyUserTokenGetRequest = new QyUserTokenGetRequest();
        qyUserTokenGetRequest.setQyUserId(wxPubOpenId);
        qyUserTokenGetRequest.setBid(bid);
        BaseResponse<String> userToken = qyWxSelfAppUserService.getUserToken(qyUserTokenGetRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(userToken)) {
            logger.error("发送企业微信失败，获取user对应的token失败, msg={}", userToken.getMsg());
            return BaseResponse.error("发送企业微信失败，获取user对应的token失败！");
        }

        QyWxMessageBuilder qyWxMessageBuilder = QyWxMessageBuilder.create();
        MqSendRequest mqSendRequest = qyWxMessageBuilder
                .setMsgType("miniprogram_notice")
                .setAppId(appId)
                .setPage(String.format(STR_TEMPLATEPATH_REFUND, leadsId))
                .setTitle("退款通知")
                .setToken(userToken.getData())
                .setDescription("有一位客户发起退款")
                .setToUser(wxPubOpenId)
                .putContentItem("商品名称", dealGoodsName(leadsDto.getGoodsName()))
                .putContentItem("客户姓名", leadsDto.getName())
                .putContentItem("客户电话", dealMobile(leadsDto))
                .putContentItem("订单时间", dealTime(leadsDto.getPayTime()))
                .getMqSendRequest(bid, 0L, QyWxMessageBuilder.QY_WX);
        try {
            mqService.send(mqSendRequest);
        } catch (Exception e) {
            logger.error("发送企业微信模板消息失败！", e);
            return BaseResponse.error("发送企业微信模板消息失败！");
        }
        return BaseResponse.success();
    }

    private String dealGoodsName(String goodsName){
        if(StringUtils.isNoneEmpty(goodsName)){
            return goodsName;
        }
        return "暂无信息";
    }

    private String dealMobile(LeadsDto leadsDto){
        String mobile = leadsDto.getMobile();
        if(StringUtils.isNoneEmpty(mobile)){
            if(leadsDto.getStatus()!=null&&leadsDto.getStatus().equals(1)){
                if(mobile.length()==11){
                    return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
                }
            }
            return mobile;
        }
        return "暂无信息";
    }

    private String dealTime(Long dateTime) {
        if(dateTime==null){
            return "暂无信息";
        }
        return DateTimeUtils.format(DateTimeUtils.MillisToLocalDateTime(dateTime),DateTimeUtils.YYYY_MM_DD_HH_MM_SS);
    }

    private LeadsDto getLeads(Integer bid, Long leadsId){
        LeadsIdsRequest leadsIdsRequest = new LeadsIdsRequest();
        leadsIdsRequest.setBid(bid);
        leadsIdsRequest.setIds(Arrays.asList(leadsId));
        BaseResponse<List<LeadsDto>> baseResponse = leadsService.getLeadsListByIds(leadsIdsRequest);
        if(!BaseResponse.responseSuccessWithNonNullData(baseResponse)) {
            logger.error("获取线索失败！resp={}", jsonService.toJson(baseResponse));
            throw new InngkeServiceException("获取线索失败！");
        }
        List<LeadsDto> leadsDtoList = baseResponse.getData();
        if(leadsDtoList.size() == 0) {
            logger.error("获取线索失败！resp={}", jsonService.toJson(baseResponse));
            throw new InngkeServiceException("获取线索失败！");
        }
        return leadsDtoList.get(0);
    }

    private String getWxMpAppId(Integer bid) {
        BaseBidRequest baseBidRequest = new BaseBidRequest();
        baseBidRequest.setBid(bid);
        BaseResponse<WxMpSimpleInfoDto> baseResponse = wxMpCommonService.getWxMpAppInfo(baseBidRequest);
        if (!BaseResponse.responseSuccessWithNonNullData(baseResponse)) {
            logger.error("获取小程序appId失败！resp={}", jsonService.toJson(baseResponse));
            throw new InngkeServiceException("获取小程序appId失败！");
        }
        return baseResponse.getData().getAppId();
    }

    private String getWxPubOpenId(int bid, Long staffId) {
        StaffListRequest staffListRequest = new StaffListRequest();
        staffListRequest.setBid(bid);
        staffListRequest.setId(staffId);
        List<StaffDto> staffList;

        try {
            staffList = staffClientForLeads.getStaffList(staffListRequest);
        } catch (Exception e) {
            logger.error("获取qyUserId失败！staffId={}", staffId);
            throw new InngkeServiceException("获取qyUserId失败！");
        }
        return staffList.get(0).getQyUserId();
    }
}

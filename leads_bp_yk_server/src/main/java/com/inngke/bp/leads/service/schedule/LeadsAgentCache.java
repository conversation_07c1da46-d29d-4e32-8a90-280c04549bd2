package com.inngke.bp.leads.service.schedule;

import com.inngke.bp.leads.client.StaffClientForLeads;
import com.inngke.bp.leads.dto.SimpleAgentDto;
import com.inngke.common.core.config.privatization.PrivatizationDb;
import com.inngke.common.service.impl.BaseBidDataCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 经销商名称缓存
 *
 * <AUTHOR>
 */
@Component
public class LeadsAgentCache extends BaseBidDataCacheService<Map<Long, SimpleAgentDto>> {

    private static final Logger logger = LoggerFactory.getLogger(LeadsAgentCache.class);


    // @Scheduled(fixedRate = 60000)
    public void refresh() {
        loadData();
    }

    @Autowired
    public StaffClientForLeads staffClientForLeads;

    @Override
    protected String getDataName() {
        return "经销商";
    }

    @Override
    protected int loadData(PrivatizationDb privatizationDb, LocalDateTime localDateTime) {
        // 查询所有的员工经销商信息
        // BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        // List<StaffDepartmentAgentSimpleDto> staffDepartmentAgentSimpleList = staffClientForLeads.getStaffDepartmentAgentSimple(baseIdsRequest);
        //
        // if (Objects.isNull(staffDepartmentAgentSimpleList)) {
        //     logger.info("没有查询到相关的员工经销商关联数据");
        //     return 0;
        // }
        //
        // // 查询出离职员工
        // StaffListRequest noExistStaffListRequest = new StaffListRequest();
        // noExistStaffListRequest.setEnable(0);
        // Map<Long, StaffDto> noExistStaffMap = staffClientForLeads.getStaffList(noExistStaffListRequest).stream().collect(Collectors.toMap(StaffDto::getId, t->t, (o, n) -> n));
        //
        // if (CollectionUtils.isEmpty(staffDepartmentAgentSimpleList)) {
        //     return 0;
        // }
        //
        // staffDepartmentAgentSimpleList.forEach(staffDepartmentAgentSimpleDto -> {
        //     Integer bid = staffDepartmentAgentSimpleDto.getBid();
        //     Long staffId = staffDepartmentAgentSimpleDto.getId();
        //     String agentName = staffDepartmentAgentSimpleDto.getAgentName();
        //     Long agentId = staffDepartmentAgentSimpleDto.getAgentId();
        //     if (Objects.nonNull(noExistStaffMap) && noExistStaffMap.get(staffDepartmentAgentSimpleDto.getId()) != null) {
        //         //无效
        //         Map<Long, SimpleAgentDto> agentDtoMap = getData(bid);
        //         if (!Objects.isNull(agentDtoMap)) {
        //             agentDtoMap.remove(staffId);
        //         }
        //     } else {
        //         SimpleAgentDto dto = new SimpleAgentDto();
        //         dto.setAgentId(agentId);
        //         dto.setAgentName(agentName);
        //         // 一般都是通过员工去获取经销商的 key-bid value-[key-staffId value-simpleAgentDto]
        //         this.bidDataMap.computeIfAbsent(bid, beid -> Maps.newHashMap()).put(staffId, dto);
        //     }
        // });
        //
        // return staffDepartmentAgentSimpleList.size();
        return 0;
    }

}

package com.inngke.bp.leads.service.strategy.leadsFollowTime;

import com.inngke.bp.leads.db.leads.entity.LeadsFollowTime;
import com.inngke.bp.leads.dto.response.LeadsEsDto;
import com.inngke.bp.leads.dto.response.LeadsFollowTimeByStaffResponse;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.utils.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 量尺率
 * @author: moqinglong
 * @chapter
 * @section
 * @since 2022/4/7 10:51
 */
@Component(value = "leadsFollowTimeMeasuringStrategy")
public class LeadsFollowTimeMeasuringStrategy implements LeadsFollowTimeStrategy {
    @Override
    public LeadsFollowTime process(LeadsFollowTime leadsFollowTime, long time) {
        leadsFollowTime.setStateMeasuring(time);
        return leadsFollowTime;
    }

    @Override
    public void process(TermsAggregationBuilder aggregation) {
        aggregation.subAggregation(
                AggregationBuilders.filter(
                        "stateMeasuring",
                        QueryBuilders.boolQuery().should(QueryBuilders.termQuery("statusLog", LeadsStatusEnum.MEASURED.getStatus()))
                                .should(QueryBuilders.termQuery(LeadsEsDto.STATUS, LeadsStatusEnum.MEASURED.getStatus()))
                )
        );
    }

    @Override
    public void process(Aggregations bucketAggregations, LeadsFollowTimeByStaffResponse dto) {
        ParsedFilter stateMeasuring = bucketAggregations.get("stateMeasuring");
        long count = stateMeasuring.getDocCount();
        dto.setStateMeasuring((int)count);
        if(dto.getLeadsCount()==0){
            return;
        }
        // 量尺率 = 量尺数 / 有效客户数
        ParsedFilter contactFilter = bucketAggregations.get("validCount");
        long contactCount = contactFilter.getDocCount();
        if (contactCount > 0L) {
            String rate = BigDecimal.valueOf(count).multiply(BigDecimal.valueOf(100)).
                    divide(BigDecimal.valueOf(contactCount),2, RoundingMode.HALF_DOWN).toString();
            dto.setMeasuringRate(rate+"%");
        }
    }

    @Override
    public void process(List<LeadsFollowTimeByStaffResponse> dtos, LeadsFollowTimeByStaffResponse dto) {
        Integer sum = dtos.stream().map(LeadsFollowTimeByStaffResponse::getStateMeasuring).reduce(Integer::sum).get();
        dto.setStateMeasuring(sum);
        if(dto.getStateAvail() == 0){
            return;
        }
        String rate = BigDecimal.valueOf(sum).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getStateAvail()),2, RoundingMode.HALF_UP).toString();
        dto.setMeasuringRate(rate+"%");
    }
}

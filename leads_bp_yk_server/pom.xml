<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.inngke.bp</groupId>
    <artifactId>leads_bp_yk_server</artifactId>
    <version>${leads_bp_yk_server.version}</version>

    <properties>
        <jdk.version>11</jdk.version>
        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        <spring-boot.version>2.2.6.RELEASE</spring-boot.version>
        <cos-sts-java.version>3.0.8</cos-sts-java.version>
        <cos_api.version>5.6.45</cos_api.version>
        <cos-sts-java.version>3.0.8</cos-sts-java.version>
        <commons-io.version>2.4</commons-io.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>2.0.3</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>merchant_bp_yk_api</artifactId>
            <version>${merchant_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>leads_bp_yk_api</artifactId>
            <version>${leads_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>reach_ip_yk_api</artifactId>
            <version>${reach_ip_yk_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-es</artifactId>
            <version>${yk-common-es.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>store_bp_yk_api</artifactId>
            <version>${store_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-utils</artifactId>
            <version>${yk-common-utils.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>common_ip_yk_api</artifactId>
            <version>${common_ip_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-app-brand</artifactId>
            <version>${yk-common-app-brand.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-db-card</artifactId>
            <version>${yk-common-db-card.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-cache</artifactId>
            <version>${yk-common-cache.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>user_bp_yk_api</artifactId>
            <version>${user_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>organize_bp_yk_api</artifactId>
            <version>${organize_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>client_bp_yk_api</artifactId>
            <version>${client_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>content_bp_yk_api</artifactId>
            <version>${content_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-wx-proxy</artifactId>
            <version>${yk-common-wx-proxy.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-mq-core</artifactId>
            <version>${yk-common-mq-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.tencent.cloud</groupId>
            <artifactId>cos-sts-java</artifactId>
            <version>${cos-sts-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>${cos_api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>

        <dependency>
            <groupId>com.tencent.ads</groupId>
            <artifactId>marketing-api-java-sdk</artifactId>
            <version>1.1.37</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>distribute_bp_yk_api</artifactId>
            <version>${distribute_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-attachment</artifactId>
            <version>${yk-common-attachment.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>shop_bp_yk_api</artifactId>
            <version>${shop_bp_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.bp</groupId>
            <artifactId>plus_bp_yk_api</artifactId>
            <version>${plus_bp_yk_api.version}</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>junit-jupiter-api</artifactId>
                    <groupId>org.junit.jupiter</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--excel处理工具-->
        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-excel-utils</artifactId>
            <version>${yk-common-excel-utils.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>auth_ip_yk_api</artifactId>
            <version>${auth_ip_yk_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>auth_ip_yk_rbac_api</artifactId>
            <version>${auth_ip_yk_rbac_api.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.inngke.common</groupId>-->
<!--            <artifactId>operator-log</artifactId>-->
<!--            <version>${operator-log.version}</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-dynamic-datasource</artifactId>
            <version>${yk-common-dynamic-datasource.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jdbc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jdbc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.7.19</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-notify</artifactId>
            <version>${yk-common-notify.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-api</artifactId>
            <version>3.0.4</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <leads_bp_yk_server.version>2.0.0-SNAPSHOT</leads_bp_yk_server.version>
                <leads_bp_yk_api.version>2.0.0-SNAPSHOT</leads_bp_yk_api.version>
                <yk-common-excel-utils.version>2.0.0-SNAPSHOT</yk-common-excel-utils.version>

                <plus_bp_yk_api.version>2.0.0-SNAPSHOT</plus_bp_yk_api.version>
                <merchant_bp_yk_api.version>2.0.0-SNAPSHOT</merchant_bp_yk_api.version>
                <yk-common-es.version>1.0.0-SNAPSHOT</yk-common-es.version>
                <store_bp_yk_api.version>2.0.0-SNAPSHOT</store_bp_yk_api.version>
                <yk-common-utils.version>2.0.0-SNAPSHOT</yk-common-utils.version>
                <common_ip_yk_api.version>2.0.0-SNAPSHOT</common_ip_yk_api.version>
                <yk-common-app-brand.version>2.0.0-SNAPSHOT</yk-common-app-brand.version>
                <yk-common-db-card.version>2.0.0-SNAPSHOT</yk-common-db-card.version>
                <user_bp_yk_api.version>2.0.0-SNAPSHOT</user_bp_yk_api.version>
                <organize_bp_yk_api.version>2.0.0-SNAPSHOT</organize_bp_yk_api.version>
                <auth_ip_yk_api.version>2.0.0-SNAPSHOT</auth_ip_yk_api.version>
                <auth_ip_yk_rbac_api.version>2.0.0-SNAPSHOT</auth_ip_yk_rbac_api.version>
                <yk-common-wx-proxy.version>1.0.0-SNAPSHOT</yk-common-wx-proxy.version>
                <yk-common-mq-core.version>2.0.0-SNAPSHOT</yk-common-mq-core.version>
                <reach_ip_yk_api.version>2.0.0-SNAPSHOT</reach_ip_yk_api.version>
                <cos-sts-java.version>3.0.8</cos-sts-java.version>
                <yk-common-cache.version>2.0.0-SNAPSHOT</yk-common-cache.version>
                <distribute_bp_yk_api.version>2.0.0-SNAPSHOT</distribute_bp_yk_api.version>
                <yk-common-attachment.version>1.0.0-SNAPSHOT</yk-common-attachment.version>
                <shop_bp_yk_api.version>2.0.0-SNAPSHOT</shop_bp_yk_api.version>
                <operator-log.version>1.0.0-SNAPSHOT</operator-log.version>
                <client_bp_yk_api.version>2.0.0-SNAPSHOT</client_bp_yk_api.version>
                <yk-common-dynamic-datasource.version>2.0.0-SNAPSHOT</yk-common-dynamic-datasource.version>
                <yk-common-notify.version>1.0.0-SNAPSHOT</yk-common-notify.version>
                <content_bp_yk_api.version>2.0.0-SNAPSHOT</content_bp_yk_api.version>
            </properties>
            <!-- master分支为false，否则为true -->
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <leads_bp_yk_server.version>3.0.278</leads_bp_yk_server.version>
                <leads_bp_yk_api.version>3.0.86</leads_bp_yk_api.version>
                <yk-common-excel-utils.version>1.0.10</yk-common-excel-utils.version>

                <plus_bp_yk_api.version>3.0.26</plus_bp_yk_api.version>
                <merchant_bp_yk_api.version>3.0.2</merchant_bp_yk_api.version>
                <yk-common-es.version>3.0.0</yk-common-es.version>
                <store_bp_yk_api.version>3.0.32</store_bp_yk_api.version>
                <yk-common-utils.version>3.0.8</yk-common-utils.version>
                <common_ip_yk_api.version>3.0.27</common_ip_yk_api.version>
                <yk-common-app-brand.version>3.0.25</yk-common-app-brand.version>
                <yk-common-db-card.version>3.0.12</yk-common-db-card.version>
                <user_bp_yk_api.version>3.0.60</user_bp_yk_api.version>
                <organize_bp_yk_api.version>3.0.78</organize_bp_yk_api.version>
                <auth_ip_yk_api.version>3.0.4</auth_ip_yk_api.version>
                <auth_ip_yk_rbac_api.version>3.0.21</auth_ip_yk_rbac_api.version>
                <yk-common-wx-proxy.version>3.0.8</yk-common-wx-proxy.version>
                <yk-common-mq-core.version>1.0.5</yk-common-mq-core.version>
                <reach_ip_yk_api.version>3.0.11</reach_ip_yk_api.version>
                <cos-sts-java.version>3.0.8</cos-sts-java.version>
                <yk-common-cache.version>1.0.3</yk-common-cache.version>
                <distribute_bp_yk_api.version>3.0.28</distribute_bp_yk_api.version>
                <yk-common-attachment.version>1.0.1</yk-common-attachment.version>
                <shop_bp_yk_api.version>3.0.29</shop_bp_yk_api.version>
                <operator-log.version>1.0.0</operator-log.version>
                <client_bp_yk_api.version>3.1.76</client_bp_yk_api.version>
                <yk-common-dynamic-datasource.version>1.1.8</yk-common-dynamic-datasource.version>
                <yk-common-notify.version>1.0.0</yk-common-notify.version>
                <content_bp_yk_api.version>3.1.54</content_bp_yk_api.version>

            </properties>
            <!-- master分支为true，否则为false -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- Mybatis-Plus的代码生成插件 -->
            <!-- 执行命令： mvn mybatis-plus-generator:generator -->
            <plugin>
                <groupId>com.github.ciweigg</groupId>
                <artifactId>mybatis-plus-generator-maven-plugin</artifactId>
                <version>1.0.1-RELEASE</version>
                <configuration>
                    <configurationFile>${basedir}/src/test/resources/mybatis-generator-leads_bp_yk.yml
                    </configurationFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.0.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>inngke-java-maven</id>
            <name>maven</name>
            <url>https://inngke-maven.pkg.coding.net/repository/java/maven/</url>
        </repository>
    </distributionManagement>
</project>

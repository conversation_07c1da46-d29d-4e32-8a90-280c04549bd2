package com.inngke.bp.leads.service.tp;

import com.inngke.bp.leads.dto.request.tp.*;
import com.inngke.bp.leads.dto.response.tp.TpAccountInfoDto;
import com.inngke.bp.leads.dto.response.tp.TpLaunchOauthDataDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * 第三方平台授权服务
 *
 * <AUTHOR>
 * @date 2022/4/19 9:56
 */
public interface LeadsTpOauthService {

    /**
     * 获取商户授权信息
     *
     * @param request
     * @return
     */
    BaseResponse<List<TpAccountInfoDto>> getAlreadyOauthList(BaseBidOptRequest request);

    /**
     * 获取发起授权数据
     *
     * @return
     */
    BaseResponse<TpLaunchOauthDataDto> getLaunchOauthData(GetTpLaunchOauthDataRequest request);


    /**
     * 预授权回调
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> oauthCallback(OauthCallbackRequest request);


    /**
     * 获取AcccessToken
     * @param request
     * @return
     */
    BaseResponse<String> getAccessToken(GetTpAccessTokenRequest request);

    /**
     * 账号解绑
     */
    BaseResponse<Boolean> unbindOauth(UnbindOauthRequest request);

    /**
     * 为授权账号设置操作员工
     *
     * @param request 请求实体
     * @return 操作结果
     */
    BaseResponse<Boolean> settingOperatorStaff(AccountSettingOperatorStaffRequest request);
}

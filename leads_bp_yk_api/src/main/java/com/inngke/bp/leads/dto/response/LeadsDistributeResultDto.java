package com.inngke.bp.leads.dto.response;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/9/17 11:51 PM
 */
public class LeadsDistributeResultDto implements Serializable {

    /**
     * 线索总数
     *
     * @demo 5
     */
    private Integer leadsAllCount;

    /**
     * 线索分配成功线索数
     *
     * @demo 5
     */
    private Integer distributedCount;

    /**
     * 线索分配失败线索数
     *
     * @demo 5
     */
    private Integer distributeErrorCount;

    public Integer getLeadsAllCount() {
        return leadsAllCount;
    }

    public void setLeadsAllCount(Integer leadsAllCount) {
        this.leadsAllCount = leadsAllCount;
    }

    public Integer getDistributedCount() {
        return distributedCount;
    }

    public void setDistributedCount(Integer distributedCount) {
        this.distributedCount = distributedCount;
    }

    public Integer getDistributeErrorCount() {
        return distributeErrorCount;
    }

    public void setDistributeErrorCount(Integer distributeErrorCount) {
        this.distributeErrorCount = distributeErrorCount;
    }
}

package com.inngke.bp.leads.dto.response;

import com.inngke.bp.leads.enums.LeadsStatusEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 13:41
 */
public class LeadsCustomerIndicatorsDto implements Serializable {

    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机
     */
    private String mobile;

    private Integer status;

    private Long clientId;

    private Integer clientStatus;


    private Integer preFollowStatus;

    /**
     * 线索状态
     */
    private String statusText;

    private Long preFollowStaffId;

    /**
     * 客服
     */
    private String preFollowName;

    private Long distributeStaffId;

    /**
     * 负责人
     */
    private String distributeStaffName;

    /**
     * 部门
     */
    private String departmentName;

    /**
     *
     */
    private Integer channel;

    /**
     * 渠道
     *
     * @demo
     */
    private String channelText;

    private Long distributeTime;

    private Long distributeFollowTime;


    /**
     * 分配时间，时间戳，粒度：毫秒
     *
     * @demo 2022-11-22 00:00:00
     */
    private String distributeTimeStr;

    private Long createTime;

    /**
     * 分配时间，时间戳，粒度：毫秒
     *
     * @demo 2022-11-22 00:00:00
     */
    private String createTimeStr;


    /**
     * 微信号
     */
    private String weChatId;

    /**
     * 经销商名称
     */
    private String agentName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 线索来源渠道
     */
    private Integer channelSource;

    /**
     * 线索来源
     */
    private String channelSourceText;

    /**
     * 创建人姓名
     */
    private String createStaffName;

    /**
     * 标签
     */
    private List<String> tagsList;

    /**
     * 跟进状态
     */
    private List<LeadsFollowStatusDto> followStatuses;

    /**
     * 客服跟进状态
     */
    private List<LeadsFollowStatusDto> kfFollowStatuses;

    /**
     * 订单留言
     */
    private String orderMessage;

    /**
     * 其它备注
     */
    private String remark;

    /**
     * 创建人员工id
     */
    private Long createStaffId;

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Integer getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(Integer channelSource) {
        this.channelSource = channelSource;
    }

    public Integer getPreFollowStatus() {
        return preFollowStatus;
    }

    public void setPreFollowStatus(Integer preFollowStatus) {
        this.preFollowStatus = preFollowStatus;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    public String getPreFollowName() {
        return preFollowName;
    }

    public void setPreFollowName(String preFollowName) {
        this.preFollowName = preFollowName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getChannelText() {
        return channelText;
    }

    public void setChannelText(String channelText) {
        this.channelText = channelText;
    }

    public String getDistributeTimeStr() {
        return distributeTimeStr;
    }

    public void setDistributeTimeStr(String distributeTimeStr) {
        this.distributeTimeStr = distributeTimeStr;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getPreFollowStaffId() {
        return preFollowStaffId;
    }

    public void setPreFollowStaffId(Long preFollowStaffId) {
        this.preFollowStaffId = preFollowStaffId;
    }

    public Long getDistributeStaffId() {
        return distributeStaffId;
    }

    public void setDistributeStaffId(Long distributeStaffId) {
        this.distributeStaffId = distributeStaffId;
    }

    public String getDistributeStaffName() {
        return distributeStaffName;
    }

    public void setDistributeStaffName(String distributeStaffName) {
        this.distributeStaffName = distributeStaffName;
    }

    public Long getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Long distributeTime) {
        this.distributeTime = distributeTime;
    }

    public Long getDistributeFollowTime() {
        return distributeFollowTime;
    }

    public void setDistributeFollowTime(Long distributeFollowTime) {
        this.distributeFollowTime = distributeFollowTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getWeChatId() {
        return weChatId;
    }

    public void setWeChatId(String weChatId) {
        this.weChatId = weChatId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getChannelSourceText() {
        return channelSourceText;
    }

    public void setChannelSourceText(String channelSourceText) {
        this.channelSourceText = channelSourceText;
    }

    public String getCreateStaffName() {
        return createStaffName;
    }

    public void setCreateStaffName(String createStaffName) {
        this.createStaffName = createStaffName;
    }

    public List<String> getTagsList() {
        return tagsList;
    }

    public void setTagsList(List<String> tagsList) {
        this.tagsList = tagsList;
    }

    public List<LeadsFollowStatusDto> getFollowStatuses() {
        return followStatuses;
    }

    public void setFollowStatuses(List<LeadsFollowStatusDto> followStatuses) {
        this.followStatuses = followStatuses;
    }

    public List<LeadsFollowStatusDto> getKfFollowStatuses() {
        return kfFollowStatuses;
    }

    public void setKfFollowStatuses(List<LeadsFollowStatusDto> kfFollowStatuses) {
        this.kfFollowStatuses = kfFollowStatuses;
    }

    public String getOrderMessage() {
        return orderMessage;
    }

    public void setOrderMessage(String orderMessage) {
        this.orderMessage = orderMessage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getClientId() {
        return clientId;
    }

    public LeadsCustomerIndicatorsDto setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public Integer getClientStatus() {
        return clientStatus;
    }

    public LeadsCustomerIndicatorsDto setClientStatus(Integer clientStatus) {
        this.clientStatus = clientStatus;
        return this;
    }
}

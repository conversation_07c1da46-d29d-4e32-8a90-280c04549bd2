package com.inngke.bp.leads.dto.response;

/**
 * 管理员查看线索统计列表部门信息
 *
 * <AUTHOR>
 */
public class LeadsStatisticsDepItemDto extends LeadsStatisticsBaseInfoDto {

    /**
     * 部门编号
     *
     * @demo 1
     */
    private Long departmentId;

    /**
     * 部门名称
     *
     * @demo 北京中天诚投科贸有限公司
     */
    private String departmentName;

    /**
     * 部门包含的员工人数
     * @demo 22
     */
    private Integer staffNum;

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getStaffNum() {
        return staffNum;
    }

    public void setStaffNum(Integer staffNum) {
        this.staffNum = staffNum;
    }
}

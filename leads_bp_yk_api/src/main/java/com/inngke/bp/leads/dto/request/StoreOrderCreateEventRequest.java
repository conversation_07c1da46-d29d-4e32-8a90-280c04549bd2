package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;

import java.math.BigDecimal;

public class StoreOrderCreateEventRequest extends BaseBidRequest {

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 定金
     */
    private BigDecimal deposit;

    /**
     * 交易金额
     */
    private BigDecimal payAmount;

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }
}

package com.inngke.bp.leads.dto.response;

import com.inngke.bp.leads.enums.LeadsStatusEnum;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * LeadsBasicDto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/14 10:02
 */
public class LeadsBasicDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 自增ID
     */
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id
     */
    private Long customerId;

    /**
     * 关联用户的ID， 即customer.uid
     */
    private Long customerUid;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 客户ID
     */
    private Long clientId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 微信号
     */
    private String weChat;

    /**
     * 线索状态： -4=删除 -3=无效线索 -2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交
     *
     * @see LeadsStatusEnum
     */
    private Integer status;

    /**
     * 省份ID，0表示未匹配
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     */
    private Integer channel;

    /**
     * 线索来源类型
     */
    private Integer channelType;

    /**
     * 线索来源渠道
     */
    private Integer channelSource;

    /**
     * 外部平台线索ID
     */
    private String tpLeadsId;

    /**
     * 批次ID，0表示非批次导入
     */
    private Long batchId;

    /**
     * 分配的经销商ID，即shop_agent_id
     */
    // private Long distributeAgentId;

    /**
     * 分配给哪个员工，即staffId
     */
    private Long distributeStaffId;

    /**
     * 分配时间
     */
    private LocalDateTime distributeTime;

    /**
     * 分配线索给客服的时间
     */
    private LocalDateTime distributeFollowTime;

    /**
     * 退回时间
     */
    private LocalDateTime pushBackTime;

    /**
     * 异常原因
     */
    private String errorMsg;

    /**
     * 最近一条待跟进记录ID
     */
    private Long lastFollowId;

    /**
     * 额外数据
     */
    private String extData;

    /**
     * 标签
     */
    private Set<String> tags;

    /**
     * 是否完整显示手机号 0：否 1：是
     */
    private Integer showPhone;

    /**
     * 被回收的线索ID
     */
    private Long recoveryFrom;

    /**
     * 首次联系时间
     */
    private LocalDateTime firstContactTime;

    /**
     * 最后联系时间
     */
    private LocalDateTime lastContactTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 客服id
     *
     * @demo 3030
     */
    private Long preFollowStaffId;

    /**
     * 客服接待时线索状态
     *
     * @demo 1
     */
    private Integer preFollowStatus;

    /**
     * 线索类型 1:订单类 2:信息类
     *
     * @demo 1
     */
    private Integer type;

    /**
     * 创建人StaffID
     *
     * @demo 110
     */
    private Long createStaffId;

    /**
     * 等级 A B C D
     */
    private String level;

    /**
     * 退回员工Id
     */
    private Long pushBackStaffId;

    /**
     * 已经被回收的线索Id
     */
    private String recoveryFromIds;

    /**
     * @see LeadsFollowStatusDto
     * 跟进状态
     */
    private String followStatuses;

    /**
     * @see LeadsFollowStatusDto
     * 客服跟进状态
     */
    private String kfFollowStatuses;

    private String remark;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getCustomerUid() {
        return customerUid;
    }

    public void setCustomerUid(Long customerUid) {
        this.customerUid = customerUid;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(Integer channelSource) {
        this.channelSource = channelSource;
    }

    public String getTpLeadsId() {
        return tpLeadsId;
    }

    public void setTpLeadsId(String tpLeadsId) {
        this.tpLeadsId = tpLeadsId;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public Long getDistributeStaffId() {
        return distributeStaffId;
    }

    public void setDistributeStaffId(Long distributeStaffId) {
        this.distributeStaffId = distributeStaffId;
    }

    public LocalDateTime getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(LocalDateTime distributeTime) {
        this.distributeTime = distributeTime;
    }

    public LocalDateTime getDistributeFollowTime() {
        return distributeFollowTime;
    }

    public void setDistributeFollowTime(LocalDateTime distributeFollowTime) {
        this.distributeFollowTime = distributeFollowTime;
    }

    public LocalDateTime getPushBackTime() {
        return pushBackTime;
    }

    public void setPushBackTime(LocalDateTime pushBackTime) {
        this.pushBackTime = pushBackTime;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Long getLastFollowId() {
        return lastFollowId;
    }

    public void setLastFollowId(Long lastFollowId) {
        this.lastFollowId = lastFollowId;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public Set<String> getTags() {
        return tags;
    }

    public void setTags(Set<String> tags) {
        this.tags = tags;
    }

    public Integer getShowPhone() {
        return showPhone;
    }

    public void setShowPhone(Integer showPhone) {
        this.showPhone = showPhone;
    }

    public Long getRecoveryFrom() {
        return recoveryFrom;
    }

    public void setRecoveryFrom(Long recoveryFrom) {
        this.recoveryFrom = recoveryFrom;
    }

    public LocalDateTime getFirstContactTime() {
        return firstContactTime;
    }

    public void setFirstContactTime(LocalDateTime firstContactTime) {
        this.firstContactTime = firstContactTime;
    }

    public LocalDateTime getLastContactTime() {
        return lastContactTime;
    }

    public void setLastContactTime(LocalDateTime lastContactTime) {
        this.lastContactTime = lastContactTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Long getPreFollowStaffId() {
        return preFollowStaffId;
    }

    public void setPreFollowStaffId(Long preFollowStaffId) {
        this.preFollowStaffId = preFollowStaffId;
    }

    public Integer getPreFollowStatus() {
        return preFollowStatus;
    }

    public void setPreFollowStatus(Integer preFollowStatus) {
        this.preFollowStatus = preFollowStatus;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Long getPushBackStaffId() {
        return pushBackStaffId;
    }

    public void setPushBackStaffId(Long pushBackStaffId) {
        this.pushBackStaffId = pushBackStaffId;
    }

    public String getRecoveryFromIds() {
        return recoveryFromIds;
    }

    public void setRecoveryFromIds(String recoveryFromIds) {
        this.recoveryFromIds = recoveryFromIds;
    }

    public String getFollowStatuses() {
        return followStatuses;
    }

    public void setFollowStatuses(String followStatuses) {
        this.followStatuses = followStatuses;
    }

    public String getKfFollowStatuses() {
        return kfFollowStatuses;
    }

    public void setKfFollowStatuses(String kfFollowStatuses) {
        this.kfFollowStatuses = kfFollowStatuses;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

package com.inngke.bp.leads.dto.response;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/9/8 10:23 PM
 */
public class LeadsStatisticsDto extends LeadsStatusStatisticsDto {
    /**
     * 经销商ID
     *
     * @demo 12345
     */
    private Long agentId;

    /**
     * 线索数量
     *
     * @demo 1234
     */
    private Integer leadsCount;

    /**
     * 订单数量
     *
     * @demo 231
     */
    private Integer orderCount;

    /**
     * 订单总金额
     *
     * @demo 2324.00
     */
    private BigDecimal orderTotalAmount;

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Integer getLeadsCount() {
        return leadsCount;
    }

    public void setLeadsCount(Integer leadsCount) {
        this.leadsCount = leadsCount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }
}

package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/13 21:00
 */
public class LeadsStatusDto implements Serializable {
    /**
     * 当前商户是否开启了线索功能
     *
     * @demo true
     */
    private Boolean leadsEnable;

    /**
     * 全部线索数量
     *
     * @demo 111
     */
    private Integer allLeadsCount;

    /**
     * 待联系数量
     *
     * @demo 123
     */
    private Integer notContactedCount;

    /**
     * 已跟进数量
     *
     * @demo 12
     */
    private Integer followedCount;

    /**
     * 已完成数量
     *
     * @demo 34
     */
    private Integer completedCount;

    /**
     * 跟进中
     */
    private Integer followingUpCount;


    /**
     * 已交付
     */
    private Integer installedCount;

    /**
     * 已流失
     */
    private Integer lostCount;

    /**
     * 待再次联系
     */
    private Integer unsureIntentCount;

    /**
     * 已下定
     */
    private Integer orderedCount;

    /**
     * 已量尺
     */
    private Integer measuredCount;

    /**
     * 已签单
     */
    private Integer tradedCount;

    private Integer storeOrderCount;

    /**
     * 待安装
     */
    private Integer toInstallCount;

    /**
     * 已联系
     */
    private Integer contractedCount;


    /**
     * 有意向
     */
    private Integer intentCount;


    /**
     * 已到店
     */
    private Integer storedCount;

    public Integer getContractedCount() {
        return contractedCount;
    }

    public void setContractedCount(Integer contractedCount) {
        this.contractedCount = contractedCount;
    }

    public Integer getIntentCount() {
        return intentCount;
    }

    public void setIntentCount(Integer intentCount) {
        this.intentCount = intentCount;
    }

    public Integer getStoredCount() {
        return storedCount;
    }

    public void setStoredCount(Integer storedCount) {
        this.storedCount = storedCount;
    }

    public Integer getUnsureIntentCount() {
        return unsureIntentCount;
    }

    public void setUnsureIntentCount(Integer unsureIntentCount) {
        this.unsureIntentCount = unsureIntentCount;
    }

    public Integer getOrderedCount() {
        return orderedCount;
    }

    public void setOrderedCount(Integer orderedCount) {
        this.orderedCount = orderedCount;
    }

    public Integer getMeasuredCount() {
        return measuredCount;
    }

    public void setMeasuredCount(Integer measuredCount) {
        this.measuredCount = measuredCount;
    }

    public Integer getTradedCount() {
        return tradedCount;
    }

    public void setTradedCount(Integer tradedCount) {
        this.tradedCount = tradedCount;
    }

    public Integer getToInstallCount() {
        return toInstallCount;
    }

    public void setToInstallCount(Integer toInstallCount) {
        this.toInstallCount = toInstallCount;
    }

    public Integer getFollowingUpCount() {
        return followingUpCount;
    }

    public void setFollowingUpCount(Integer followingUpCount) {
        this.followingUpCount = followingUpCount;
    }

    public Integer getInstalledCount() {
        return installedCount;
    }

    public void setInstalledCount(Integer installedCount) {
        this.installedCount = installedCount;
    }

    public Integer getLostCount() {
        return lostCount;
    }

    public void setLostCount(Integer lostCount) {
        this.lostCount = lostCount;
    }

    public Integer getAllLeadsCount() {
        return allLeadsCount;
    }

    public void setAllLeadsCount(Integer allLeadsCount) {
        this.allLeadsCount = allLeadsCount;
    }

    public Boolean getLeadsEnable() {
        return leadsEnable;
    }

    public void setLeadsEnable(Boolean leadsEnable) {
        this.leadsEnable = leadsEnable;
    }

    public Integer getNotContactedCount() {
        return notContactedCount;
    }

    public void setNotContactedCount(Integer notContactedCount) {
        this.notContactedCount = notContactedCount;
    }

    public Integer getFollowedCount() {
        return followedCount;
    }

    public void setFollowedCount(Integer followedCount) {
        this.followedCount = followedCount;
    }

    public Integer getCompletedCount() {
        return completedCount;
    }

    public void setCompletedCount(Integer completedCount) {
        this.completedCount = completedCount;
    }

    public Integer getStoreOrderCount() {
        return storeOrderCount;
    }

    public void setStoreOrderCount(Integer storeOrderCount) {
        this.storeOrderCount = storeOrderCount;
    }
}

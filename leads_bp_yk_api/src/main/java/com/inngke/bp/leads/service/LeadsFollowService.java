package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsFollowDto;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.bp.leads.dto.response.LeadsFollowSimpleDto;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索跟进
 * @since 2021/9/8 5:46 PM
 */
public interface LeadsFollowService {
    /**
     * 创建线索跟进
     *
     * @param request 跟进请求
     * @return 是否创建成功
     */
    BaseResponse<Boolean> createFollow(LeadsFollowCreateRequest request);

    /**
     * 创建线索跟进
     *
     * @param request 跟进请求
     * @return 是否创建成功
     */
    BaseResponse<Boolean> createFollowForOpt(LeadsFollowCreateRequest request);

    /**
     * 查询某个线索的跟进记录
     *
     * @param query 筛选条件
     * @return 跟进记录列表
     */
    BaseResponse<List<LeadsFollowDto>> list(LeadsFollowQuery query);

    /**
     * 查询某个线索的跟进记录数
     *
     * @param request 筛选条件
     * @return 跟进记录数
     */
    BaseResponse<Integer> getLeadsFollowCount(LeadsGetRequest request);

    /**
     * 查询跟进列表信息
     * @return
     */
    BaseResponse<BasePaginationResponse<LeadsFollowDto>> getLeadsFollowList(LeadsFollowListRequest request);

    /**
     * 添加导购联系线索的跟进记录
     * @param privateVoiceRecordDTO
     * @return
     */
    BaseResponse<Boolean> createContactFollow(PrivateVoiceRecordDTO privateVoiceRecordDTO);

    BaseResponse<List<LeadsFollowDto>> getLastFollow(LeadsFollowListRequest request);

    /**
     * 查询最新的根进记录
     * @return
     */
    BaseResponse<List<LeadsFollowSimpleDto>> getLeadsLatestFollowList(LeadsFollowListRequest request);

    /**
     * 获取客户相关的线索跟进数
     *
     * @param request 客户id请求实体
     * @return 跟进数
     */
    BaseResponse<Integer> getLeadsFollowCountByClient(BaseIdRequest request);

    BaseResponse<Map<Long,Integer>> getLeadsFollowCountByClientIds(BaseIdsRequest request);

    /**
     * 获取客户的线索跟进
     *
     * @param request 请求实体
     * @return 线索跟进
     */
    BaseResponse<List<LeadsFollowDto>> findLeadsFollowByClient(BaseIdRequest request);

    BaseResponse<List<LeadsFollowDto>> batchGetFollow(BaseIdsRequest request);

    BaseResponse<List<LeadsFollowDto>> batchGetByFollowIds(BaseIdsRequest request);

    BaseResponse<List<LeadsFollowDto>> getLeadsFollowListOpen(GetLeadsFollowListRequest request);
}

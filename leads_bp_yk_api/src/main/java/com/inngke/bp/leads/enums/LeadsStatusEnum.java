package com.inngke.bp.leads.enums;

import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线索状态
 *
 * <AUTHOR>
 * @since 2021/9/8 11:37 PM
 */
public enum LeadsStatusEnum {

    /**
     * 已流失
     */
    LOST(-6,"已流失"),
    /**
     * 已回收
     */
    RECOVERY(-5,"已回收"),
    /**
     * 删除
     */
    DELETED(-4, "删除"),

    /**
     * 无效线索
     */
    INVALID(-3, "无效线索"),

    /**
     * 分配失败
     */
    DISTRIBUTE_ERR(-2, "分配失败"),

    /**
     * 员工退回
     */
    PUSH_BACK(-1, "员工退回"),

    /**
     * 待分配
     */
    TO_DISTRIBUTE(0, "待分配"),

    /**
     * 已分配，未联系
     */
    DISTRIBUTED(1, "待联系"),

    /**
     * 已联系(24h内联系)
     */
    CONTACTED(2, "已联系"),

    /**
     * 已成功联系(24h后联系)
     */
    SUCCESS_CONTACT(3, "已成功联系"),

    /**
     * 有意向
     */
    INTENT(4, "有意向"),

    /**
     * 量尺
     */
    MEASURED(5, "已量尺"),

    /**
     * 到店
     */
    STORED(6, "已到店"),

    /**
     * 报价
     */
    QUOTED_PRICE(7, "已到店"),

    /**
     * 定金
     */
    ORDERED(8, "已下定"),

    /**
     * 待安装
     */
    TO_INSTALL(9, "待安装"),

    /**
     * 已安装
     */
    INSTALLED(10, "已交付"),

    /**
     * 已成交
     */
    TRADED(11, "已签单"),

    /**
     * 客服接待
     */
    PRE_FOLLOW(21, "客服接待"),

    /**
     * 待确认意向
     */
    UNSURE_INTENT(-10, "待再次联系"),

    ;

    /**
     * 状态
     */
    private final int status;

    /**
     * 名称
     */
    private final String name;

    LeadsStatusEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public static LeadsStatusEnum parse(Integer status) {
        if (status == null) {
            return null;
        }
        for (LeadsStatusEnum leadsStatusEnum : LeadsStatusEnum.values()) {
            if (leadsStatusEnum.status == status) {
                return leadsStatusEnum;
            }
        }
        return null;
    }

    public static LeadsStatusEnum parse(String name) {
        if (Objects.isNull(name)) {
            return null;
        }
        for (LeadsStatusEnum leadsStatusEnum : LeadsStatusEnum.values()) {
            if (leadsStatusEnum.getName().equals(name)) {
                return leadsStatusEnum;
            }
        }
        return null;
    }

    public static List<Integer> getAllStatusValue() {
        List<Integer> status = new ArrayList<>();
        for (LeadsStatusEnum leadsStatusEnum : LeadsStatusEnum.values()) {
            status.add(leadsStatusEnum.getStatus());
        }
        return status;
    }

    public static List<Integer> getAllCONTACT() {
        return Lists.newArrayList(LeadsStatusEnum.CONTACTED.getStatus(),LeadsStatusEnum.SUCCESS_CONTACT.getStatus());
    }

    public static List<Integer> getNonAllocatedLeadsStatus() {
        return Arrays.asList(LeadsStatusEnum.RECOVERY.getStatus(),
                LeadsStatusEnum.DELETED.getStatus(),
                LeadsStatusEnum.DISTRIBUTE_ERR.getStatus(),
                LeadsStatusEnum.PUSH_BACK.getStatus(),
                LeadsStatusEnum.TO_DISTRIBUTE.getStatus(),
                LeadsStatusEnum.PRE_FOLLOW.getStatus()
        );
    }

    public static List<Integer> getNotStatisticsLeadsStatus() {
        List<Integer> result = Lists.newArrayList(LeadsStatusEnum.getNonAllocatedLeadsStatus());
        result.add(LeadsStatusEnum.LOST.getStatus());
        result.add(LeadsStatusEnum.INVALID.getStatus());
        return result;
    }

    public static Set<Integer> delStatus(){
        HashSet<Integer> result  = new HashSet<>(2);
        result.add(LeadsStatusEnum.DELETED.getStatus());
        result.add(LeadsStatusEnum.RECOVERY.getStatus());
        return result;
    }

    public static List<Integer> getAllocatedLeadsStatus() {
        List<Integer> statusValue = getAllStatusValue();
        statusValue.removeAll(getNonAllocatedLeadsStatus());
        return statusValue;
    }

    public static List<Integer> getUsedLeadsStatus() {
        List<Integer> usedLeadsStatus = getAllStatusValue();
        usedLeadsStatus.removeAll(Arrays.asList(LeadsStatusEnum.RECOVERY.getStatus(),LeadsStatusEnum.DELETED.getStatus()));
        return usedLeadsStatus;
    }

    /**
     * 已联系：线索状态不为“待联系&无效线索&已流失”且未转客户
     * 加上删除、退回、回收
     * @param
     * @return
     */
    public static List<Integer> toLeadsStatus(Integer status) {
        if (Objects.equals(status,CONTACTED.status)) {
            List<Integer> needFilterLeadsStatus = Arrays.asList(LeadsStatusEnum.DISTRIBUTED.status,
                    LeadsStatusEnum.INVALID.status,
                    LeadsStatusEnum.RECOVERY.status,
                    LeadsStatusEnum.DELETED.status,
                    LeadsStatusEnum.PUSH_BACK.status,
                    LeadsStatusEnum.LOST.status
            );
            return LeadsStatusEnum.getAllStatusValue().stream().filter(i -> !needFilterLeadsStatus.contains(i)).collect(Collectors.toList());
        }

        return Arrays.asList(status);
    }

    public static List<Integer> getContactLeadsStatus() {
        return Arrays.asList(LeadsStatusEnum.UNSURE_INTENT.getStatus(),
                LeadsStatusEnum.INTENT.getStatus(),
                LeadsStatusEnum.INVALID.getStatus(),
                LeadsStatusEnum.LOST.getStatus()
        );
    }

}

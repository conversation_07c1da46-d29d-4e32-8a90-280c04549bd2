package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsStaffNameEsGetRequest;
import com.inngke.bp.leads.dto.request.LeadsStatisticsQuery;
import com.inngke.bp.leads.dto.request.LeadsStatusRequest;
import com.inngke.bp.leads.dto.request.StaffLeadsCountRequest;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索统计接口
 * @since 2021/9/8 8:43 PM
 */
public interface LeadsStatisticsService {
    /**
     * 线索统计查询
     *
     * @param request 筛选条件
     * @return 统计信息列表
     */
    BaseResponse<BasePaginationResponse<LeadsStatisticsDto>> getLeadsStatistics(LeadsStatisticsQuery request);

    /**
     * 导出线索统计查询
     *
     * @param request 筛选条件
     * @return 生成的统计文件链接
     */
    BaseResponse<String> getLeadsStatisticsExport(LeadsStatisticsQuery request);

    /**
     * 员工索数各状态统计
     *
     * @param request 请求
     * @return 各状态统计
     */
    BaseResponse<LeadsStatusDto> getLeadsStatusStatistics(LeadsStatusRequest request);

    BaseResponse<LeadsStatusDto> getLeadsStatusStatisticsForList(LeadsStatusRequest request);

    /**
     * 获取某天各员工分配的线索数量
     *
     * @param request 请求
     * @return 某天各员工分配的线索数量
     */
    BaseResponse<List<StaffLeadsCountDto>> getStaffLeadsCount(StaffLeadsCountRequest request);

    /**
     * 获取员工的待联系线索数
     * @param request
     * @return
     */
    BaseResponse<Integer> getNotContactLeads(LeadsStatusRequest request);

    /**
     * 统计员工线索数据
     * @param request
     * @return
     */
    BaseResponse<List<LeadsEsDto>> getLeadsStatisticsByStaffName(LeadsStaffNameEsGetRequest request);
}

package com.inngke.bp.leads.enums;

public enum LeadsTpTypeEnum {

    /**
     * 腾讯广告
     */
    TENCENT(1, "腾讯广告"),

    /**
     * 飞鱼
     */
    FEI_YU(2, "飞鱼");


    private final Integer code;

    private final String name;

    LeadsTpTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LeadsTpTypeEnum parse(Integer code) {
        if (code == null) {
            return null;
        }

        for (LeadsTpTypeEnum leadsTpTypeEnum : LeadsTpTypeEnum.values()) {
            if (leadsTpTypeEnum.getCode().equals(code)) {
                return leadsTpTypeEnum;
            }
        }

        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

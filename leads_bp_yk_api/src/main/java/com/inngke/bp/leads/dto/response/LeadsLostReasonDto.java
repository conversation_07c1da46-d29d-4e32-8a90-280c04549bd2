package com.inngke.bp.leads.dto.response;

import com.inngke.bp.leads.enums.LeadsLostReasonEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/7 17:14
 */
public class LeadsLostReasonDto implements Serializable {

    /**
     * 原因id
     */
    private Integer type;

    /**
     * 原因
     * @see LeadsLostReasonEnum
     */
    private String reason;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

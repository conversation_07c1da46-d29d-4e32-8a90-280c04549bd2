package com.inngke.bp.leads.service.tp;

import com.inngke.bp.leads.dto.request.tp.TencentLeadsPushDto;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/3/8 10:21
 */
public interface LeadsTencentCallbackService {

    /**
     * 处理腾讯广告回调
     *
     * @param tencentLeadsPushDto
     * @return
     */
    BaseResponse<String> handle(TencentLeadsPushDto tencentLeadsPushDto);
}

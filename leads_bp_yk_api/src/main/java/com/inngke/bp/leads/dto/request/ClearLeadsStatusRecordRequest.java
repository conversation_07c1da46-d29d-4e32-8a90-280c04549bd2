package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;

import java.util.Set;

public class ClearLeadsStatusRecordRequest extends BaseBidRequest {
    /**
     * 线索id
     */
    private Set<Long> leadsIds;

    /**
     * 线索状态
     */
    private Integer status;

    /**
     * 是否为客服线索
     */
    private Boolean hasKf;

    public Set<Long> getLeadsIds() {
        return leadsIds;
    }

    public void setLeadsIds(Set<Long> leadsIds) {
        this.leadsIds = leadsIds;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getHasKf() {
        return hasKf;
    }

    public void setHasKf(Boolean hasKf) {
        this.hasKf = hasKf;
    }
}

package com.inngke.bp.leads.service.tp;

import com.inngke.bp.leads.dto.request.tp.SaveLeadsTpPullConditionRequest;
import com.inngke.bp.leads.dto.response.tp.LeadsTpPullConditionDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseListResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 14:22
 */
public interface LeadsTpPullConditionService {

    /**
     * 获取线索拉取条件
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsTpPullConditionDto>> getLeadsTpPullConditionList(BaseBidOptRequest request);

    /**
     * 保存线索拉取条件
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsTpPullConditionDto>> saveLeadsTpPullConditionList(SaveLeadsTpPullConditionRequest request);

}

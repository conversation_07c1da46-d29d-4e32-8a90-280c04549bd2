package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.DistributeConfMapDto;
import com.inngke.bp.leads.dto.response.RegionConfDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索下发配置
 * @since 2021/9/7 4:01 PM
 */
public interface LeadsDistributeConfService {
    /**
     * 获取行政区域树
     *
     * @param request 请求
     * @return 行政区域树
     */
    BaseResponse<List<RegionConfDto>> getRegionTree(GetLeadsDistributeConfRequest request);

    /**
     * 保存区域接收人
     *
     * @param request 保存请求参数
     * @return 是否保存成功
     */
    BaseResponse<Boolean> save(RegionConfSaveRequest request);

    /**
     * 清除区域接收人
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> clear(RegionConfClearRequest request);

    BaseResponse<DistributeConfMapDto> getDistributeMap(GetDistributeMapRequest request);

    BaseResponse<List<String>> batchImport(LeadsBatchImportRequest request);
}

package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsGetRequest;
import com.inngke.bp.leads.dto.request.LeadsInformationAddRequest;
import com.inngke.bp.leads.dto.request.LeadsInformationUpdateRequest;
import com.inngke.bp.leads.dto.response.LeadsInformationDto;
import com.inngke.bp.leads.dto.response.LeadsInformationVo;
import com.inngke.bp.leads.dto.response.LeadsVo;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/3/8 15:24
 */
public interface LeadsInformationService {

    /**
     * 获取信息类扩展数据
     *
     * @param request
     * @return
     */
    BaseResponse<LeadsInformationDto> getInfo(LeadsGetRequest request);


    /**
     * 获取线索详情（前端）
     *
     * @param request 线索请求
     * @return 线索详情
     */
    BaseResponse<LeadsInformationVo> getLeadsDetail(LeadsGetRequest request);

    /**
     * 保存线索信息
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> update(LeadsInformationUpdateRequest request);

    /**
     * 添加线索
     *
     * @param request
     * @return
     */
    BaseResponse<LeadsInformationDto> add(LeadsInformationAddRequest request);
}

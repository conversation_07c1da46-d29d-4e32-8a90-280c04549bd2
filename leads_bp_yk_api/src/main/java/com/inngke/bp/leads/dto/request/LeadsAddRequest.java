package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/9/8 4:44 PM
 */
@Data
public class LeadsAddRequest extends BaseBidOptRequest {

    private Long customerId;

    private Long staffId;

    /**
     * 线索ID 更新线索信息时必填
     */
    private Long id;

    private List<Long> ids;
    /**
     * 姓名
     *
     * @required
     * @demo 张三
     */
    private String name;

    /**
     * 渠道ID
     */
    private Long channelId;


    /**
     * 手机号码
     *
     * @required
     * @demo 13800138000
     */
    private String mobile;

    /**
     * 微信
     */
    private String weChat;

    /**
     * 省份ID，0表示未匹配。手工添加时必填
     *
     * @demo 123
     */
    private Integer provinceId;

    /**FR
     * 省份名称
     *
     * @demo 广东省
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配。手工添加时必填
     *
     * @demo 4232
     */
    private Integer cityId;

    /**
     * 城市名称
     *
     * @demo 广州市
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配。手工添加时必填
     *
     * @demo 102432
     */
    private Integer areaId;

    /**
     * 区域名称
     *
     * @demo 越秀区
     */
    private String areaName;

    /**
     * 详细地址。手工添加时必填
     *
     * @demo 流花展贸中心10号馆
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     *
     * @required
     * @demo 1
     */
    private Integer channel;

    private Integer channelType;

    private Integer channelSource;

    /**
     * 下单账号
     *
     * @demo <EMAIL>
     */
    private String orderAccount;

    /**
     * 订单编号
     *
     * @demo SN-DQEFR1024
     */
    private String orderSn;

    /**
     * 商品名称
     *
     * @demo 豪华无敌吊顶
     */
    private String goodsName;

    /**
     * 订购商品数量
     *
     * @demo 1
     */
    private Integer goodsNum;

    /**
     * 付款时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long payTime;

    /**
     * 付款金额
     *
     * @demo 1000.05
     */
    private BigDecimal payAmount;

    /**
     * 订单留言
     *
     * @demo 要做好呀
     */
    private String orderMessage;

    /**
     * 其它备注
     *
     * @demo 没了
     */
    private String remark;

    /**
     * 外部平台线索ID
     *
     * @demo 12343
     */
    private String tpLeadsId;

    /**
     * 广告活动名称
     *
     * @demo 1元秒杀
     */
    private String promotionName;

    /**
     * 报名时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long registryTime;

    /**
     * 需求时间：0=未指定 1=一个月内 2=三个月内 3=六个月内 4=一年内 5=一年以上
     *
     * @demo 1
     */
    private Integer expectIn;

    /**
     * 装修风格，详见枚举
     *
     * @demo 1
     */
    private Integer style;

    /**
     * 批次ID，0表示非批次导入
     *
     * @demo 1232
     */
    private Long batchId;

    /**
     * 額外数据（isRefund=是否退款，refundRemark=退款备注）
     *
     * @demo {"isRefund":0=否/1=是,"refundRemark:"}
     */
    private String extData;

    /**
     * 商品链接
     */
    private String goodsLink;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 企业标签
     */
    private List<String> enterpriseTags;

    /**
     * 外部标签
     */
    private List<String> externalTags;

    /**
     * ES构建需要的字段!!
     * 维护分发经销商的数据在ES所需要的字段
     * key staffId 导购
     * value agentId 经销商编号
     */
    private Map<Long, Long> staffToAgent;

    /**
     * 线索类型 1:订单类 2:信息类
     *
     * @demo 1
     */
    private Integer type;

    /**
     * 创建人StaffID
     *
     * @demo 110
     */
    private Long createStaffId;

    /**
     * 等级 A B C D
     */
    @Deprecated
    private String level;

    /**
     * 需求产品
     */
    private String demandProduct;

    private Integer levelId;

    /**
     * 平台ID
     *
     * @demo abcdefghijklmn
     */
    private String tpId;

    /**
     * 附件列表，文件最多可上传5个
     */
    private List<LeadsAttachment> attachmentList;

    /**
     * 产品ids
     *
     * @demo [1, 2, 3]
     */
    private List<Long> productIds;

    private Boolean needSendMQ = true;

    private Long reportStaffId;

    public List<LeadsAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<LeadsAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }


    public Map<Long, Long> getStaffToAgent() {
        return staffToAgent;
    }

    public void setStaffToAgent(Map<Long, Long> staffToAgent) {
        this.staffToAgent = staffToAgent;
    }

    public void setStaffToAgentBySingle(Long staffId, Long agentId) {
        Map<Long, Long> staffToAgent = new HashMap<>();
        staffToAgent.put(staffId, agentId);
        this.staffToAgent = staffToAgent;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(Integer channelSource) {
        this.channelSource = channelSource;
    }

    public String getOrderAccount() {
        return orderAccount;
    }

    public void setOrderAccount(String orderAccount) {
        this.orderAccount = orderAccount;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getGoodsNum() {
        return goodsNum;
    }

    public void setGoodsNum(Integer goodsNum) {
        this.goodsNum = goodsNum;
    }

    public Long getPayTime() {
        return payTime;
    }

    public void setPayTime(Long payTime) {
        this.payTime = payTime;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getOrderMessage() {
        return orderMessage;
    }

    public void setOrderMessage(String orderMessage) {
        this.orderMessage = orderMessage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTpLeadsId() {
        return tpLeadsId;
    }

    public void setTpLeadsId(String tpLeadsId) {
        this.tpLeadsId = tpLeadsId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Long getRegistryTime() {
        return registryTime;
    }

    public void setRegistryTime(Long registryTime) {
        this.registryTime = registryTime;
    }

    public Integer getExpectIn() {
        return expectIn;
    }

    public void setExpectIn(Integer expectIn) {
        this.expectIn = expectIn;
    }

    public Integer getStyle() {
        return style;
    }

    public void setStyle(Integer style) {
        this.style = style;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getGoodsLink() {
        return goodsLink;
    }

    public void setGoodsLink(String goodsLink) {
        this.goodsLink = goodsLink;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public String getTpId() {
        return tpId;
    }

    public void setTpId(String tpId) {
        this.tpId = tpId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getDemandProduct() {
        return demandProduct;
    }

    public void setDemandProduct(String demandProduct) {
        this.demandProduct = demandProduct;
    }

    public List<String> getExternalTags() {
        return externalTags;
    }

    public void setExternalTags(List<String> externalTags) {
        this.externalTags = externalTags;
    }

    public List<Long> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<Long> productIds) {
        this.productIds = productIds;
    }

    public Boolean getNeedSendMQ() {
        return needSendMQ;
    }

    public void setNeedSendMQ(Boolean needSendMQ) {
        this.needSendMQ = needSendMQ;
    }

    public List<String> getEnterpriseTags() {
        return enterpriseTags;
    }

    public LeadsAddRequest setEnterpriseTags(List<String> enterpriseTags) {
        this.enterpriseTags = enterpriseTags;
        return this;
    }

    public Long getReportStaffId() {
        return reportStaffId;
    }

    public void setReportStaffId(Long reportStaffId) {
        this.reportStaffId = reportStaffId;
    }
}

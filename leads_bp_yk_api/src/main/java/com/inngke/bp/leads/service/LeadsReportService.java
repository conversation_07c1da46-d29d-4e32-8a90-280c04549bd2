package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsAchievementReportDetailRequest;
import com.inngke.bp.leads.dto.request.LeadsAchievementReportRequest;
import com.inngke.bp.leads.dto.response.LeadsAchievementReportResponse;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * LeadsReportService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/13 20:53
 */
public interface LeadsReportService {

    BaseResponse<BasePaginationResponse<LeadsAchievementReportResponse>> getReportList(LeadsAchievementReportRequest request);

    /**
     * 业绩数据报表查看明细
     */
    BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> getReportListDetail(LeadsAchievementReportDetailRequest request);

    BaseResponse<List<LeadsAchievementReportResponse>> exportList(LeadsAchievementReportRequest request);
}

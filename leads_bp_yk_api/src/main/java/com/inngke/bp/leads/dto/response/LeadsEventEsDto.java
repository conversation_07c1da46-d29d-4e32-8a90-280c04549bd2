package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class LeadsEventEsDto implements Serializable {

    private Integer bid;

    /**
     * 雪花ID
     */
    private Long id;

    /**
     * 事件类型 1:跟进(状态变更)
     */
    private Integer eventType;

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 跟进员工ID
     */
    private Long followStaffId;

    /**
     * 跟进员工部门IDs包含所有的父级部门
     */
    private List<Long> followDepartmentIds;

    /**
     * 跟进员工部门Id
     */
    private Long followDepartmentId;

    /**
     * 跟进客服
     */
    private Long preFollowStaffId;

    /**
     * 跟进客服部门Ids包含所有的父级部门
     */
    private List<Long> preFollowDepartmentIds;

    /**
     * 跟进客服部门Id
     */
    private Long preFollowDepartmentId;

    /**
     * 事件类型
     * 看数据库配置：leads_bp_yk.leads_event_conf
     */
    private Integer eventId;

    /**
     * 创建跟进时间
     */
    private Long createTime;

    /**
     * 线索创建时间
     */
    private Long leadsCreateTime;

    /**
     * 线索分配时间
     */
    private Long leadsDistributeTime;

    /**
     * 定金单位分
     */
    private BigDecimal deposit;

    /**
     * 交易金额
     */
    private BigDecimal payAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getFollowStaffId() {
        return followStaffId;
    }

    public void setFollowStaffId(Long followStaffId) {
        this.followStaffId = followStaffId;
    }

    public List<Long> getFollowDepartmentIds() {
        return followDepartmentIds;
    }

    public void setFollowDepartmentIds(List<Long> followDepartmentIds) {
        this.followDepartmentIds = followDepartmentIds;
    }

    public Long getFollowDepartmentId() {
        return followDepartmentId;
    }

    public void setFollowDepartmentId(Long followDepartmentId) {
        this.followDepartmentId = followDepartmentId;
    }

    public Long getPreFollowStaffId() {
        return preFollowStaffId;
    }

    public void setPreFollowStaffId(Long preFollowStaffId) {
        this.preFollowStaffId = preFollowStaffId;
    }

    public List<Long> getPreFollowDepartmentIds() {
        return preFollowDepartmentIds;
    }

    public void setPreFollowDepartmentIds(List<Long> preFollowDepartmentIds) {
        this.preFollowDepartmentIds = preFollowDepartmentIds;
    }

    public Long getPreFollowDepartmentId() {
        return preFollowDepartmentId;
    }

    public void setPreFollowDepartmentId(Long preFollowDepartmentId) {
        this.preFollowDepartmentId = preFollowDepartmentId;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getLeadsCreateTime() {
        return leadsCreateTime;
    }

    public void setLeadsCreateTime(Long leadsCreateTime) {
        this.leadsCreateTime = leadsCreateTime;
    }

    public Long getLeadsDistributeTime() {
        return leadsDistributeTime;
    }

    public void setLeadsDistributeTime(Long leadsDistributeTime) {
        this.leadsDistributeTime = leadsDistributeTime;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }
}
package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.response.LeadsStatisticsBaseInfoDto;
import com.inngke.common.dto.response.BaseResponse;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 批量从es中获取线索数据
 *
 * <AUTHOR>
 */
public interface LeadsEsInfoGetService {

    /**
     * 根据部门编号集合查询该部门以及子部门的所有线索
     * @param bid 商户编号
     * @param depIds 部门集合
     * @param belongingDepartmentIds 有权限管理的部门
     * @return 聚合数据map
     * @throws IOException 异常
     */
    BaseResponse<Map<Long, LeadsStatisticsBaseInfoDto>> getDepLeadsListIncludeSubsByDepId(Integer bid,List<Long> depIds,List<Long> belongingDepartmentIds, Long startDistributeTime, Long endDistributeTime) ;

    /**
     * 根据员工编号集合查询所有线索
     * @param bid 商户编号
     * @param staffIds 员工集合
     * @return 聚合数据map
     * @throws IOException 异常
     */
    BaseResponse<Map<Long, LeadsStatisticsBaseInfoDto>> getLeadsListByStaffIds(Integer bid,List<Long> staffIds,List<Long> belongingDepartmentIds, Long startDistributeTime, Long endDistributeTime);
}

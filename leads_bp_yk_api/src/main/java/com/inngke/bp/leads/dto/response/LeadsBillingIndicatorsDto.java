package com.inngke.bp.leads.dto.response;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 13:56
 */
public class LeadsBillingIndicatorsDto extends LeadsCustomerIndicatorsDto {

    /**
     *
     */
    private Integer depositOrderCount = 0;

    /**
     * 开单数
     */
    private Integer fullPayOrderCount = 0;

    /**
     * 定金金额（累计）
     */
    private BigDecimal depositAmount = BigDecimal.ZERO;

    /**
     * 成交金额（累计）
     */
    private BigDecimal orderAmount = BigDecimal.ZERO;

    private Long stateDeposit = 0L;

    /**
     * 定金时间（开定金单时间）
     */
    private String stateDepositTimeStr;

    private Long stateOrderSuccess = 0L;

    /**
     * 成交时间（开成交单时间，包括定金转成交的实际）
     */
    private String stateOrderSuccessTimeStr;

    private Long updateTime;


    public Integer getDepositOrderCount() {
        return depositOrderCount;
    }

    public void setDepositOrderCount(Integer depositOrderCount) {
        this.depositOrderCount = depositOrderCount;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getFullPayOrderCount() {
        return fullPayOrderCount;
    }

    public void setFullPayOrderCount(Integer fullPayOrderCount) {
        this.fullPayOrderCount = fullPayOrderCount;
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Long getStateDeposit() {
        return stateDeposit;
    }

    public void setStateDeposit(Long stateDeposit) {
        this.stateDeposit = stateDeposit;
    }

    public String getStateDepositTimeStr() {
        return stateDepositTimeStr;
    }

    public void setStateDepositTimeStr(String stateDepositTimeStr) {
        this.stateDepositTimeStr = stateDepositTimeStr;
    }

    public Long getStateOrderSuccess() {
        return stateOrderSuccess;
    }

    public void setStateOrderSuccess(Long stateOrderSuccess) {
        this.stateOrderSuccess = stateOrderSuccess;
    }

    public String getStateOrderSuccessTimeStr() {
        return stateOrderSuccessTimeStr;
    }

    public void setStateOrderSuccessTimeStr(String stateOrderSuccessTimeStr) {
        this.stateOrderSuccessTimeStr = stateOrderSuccessTimeStr;
    }
}

package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.RejectLeadsPushBackRequest;
import com.inngke.common.dto.response.BaseResponse;

/**
 * LeadsPushBackLogService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/6 17:53
 */
public interface LeadsPushBackLogService {

    /**
     * 驳回线索退回申请
     *
     * @param request 请求实体
     * @return 操作结果
     */
    BaseResponse<Long> rejectLeadsPushBack(RejectLeadsPushBackRequest request);
}

package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;

public class EntityIdNameDto implements Serializable {

    /**
     * 实体id
     */
    private Long id;

    /**
     * 实体名称
     */
    private String name;
    private Integer type;
    private List<String> classifyNames;

    public Long getId() {
        return id;
    }

    public EntityIdNameDto setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public EntityIdNameDto setName(String name) {
        this.name = name;
        return this;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setClassifyNames(List<String> classifyNames) {
        this.classifyNames = classifyNames;
    }

    public List<String> getClassifyNames() {
        return classifyNames;
    }
}

package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/9/7 4:13 PM
 */
public class ListByMobileOrWeChatRequest extends BaseBidOptRequest {

    private Long id;

    private String mobile;

    private String weChat;

    private Set<Long> excludeIds;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Set<Long> getExcludeIds() {
        return excludeIds;
    }

    public void setExcludeIds(Set<Long> excludeIds) {
        this.excludeIds = excludeIds;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}

package com.inngke.bp.leads.dto.response.tp;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/4/19 13:45
 */
public class TpAccountInfoDto implements Serializable {

    /**
     * 1:腾讯广告 2:飞鱼
     *
     * @demo 1
     */
    private Integer type;

    /**
     * 账号ID
     *
     * @demo ***********
     */
    private String accountId;

    /**
     * 企业名称-腾讯
     *
     * @demo 营客科技
     */
    private String corporationName;

    /**
     * 商户管家账号-腾讯
     *
     * @demo  asdfasdf
     */
    private String businessId;

    /**
     * 商务管家账号类型-腾讯
     *
     * @demo  账号类型
     */
    private String accountType;

    /**
     * 账户名-飞鱼
     *
     * @demo 营客科技
     */
    private String accountName;

    /**
     * 角色-飞鱼
     *
     * @demo 角色
     */
    private String accountRole;

    /**
     * 员工id
     *
     * @demo 123
     */
    private Long operatorCreateStaffId;

    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String operatorCreateStaffName;

    private Long operatorCreateStaffDepartmentId;

    private String operatorCreateStaffDepartmentName;

    /**
     * 创建时间
     *
     * @demo ************
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     *
     * @demo ************
     */
    private LocalDateTime updateTime;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getCorporationName() {
        return corporationName;
    }

    public void setCorporationName(String corporationName) {
        this.corporationName = corporationName;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountRole() {
        return accountRole;
    }

    public void setAccountRole(String accountRole) {
        this.accountRole = accountRole;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getOperatorCreateStaffId() {
        return operatorCreateStaffId;
    }

    public void setOperatorCreateStaffId(Long operatorCreateStaffId) {
        this.operatorCreateStaffId = operatorCreateStaffId;
    }

    public String getOperatorCreateStaffName() {
        return operatorCreateStaffName;
    }

    public void setOperatorCreateStaffName(String operatorCreateStaffName) {
        this.operatorCreateStaffName = operatorCreateStaffName;
    }

    public Long getOperatorCreateStaffDepartmentId() {
        return operatorCreateStaffDepartmentId;
    }

    public void setOperatorCreateStaffDepartmentId(Long operatorCreateStaffDepartmentId) {
        this.operatorCreateStaffDepartmentId = operatorCreateStaffDepartmentId;
    }

    public String getOperatorCreateStaffDepartmentName() {
        return operatorCreateStaffDepartmentName;
    }

    public void setOperatorCreateStaffDepartmentName(String operatorCreateStaffDepartmentName) {
        this.operatorCreateStaffDepartmentName = operatorCreateStaffDepartmentName;
    }
}

package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/9/7 4:13 PM
 */
public class PreFollowStaffUpdateRuleRequest extends BaseBidOptRequest {

    /**
     * id
     */
    @NotNull
    private Long id;

    /**
     * 规则名称
     */
    @NotEmpty
    private String name;

    /**
     * 渠道Ids
     */
    @NotNull
    private Set<String> channelIds;

    /**
     * 区域Ids
     */
    @NotNull
    private Set<String> regionIds;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Set<String> getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(Set<String> channelIds) {
        this.channelIds = channelIds;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Set<String> getRegionIds() {
        return regionIds;
    }

    public void setRegionIds(Set<String> regionIds) {
        this.regionIds = regionIds;
    }
}

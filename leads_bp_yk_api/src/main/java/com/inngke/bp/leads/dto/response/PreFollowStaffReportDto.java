package com.inngke.bp.leads.dto.response;

import com.inngke.common.InngkeApiConst;
import com.inngke.common.annotation.TranslateName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/7 17:14
 */
public class PreFollowStaffReportDto implements Serializable {

    /**
     * 客服id
     */
    private Long staffId;

    /**
     * 客服姓名
     */
    private String name;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 部门id
     */
    @TranslateName(type = InngkeApiConst.TRANSLATE_TYPE_DEPARTMENT,nameField = "deptName")
    private Long deptId;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 成交数量
     */
    private Integer dealCount = 0;

    /**
     * 成交总金额
     */
    private String dealTotalAmount;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getDealCount() {
        return dealCount;
    }

    public void setDealCount(Integer dealCount) {
        this.dealCount = dealCount;
    }

    public String getDealTotalAmount() {
        return dealTotalAmount;
    }

    public void setDealTotalAmount(String dealTotalAmount) {
        this.dealTotalAmount = dealTotalAmount;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
}

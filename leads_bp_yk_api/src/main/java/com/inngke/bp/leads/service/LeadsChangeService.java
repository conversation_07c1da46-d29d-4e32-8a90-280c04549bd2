package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.LeadsDistributePreviewDto;
import com.inngke.bp.leads.dto.response.LeadsDistributeResultDto;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索接口
 * @since 2021/9/8 4:42 PM
 */
public interface LeadsChangeService {
    /**
     * 添加线索
     *
     * @param request 线索信息
     * @return 保存成功后的线索信息
     */
    BaseResponse<LeadsDto> add(LeadsAddRequest request);

    /**
     * 添加线索(已经包含员工和经销商信息)
     *
     * @param request 线索信息
     * @return 保存成功后的线索信息
     */
    BaseResponse<LeadsDto> addIncludeStaff(LeadsAddIncludeStaffRequest request);

    /**
     * 修改线索信息
     *
     * @param request 修改请求
     * @return 修改成功后的线索信息
     */
    BaseResponse<LeadsDto> save(LeadsUpdateRequest request);

    /**
     * 修改线索状态
     * @param request 线索状态修改入参
     * @return 成功与否
     */
    BaseResponse<Boolean> updateStatus(LeadsStatusUpdateRequest request);


    /**
     * 批量分配线索
     *
     * @param request 分配请求
     * @return 是否分配成功
     */
    BaseResponse<LeadsDistributeResultDto> distribute(LeadsDistributeRequest request);

    /**
     *  批量分配线索，有客服清洗功能
     */
    BaseResponse<LeadsDistributeResultDto> distributeWithFollowStaff(LeadsDistributeRequest request);

    /**
     * 分配预览接口
     * @param request 分配预览时的入参，跟一键分配的一样
     * @return 返回分配后的集合
     */
    BaseResponse<List<LeadsDistributePreviewDto>> distributePreview(LeadsDistributeRequest request);

    /**
     * 退回线索
     *
     * @param request 退回请求
     * @return 是否退回成功
     */
    BaseResponse<Boolean> pushBack(LeadsPushBackRequest request);

    /**
     * 删除线索
     * 仅允许删除分配失败和退回状态的线索
     *
     * @param request 删除请求
     * @return 是否删除成功
     */
    BaseResponse<Boolean> delete(LeadsBatchRequest request);

    /**
     * 转移线索
     *
     * @param request 转移请求
     * @return 是否转移成功
     */
    BaseResponse<Boolean> forward(LeadsForwardRequest request);

    /**
     * 修改线索信息v2
     * @param request
     * @return
     */
    BaseResponse<Boolean> updateLeads(LeadsUpdateRequest request);

    /**
     * 线索回收
     * @param request 被回收的线索集合入参
     * @return 回收成功与否
     */
    BaseResponse<Boolean> recoveryLeads(LeadsRecoveryRequest request);

    /**
     * 导购联系线索客户
     * @return
     */
    BaseResponse<Boolean> contactLeads(PrivateVoiceRecordDTO privateVoiceRecordDTO);

    /**
     * 合伙人线索转移
     * @param request
     * @return
     */
    BaseResponse<Boolean> transfer(LeadTransferRequest request);

    /**
     * 更新线索客户状态
     *
     * @return
     */
    BaseResponse<Boolean> updateLeadsClientStatus(LeadsClientStatusUpdateRequest request);

    BaseResponse<Boolean> batchUpdateLeadsClientStatus(BatchLeadsClientStatusUpdateRequest request);

    BaseResponse<Boolean> updateStaffId(LeadsCreateUpdateRequest request);
}

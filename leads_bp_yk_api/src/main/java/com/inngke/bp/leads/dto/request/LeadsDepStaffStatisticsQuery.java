package com.inngke.bp.leads.dto.request;

import com.google.common.collect.Sets;
import com.inngke.common.dto.request.BaseBidOptRequest;
import lombok.Data;

import java.util.Set;

/**
 * 线索部门员工数据统计列表查询入参
 *
 * <AUTHOR>
 */
@Data
public class LeadsDepStaffStatisticsQuery extends BaseBidOptRequest {

    /**
     * 当前的员工编号，对应的t_qy_wx_staff.id
     *
     * @demo 1
     */
    private Long staffId;

    /**
     * 当前列表tab页下的部门编号
     *
     * @demo 1
     */
    private Long departmentId;

    /**
     * 用于搜索员工的关键字
     *
     * @demo 张三
     */
    private String keyword;

    private Long customerId;

    private Long startDistributeTime;

    private Long endDistributeTime;

    /**
     * 执行中构建参数
     */
    private Set<Long> managerDepartmentIds;

    private Set<Long> filterDepartmentIds = Sets.newHashSet();

    private Set<Long> filterStaffIds = Sets.newHashSet();
}

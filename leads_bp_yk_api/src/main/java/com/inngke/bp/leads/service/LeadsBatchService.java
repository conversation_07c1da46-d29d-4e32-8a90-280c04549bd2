package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.BatchGetLeadsQuery;
import com.inngke.bp.leads.dto.request.LeadsBatchGetRequest;
import com.inngke.bp.leads.dto.request.LeadsBatchImportRequest;
import com.inngke.bp.leads.dto.request.LeadsBatchListRequest;
import com.inngke.bp.leads.dto.response.LeadsBatchDto;
import com.inngke.bp.leads.dto.response.LeadsBatchImportDto;
import com.inngke.bp.leads.dto.response.LeadsBillingIndicatorsDto;
import com.inngke.bp.leads.dto.response.LeadsDraftDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索批量接口
 * @since 2021/9/8 8:04 PM
 */
public interface LeadsBatchService {
    /**
     * 创建批量导入
     *
     * @param request 批量导入请求
     * @return 返回批次ID，可以通过此批次ID查询批次处理结果
     */
    BaseResponse<Long> create(LeadsBatchImportRequest request);

    /**
     * 查询线索导入批次信息
     *
     * @param request 批次请求
     * @return 线索批次信息
     */
    BaseResponse<LeadsBatchDto> get(LeadsBatchGetRequest request);

    /**
     * 查询某个批次的线索预览
     *
     * @param request 批次请求
     * @return 某个批次的线索列表
     */
    BaseResponse<BasePaginationResponse<LeadsDraftDto>> getLeadsDraftList(LeadsBatchListRequest request);

    /**
     * 导入某个批次
     *
     * @param request 批次请求
     * @return 是否导入成功
     */
    BaseResponse<LeadsBatchImportDto> importLeadsBatch(LeadsBatchGetRequest request);

    /**
     * 查询30天内所有的批次状态
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsBatchDto>> getBatchList(BaseBidOptRequest request);

    BaseResponse<BasePaginationResponse<LeadsBillingIndicatorsDto>> batchGetLeads(BatchGetLeadsQuery query);
}

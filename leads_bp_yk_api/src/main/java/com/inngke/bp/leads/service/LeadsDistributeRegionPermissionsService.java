package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.SaveRegionPermissionsRequest;
import com.inngke.bp.leads.dto.response.RegionConfDto;
import com.inngke.bp.leads.dto.response.RegionPermissionsDto;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface LeadsDistributeRegionPermissionsService {

    /**
     * 获取区域权限配置
     */
    BaseResponse<List<RegionConfDto>> getRegionPermissions(BaseBidRequest request);

    /**
     * 保存区域权限配置
     */
    BaseResponse<Boolean> saveRegionPermissions(SaveRegionPermissionsRequest request);

    BaseResponse<Boolean> removeRegionPermissions(BaseIdRequest request);
}

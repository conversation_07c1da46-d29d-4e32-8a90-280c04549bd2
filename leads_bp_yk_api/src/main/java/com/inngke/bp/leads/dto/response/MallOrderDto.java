package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/31 8:05 PM
 */
public class MallOrderDto implements Serializable {

    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 价格
     */
    private String price;

    /**
     * 修改后的价格
     */
    private String newPrice;


    private Integer credit;


    private Integer dispatch;


    private String dispatchName;


    private String dispatchPrice;


    private Integer status;

    private Integer isComment;

    private Long createTime;

    private Long coupon_id;

    private String couponMoney;

    private Integer orderType;

    private Integer payType;

    private String payTypeName;

    private String discount;

    private String isPreOrder;

    private Integer preStatus;

    private Integer isOfflinePayPre;

    private Long cardId;

    private String addressAddress;

    private String addressArea;

    private String addressCity;

    private String addressProvince;

    private String addressRealName;

    private String addressMobile;

    private String refundInfo;

    private Integer type;

    private String statusName;

    private Integer hasCommentFinished;

    private String goodsTotalMoney;

    private List<GoodDto> goodDtoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(String newPrice) {
        this.newPrice = newPrice;
    }

    public Integer getCredit() {
        return credit;
    }

    public void setCredit(Integer credit) {
        this.credit = credit;
    }

    public Integer getDispatch() {
        return dispatch;
    }

    public void setDispatch(Integer dispatch) {
        this.dispatch = dispatch;
    }

    public String getDispatchName() {
        return dispatchName;
    }

    public void setDispatchName(String dispatchName) {
        this.dispatchName = dispatchName;
    }

    public String getDispatchPrice() {
        return dispatchPrice;
    }

    public void setDispatchPrice(String dispatchPrice) {
        this.dispatchPrice = dispatchPrice;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsComment() {
        return isComment;
    }

    public void setIsComment(Integer isComment) {
        this.isComment = isComment;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getCoupon_id() {
        return coupon_id;
    }

    public void setCoupon_id(Long coupon_id) {
        this.coupon_id = coupon_id;
    }

    public String getCouponMoney() {
        return couponMoney;
    }

    public void setCouponMoney(String couponMoney) {
        this.couponMoney = couponMoney;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public String getIsPreOrder() {
        return isPreOrder;
    }

    public void setIsPreOrder(String isPreOrder) {
        this.isPreOrder = isPreOrder;
    }

    public Integer getPreStatus() {
        return preStatus;
    }

    public void setPreStatus(Integer preStatus) {
        this.preStatus = preStatus;
    }

    public Integer getIsOfflinePayPre() {
        return isOfflinePayPre;
    }

    public void setIsOfflinePayPre(Integer isOfflinePayPre) {
        this.isOfflinePayPre = isOfflinePayPre;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getAddressAddress() {
        return addressAddress;
    }

    public void setAddressAddress(String addressAddress) {
        this.addressAddress = addressAddress;
    }

    public String getAddressArea() {
        return addressArea;
    }

    public void setAddressArea(String addressArea) {
        this.addressArea = addressArea;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressProvince() {
        return addressProvince;
    }

    public void setAddressProvince(String addressProvince) {
        this.addressProvince = addressProvince;
    }

    public String getAddressRealName() {
        return addressRealName;
    }

    public void setAddressRealName(String addressRealName) {
        this.addressRealName = addressRealName;
    }

    public String getAddressMobile() {
        return addressMobile;
    }

    public void setAddressMobile(String addressMobile) {
        this.addressMobile = addressMobile;
    }

    public String getRefundInfo() {
        return refundInfo;
    }

    public void setRefundInfo(String refundInfo) {
        this.refundInfo = refundInfo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Integer getHasCommentFinished() {
        return hasCommentFinished;
    }

    public void setHasCommentFinished(Integer hasCommentFinished) {
        this.hasCommentFinished = hasCommentFinished;
    }

    public String getGoodsTotalMoney() {
        return goodsTotalMoney;
    }

    public void setGoodsTotalMoney(String goodsTotalMoney) {
        this.goodsTotalMoney = goodsTotalMoney;
    }

    public List<GoodDto> getGoodDtoList() {
        return goodDtoList;
    }

    public void setGoodDtoList(List<GoodDto> goodDtoList) {
        this.goodDtoList = goodDtoList;
    }
}

package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/9/8 8:12 PM
 */
public class LeadsDraftDto implements Serializable {

    /**
     * 自增ID
     *
     * @demo 134543
     */
    private Long id;

    /**
     * 商户ID
     *
     * @demo 1142
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id，0表示未关联上
     *
     * @demo 0
     */
    private Long customerId;

    /**
     * 姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 手机号码
     *
     * @demo 13800138000
     */
    private String mobile;

    /**
     * 省份ID，0表示未匹配
     *
     * @demo 123
     */
    private Integer provinceId;

    /**
     * 省份名称
     *
     * @demo 广东省
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     *
     * @demo 1234
     */
    private Integer cityId;

    /**
     * 城市名称
     *
     * @demo 广州市
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     *
     * @demo 13231
     */
    private Integer areaId;

    /**
     * 区域名称
     *
     * @demo 越秀区
     */
    private String areaName;

    /**
     * 详细地址
     *
     * @demo 流花展贸中心10号馆
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     *
     * @demo 1
     */
    private Integer channel;

    /**
     * 下单账号
     *
     * @demo 1234564
     */
    private String orderAccount;

    /**
     * 订单编号
     *
     * @demo SN-FW32921
     */
    private String orderSn;

    /**
     * 商品名称
     *
     * @demo 霸道吊顶
     */
    private String goodsName;

    /**
     * 订购商品数量
     *
     * @demo 1
     */
    private Integer goodsNum;

    /**
     * 付款时间
     *
     * @demo *************
     */
    private Long payTime;

    /**
     * 付款金额
     *
     * @demo 12324.00
     */
    private BigDecimal payAmount;

    /**
     * 订单留言
     *
     * @demo 好东西
     */
    private String orderMessage;

    /**
     * 其它备注
     *
     * @demo 没有了
     */
    private String remark;

    /**
     * 外部平台线索ID
     *
     * @demo 234354
     */
    private String tpLeadsId;

    /**
     * 广告活动名称
     *
     * @demo 9.9元包邮大活动
     */
    private String promotionName;

    /**
     * 报名时间
     *
     * @demo *************
     */
    private Long registryTime;

    /**
     * 需求时间：0=未指定 1=一个月内 2=三个月内 3=六个月内 4=一年内 5=一年以上
     *
     * @demo 1
     */
    private Integer expectIn;

    /**
     * 装修风格，详见枚举
     *
     * @demo 23
     */
    private Integer style;

    /**
     * 批次ID，0表示非批次导入
     *
     * @demo 1234353
     */
    private Long batchId;

    /**
     * 异常信息
     *
     * @demo 稳得一批，没有异常
     */
    private String errorMsg;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long createTime;

    /**
     * 更新时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getOrderAccount() {
        return orderAccount;
    }

    public void setOrderAccount(String orderAccount) {
        this.orderAccount = orderAccount;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getGoodsNum() {
        return goodsNum;
    }

    public void setGoodsNum(Integer goodsNum) {
        this.goodsNum = goodsNum;
    }

    public Long getPayTime() {
        return payTime;
    }

    public void setPayTime(Long payTime) {
        this.payTime = payTime;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getOrderMessage() {
        return orderMessage;
    }

    public void setOrderMessage(String orderMessage) {
        this.orderMessage = orderMessage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTpLeadsId() {
        return tpLeadsId;
    }

    public void setTpLeadsId(String tpLeadsId) {
        this.tpLeadsId = tpLeadsId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Long getRegistryTime() {
        return registryTime;
    }

    public void setRegistryTime(Long registryTime) {
        this.registryTime = registryTime;
    }

    public Integer getExpectIn() {
        return expectIn;
    }

    public void setExpectIn(Integer expectIn) {
        this.expectIn = expectIn;
    }

    public Integer getStyle() {
        return style;
    }

    public void setStyle(Integer style) {
        this.style = style;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}

package com.inngke.bp.leads.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/7 4:13 PM
 */
public class LeadsDto extends LeadsListItemDto {


    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 下单账号
     *
     * @demo <EMAIL>
     */
    private String orderAccount;

    /**
     * 订单编号
     *
     * @demo SN-DQEFR1024
     */
    private String orderSn;

    /**
     * 商品名称
     *
     * @demo 豪华无敌吊顶
     */
    private String goodsName;

    /**
     * 订购商品数量
     *
     * @demo 1
     */
    private Integer goodsNum;

    /**
     * 付款时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long payTime;

    /**
     * 付款金额
     *
     * @demo 1000.05
     */
    private BigDecimal payAmount;

    /**
     * 订单留言
     *
     * @demo 要做好呀
     */
    private String orderMessage;

    /**
     * 其它备注
     *
     * @demo 没了
     */
    private String remark;

    /**
     * 外部平台线索ID
     *
     * @demo 12343
     */
    private String tpLeadsId;

    /**
     * 广告活动名称
     *
     * @demo 1元秒杀
     */
    private String promotionName;

    /**
     * 报名时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long registryTime;

    /**
     * 需求时间：0=未指定 1=一个月内 2=三个月内 3=六个月内 4=一年内 5=一年以上
     *
     * @demo 1
     */
    private Integer expectIn;

    /**
     * 装修风格，详见枚举
     *
     * @demo 1
     */
    private Integer style;

    /**
     * 批次ID，0表示非批次导入
     *
     * @demo 1232
     */
    private Long batchId;

    private Integer showPhone;

    /**
     * 商品链接
     *
     * @demo https://e.duoec.com/open/admin/index/beid/1/agent/1#/clue/list
     */
    private String goodsLink;

    /**
     * 自动分配状态  -1：失败  0：非自动分配 1:分配成功
     */
    private Integer distributeStatus;

    /**
     * 数据来源
     */
    private String channelTypeText;

    /**
     * 额外数据
     */
    private String extData;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 外部标签
     */
    private List<String> externalTags;


    /**
     * 标签带类型
     */
    private List tagsList;

    /**
     * 线索个数
     */
    private Integer tagsSize;

    private Long createStaffId;

    private String createStaffName;

    private Integer type;

    private String level;

    private Integer levelId;

    /**
     * 客户等级
     *
     * @demo A
     */
    private String levelText;

    /**
     * 客户等级详细说明
     *
     * @demo 有明确意向
     */
    private String levelDes;

    /**
     * 已经被回收的线索Id
     */
    private String recoveryFromIds;

    /**
     * 需求产品
     */
    private String demandProduct;

    /**
     * 首次联系时间
     *
     * @demo 19392939293000
     */
    private Long firstContactTime;

    /**
     * 平台id
     *
     * @demo 19392939293000
     */
    private String tpId;

    /**
     * 客服最后联系时间(单位:毫秒)
     */
    private Long lastContactTime;

    /**
     * 最新跟进时间(单位:毫秒)
     */
    private Long lastFollowTime;

    /**
     * 分配时间(单位:毫秒)
     */
    private Long distributeTime;

    /**
     * 产品ids
     */
    private List<Long> productIds;

    public Long getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(Long lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public Long getLastContactTime() {
        return lastContactTime;
    }

    public void setLastContactTime(Long lastContactTime) {
        this.lastContactTime = lastContactTime;
    }

    public String getDemandProduct() {
        return demandProduct;
    }

    public void setDemandProduct(String demandProduct) {
        this.demandProduct = demandProduct;
    }
    /**
     * 关联客户时间
     */
    private LocalDateTime relationClientTime;


    public String getLevelText() {
        return levelText;
    }

    public void setLevelText(String levelText) {
        this.levelText = levelText;
    }

    public String getLevelDes() {
        return levelDes;
    }

    public void setLevelDes(String levelDes) {
        this.levelDes = levelDes;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public String getRecoveryFromIds() {
        return recoveryFromIds;
    }

    public void setRecoveryFromIds(String recoveryFromIds) {
        this.recoveryFromIds = recoveryFromIds;
    }

    public List getTagsList() {
        return tagsList;
    }

    public void setTagsList(List tagsList) {
        this.tagsList = tagsList;
    }

    public Integer getTagsSize() {
        return tagsSize;
    }

    public void setTagsSize(Integer tagsSize) {
        this.tagsSize = tagsSize;
    }

    public Integer getDistributeStatus() {
        return distributeStatus;
    }

    public void setDistributeStatus(Integer distributeStatus) {
        this.distributeStatus = distributeStatus;
    }

    public String getOrderAccount() {
        return orderAccount;
    }

    public void setOrderAccount(String orderAccount) {
        this.orderAccount = orderAccount;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getGoodsNum() {
        return goodsNum;
    }

    public void setGoodsNum(Integer goodsNum) {
        this.goodsNum = goodsNum;
    }

    public Long getPayTime() {
        return payTime;
    }

    public void setPayTime(Long payTime) {
        this.payTime = payTime;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getOrderMessage() {
        return orderMessage;
    }

    public void setOrderMessage(String orderMessage) {
        this.orderMessage = orderMessage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTpLeadsId() {
        return tpLeadsId;
    }

    public void setTpLeadsId(String tpLeadsId) {
        this.tpLeadsId = tpLeadsId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Long getRegistryTime() {
        return registryTime;
    }

    public void setRegistryTime(Long registryTime) {
        this.registryTime = registryTime;
    }

    public Integer getExpectIn() {
        return expectIn;
    }

    public void setExpectIn(Integer expectIn) {
        this.expectIn = expectIn;
    }

    public Integer getStyle() {
        return style;
    }

    public void setStyle(Integer style) {
        this.style = style;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getChannelTypeText() {
        return channelTypeText;
    }

    public void setChannelTypeText(String channelTypeText) {
        this.channelTypeText = channelTypeText;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Integer getShowPhone() {
        return showPhone;
    }

    public void setShowPhone(Integer showPhone) {
        this.showPhone = showPhone;
    }

    public String getGoodsLink() {
        return goodsLink;
    }

    public void setGoodsLink(String goodsLink) {
        this.goodsLink = goodsLink;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public String getCreateStaffName() {
        return createStaffName;
    }

    public void setCreateStaffName(String createStaffName) {
        this.createStaffName = createStaffName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public LocalDateTime getRelationClientTime() {
        return relationClientTime;
    }

    public void setRelationClientTime(LocalDateTime relationClientTime) {
        this.relationClientTime = relationClientTime;
    }

    public Long getFirstContactTime() {
        return firstContactTime;
    }

    public void setFirstContactTime(Long firstContactTime) {
        this.firstContactTime = firstContactTime;
    }

    public String getTpId() {
        return tpId;
    }

    public void setTpId(String tpId) {
        this.tpId = tpId;
    }

    public List<String> getExternalTags() {
        return externalTags;
    }

    public void setExternalTags(List<String> externalTags) {
        this.externalTags = externalTags;
    }

    @Override
    public Long getDistributeTime() {
        return distributeTime;
    }

    @Override
    public void setDistributeTime(Long distributeTime) {
        this.distributeTime = distributeTime;
    }

    public List<Long> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<Long> productIds) {
        this.productIds = productIds;
    }
}

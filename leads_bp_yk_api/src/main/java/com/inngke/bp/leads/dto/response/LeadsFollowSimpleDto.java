package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/9/9 12:06 PM
 */
public class LeadsFollowSimpleDto implements Serializable {
    /**
     * 自增ID
     *
     * @demo 1234
     */
    private Long id;

    /**
     * 自增ID
     *
     * @demo 1234
     */
    private Long leadsId;

    /**
     * 跟进内容
     *
     * @demo 已量尺
     */
    private String followContent;

    /**
     * 跟进人
     *
     * @demo 张三
     */
    private String followUserName;

    private Long followUserId;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getFollowUserName() {
        return followUserName;
    }

    public void setFollowUserName(String followUserName) {
        this.followUserName = followUserName;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getFollowUserId() {
        return followUserId;
    }

    public void setFollowUserId(Long followUserId) {
        this.followUserId = followUserId;
    }
}

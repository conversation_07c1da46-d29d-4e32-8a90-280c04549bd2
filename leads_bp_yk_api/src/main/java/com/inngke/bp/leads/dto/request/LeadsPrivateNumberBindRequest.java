package com.inngke.bp.leads.dto.request;


import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 线索绑定隐私号码请求类
 * <AUTHOR>
 */
public class LeadsPrivateNumberBindRequest extends BaseBidOptRequest {

    /**
     * 线索id
     */
    private Long leadsId;

    /**
     * 导购ID
     * @demo 321
     */
    @NotNull
    @Min(value = 1)
    private Long guideId;

    /**
     * 消费者ID
     * @demo 123
     */
    @NotNull
    @Min(value = 1)
    private Long customerId;

    /**
     * 导购手机号码
     * @demo 1324122
     */
    private String guideMobile;

    /**
     * 客户手机号码
     * @demo 131231
     */
    private String customerMobile;

    /**
     * 来源：0=点击拨打手机号 1=点击获取完整手机号
     */
    private Integer source;

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getGuideId() {
        return guideId;
    }

    public void setGuideId(Long guideId) {
        this.guideId = guideId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getGuideMobile() {
        return guideMobile;
    }

    public void setGuideMobile(String guideMobile) {
        this.guideMobile = guideMobile;
    }

    public String getCustomerMobile() {
        return customerMobile;
    }

    public void setCustomerMobile(String customerMobile) {
        this.customerMobile = customerMobile;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }
}

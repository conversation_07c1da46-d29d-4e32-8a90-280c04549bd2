package com.inngke.bp.leads.dto.response;

import com.google.common.collect.Lists;
import com.inngke.bp.leads.enums.LeadsStatusEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * ES线索实体
 *
 * <AUTHOR>
 */
public class LeadsEsDto implements Serializable {
    /**
     * 索引ID
     *
     * @demo 1024
     */
    private String id;

    /**
     * 线索ID
     */
    private Long leadsId;

    /**
     * 商户ID
     *
     * @demo 1142
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id
     *
     * @demo 102432
     */
    private Long customerId;

    /**
     * 关联的用户UID，即customer.uid
     *
     * @demo 102432
     */
    private Long customerUid;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 手机号码
     *
     * @demo 13800138000
     */
    private String mobile;

    /**
     * 线索状态： -4=删除 -3=无效线索 -2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交
     *
     * @demo 1
     * @see LeadsStatusEnum
     */
    private Integer status;

    /**
     * 线索的历史状态
     */
    private Set<Integer> statusLog;

    /**
     * 客服是否添加跟进记录,1客服添加了跟进记录
     */
    private Integer preFollowLeadsUp;

    /**
     * 省份ID，0表示未匹配
     *
     * @demo 123
     */
    private Integer provinceId;

    /**
     * 省份名称
     *
     * @demo 广东省
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     *
     * @demo 4232
     */
    private Integer cityId;

    /**
     * 是否在24小时内联系 0否 1是
     */
    private Integer contactIn24;

    /**
     * 城市名称
     *
     * @demo 广州市
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     *
     * @demo 102432
     */
    private Integer areaId;

    /**
     * 区域名称
     *
     * @demo 越秀区
     */
    private String areaName;

    /**
     * 详细地址
     *
     * @demo 流花展贸中心10号馆
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     *
     * @demo 1
     */
    private Integer channel;

    /**
     * 分配的经销商ID，即shop_agent_id
     *
     * @demo 10086
     */
    private Long distributeAgentId;

    /**
     * 分配给哪个员工，即staffId
     *
     * @demo 1008613
     */
    private Long distributeStaffId;

    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String distributeStaffName;

    /**
     * 职位
     *
     * @demo 啊啊啊
     */
    private String position;

    /**
     * 经销商姓名
     *
     * @demo 张三
     */
    private String distributeAgentName;

    /**
     * 分配时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long distributeTime;

    /**
     * 分配线索给客服的时间
     */
    private Long distributeFollowTime;

    /**
     * 退回时间时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long pushBackTime;

    /**
     * 商品链接
     */
    private String goodsLink;

    /**
     * 经销商ID
     */
    private Long agentId;

    /**
     * 经销商名字
     */
    private String agentName;

    /**
     * 跟进人员工ID，即staffId
     */
    private Long staffId;

    /**
     * 跟进人员工名字
     */
    private String staffName;

    /**
     * 导购是否被删除 0是，1否
     */
    private Integer staffIsDel;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门名字
     */
    private String departmentName;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 线索分配的时间 时间戳
     */
    private Long distributeTimeL;

    /**
     * 被标识为无效的时间
     */
    private Long stateNoAvail;

    /**
     * 待联系数
     */
    private int contactCount;

    /**
     * 首次联系时间
     */
    private Long stateContact;

    /**
     * 成功联系时间
     */
    private Long stateContactSuccess;

    /**
     * 量尺时间
     */
    private Long stateMeasuring;

    /**
     * 到店时间
     */
    private Long stateArrivalStore;

    /**
     * 定金记录创建时间
     */
    private Long stateDeposit;

    /**
     * 定金金额
     */
    private BigDecimal depositAmount;
    /**
     * 定金订单数量
     */
    private Integer depositOrderCount;

    /**
     * 全款订单金额
     */
    private String payAmount;

    /**
     * 全款订单数量
     */
    private Integer fullPayOrderCount;


    /**
     * 成交时间
     */
    private Long stateOrderSuccess;

    /**
     * 是否有成交单
     */
    private Integer hasStoreOrder;

    /**
     * 是否有量尺记录
     */
    private Integer hasMeasure;

    /**
     * 实际成交金额
     */
    private BigDecimal orderAmount;

    /**
     * 报价时间
     */
    private Long stateOfferPrice;

    /**
     * 安装时间
     */
    private Long stateInstall;
    /**
     * 首次拨号间隔时间(秒)
     */
    private Long firstCallIntervalTime;

    /**
     * 各级部门
     *
     * @demo {1, 2, 3}
     */
    private List<Long> deptIds;

    private List<Long> belongingDepartmentIds;

    private List<Long> historyDistributeStaffIds = Lists.newArrayList();

    /**
     * 各个员工
     *
     * @demo {1, 2, 3}
     */
    private List<Long> staffIds;

    /**
     * 客服id
     *
     * @demo 3030
     */
    private Long preFollowStaffId;

    /**
     * 客服所在部门
     */
    private Set<Long> preFollowDepartmentIds;

    /**
     * 客服接待时线索状态
     *
     * @demo 1
     */
    private Integer preFollowStatus;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long createTime;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long updateTime;

    /**
     * 订单编号
     *
     * @demo SN-FW32921
     */
    private String orderSn;

    /**
     * 线索来源类型
     */
    private Integer channelType;

    /**
     * 线索来源渠道
     */
    private Integer channelSource;

    /**
     * 标签
     */
    private String tags;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 下单账号
     */
    private String orderAccount;

    /**
     * 额外数据
     */
    private String extData;

    /**
     * 等级
     */
    private String level;

    /**
     * 等级Id
     */
    private Integer levelId;

    /**
     * 创建人staffId
     */
    private Long createStaffId;

    /**
     * 创建人部门ID
     */
    private Set<Long> createDepartmentId;

    /**
     * 退回人staffId
     */
    private Long pushBackStaffId;

    /**
     * 退回人部门ID
     */
    private Set<Long> pushbackDepartmentId;

    /**
     * 退回人agentId
     */
    private Long pushBackAgentId;

    /**
     * 最后一次异常信息
     */
    private String errorMsg;

    /**
     * 最新的原因id
     */
    private Long lastReasonId;

    /**
     * 客户微信号
     *
     * @demo 154514
     */
    private String weChatId;

    /**
     * ；客户标签
     */
    private List<String> tagsList;

    /**
     * 企业标签
     */
    private List<String> enterpriseTags;

    /**
     * 计划名称
     */
    private String campaignName;

    /**
     * 客户状态
     */
    private Integer clientStatus;

    /**
     * 最后跟进时间
     */
    private Long lastFollowTime;

    /**
     * 产品ids
     */
    private List<Long> productIds;

    /**
     * 上报员工id
     */
    private Long reportStaffId;

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    /**
     * 转客户的Id，默认=0
     */
    private Long clientId;

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public Integer getPreFollowLeadsUp() {
        return preFollowLeadsUp;
    }

    public void setPreFollowLeadsUp(Integer preFollowLeadsUp) {
        this.preFollowLeadsUp = preFollowLeadsUp;
    }

    public Integer getPreFollowStatus() {
        return preFollowStatus;
    }

    public void setPreFollowStatus(Integer preFollowStatus) {
        this.preFollowStatus = preFollowStatus;
    }

    public Long getPreFollowStaffId() {
        return preFollowStaffId;
    }

    public void setPreFollowStaffId(Long preFollowStaffId) {
        this.preFollowStaffId = preFollowStaffId;
    }

    public Set<Long> getPreFollowDepartmentIds() {
        return preFollowDepartmentIds;
    }

    public void setPreFollowDepartmentIds(Set<Long> preFollowDepartmentIds) {
        this.preFollowDepartmentIds = preFollowDepartmentIds;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getCustomerUid() {
        return customerUid;
    }

    public void setCustomerUid(Long customerUid) {
        this.customerUid = customerUid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Long getDistributeAgentId() {
        return distributeAgentId;
    }

    public void setDistributeAgentId(Long distributeAgentId) {
        this.distributeAgentId = distributeAgentId;
    }

    public Long getDistributeStaffId() {
        return distributeStaffId;
    }

    public void setDistributeStaffId(Long distributeStaffId) {
        this.distributeStaffId = distributeStaffId;
    }

    public String getDistributeStaffName() {
        return distributeStaffName;
    }

    public void setDistributeStaffName(String distributeStaffName) {
        this.distributeStaffName = distributeStaffName;
    }

    public String getDistributeAgentName() {
        return distributeAgentName;
    }

    public void setDistributeAgentName(String distributeAgentName) {
        this.distributeAgentName = distributeAgentName;
    }

    public Long getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Long distributeTime) {
        this.distributeTime = distributeTime;
    }

    public Long getDistributeFollowTime() {
        return distributeFollowTime;
    }

    public void setDistributeFollowTime(Long distributeFollowTime) {
        this.distributeFollowTime = distributeFollowTime;
    }

    public Long getPushBackTime() {
        return pushBackTime;
    }

    public void setPushBackTime(Long pushBackTime) {
        this.pushBackTime = pushBackTime;
    }

    public List<Long> getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }

    public List<Long> getStaffIds() {
        return staffIds;
    }

    public void setStaffIds(List<Long> staffIds) {
        this.staffIds = staffIds;
    }

    public Integer getContactIn24() {
        return contactIn24;
    }

    public void setContactIn24(Integer contactIn24) {
        this.contactIn24 = contactIn24;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Set<Integer> getStatusLog() {
        return statusLog;
    }

    public void setStatusLog(Set<Integer> statusLog) {
        this.statusLog = statusLog;
    }

    public List<Long> getHistoryDistributeStaffIds() {
        return historyDistributeStaffIds;
    }

    public void setHistoryDistributeStaffIds(List<Long> historyDistributeStaffIds) {
        this.historyDistributeStaffIds = historyDistributeStaffIds;
    }

    public String getGoodsLink() {
        return goodsLink;
    }

    public void setGoodsLink(String goodsLink) {
        this.goodsLink = goodsLink;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getDistributeTimeL() {
        return distributeTimeL;
    }

    public void setDistributeTimeL(Long distributeTimeL) {
        this.distributeTimeL = distributeTimeL;
    }

    public Long getStateNoAvail() {
        return stateNoAvail;
    }

    public void setStateNoAvail(Long stateNoAvail) {
        this.stateNoAvail = stateNoAvail;
    }

    public Long getStateContact() {
        return stateContact;
    }

    public void setStateContact(Long stateContact) {
        this.stateContact = stateContact;
    }

    public Long getStateContactSuccess() {
        return stateContactSuccess;
    }

    public void setStateContactSuccess(Long stateContactSuccess) {
        this.stateContactSuccess = stateContactSuccess;
    }

    public Long getStateMeasuring() {
        return stateMeasuring;
    }

    public void setStateMeasuring(Long stateMeasuring) {
        this.stateMeasuring = stateMeasuring;
    }

    public Long getStateArrivalStore() {
        return stateArrivalStore;
    }

    public void setStateArrivalStore(Long stateArrivalStore) {
        this.stateArrivalStore = stateArrivalStore;
    }

    public Long getStateDeposit() {
        return stateDeposit;
    }

    public void setStateDeposit(Long stateDeposit) {
        this.stateDeposit = stateDeposit;
    }

    public Long getStateOrderSuccess() {
        return stateOrderSuccess;
    }

    public void setStateOrderSuccess(Long stateOrderSuccess) {
        this.stateOrderSuccess = stateOrderSuccess;
    }

    public Long getStateOfferPrice() {
        return stateOfferPrice;
    }

    public void setStateOfferPrice(Long stateOfferPrice) {
        this.stateOfferPrice = stateOfferPrice;
    }

    public Long getStateInstall() {
        return stateInstall;
    }

    public void setStateInstall(Long stateInstall) {
        this.stateInstall = stateInstall;
    }

    public Integer getStaffIsDel() {
        return staffIsDel;
    }

    public void setStaffIsDel(Integer staffIsDel) {
        this.staffIsDel = staffIsDel;
    }

    public List<Long> getBelongingDepartmentIds() {
        return belongingDepartmentIds;
    }

    public void setBelongingDepartmentIds(List<Long> belongingDepartmentIds) {
        this.belongingDepartmentIds = belongingDepartmentIds;
    }


    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public int getContactCount() {
        return contactCount;
    }

    public void setContactCount(int contactCount) {
        this.contactCount = contactCount;
    }

    public Long getFirstCallIntervalTime() {
        return firstCallIntervalTime;
    }

    public void setFirstCallIntervalTime(Long firstCallIntervalTime) {
        this.firstCallIntervalTime = firstCallIntervalTime;
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Integer getDepositOrderCount() {
        return depositOrderCount;
    }

    public void setDepositOrderCount(Integer depositOrderCount) {
        this.depositOrderCount = depositOrderCount;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getFullPayOrderCount() {
        return fullPayOrderCount;
    }

    public void setFullPayOrderCount(Integer fullPayOrderCount) {
        this.fullPayOrderCount = fullPayOrderCount;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(Integer channelSource) {
        this.channelSource = channelSource;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getOrderAccount() {
        return orderAccount;
    }

    public void setOrderAccount(String orderAccount) {
        this.orderAccount = orderAccount;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Long getPushBackStaffId() {
        return pushBackStaffId;
    }

    public void setPushBackStaffId(Long pushBackStaffId) {
        this.pushBackStaffId = pushBackStaffId;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Set<Long> getCreateDepartmentId() {
        return createDepartmentId;
    }

    public void setCreateDepartmentId(Set<Long> createDepartmentId) {
        this.createDepartmentId = createDepartmentId;
    }

    public Set<Long> getPushbackDepartmentId() {
        return pushbackDepartmentId;
    }

    public void setPushbackDepartmentId(Set<Long> pushbackDepartmentId) {
        this.pushbackDepartmentId = pushbackDepartmentId;
    }

    public List<String> getTagsList() {
        return tagsList;
    }

    public void setTagsList(List<String> tagsList) {
        this.tagsList = tagsList;
    }

    public String getWeChatId() {
        return weChatId;
    }

    public void setWeChatId(String weChatId) {
        this.weChatId = weChatId;
    }

    public Long getPushBackAgentId() {
        return pushBackAgentId;
    }

    public void setPushBackAgentId(Long pushBackAgentId) {
        this.pushBackAgentId = pushBackAgentId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getLastReasonId() {
        return lastReasonId;
    }

    public void setLastReasonId(Long lastReasonId) {
        this.lastReasonId = lastReasonId;
    }

    public void setClientStatus(Integer clientStatus) {
        this.clientStatus = clientStatus;
    }

    public Integer getClientStatus() {
        return clientStatus;
    }

    public Long getLastFollowTime() {
        return lastFollowTime;
    }

    public LeadsEsDto setLastFollowTime(Long lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
        return this;
    }

    public Integer getHasStoreOrder() {
        return hasStoreOrder;
    }

    public void setHasStoreOrder(Integer hasStoreOrder) {
        this.hasStoreOrder = hasStoreOrder;
    }

    public List<Long> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<Long> productIds) {
        this.productIds = productIds;
    }

    public Integer getHasMeasure() {
        return hasMeasure;
    }

    public void setHasMeasure(Integer hasMeasure) {
        this.hasMeasure = hasMeasure;
    }

    public List<String> getEnterpriseTags() {
        return enterpriseTags;
    }

    public LeadsEsDto setEnterpriseTags(List<String> enterpriseTags) {
        this.enterpriseTags = enterpriseTags;
        return this;
    }

    public Long getReportStaffId() {
        return reportStaffId;
    }

    public void setReportStaffId(Long reportStaffId) {
        this.reportStaffId = reportStaffId;
    }

    public static final String ID = "id";
    public static final String LEADS_ID = "leadsId";
    public static final String BID = "bid";
    public static final String CUSTOMER_ID = "customerId";
    public static final String CUSTOMER_UID = "customerUid";
    public static final String CHANNEL_ID = "channelId";
    public static final String NAME = "name";
    public static final String MOBILE = "mobile";
    public static final String STATUS = "status";
    public static final String STATUS_LOG = "statusLog";
    public static final String PRE_FOLLOW_LEADS_UP = "preFollowLeadsUp";
    public static final String PROVINCE_ID = "provinceId";
    public static final String PROVINCE_NAME = "provinceName";
    public static final String CITY_ID = "cityId";
    public static final String CONTACT_IN24 = "contactIn24";
    public static final String CITY_NAME = "cityName";
    public static final String AREA_ID = "areaId";
    public static final String AREA_NAME = "areaName";
    public static final String ADDRESS = "address";
    public static final String CHANNEL = "channel";
    public static final String DISTRIBUTE_AGENT_ID = "distributeAgentId";
    public static final String DISTRIBUTE_STAFF_ID = "distributeStaffId";
    public static final String DISTRIBUTE_STAFF_NAME = "distributeStaffName";
    public static final String POSITION = "position";
    public static final String DISTRIBUTE_AGENT_NAME = "distributeAgentName";
    public static final String DISTRIBUTE_TIME = "distributeTime";
    public static final String DISTRIBUTE_FOLLOW_TIME = "distributeFollowTime";
    public static final String PUSH_BACK_TIME = "pushBackTime";
    public static final String GOODS_LINK = "goodsLink";
    public static final String AGENT_ID = "agentId";
    public static final String AGENT_NAME = "agentName";
    public static final String STAFF_ID = "staffId";
    public static final String STAFF_NAME = "staffName";
    public static final String STAFF_IS_DEL = "staffIsDel";
    public static final String DEPARTMENT_ID = "departmentId";
    public static final String DEPARTMENT_NAME = "departmentName";
    public static final String PARENT_ID = "parentId";
    public static final String DISTRIBUTE_TIME_L = "distributeTimeL";
    public static final String STATE_NO_AVAIL = "stateNoAvail";
    public static final String CONTACT_COUNT = "contactCount";
    public static final String STATE_CONTACT = "stateContact";
    public static final String STATE_CONTACT_SUCCESS = "stateContactSuccess";
    public static final String STATE_MEASURING = "stateMeasuring";
    public static final String STATE_ARRIVAL_STORE = "stateArrivalStore";
    public static final String STATE_DEPOSIT = "stateDeposit";
    public static final String DEPOSIT_AMOUNT = "depositAmount";
    public static final String DEPOSIT_ORDER_COUNT = "depositOrderCount";
    public static final String PAY_AMOUNT = "payAmount";
    public static final String FULL_PAY_ORDER_COUNT = "fullPayOrderCount";
    public static final String STATE_ORDER_SUCCESS = "stateOrderSuccess";
    public static final String HAS_STORE_ORDER = "hasStoreOrder";

    public static final String HAS_MEASURE = "hasMeasure";
    public static final String ORDER_AMOUNT = "orderAmount";
    public static final String STATE_OFFER_PRICE = "stateOfferPrice";
    public static final String STATE_INSTALL = "stateInstall";
    public static final String FIRST_CALL_INTERVAL_TIME = "firstCallIntervalTime";
    public static final String DEPT_IDS = "deptIds";
    public static final String BELONGING_DEPARTMENT_IDS = "belongingDepartmentIds";
    public static final String HISTORY_DISTRIBUTE_STAFF_IDS = "historyDistributeStaffIds";
    public static final String STAFF_IDS = "staffIds";
    public static final String PRE_FOLLOW_STAFF_ID = "preFollowStaffId";
    public static final String PRE_FOLLOW_DEPARTMENT_IDS = "preFollowDepartmentIds";
    public static final String PRE_FOLLOW_STATUS = "preFollowStatus";
    public static final String CREATE_TIME = "createTime";
    public static final String UPDATE_TIME = "updateTime";
    public static final String ORDER_SN = "orderSn";
    public static final String CHANNEL_TYPE = "channelType";
    public static final String CHANNEL_SOURCE = "channelSource";
    public static final String TAGS = "tags";
    public static final String GOODS_NAME = "goodsName";
    public static final String ORDER_ACCOUNT = "orderAccount";
    public static final String EXT_DATA = "extData";
    public static final String LEVEL = "level";
    public static final String LEVEL_ID = "levelId";
    public static final String CREATE_STAFF_ID = "createStaffId";
    public static final String CREATE_DEPARTMENT_ID = "createDepartmentId";
    public static final String PUSH_BACK_STAFF_ID = "pushBackStaffId";
    public static final String PUSHBACK_DEPARTMENT_ID = "pushbackDepartmentId";
    public static final String PUSH_BACK_AGENT_ID = "pushBackAgentId";
    public static final String ERROR_MSG = "errorMsg";
    public static final String LAST_REASON_ID = "lastReasonId";
    public static final String WE_CHAT_ID = "weChatId";
    public static final String TAGS_LIST = "tagsList";
    public static final String CAMPAIGN_NAME = "campaignName";
    public static final String CLIENT_STATUS = "clientStatus";
    public static final String LAST_FOLLOW_TIME = "lastFollowTime";
    public static final String CLIENT_ID = "clientId";


    /**
     * keyword
     */
    public static final String NAME_KEYWORD = "name.keyword";
    public static final String MOBILE_KEYWORD = "mobile.keyword";
    public static final String STATUS_KEYWORD = "status.keyword";
    public static final String STATUS_LOG_KEYWORD = "statusLog.keyword";
    public static final String PRE_FOLLOW_LEADS_UP_KEYWORD = "preFollowLeadsUp.keyword";
    public static final String PROVINCE_ID_KEYWORD = "provinceId.keyword";
    public static final String PROVINCE_NAME_KEYWORD = "provinceName.keyword";
    public static final String CITY_ID_KEYWORD = "cityId.keyword";
    public static final String CONTACT_IN24_KEYWORD = "contactIn24.keyword";
    public static final String CITY_NAME_KEYWORD = "cityName.keyword";
    public static final String AREA_ID_KEYWORD = "areaId.keyword";
    public static final String AREA_NAME_KEYWORD = "areaName.keyword";
    public static final String ADDRESS_KEYWORD = "address.keyword";
    public static final String CHANNEL_KEYWORD = "channel.keyword";
    public static final String DISTRIBUTE_AGENT_ID_KEYWORD = "distributeAgentId.keyword";
    public static final String DISTRIBUTE_STAFF_ID_KEYWORD = "distributeStaffId.keyword";
    public static final String DISTRIBUTE_STAFF_NAME_KEYWORD = "distributeStaffName.keyword";
    public static final String POSITION_KEYWORD = "position.keyword";
    public static final String DISTRIBUTE_AGENT_NAME_KEYWORD = "distributeAgentName.keyword";
    public static final String DISTRIBUTE_TIME_KEYWORD = "distributeTime.keyword";
    public static final String DISTRIBUTE_FOLLOW_TIME_KEYWORD = "distributeFollowTime.keyword";
    public static final String PUSH_BACK_TIME_KEYWORD = "pushBackTime.keyword";
    public static final String GOODS_LINK_KEYWORD = "goodsLink.keyword";
    public static final String AGENT_ID_KEYWORD = "agentId.keyword";
    public static final String AGENT_NAME_KEYWORD = "agentName.keyword";
    public static final String STAFF_ID_KEYWORD = "staffId.keyword";
    public static final String STAFF_NAME_KEYWORD = "staffName.keyword";
    public static final String STAFF_IS_DEL_KEYWORD = "staffIsDel.keyword";
    public static final String DEPARTMENT_ID_KEYWORD = "departmentId.keyword";
    public static final String DEPARTMENT_NAME_KEYWORD = "departmentName.keyword";
    public static final String PARENT_ID_KEYWORD = "parentId.keyword";
    public static final String DISTRIBUTE_TIME_L_KEYWORD = "distributeTimeL.keyword";
    public static final String STATE_NO_AVAIL_KEYWORD = "stateNoAvail.keyword";
    public static final String CONTACT_COUNT_KEYWORD = "contactCount.keyword";
    public static final String STATE_CONTACT_KEYWORD = "stateContact.keyword";
    public static final String STATE_CONTACT_SUCCESS_KEYWORD = "stateContactSuccess.keyword";
    public static final String STATE_MEASURING_KEYWORD = "stateMeasuring.keyword";
    public static final String STATE_ARRIVAL_STORE_KEYWORD = "stateArrivalStore.keyword";
    public static final String STATE_DEPOSIT_KEYWORD = "stateDeposit.keyword";
    public static final String DEPOSIT_AMOUNT_KEYWORD = "depositAmount.keyword";
    public static final String DEPOSIT_ORDER_COUNT_KEYWORD = "depositOrderCount.keyword";
    public static final String PAY_AMOUNT_KEYWORD = "payAmount.keyword";
    public static final String FULL_PAY_ORDER_COUNT_KEYWORD = "fullPayOrderCount.keyword";
    public static final String STATE_ORDER_SUCCESS_KEYWORD = "stateOrderSuccess.keyword";
    public static final String HAS_STORE_ORDER_KEYWORD = "hasStoreOrder.keyword";
    public static final String ORDER_AMOUNT_KEYWORD = "orderAmount.keyword";
    public static final String STATE_OFFER_PRICE_KEYWORD = "stateOfferPrice.keyword";
    public static final String STATE_INSTALL_KEYWORD = "stateInstall.keyword";
    public static final String FIRST_CALL_INTERVAL_TIME_KEYWORD = "firstCallIntervalTime.keyword";
    public static final String DEPT_IDS_KEYWORD = "deptIds.keyword";
    public static final String BELONGING_DEPARTMENT_IDS_KEYWORD = "belongingDepartmentIds.keyword";
    public static final String HISTORY_DISTRIBUTE_STAFF_IDS_KEYWORD = "historyDistributeStaffIds.keyword";
    public static final String STAFF_IDS_KEYWORD = "staffIds.keyword";
    public static final String PRE_FOLLOW_STAFF_ID_KEYWORD = "preFollowStaffId.keyword";
    public static final String PRE_FOLLOW_DEPARTMENT_IDS_KEYWORD = "preFollowDepartmentIds.keyword";
    public static final String PRE_FOLLOW_STATUS_KEYWORD = "preFollowStatus.keyword";
    public static final String CREATE_TIME_KEYWORD = "createTime.keyword";
    public static final String UPDATE_TIME_KEYWORD = "updateTime.keyword";
    public static final String ORDER_SN_KEYWORD = "orderSn.keyword";
    public static final String CHANNEL_TYPE_KEYWORD = "channelType.keyword";
    public static final String CHANNEL_SOURCE_KEYWORD = "channelSource.keyword";
    public static final String TAGS_KEYWORD = "tags.keyword";
    public static final String GOODS_NAME_KEYWORD = "goodsName.keyword";
    public static final String ORDER_ACCOUNT_KEYWORD = "orderAccount.keyword";
    public static final String EXT_DATA_KEYWORD = "extData.keyword";
    public static final String LEVEL_KEYWORD = "level.keyword";
    public static final String LEVEL_ID_KEYWORD = "levelId.keyword";
    public static final String CREATE_STAFF_ID_KEYWORD = "createStaffId.keyword";
    public static final String CREATE_DEPARTMENT_ID_KEYWORD = "createDepartmentId.keyword";
    public static final String PUSH_BACK_STAFF_ID_KEYWORD = "pushBackStaffId.keyword";
    public static final String PUSHBACK_DEPARTMENT_ID_KEYWORD = "pushbackDepartmentId.keyword";
    public static final String PUSH_BACK_AGENT_ID_KEYWORD = "pushBackAgentId.keyword";
    public static final String ERROR_MSG_KEYWORD = "errorMsg.keyword";
    public static final String LAST_REASON_ID_KEYWORD = "lastReasonId.keyword";
    public static final String WECHATID_KEYWORD = "weChatId.keyword";
    public static final String TAGS_LIST_KEYWORD = "tagsList.keyword";
    public static final String ENTERPRISE_TAGS_KEYWORD = "enterpriseTags.keyword";
    public static final String CAMPAIGN_NAME_KEYWORD = "campaignName.keyword";
    public static final String LAST_FOLLOW_TIME_KEYWORD = "lastFollowTime.keyword";

    public static final String REPORT_STAFF_ID = "reportStaffId";
}

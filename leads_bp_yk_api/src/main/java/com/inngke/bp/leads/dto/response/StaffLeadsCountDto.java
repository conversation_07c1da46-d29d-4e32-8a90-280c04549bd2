package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/9/17 5:00 PM
 */
public class StaffLeadsCountDto implements Serializable {
    /**
     * 商户ID
     *
     * @demo 1
     */
    private Integer bid;

    /**
     * 员工ID
     *
     * @demo 1234
     */
    private Long staffId;

    /**
     * 新分配的线索数量
     *
     * @demo 123
     */
    private Integer count;

    /**
     * 待跟进的线索数
     *
     * @demo 23
     */
    private Integer toFollowCount;

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getToFollowCount() {
        return toFollowCount;
    }

    public void setToFollowCount(Integer toFollowCount) {
        this.toFollowCount = toFollowCount;
    }
}

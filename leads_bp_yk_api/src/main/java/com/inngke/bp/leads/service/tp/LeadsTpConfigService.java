package com.inngke.bp.leads.service.tp;

import com.inngke.bp.leads.dto.request.MultiSetLeadsTpConfRequest;
import com.inngke.bp.leads.dto.request.SetLeadsTpConfRequest;
import com.inngke.bp.leads.dto.response.tp.TpAiCustomerServiceConfigDto;
import com.inngke.bp.leads.dto.response.tp.TpConfigDto;
import com.inngke.bp.leads.dto.response.tp.TpFlyFishConfigDto;
import com.inngke.bp.leads.dto.response.tp.TpTencentAdConfigDto;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @date 2022/3/3 10:27
 */
public interface LeadsTpConfigService {

    /**
     * 获取Ai客服配置
     *
     * @param request bid
     * @return config
     */
    BaseResponse<TpAiCustomerServiceConfigDto> getAiCustomerServiceConfig(BaseBidRequest request);

    /**
     * 获取飞鱼配置
     *
     * @param request bid
     * @return config
     */
    BaseResponse<TpFlyFishConfigDto> getFlyFishConfig(BaseBidRequest request);

    /**
     * 获取腾讯广告配置
     *
     * @param request bid
     * @return config
     */
    BaseResponse<TpTencentAdConfigDto> getTencentAdConfig(BaseBidRequest request);

    /**
     * 获取所有第三方平台配置
     *
     * @param request bid
     * @return config
     */
    BaseResponse<TpConfigDto> getTpConfig(BaseBidRequest request);

    /**
     * 保存第三方平台配置
     *
     * @param request
     * @return
     */
    BaseResponse<TpConfigDto> setTpConfig(SetLeadsTpConfRequest request);

    /**
     * 批量保存
     *
     * @param request
     * @return
     */
    BaseResponse<TpConfigDto> multiSetTpConfig(MultiSetLeadsTpConfRequest request);
}

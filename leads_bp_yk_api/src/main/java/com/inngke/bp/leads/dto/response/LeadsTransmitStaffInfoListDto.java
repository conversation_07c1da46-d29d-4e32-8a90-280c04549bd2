package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/3 14:35
 */
public class LeadsTransmitStaffInfoListDto implements Serializable {
    /**
     * 部门层级信息集合
     */
    private List<SimpleDepartment> depSimpleInfoList;

    /**
     * 部门列表
     */
    private List<Department> departmentList;

    /**
     * 员工列表
     */
    private List<Staff> staffList;

    public List<SimpleDepartment> getDepSimpleInfoList() {
        return depSimpleInfoList;
    }

    public void setDepSimpleInfoList(List<SimpleDepartment> depSimpleInfoList) {
        this.depSimpleInfoList = depSimpleInfoList;
    }

    public List<Department> getDepartmentList() {
        return departmentList;
    }

    public void setDepartmentList(List<Department> departmentList) {
        this.departmentList = departmentList;
    }

    public List<Staff> getStaffList() {
        return staffList;
    }

    public void setStaffList(List<Staff> staffList) {
        this.staffList = staffList;
    }

    /**
     * 简要部门信息
     */
    public static class SimpleDepartment implements Serializable {
        /**
         * 部门ID
         */
        private Long departmentId;

        /**
         * 部门名称
         *
         * @demo "北京中天诚投科贸有限公司"
         */
        private String departmentName;

        public Long getDepartmentId() {
            return departmentId;
        }

        public void setDepartmentId(Long departmentId) {
            this.departmentId = departmentId;
        }

        public String getDepartmentName() {
            return departmentName;
        }

        public void setDepartmentName(String departmentName) {
            this.departmentName = departmentName;
        }
    }

    /**
     * 部门信息
     */
    public static class Department implements Serializable {
        /**
         * 部门ID
         */
        private Long departmentId;

        /**
         * 部门名称
         *
         * @demo "北京中天诚投科贸有限公司"
         */
        private String departmentName;

        public Long getDepartmentId() {
            return departmentId;
        }

        public void setDepartmentId(Long departmentId) {
            this.departmentId = departmentId;
        }

        public String getDepartmentName() {
            return departmentName;
        }

        public void setDepartmentName(String departmentName) {
            this.departmentName = departmentName;
        }
    }

    /**
     * 员工信息
     */
    public static class Staff implements Serializable {

        private Long staffId;

        /**
         * 员工名称
         *
         * @demo "奥普集成吊顶李意旺"
         */
        private String staffName;

        /**
         * 是否关注
         * @demo false： 导购未关注公众号且不能通过企业微信接收通知
         */
        private Boolean isSubscribe;


        public Long getStaffId() {
            return staffId;
        }

        public void setStaffId(Long staffId) {
            this.staffId = staffId;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public Boolean getSubscribe() {
            return isSubscribe;
        }

        public void setSubscribe(Boolean subscribe) {
            isSubscribe = subscribe;
        }
    }
}

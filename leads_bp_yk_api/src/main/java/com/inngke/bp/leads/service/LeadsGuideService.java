package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.GuideCreateLeadsRequest;
import com.inngke.common.dto.response.BaseResponse;

/**
 * LeadsGuideService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/15 14:41
 */
public interface LeadsGuideService {

    /**
     * 小程序导购创建线索
     *
     * @param request 请求实体
     * @return 线索id
     */
    BaseResponse<Long> guideCreateLeads(GuideCreateLeadsRequest request);
}

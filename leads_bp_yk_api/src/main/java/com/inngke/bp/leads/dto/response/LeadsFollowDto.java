package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8 5:52 PM
 */
public class LeadsFollowDto implements Serializable {
    /**
     * 自增ID
     *
     * @demo 1234
     */
    private Long id;

    /**
     * 商户ID
     *
     * @demo 1142
     */
    private Integer bid;

    /**
     * 线索ID，即leads.id
     *
     * @demo 23456
     */
    private Long leadsId;

    /**
     * 跟进人员工ID，即staffId
     *
     * @demo 23233
     */
    private Long staffId;

    /**
     * 跟进人名称，即员工名称
     *
     * @demo 张三
     */
    private String staffName;

    /**
     * 员工编号
     */
    private Long userId;

    /**
     * 跟进类型
     *
     * @demo 5
     */
    private Integer followType;

    /**
     * 跟进内容
     *
     * @demo 已量尺
     */
    private String followContent;

    /**
     * 跟进图片URL列表，多个使用半角逗号分隔
     *
     * @demo ["https://card-1259510193.image.myqcloud.com/image/20210831/edbf4ec4167fcd47.jpg"]
     */
    private List<String> followImages;

    /**
     * 跟进时设置的线索状态，详见leads.status
     *
     * @demo 5
     */
    private Integer leadsStatus;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long createTime;

    /**
     * 无效线索/线索流失原因id
     *
     */
    private Long reasonId;

    /**
     * 无效线索/线索流失原因
     */
    private String reason;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getFollowType() {
        return followType;
    }

    public void setFollowType(Integer followType) {
        this.followType = followType;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public List<String> getFollowImages() {
        return followImages;
    }

    public void setFollowImages(List<String> followImages) {
        this.followImages = followImages;
    }

    public Integer getLeadsStatus() {
        return leadsStatus;
    }

    public void setLeadsStatus(Integer leadsStatus) {
        this.leadsStatus = leadsStatus;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }
}

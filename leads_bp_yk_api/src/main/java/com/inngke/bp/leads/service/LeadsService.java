package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索接口
 * @since 2021/9/7 4:12 PM
 */
public interface LeadsService {
    /**
     * 线索搜索
     *
     * @param query 筛选条件
     * @return 分页的线索列表
     */
    BaseResponse<LeadsListVo> search(LeadsQuery query);

    /**
     * 导出查询数据
     *
     * @param query 筛选条件
     * @return 导出的数据文件地址
     */
    BaseResponse<String> export(LeadsQuery query) throws IOException;

    /**
     * 获取线索详情（后端）
     *
     * @param request 线索请求
     * @return 线索详情
     */
    BaseResponse<LeadsDto> getLeads(LeadsGetRequest request);

    /**
     * 获取线索详情（前端）
     *
     * @param request 线索请求
     * @return 线索详情
     */
    BaseResponse<LeadsVo> getLeadsDetail(LeadsGetRequest request);

    /**
     * 获取线索有效经销商列表
     *
     * @param request 请求
     * @return 经销商列表
     */
    BaseResponse<List<LeadsAgentSimpleInfoDto>> getAgentList(BaseBidOptRequest request);

    /**
     * 获取线索有效负责人列表
     *
     * @param request 请求
     * @return 经销商列表
     */
    BaseResponse<List<LeadsStaffSimpleInfoDto>> getStaffName(LeadsStaffNameGetRequest request);

    /**
     * 根据线索id获取对应的线索简要信息
     *
     * @param request
     * @return
     */
    BaseResponse<LeadsSimpleInfoDto> getLeadsSimpleInfoById(LeadsGetRequest request);

    /**
     * 查询有权限的员工所管理部门下的线索列表统计数据接口（分页）
     *
     * @param request 查询入参
     * @return 列表数据
     */
    BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsList(LeadsDepStaffStatisticsQuery request);

    BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsList2(LeadsDepStaffStatisticsQuery query);

    /**
     * 批量删除线索
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> deleteBatch(LeadsDeleteBatchRequest request);

    /**
     * 线索搜索
     *
     * @return 搜索线索
     */
    BaseResponse<LeadsListVo> searchLeads(LeadsQuery request);

    /**
     * 管理者对应的部门已经部门下员工信息
     *
     * @param request
     * @return
     */
    BaseResponse<LeadsTransmitStaffInfoListDto> leadsTransmitStaffList(LeadsDepEsGetRequest request);

    /**
     * 根据线索ids查询线索集合
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsDto>> getLeadsListByIds(LeadsIdsRequest request);

    BaseResponse<List<LeadsDto>> getLeadsByIds(LeadsIdsRequest request);

    /**
     * 根据手机号码判断线索是否存在
     *
     * @param request 入参
     * @return 是否存在
     */
    BaseResponse<List<LeadsDto>> queryLeadsByMobile(MobileLeadsGetRequest request);

    /**
     * 历史数据
     *
     * @param query
     * @return
     */
    BaseResponse<Integer> historyCount(LeadsQuery query);

    /**
     * 获取历史转交记录列表
     *
     * @param query
     * @return
     */
    BaseResponse<LeadsListVo> historyList(LeadsQuery query);

    /**
     * 获取渠道列表
     *
     * @return
     */
    BaseResponse<List<LeadsChannelVo>> getChannelList();

    /**
     * 获取数据来源
     *
     * @return
     */
    BaseResponse<List<LeadsChannelVo>> getChannelTypeList();

    /**
     * 获取线索来源
     *
     * @return
     */
    BaseResponse<List<LeadsChannelVo>> getChannelSourceList();

    /**
     * 线索绑定隐私号码
     *
     * @param request
     * @return
     */
    BaseResponse<BindMobileDto> bindMobile(LeadsPrivateNumberBindRequest request);

    /**
     * 模糊查询线索用户名
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsNameDto>> getLeadsName(LeadsAddRequest request);

    /**
     * 查询转换导购
     *
     * @param request 请求实体
     * @return 返回实体
     */
    BaseResponse<LeadsTransmitStaffInfoListDto> leadsTransmitGuideList(LeadsDepEsGetRequest request);

    /**
     * 线索
     *
     * @param request 请求实体
     * @return 操作结果
     */
    BaseResponse<Boolean> staffTransferLeads(LeadsTransferStaffRequest request);


    /**
     * 组装退回员工名称信息
     */
    void installPushBackLeadsStaffAndAgentName(Integer bid, List<Long> leadsId, List<LeadsListItemDto> leadsList);

    /**
     * 查询有权限的员工所管理部门下的线索列表统计数据接口V2
     *
     * @param request 查询入参
     * @return 列表数据
     */
    BaseResponse<LeadsDepartmentStaffStatisticsDto> getLeadsStatisticsInfosV2(LeadsDepStaffStatisticsQuery request);

    /**
     * 获取线索跟进时间
     *
     * @param request
     * @return
     */
    BaseResponse<Map<Long, LeadsFollowTimeDto>>  getLeadsFollowTime(BaseIdsRequest request);

    /**
     * 线索数量搜索
     * 根据statusList进行筛选
     *
     * @param query 筛选条件
     * @return 分页的线索列表
     */
    BaseResponse<Map<Integer,Integer>> count(LeadsQuery query);

    /**
     * 线索绑定客户
     *
     * @param request 请求实体
     * @return 绑定结果
     */
    BaseResponse<Long> bindClient(LeadsBindClientRequest request);

    /**
     * 线索转客户
     *
     * @param request 请求实体
     * @return 转换结果
     */
    BaseResponse<Long> transformClient(TransformClientRequest request);

    /**
     * 获取客户的所有线索
     *
     * @param query 查询实体
     * @return 线索列表
     */
    BaseResponse<List<SimpleLeadsDto>> findLeadsByClient(LeadsClientQuery query);

    /**
     * 判断线索是否存在
     */
    BaseResponse<Boolean> existLeads(ExistLeadsRequest request);

    BaseResponse<List<LeadsEsDto>> batchGetLeads(BaseIdRequest request);

    /**
     * 获取未关联客户的线索
     *
     * @param query 查询实体
     * @return 线索列表
     */
    BaseResponse<List<LeadsNameDto>> findLeadsByMobileOrWxWhenStaffIdEq(LeadsNoRelationClientQuery query);

    /**
     * 线索批量关联客户
     *
     * @param request 请求实体
     * @return 操作结果
     */
    BaseResponse<Boolean> leadsBatchRelationClient(LeadsBatchRelationClientRequest request);

    /**
     * 线索一键提醒
     */
    BaseResponse<Boolean> leadsNotifyGuide(LeadsNotifyGuideRequest request);
}

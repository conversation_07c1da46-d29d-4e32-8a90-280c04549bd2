package com.inngke.bp.leads.dto.response;

import com.inngke.bp.leads.dto.request.LeadsAttachment;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/4 10:40
 */
public class LeadsInformationVo extends LeadsInformationDto {

    /**
     * 线索跟进
     *
     * @demo 23
     */
    private Integer leadsFollowCount;

    private Integer transactionOrderCount;

    /**
     * 是否可回退 0:不可回退 1，可回退
     *
     * @demo 0
     */
    private Integer canRollback;

    /**
     * 是否可转移 0:不可转移 1，可转移
     *
     * @demo
     */
    private Integer canForward;

    private List<LeadsCallLogDto> callLogList;

    private List<LeadsCallLogDto> closePrivatePhoneCallLogList;

    private List<LeadsAttachment> imgTypeAttachmentList;

    private List<LeadsAttachment> otherTypeAttachtmentList;


    /**
     *   渠道来源为报备才有，报备该客户的合伙人姓名
     */
    private String partnerName;

    /**
     * 渠道来源为报备才有，报备该客户的合伙人身份
     */
    private String partnerRoleName;

    /**
     * 渠道来源为报备才有，该合伙人所属企业
     */
    private String partnerCompanyName;

    private Long clientId;

    /**
     * 客户信息（线索转客户后有）
     */
    private LeadsClientInfoDto clientInfo;

    /**
     * 商品信息
     */
    private List<ProductDto> products;



    /**
     * 线索重复项列表
     */
    private List<LeadsRepeatItemDto> leadsRepeatItemDtoList = new ArrayList<>(0);


    public LeadsClientInfoDto getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(LeadsClientInfoDto clientInfo) {
        this.clientInfo = clientInfo;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerRoleName() {
        return partnerRoleName;
    }

    public void setPartnerRoleName(String partnerRoleName) {
        this.partnerRoleName = partnerRoleName;
    }

    public String getPartnerCompanyName() {
        return partnerCompanyName;
    }

    public void setPartnerCompanyName(String partnerCompanyName) {
        this.partnerCompanyName = partnerCompanyName;
    }

    public Integer getTransactionOrderCount() {
        return transactionOrderCount;
    }

    public void setTransactionOrderCount(Integer transactionOrderCount) {
        this.transactionOrderCount = transactionOrderCount;
    }

    public Integer getLeadsFollowCount() {
        return leadsFollowCount;
    }

    public void setLeadsFollowCount(Integer leadsFollowCount) {
        this.leadsFollowCount = leadsFollowCount;
    }

    public Integer getCanRollback() {
        return canRollback;
    }

    public void setCanRollback(Integer canRollback) {
        this.canRollback = canRollback;
    }

    public Integer getCanForward() {
        return canForward;
    }

    public void setCanForward(Integer canForward) {
        this.canForward = canForward;
    }

    public List<LeadsCallLogDto> getCallLogList() {
        return callLogList;
    }

    public void setCallLogList(List<LeadsCallLogDto> callLogList) {
        this.callLogList = callLogList;
    }

    public List<LeadsCallLogDto> getClosePrivatePhoneCallLogList() {
        return closePrivatePhoneCallLogList;
    }

    public void setClosePrivatePhoneCallLogList(List<LeadsCallLogDto> closePrivatePhoneCallLogList) {
        this.closePrivatePhoneCallLogList = closePrivatePhoneCallLogList;
    }

    public List<LeadsRepeatItemDto> getLeadsRepeatItemDtoList() {
        return leadsRepeatItemDtoList;
    }

    public void setLeadsRepeatItemDtoList(List<LeadsRepeatItemDto> leadsRepeatItemDtoList) {
        this.leadsRepeatItemDtoList = leadsRepeatItemDtoList;
    }

    public List<LeadsAttachment> getImgTypeAttachmentList() {
        return imgTypeAttachmentList;
    }

    public void setImgTypeAttachmentList(List<LeadsAttachment> imgTypeAttachmentList) {
        this.imgTypeAttachmentList = imgTypeAttachmentList;
    }

    public List<LeadsAttachment> getOtherTypeAttachtmentList() {
        return otherTypeAttachtmentList;
    }

    public void setOtherTypeAttachtmentList(List<LeadsAttachment> otherTypeAttachtmentList) {
        this.otherTypeAttachtmentList = otherTypeAttachtmentList;
    }

    public List<ProductDto> getProducts() {
        return products;
    }

    public void setProducts(List<ProductDto> products) {
        this.products = products;
    }
}

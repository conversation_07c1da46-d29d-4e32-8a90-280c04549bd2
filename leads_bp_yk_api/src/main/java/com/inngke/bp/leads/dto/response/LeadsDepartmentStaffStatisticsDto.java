package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;

/**
 * 管理员查看线索列表回参
 *
 * <AUTHOR>
 */
public class LeadsDepartmentStaffStatisticsDto implements Serializable {

    /**
     * 部门层级信息集合 不包含本身
     */
    private List<LeadsDepSimpleDto> depSimpleInfoList;

    /**
     * 部门列表
     */
    private List<LeadsStatisticsDepItemDto> departments;

    /**
     * 员工列表
     */
    private List<LeadsStatisticsStaffItemDto> staffs;

    public List<LeadsDepSimpleDto> getDepSimpleInfoList() {
        return depSimpleInfoList;
    }

    public void setDepSimpleInfoList(List<LeadsDepSimpleDto> depSimpleInfoList) {
        this.depSimpleInfoList = depSimpleInfoList;
    }

    public List<LeadsStatisticsDepItemDto> getDepartments() {
        return departments;
    }

    public void setDepartments(List<LeadsStatisticsDepItemDto> departments) {
        this.departments = departments;
    }

    public List<LeadsStatisticsStaffItemDto> getStaffs() {
        return staffs;
    }

    public void setStaffs(List<LeadsStatisticsStaffItemDto> staffs) {
        this.staffs = staffs;
    }
}

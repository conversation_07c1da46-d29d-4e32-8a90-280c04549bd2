package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 客服
 * @section 客服接口
 * @since 2022/9/7 4:12 PM
 */
public interface PreFollowStaffService {

    /**
     * 客服列表
     *
     * @param request
     * @return
     */
    BaseResponse<List<SimplePreFollowStaffDto>> list(PreFollowStaffListRequest request);

    /**
     * 添加接收规则
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> addRule(PreFollowStaffAddRuleRequest request);

    /**
     * 修改接收规则
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> updateRule(PreFollowStaffUpdateRuleRequest request);

    /**
     * 获取客服已选择的渠道
     *
     * @param bid
     * @param id
     * @return
     */
    BaseResponse<List<SimpleChannelDto>> getChooseChannelsByStaffId(int bid, Long id);

    /**
     * 根据员工id获取规则列表
     *
     * @param bid
     * @param id
     * @return
     */
    BaseResponse<List<SimplePreFollowStaffRuleDto>> getRulesByStaffId(int bid, Long id);

    /**
     * 根据规则id获取列表详情
     *
     * @param bid
     * @param id
     * @return
     */
    BaseResponse<FollowStaffRuleDto> getRuleDetail(int bid, Long id);

    /**
     * 查询客服
     *
     * @param request
     * @return
     */
    BaseResponse<SimplePreFollowStaffDto> getPreFollowStaff(GetPreFollowStaffRequest request);

    /**
     * 业绩报表
     *
     * @param request
     * @return
     */
    BaseResponse<List<PreFollowStaffReportDto>> getPreFollowStaffReport(GetPreFollowStaffReportRequest request);

    /**
     * 获取客服所有的渠道和规则
     */
    BaseResponse<GetStaffAllChannelAndRegionDto> staffChannelRegion(int bid, Long id);

    /**
     * 获取客服分配版本号
     */
    String getPreFollowVersion(int bid);

    /**
     * 获取客服Id
     * @param bid bid
     * @param version 客服分配版本号
     * @param channelId 渠道
     * @param regionId 区域
     */
    Long getPreFollow(int bid, String version, Integer channelId, Integer regionId);

    /**
     * 删除客服配置
     */
    BaseResponse<Boolean> removePreFollowConfig(BaseIdsRequest request);

    String incPreFollowVersion(int bid);

    /**
     * 获取所有客服列表和设置
     */
    BaseResponse<List<PreFollowStaffAndSetDto>> preFollowAndSet(PreFollowStaffListRequest request);
}

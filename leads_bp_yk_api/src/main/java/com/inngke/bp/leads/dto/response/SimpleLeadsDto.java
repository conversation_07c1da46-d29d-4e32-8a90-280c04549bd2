package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SimpleLeadsDto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/3/2 17:57
 */
public class SimpleLeadsDto implements Serializable {

    private Long id;

    private Integer bid;

    private String name;

    private String mobile;

    private Integer status;

    /**
     * 关联客户时间
     */
    private LocalDateTime relationClientTime;

    public LocalDateTime getRelationClientTime() {
        return relationClientTime;
    }

    public void setRelationClientTime(LocalDateTime relationClientTime) {
        this.relationClientTime = relationClientTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

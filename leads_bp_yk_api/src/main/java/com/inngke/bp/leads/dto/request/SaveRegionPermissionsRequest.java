package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SaveRegionPermissionsRequest extends BaseBidRequest {

    /**
     * 区域id
     */
    private Integer regionId;

    /**
     * 员工ids
     */
    private List<Long> staffIds;
}

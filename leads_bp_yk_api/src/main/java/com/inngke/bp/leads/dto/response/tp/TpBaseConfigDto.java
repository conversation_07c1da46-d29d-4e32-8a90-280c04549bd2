package com.inngke.bp.leads.dto.response.tp;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/3 10:20
 */
public class TpBaseConfigDto implements Serializable {

    /**
     * 使用开关
     *
     * @demo true
     */
    private Boolean useSwitch;

    /**
     * token
     *
     * @demo abcdefg
     */
    private String token;

    /**
     * secret
     *
     * @demo lkxzjhcvkwqbesaldkkfjxcx
     */
    private String secret;

    /**
     * 渠道流入线索设置的创建者id
     *
     * @demo 1334
     */
    private Long operatorCreateStaffId;


    private String operatorCreateStaffName;

    private Long operatorCreateStaffDepartmentId;

    private String operatorCrateStaffDepartmentName;

    public Boolean getUseSwitch() {
        return useSwitch;
    }

    public void setUseSwitch(Boolean useSwitch) {
        this.useSwitch = useSwitch;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Long getOperatorCreateStaffId() {
        return operatorCreateStaffId;
    }

    public void setOperatorCreateStaffId(Long operatorCreateStaffId) {
        this.operatorCreateStaffId = operatorCreateStaffId;
    }

    public String getOperatorCreateStaffName() {
        return operatorCreateStaffName;
    }

    public void setOperatorCreateStaffName(String operatorCreateStaffName) {
        this.operatorCreateStaffName = operatorCreateStaffName;
    }

    public Long getOperatorCreateStaffDepartmentId() {
        return operatorCreateStaffDepartmentId;
    }

    public void setOperatorCreateStaffDepartmentId(Long operatorCreateStaffDepartmentId) {
        this.operatorCreateStaffDepartmentId = operatorCreateStaffDepartmentId;
    }

    public String getOperatorCrateStaffDepartmentName() {
        return operatorCrateStaffDepartmentName;
    }

    public void setOperatorCrateStaffDepartmentName(String operatorCrateStaffDepartmentName) {
        this.operatorCrateStaffDepartmentName = operatorCrateStaffDepartmentName;
    }
}

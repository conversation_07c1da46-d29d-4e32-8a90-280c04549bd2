package com.inngke.bp.leads.dto.response;

/**
 * 管理员查看线索统计列表员工信息
 *
 * <AUTHOR>
 */
public class LeadsStatisticsStaffItemDto extends LeadsStatisticsBaseInfoDto {

    /**
     * 员工编号
     *
     * @demo 1
     */
    private Long staffId;

    /**
     * 员工名称
     *
     * @demo 奥普集成吊顶李意旺13569953214
     */
    private String staffName;

    /**
     * 员工职位
     *
     * @demo 总经理
     */
    private String position;

    /**
     * 成员头像的url
     *
     * @demo https://image.inngke.com/aaaa.jpg
     */
    private String avatar;

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
}

package com.inngke.bp.leads.service;

import com.inngke.common.dto.request.BaseBidRequest;

public class LeadsClientStatusUpdateRequest extends BaseBidRequest {

    private Long id;

    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

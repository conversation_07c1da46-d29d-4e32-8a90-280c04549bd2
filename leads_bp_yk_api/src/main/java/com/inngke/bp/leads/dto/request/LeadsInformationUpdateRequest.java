package com.inngke.bp.leads.dto.request;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/8 16:35
 */
public class LeadsInformationUpdateRequest extends LeadsUpdateRequest{

    /**
     * 性别 1:男 2:女
     */
    private Integer gender;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * QQ号
     */
    private String qq;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 计划ID
     */
    private String campaignId;
    /**
     * 计划名称
     */
    private String campaignName;
    /**
     * 广告主ID 腾讯,飞鱼用户ID
     */
    private String accountId;
    /**
     * 广告主名称
     */
    private String accountName;
    /**
     * 线索提交时间
     */
    private Long submitTime;

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Long getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Long submitTime) {
        this.submitTime = submitTime;
    }

}

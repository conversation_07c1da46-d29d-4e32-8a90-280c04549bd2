package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/9/8 8:05 PM
 */
public class LeadsBatchDto implements Serializable {

    /**
     * 线索导入批次
     *
     * @demo 123454
     */
    private Long id;

    /**
     * 商户ID
     *
     * @demo 1142
     */
    private Integer bid;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     *
     * @demo 1
     */
    private Integer channel;

    /**
     * 批量导入文件URL
     *
     * @demo https://static.inngke.com/file/tmall_18_20210908.xml
     */
    private String fileUrl;

    /**
     * 批量导入文件类型：0=未知 1=excel
     *
     * @demo 1
     */
    private Integer fileType;

    /**
     * 处理状态：0=未处理 1=处理中 2=已处理（包括处理失败）
     *
     * @demo 1
     */
    private Integer processStatus;

    /**
     * 成功导入数量
     *
     * @demo 1000
     */
    private Integer successCount;

    /**
     * 未成功导入数量
     *
     * @demo 0
     */
    private Integer errorCount;

    /**
     * 上传者员工ID
     *
     * @demo 1223
     */
    private Long staffId;

    /**
     * 导入失败的文件
     *
     * @demo https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/file/88248654%E7%94%B5%E5%95%86%E7%B1%BB%E7%BA%BF%E7%B4%A2%E6%A8%A1%E6%9D%BF%20%282%29.xlsx
     */
    private String errorFileUrl;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long createTime;

    /**
     * 更新时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getErrorFileUrl() {
        return errorFileUrl;
    }

    public void setErrorFileUrl(String errorFileUrl) {
        this.errorFileUrl = errorFileUrl;
    }
}

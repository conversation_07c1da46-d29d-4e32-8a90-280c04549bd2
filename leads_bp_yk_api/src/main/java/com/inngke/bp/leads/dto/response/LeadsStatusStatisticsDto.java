package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/9/8 11:26 PM
 */
public class LeadsStatusStatisticsDto implements Serializable {

    /**
     * 品牌id
     */
    private Integer bid;

    /**
     * uid列表，逗号分隔
     *
     * @demo 2,3,4
     */
    private Set<Long> customerUids;

    /**
     * 手机号列表，逗号分隔
     *
     * @demo 13313131333,14414141444
     */
    private Set<String> mobiles;

    /**
     * 员工staffId
     *
     * @demo 张三
     */
    private Long staffId;

    /**
     * 无效线索数量(status=-3)
     *
     * @demo 51
     */
    private Integer statusInvalid = 0;

    /**
     * 员工退回数量(status=-2)
     *
     * @demo 3
     */
    private Integer statusDistributeError = 0;

    /**
     * 员工退回数量(status=-1)
     *
     * @demo 43
     */
    private Integer statusPushBack = 0;

    /**
     * 待分配数量(status=0)
     *
     * @demo 3
     */
    private Integer status0 = 0;

    /**
     * 未联系数量(status=1)
     *
     * @demo 6
     */
    private Integer status1 = 0;

    /**
     * 24小时内联系数量(status=2)
     *
     * @demo 13
     */
    private Integer status2 = 0;

    /**
     * 24h后联系数量(status=3)
     *
     * @demo 23
     */
    private Integer status3 = 0;

    /**
     * 有意向数量(status=4)
     *
     * @demo 2
     */
    private Integer status4 = 0;

    /**
     * 量尺数量(status=5)
     *
     * @demo 24
     */
    private Integer status5 = 0;

    /**
     * 到店数量(status=6)
     *
     * @demo 24
     */
    private Integer status6 = 0;

    /**
     * 报价数量(status=7)
     *
     * @demo 24
     */
    private Integer status7 = 0;

    /**
     * 定金数量(status=8)
     *
     * @demo 14
     */
    private Integer status8 = 0;

    /**
     * 待安装数量(status=9)
     *
     * @demo 14
     */
    private Integer status9 = 0;

    /**
     * 已安装数量(status=10)
     *
     * @demo 14
     */
    private Integer status10 = 0;

    /**
     * 已成交数量(status=11)
     *
     * @demo 4
     */
    private Integer status11 = 0;

    /**
     * 聚合后的线索最早的分配时间
     */
    private LocalDateTime earliestDistributeTime;

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getStatusInvalid() {
        return statusInvalid;
    }

    public void setStatusInvalid(Integer statusInvalid) {
        this.statusInvalid = statusInvalid;
    }

    public Integer getStatusDistributeError() {
        return statusDistributeError;
    }

    public void setStatusDistributeError(Integer statusDistributeError) {
        this.statusDistributeError = statusDistributeError;
    }

    public Integer getStatusPushBack() {
        return statusPushBack;
    }

    public void setStatusPushBack(Integer statusPushBack) {
        this.statusPushBack = statusPushBack;
    }

    public Integer getStatus0() {
        return status0;
    }

    public void setStatus0(Integer status0) {
        this.status0 = status0;
    }

    public Integer getStatus1() {
        return status1;
    }

    public void setStatus1(Integer status1) {
        this.status1 = status1;
    }

    public Integer getStatus2() {
        return status2;
    }

    public void setStatus2(Integer status2) {
        this.status2 = status2;
    }

    public Integer getStatus3() {
        return status3;
    }

    public void setStatus3(Integer status3) {
        this.status3 = status3;
    }

    public Integer getStatus4() {
        return status4;
    }

    public void setStatus4(Integer status4) {
        this.status4 = status4;
    }

    public Integer getStatus5() {
        return status5;
    }

    public void setStatus5(Integer status5) {
        this.status5 = status5;
    }

    public Integer getStatus6() {
        return status6;
    }

    public void setStatus6(Integer status6) {
        this.status6 = status6;
    }

    public Integer getStatus7() {
        return status7;
    }

    public void setStatus7(Integer status7) {
        this.status7 = status7;
    }

    public Integer getStatus8() {
        return status8;
    }

    public void setStatus8(Integer status8) {
        this.status8 = status8;
    }

    public Integer getStatus9() {
        return status9;
    }

    public void setStatus9(Integer status9) {
        this.status9 = status9;
    }

    public Integer getStatus10() {
        return status10;
    }

    public void setStatus10(Integer status10) {
        this.status10 = status10;
    }

    public Integer getStatus11() {
        return status11;
    }

    public void setStatus11(Integer status11) {
        this.status11 = status11;
    }

    public Set<Long> getCustomerUids() {
        return customerUids;
    }

    public void setCustomerUids(Set<Long> customerUids) {
        this.customerUids = customerUids;
    }

    public Set<String> getMobiles() {
        return mobiles;
    }

    public void setMobiles(Set<String> mobiles) {
        this.mobiles = mobiles;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public LocalDateTime getEarliestDistributeTime() {
        return earliestDistributeTime;
    }

    public void setEarliestDistributeTime(LocalDateTime earliestDistributeTime) {
        this.earliestDistributeTime = earliestDistributeTime;
    }
}

package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsConfSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsConfDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @chapter 线索
 * @section 线索配置
 * @since 2021/9/7 4:01 PM
 */
public interface LeadsConfService {
    /**
     * 开启线索功能
     *
     * @param request 开启请求
     * @return 是否开启成功
     */
    BaseResponse<Boolean> enableLeads(BaseBidOptRequest request);

    /**
     * 关闭线索功能
     *
     * @param request 关闭请求
     * @return 是否关闭成功
     */
    BaseResponse<Boolean> disableLeads(BaseBidOptRequest request);

    /**
     * 设置线索配置
     *
     * @param request 配置请求
     * @return 保存成功后的线索配置
     */
    BaseResponse<LeadsConfDto> setLeadsConf(LeadsConfSaveRequest request);

    /**
     * 获取线索配置
     *
     * @param request 获取请求
     * @return 线索配置
     */
    BaseResponse<LeadsConfDto> getLeadsConf(BaseBidOptRequest request);
}

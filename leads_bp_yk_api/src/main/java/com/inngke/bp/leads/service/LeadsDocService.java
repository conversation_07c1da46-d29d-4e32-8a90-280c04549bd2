package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsQuery;
import com.inngke.bp.leads.dto.request.LeadsStatusRequest;
import com.inngke.bp.leads.dto.response.LeadsListVo;
import com.inngke.bp.leads.dto.response.LeadsStatusDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.Map;

/**
 * LeadsIndexService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/11 19:24
 */
public interface LeadsDocService {

    BaseResponse<LeadsListVo> searchLeads(LeadsQuery request);

    BaseResponse<LeadsStatusDto> dimensionStatisticsCount(LeadsStatusRequest request);


    BaseResponse<Map<Integer,Integer>> count(LeadsQuery query);
}

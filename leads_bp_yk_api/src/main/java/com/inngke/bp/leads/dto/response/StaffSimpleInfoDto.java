package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/9/7 3:53 PM
 */
public class StaffSimpleInfoDto implements Serializable {
    /**
     * 员工ID，即staff.id
     *
     * @demo 1024
     */
    private Long id;

    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 经销商名称
     */
    private String agentName;

    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public Integer getStatus() {
        return status;
    }

    public StaffSimpleInfoDto setStatus(Integer status) {
        this.status = status;
        return this;
    }
}

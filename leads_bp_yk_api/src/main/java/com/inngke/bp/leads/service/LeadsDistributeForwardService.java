package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.SaveForwardConfRequest;
import com.inngke.bp.leads.dto.response.StaffForwardConfDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.Set;

public interface LeadsDistributeForwardService {
    BaseResponse<Boolean> saveForwardConf(SaveForwardConfRequest request);

    BaseResponse<StaffForwardConfDto> getForwardConf(BaseIdRequest request);

    BaseResponse<Set<Long>> getExistForwardStaffIds(BaseBidRequest request);

    BaseResponse<Boolean> closeForward(SaveForwardConfRequest request);

    BaseResponse<Boolean> isForwarder(BaseBidOptRequest request);
}

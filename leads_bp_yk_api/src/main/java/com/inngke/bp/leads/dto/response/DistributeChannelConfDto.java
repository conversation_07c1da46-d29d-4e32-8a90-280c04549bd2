package com.inngke.bp.leads.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DistributeChannelConfDto implements Serializable {

    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 线索渠道
     */
    private List<Long> channelIds;

    /**
     * 选中的线索渠道名称
     */
    private List<String> channelNameList;

}

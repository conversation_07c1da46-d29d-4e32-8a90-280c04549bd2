package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线索隐私号码通话记录请求实体
 * <AUTHOR>
 */
public class LeadsPrivatePhoneCallLogRequest extends BaseBidOptRequest {

    /**
     * 线索id
     * @demo 1231
     */
    @Min(0)
    private Long leadsId;

    /**
     * 通话开始时间
     */
    private LocalDateTime callTime;
    /**
     * 通话记录id
     * @demo 3123
     */
    @Min(0)
    private Long privateVoiceRecordId;

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getPrivateVoiceRecordId() {
        return privateVoiceRecordId;
    }

    public void setPrivateVoiceRecordId(Long privateVoiceRecordId) {
        this.privateVoiceRecordId = privateVoiceRecordId;
    }

    public LocalDateTime getCallTime() {
        return callTime;
    }

    public void setCallTime(LocalDateTime callTime) {
        this.callTime = callTime;
    }
}

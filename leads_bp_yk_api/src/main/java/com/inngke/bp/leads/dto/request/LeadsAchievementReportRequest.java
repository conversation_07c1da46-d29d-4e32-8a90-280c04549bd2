package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptPageRequest;

import javax.validation.constraints.NotBlank;

/**
 * LeadsAchievementRepoReportRequest
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 15:40
 */
public class LeadsAchievementReportRequest extends BaseBidOptPageRequest {

    /**
     * 维度代码
     *
     * @demo [staff, department, customerService]
     */
    @NotBlank(message = "请选择统计维度")
    private String dimensionCode;

    /**
     * 员工id
     *
     * @demo 1231
     */
    private Long selectStaffId;

    /**
     * 部门id
     *
     * @demo 12312
     */
    private Long selectDeptId;

    /**
     * 事件开始时间
     *
     * @demo 1970-09-09 12:00:00
     */
    @NotBlank(message = "事件开始事件不能为空")
    private String startEventTime;

    /**
     * 事件结束事件
     *
     * @demo 1970-09-09 12:00:00
     */
    @NotBlank(message = "事件结束时间不能为空")
    private String endEventTime;

    /**
     * 分配开始时间
     *
     * @demo 1970-09-09 12:00:00
     */
    private String startDistributeTime;

    /**
     * 分配结束时间
     *
     * @demo 1970-09-09 12:00:00
     */
    private String endDistributeTime;

    /**
     * 线索创建开始时间
     *
     * @demo 1970-09-09 12:00:00
     */
    private String startCreateTime;

    /**
     * 线索创建结束时间
     *
     * @demo 1970-09-09 12:00:00
     */
    private String endCreateTime;

    private Long currentStaffId;

    public String getDimensionCode() {
        return dimensionCode;
    }

    public void setDimensionCode(String dimensionCode) {
        this.dimensionCode = dimensionCode;
    }

    public Long getSelectStaffId() {
        return selectStaffId;
    }

    public void setSelectStaffId(Long selectStaffId) {
        this.selectStaffId = selectStaffId;
    }

    public Long getSelectDeptId() {
        return selectDeptId;
    }

    public void setSelectDeptId(Long selectDeptId) {
        this.selectDeptId = selectDeptId;
    }


    public String getStartEventTime() {
        return startEventTime;
    }

    public void setStartEventTime(String startEventTime) {
        this.startEventTime = startEventTime;
    }

    public String getEndEventTime() {
        return endEventTime;
    }

    public void setEndEventTime(String endEventTime) {
        this.endEventTime = endEventTime;
    }

    public String getStartDistributeTime() {
        return startDistributeTime;
    }

    public void setStartDistributeTime(String startDistributeTime) {
        this.startDistributeTime = startDistributeTime;
    }

    public String getEndDistributeTime() {
        return endDistributeTime;
    }

    public void setEndDistributeTime(String endDistributeTime) {
        this.endDistributeTime = endDistributeTime;
    }

    public String getStartCreateTime() {
        return startCreateTime;
    }

    public void setStartCreateTime(String startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public String getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(String endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getCurrentStaffId() {
        return currentStaffId;
    }

    public void setCurrentStaffId(Long currentStaffId) {
        this.currentStaffId = currentStaffId;
    }
}

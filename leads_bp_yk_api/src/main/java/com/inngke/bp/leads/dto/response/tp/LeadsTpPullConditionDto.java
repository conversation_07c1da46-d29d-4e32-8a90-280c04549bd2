package com.inngke.bp.leads.dto.response.tp;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 14:24
 */
public class LeadsTpPullConditionDto implements Serializable {

    /**
     * 1:腾讯广告 2:飞鱼
     *
     * @demo 1;
     */
    private Integer type;

    /**
     * 字段名
     *
     * @demo status
     */
    private String fields;

    /**
     * 条件
     *
     * @demo true
     */
    private Boolean equal;

    /**
     * 字段值
     *
     * @demo ['a','b','c']
     */
    private List<String> valueList;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public Boolean getEqual() {
        return equal;
    }

    public void setEqual(Boolean equal) {
        this.equal = equal;
    }

    public List<String> getValueList() {
        return valueList;
    }

    public void setValueList(List<String> valueList) {
        this.valueList = valueList;
    }
}

package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.AddDistributeChannelConfRequest;
import com.inngke.bp.leads.dto.request.UpdateDistributeChannelConfRequest;
import com.inngke.bp.leads.dto.response.DistributeChannelConfDto;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface LeadsDistributeChannelConfService {

    BaseResponse<List<DistributeChannelConfDto>> getList(BaseBidRequest request);

    /**
     * 新增接收规则
     */
    BaseResponse<Boolean> addDistributeChannelConf(AddDistributeChannelConfRequest request);

    /**
     * 更新接收规则
     */
    BaseResponse<Boolean> updateDistributeChannelConf(UpdateDistributeChannelConfRequest request);
}

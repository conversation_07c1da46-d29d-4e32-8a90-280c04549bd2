package com.inngke.bp.leads.enums;

import com.inngke.bp.leads.dto.response.LeadsLostReasonDto;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum LeadsLostReasonEnum {

    PRICE_MISMATCH(1, "价格不匹配"),

    REQUIREMENT_MISMATCH(2, "产品功能无法满足"),

    ALREADY_PURCHASE_OTHERS(3, "客户已选购其他品牌"),

    WAITING_TOO_LONG(4, "货期太长"),

    NOT_IN_SERVICE(6, "不在服务范围内"),

    INVALID_MOBILE(7, "无效：多次联系不上/空号/电话错误"),

    INVALID_NO_DEMAND(8, "无效：首次联系确认无定制需求"),

    INVALID_REFUSE_COMM(9, "无效：客户拒绝沟通"),

    INVALID_NO_PRODUCT(10, "无效：产品不符合（玻璃、五金、纱窗等）"),

    OTHERS(5, "其他"),
    ;
    /**
     * 状态
     */
    private final int status;

    /**
     * 名称
     */
    private final String name;

    LeadsLostReasonEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }


    /**
     * 将enums转换为List
     *
     * @return
     */
    public static List<LeadsLostReasonDto> getLeadsLostReasonList() {
        ArrayList<LeadsLostReasonDto> leadsLostReasonList = new ArrayList<>();
        for (LeadsLostReasonEnum leadsLostReasonEnum : LeadsLostReasonEnum.values()) {
            LeadsLostReasonDto leadsLostReasonDto = new LeadsLostReasonDto();
            leadsLostReasonDto.setType(leadsLostReasonEnum.getStatus());
            leadsLostReasonDto.setReason(leadsLostReasonEnum.getName());
            leadsLostReasonList.add(leadsLostReasonDto);
        }
        return leadsLostReasonList;
    }

    public static String getNameByType(Integer value) {
        for (LeadsLostReasonEnum ele : values()) {
            if (ele.getStatus() == value) {
                return ele.getName();
            }
        }
        return null;
    }
}

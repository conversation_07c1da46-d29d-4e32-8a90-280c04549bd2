package com.inngke.bp.leads.dto.response;

/**
 * <AUTHOR>
 */
public class LeadsBatchImportDto {
    private Integer successCount;

    private Integer errorCount;

    private Boolean autoAllot;


    /**
     * 导入失败的文件
     *
     * @demo https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/file/88248654%E7%94%B5%E5%95%86%E7%B1%BB%E7%BA%BF%E7%B4%A2%E6%A8%A1%E6%9D%BF%20%282%29.xlsx
     */
    private String errorFileUrl;


    public String getErrorFileUrl() {
        return errorFileUrl;
    }

    public void setErrorFileUrl(String errorFileUrl) {
        this.errorFileUrl = errorFileUrl;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public Boolean getAutoAllot() {
        return autoAllot;
    }

    public void setAutoAllot(Boolean autoAllot) {
        this.autoAllot = autoAllot;
    }
}

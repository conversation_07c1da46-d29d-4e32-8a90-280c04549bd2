package com.inngke.bp.leads.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/8/31 8:05 PM
 */
public class GoodDto implements Serializable {

    private Long orderId;

    private String thumb;

    private Long goodSid;

    private Integer status;

    private String title;

    private String price;

    private Integer total;

    private Integer optionId;

    private String optionName;

    private Integer isComment;

    private Integer isPreSale;

    private String preSalePrice;

    private Long preSaleTime;

    private String preDepositPrice;

    private String preBalancePrice;

    public String getThumb() {
        return thumb;
    }

    public void setThumb(String thumb) {
        this.thumb = thumb;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getGoodSid() {
        return goodSid;
    }

    public void setGoodSid(Long goodSid) {
        this.goodSid = goodSid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOptionId() {
        return optionId;
    }

    public void setOptionId(Integer optionId) {
        this.optionId = optionId;
    }

    public String getOptionName() {
        return optionName;
    }

    public void setOptionName(String optionName) {
        this.optionName = optionName;
    }

    public Integer getIsComment() {
        return isComment;
    }

    public void setIsComment(Integer isComment) {
        this.isComment = isComment;
    }

    public Integer getIsPreSale() {
        return isPreSale;
    }

    public void setIsPreSale(Integer isPreSale) {
        this.isPreSale = isPreSale;
    }

    public String getPreSalePrice() {
        return preSalePrice;
    }

    public void setPreSalePrice(String preSalePrice) {
        this.preSalePrice = preSalePrice;
    }

    public Long getPreSaleTime() {
        return preSaleTime;
    }

    public void setPreSaleTime(Long preSaleTime) {
        this.preSaleTime = preSaleTime;
    }

    public String getPreDepositPrice() {
        return preDepositPrice;
    }

    public void setPreDepositPrice(String preDepositPrice) {
        this.preDepositPrice = preDepositPrice;
    }

    public String getPreBalancePrice() {
        return preBalancePrice;
    }

    public void setPreBalancePrice(String preBalancePrice) {
        this.preBalancePrice = preBalancePrice;
    }
}

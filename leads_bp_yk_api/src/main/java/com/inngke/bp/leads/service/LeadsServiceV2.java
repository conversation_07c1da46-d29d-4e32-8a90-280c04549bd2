package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.*;
import com.inngke.bp.leads.dto.response.*;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/8 15:18
 */
public interface LeadsServiceV2 {
    /**
     * 获取线索详情（后端）
     *
     * @param request 线索请求
     * @return 线索详情
     */
    BaseResponse<LeadsInformationDto> getLeads(LeadsGetRequest request);

    /**
     * 获取线索详情（前端）
     *
     * @param request 线索请求
     * @return 线索详情
     */
    BaseResponse<LeadsInformationVo> getLeadsDetail(LeadsGetRequest request);

    /**
     * 修改线索
     *
     * @return
     */
    BaseResponse<Boolean> updateLeads(LeadsInformationUpdateRequest request);

    /**
     * 添加线索
     *
     * @param request
     * @return
     */
    BaseResponse<LeadsInformationDto> add(LeadsInformationAddRequest request);

    /**
     * 修改线索 订单类
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> editByOrder(LeadsEditByOrderRequest request);

    /**
     * 修改线索 信息类
     *
     *
     * @param request
     * @return
     */
    BaseResponse<Boolean> editByInfo(LeadsEditByInforRequest request);

    /**
     * 增加线索 供第三方使用
     * @param request 线索信息
     * @return 增加线索是否成功
     */
    BaseResponse<Long> addFromOtherSystem(LeadsInformationAddRequest request);

    /**
     * 成交订单
     *
     * @param request
     * @return
     */
    BaseResponse<List<MallOrderDto>> getMallOrderList(LeadsGetRequest request);

    /**
     * 获取线索流失原因列表
     * @return
     */
    BaseResponse<List<LeadsLostReasonDto>> getLeadsLostReasonList();

    /**
     * 历史数据处理：
     * 1.渠道来源为：天猫、京东、抖音订单的为订单类
     * 2.渠道来源不为上述三种的为信息类
     */
    Integer getLeadsDetailTypeHandle(Integer type, Integer channel);

    /**
     * 线索状态记录更新
     * @param request
     * @return
     */
    BaseResponse<Boolean> updateLeadsStatusRecord(UpdateLeadsStatusRecordRequest request);

    /**
     * 清除线索状态记录
     * @param request
     * @return
     */
    BaseResponse<Boolean> clearLeadsStatusRecord(ClearLeadsStatusRecordRequest request);

    /**
     *  根据阿里云呼叫中心信息 更新线索
     * @param request 请求实体
     * @return
     */
    BaseResponse updateLeadsInfoByAliCloudCallCenterMessage (AliCloudCallCenterMessageRequest request);

    /**
     * 根据客户手机号,姓名进行模糊搜索
     */
    BaseResponse<List<LeadsCustomerInfoDto>> getLeadsCustomerInfoByKeyword(GetLeadsCustomerInfoByKeywordRequest request);

}

package com.inngke.bp.leads.enums;

/**
 * 线索填写来源
 *
 * <AUTHOR>
 */

public enum LeadsInputSourceEnum {
    //线索填写来源: 1=转发推广,2=填写报备,3=表单提交, 4=在线咨询, 5=智能电话, 6=普通电话, 7=卡券, 8=网页回呼, 9=抽奖, 10=智能咨询, 11=加企业微信客服, 12=其他
//    FORWARDING_PROMOTION(1, "转发推广"),
//    FILL_IN_THE_REPORT(2, "填写报备"),
    FORM_SUBMISSION(3, "表单提交"),
    ONLINE_CONSULTATION(4, "在线咨询"),
    SMART_PHONE(5, "智能电话"),
    ORDINARY_TELEPHONE(6, "普通电话"),
    CARD_VOUCHER(7, "卡券"),
    PAGE_CALLBACK(8, "网页回呼"),
    LUCK_DRAW(9, "抽奖"),
    INTELLIGENT_CONSULTATION(10, "智能咨询"),
    CORP_CUSTOMER_SERVICE(11, "加企业微信客服"),
    DEPOSIT(13, "订金单"),
    FULL_PAYMENT(14, "全款单"),
    OTHER(12, "其他");

    private final Integer code;
    private final String name;

    LeadsInputSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LeadsInputSourceEnum parse(Integer code) {
        if (code == null) {
            return null;
        }
        for (LeadsInputSourceEnum leadsInputSourceEnum : LeadsInputSourceEnum.values()) {
            if (leadsInputSourceEnum.code.equals(code)) {
                return leadsInputSourceEnum;
            }
        }
        return null;
    }

    public static LeadsInputSourceEnum parse(String name) {
        if (name == null || name.isEmpty()) {
            return null;
        }
        for (LeadsInputSourceEnum leadsInputSourceEnum : LeadsInputSourceEnum.values()) {
            if (leadsInputSourceEnum.name.equals(name)) {
                return leadsInputSourceEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

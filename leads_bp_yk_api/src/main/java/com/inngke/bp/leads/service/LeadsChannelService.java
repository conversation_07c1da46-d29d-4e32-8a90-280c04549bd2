package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsChannelListRequest;
import com.inngke.bp.leads.dto.request.LeadsChannelMoveSortRequest;
import com.inngke.bp.leads.dto.request.LeadsChannelSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsChannelDto;
import com.inngke.bp.leads.dto.response.LeadsChannelValueDto;
import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface LeadsChannelService {

    BaseResponse<List<LeadsChannelDto>> getList(LeadsChannelListRequest request);

    BaseResponse save(LeadsChannelSaveRequest request);

    BaseResponse switchSort(LeadsChannelMoveSortRequest request);

    BaseResponse<List<LeadsChannelValueDto>> getValueList(BaseBidOptRequest request);

    BaseResponse<List<LeadsChannelDto>> findChildrenChannel(BaseIdRequest request);
}

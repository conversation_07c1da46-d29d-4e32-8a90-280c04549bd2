package com.inngke.bp.leads.enums;

import java.util.Objects;

/**
 * 十月需求 客服线索状态调整：待联系、已联系、邀约到店、邀约量尺、无效线索、已流失
 *
 * <AUTHOR>
 * @since 2022/11/7
 **/
public enum LeadsPreFollowStatusEnum {
    /**
     * 无用状态，防止空指针
     */
    DEFAULT_VALUE(-9999, ""),

    /**
     * 已流失
     */
    LOST(-6,"已流失"),

    /**
     * 无效线索
     */
    INVALID(-3, "无效线索"),

    /**
     * 已分配，未联系
     */
    DISTRIBUTED(1, "待联系"),

    /**
     * 已联系(24h内联系)
     */
    CONTACTED(2, "已联系"),

    /**
     * 量尺
     */
    MEASURED(5, "邀约量尺"),

    /**
     * 到店
     */
    STORED(6, "邀约到店"),

    ;


    /**
     * 状态
     */
    private final Integer status;

    /**
     * 名称
     */
    private final String name;

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public static LeadsPreFollowStatusEnum parse(Integer status) {
        if (status == null) {
            return DEFAULT_VALUE;
        }
        for (LeadsPreFollowStatusEnum leadsStatusEnum : LeadsPreFollowStatusEnum.values()) {
            if (leadsStatusEnum.getStatus().equals(status)) {
                return leadsStatusEnum;
            }
        }
        return DEFAULT_VALUE;
    }

    public static LeadsPreFollowStatusEnum parse(String name) {
        if (Objects.isNull(name)) {
            return null;
        }
        for (LeadsPreFollowStatusEnum leadsStatusEnum : LeadsPreFollowStatusEnum.values()) {
            if (leadsStatusEnum.getName().equals(name)) {
                return leadsStatusEnum;
            }
        }
        return null;
    }

    LeadsPreFollowStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }
}

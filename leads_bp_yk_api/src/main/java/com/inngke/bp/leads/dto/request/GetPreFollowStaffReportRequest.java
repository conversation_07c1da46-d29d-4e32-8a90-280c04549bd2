package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

/**
 * <AUTHOR>
 * @since 2022/9/7 4:13 PM
 */
public class GetPreFollowStaffReportRequest extends BaseBidOptRequest {

    /**
     * 客服id
     */
    private Long id;

    /**
     * 开始时间
     * @demo 2022-08-02
     * 后面补0分0秒
     */
    private String startTime;

    /**
     * 结束时间
     * @demo 2022-08-31
     * 后面补23分59秒
     */
    private String endTime;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}

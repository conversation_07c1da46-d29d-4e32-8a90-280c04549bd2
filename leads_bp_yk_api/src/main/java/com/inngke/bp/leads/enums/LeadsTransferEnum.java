package com.inngke.bp.leads.enums;

import lombok.Getter;

/**
 * LeadsTransferEnum
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 16:50
 */
@Getter
public enum LeadsTransferEnum {
    STAFF_TRANSFER("staff_transfer", "staffTransferHandler", "员工转交"),
    CLIENT_TRANSFER("client_transfer", "clientTransferHandler", "转交客户转交线索");
    private final String businessCode;

    private final String handlerName;

    private final String desc;

    LeadsTransferEnum(String businessCode, String handlerName, String desc) {
        this.businessCode = businessCode;
        this.handlerName = handlerName;
        this.desc = desc;
    }
}

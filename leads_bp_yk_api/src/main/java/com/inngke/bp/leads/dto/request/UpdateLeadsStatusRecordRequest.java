package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;

import java.util.Set;

public class UpdateLeadsStatusRecordRequest extends BaseBidRequest {
    /**
     * 线索id
     */
    private Long leadsId;

    /**
     * 线索状态
     */
    private Integer status;

    /**
     * 手机号
     */
    private Set<Long> leadsIds;

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Set<Long> getLeadsIds() {
        return leadsIds;
    }

    public void setLeadsIds(Set<Long> leadsIds) {
        this.leadsIds = leadsIds;
    }
}

package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/9/7 4:13 PM
 */
public class PreFollowStaffAddRuleRequest extends BaseBidOptRequest {

    /**
     * 规则名称
     */
    @NotEmpty
    private String name;


    /**
     * 客服员工Id
     */
    @NotNull
    private Long staffId;

    /**
     * 渠道IDS
     */
    @NotNull
    private Set<String> channelIds;

    /**
     * 区域Ids
     */
    @NotNull
    private Set<String> regionIds;


    public Set<String> getRegionIds() {
        return regionIds;
    }

    public void setRegionIds(Set<String> regionIds) {
        this.regionIds = regionIds;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Set<String> getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(Set<String> channelIds) {
        this.channelIds = channelIds;
    }
}

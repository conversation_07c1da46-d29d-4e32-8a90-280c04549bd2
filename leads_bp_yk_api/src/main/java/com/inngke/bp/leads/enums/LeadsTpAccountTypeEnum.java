package com.inngke.bp.leads.enums;

/**
 * <AUTHOR>
 * @date 2022/4/22 17:17
 */
public enum LeadsTpAccountTypeEnum {

    /**
     * 未知
     */
    ACCOUNT_TYPE_UNKNOWN("ACCOUNT_TYPE_UNKNOWN", "未知"),

    /**
     * 广告主
     */
    ACCOUNT_TYPE_ADVERTISER("ACCOUNT_TYPE_ADVERTISER", "广告主"),

    /**
     * 代理商
     */
    ACCOUNT_TYPE_AGENCY("ACCOUNT_TYPE_AGENCY", "代理商"),

    /**
     * DSP
     */
    ACCOUNT_TYPE_DSP("ACCOUNT_TYPE_DSP", "DSP"),

    /**
     * 开发者
     */
    ACCOUNT_TYPE_DEVELOPER("ACCOUNT_TYPE_DEVELOPER", "开发者"),

    /**
     * 流量主
     */
    ACCOUNT_TYPE_MEMBER("ACCOUNT_TYPE_MEMBER", "流量主"),

    /**
     * 创意供应商
     */
    ACCOUNT_TYPE_EXTERNAL_SUPPLIER("ACCOUNT_TYPE_EXTERNAL_SUPPLIER", "创意供应商"),

    /**
     * TDC
     */
    ACCOUNT_TYPE_TDC("ACCOUNT_TYPE_TDC", "TDC"),

    /**
     * TONE
     */
    ACCOUNT_TYPE_TONE("ACCOUNT_TYPE_TONE", "TONE"),

    /**
     * BM
     */
    ACCOUNT_TYPE_BM("ACCOUNT_TYPE_BM", "BM");

    /**
     * code
     */
    private final String code;

    /**
     * 账户类型
     */
    private final String name;

    LeadsTpAccountTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    static public LeadsTpAccountTypeEnum parse(String code){
        if (code == null){
            return null;
        }

        for (LeadsTpAccountTypeEnum leadsTpAccountTypeEnum : LeadsTpAccountTypeEnum.values()) {
            if (leadsTpAccountTypeEnum.code.equals(code)){
                return leadsTpAccountTypeEnum;
            }

        }

        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsSmsSendRequest;
import com.inngke.bp.leads.enums.LeadsSmsTemplateEnum;
import com.inngke.common.dto.response.BaseResponse;

/**
 * 线索短信
 *
 * <AUTHOR>
 * @date 2022/4/6 9:23
 */
public interface LeadsSmsService {

    BaseResponse<Boolean> send(LeadsSmsSendRequest request);

    LeadsSmsTemplateEnum getLeadsSmsTemplateEnum();
}

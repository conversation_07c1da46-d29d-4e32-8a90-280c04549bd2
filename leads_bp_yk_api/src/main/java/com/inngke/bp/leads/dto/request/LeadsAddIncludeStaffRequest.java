package com.inngke.bp.leads.dto.request;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/19 4:44 PM
 */
public class LeadsAddIncludeStaffRequest extends LeadsAddRequest {
    /**
     * 类型 1=主动报备（默认） 2=代报备 3=设计圈客户信息回传
     */
    private Integer reportType;

    private List<String> images;

    private Long sid;


    /**
     * 分配的经销商ID，即shop_agent_id
     *
     * @demo 10086
     */
    private Long distributeAgentId;

    /**
     * 分配给哪个员工，即staffId
     *
     * @demo 1008613
     */
    private Long distributeStaffId;

    /**
     * customer.id
     *
     * @demo 102432
     */
    private Long customerId;

    /**
     * 关联的用户UID，即customer.uid
     *
     * @demo 102432
     */
    private Long customerUid;

    /**
     * 額外数据（isRefund=是否退款，refundRemark=退款备注）
     *
     * @demo {"isRefund":0=否/1=是,"refundRemark:","weChatId":"5555"}
     */
    private String extData;

    private Boolean notifyGuide = true;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }

    public Long getDistributeAgentId() {
        return distributeAgentId;
    }

    public void setDistributeAgentId(Long distributeAgentId) {
        this.distributeAgentId = distributeAgentId;
    }

    public Long getDistributeStaffId() {
        return distributeStaffId;
    }

    public void setDistributeStaffId(Long distributeStaffId) {
        this.distributeStaffId = distributeStaffId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getCustomerUid() {
        return customerUid;
    }

    public void setCustomerUid(Long customerUid) {
        this.customerUid = customerUid;
    }

    @Override
    public String getExtData() {
        return extData;
    }

    @Override
    public void setExtData(String extData) {
        this.extData = extData;
    }

    public Boolean getNotifyGuide() {
        return notifyGuide;
    }

    public void setNotifyGuide(Boolean notifyGuide) {
        this.notifyGuide = notifyGuide;
    }
}

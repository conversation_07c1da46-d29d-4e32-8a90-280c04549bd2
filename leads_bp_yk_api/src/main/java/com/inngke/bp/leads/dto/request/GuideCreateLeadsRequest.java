package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Set;

/**
 * GuideCreateLeadsRequest
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/15 14:43
 */
public class GuideCreateLeadsRequest extends BaseBidOptRequest {
    /**
     * 创建模式：1=门店跟进；2=上报总部
     *
     * @demo 1
     */
    @NotNull(message = "请选择线索类型")
    private Integer crateMode;

    /**
     * 客户姓名
     *
     * @demo 张三
     */
    @NotBlank(message = "客户姓名不能为空")
    private String name;

    /**
     * 手机号
     *
     * @demo 15123412312
     */
    @Pattern(regexp = "^$|^1[3-9]\\d{9}$", message = "手机号码不合法")
    private String mobile;

    /**
     * 微信号
     *
     * @demo yk0101
     */
    private String weChat;

    /**
     * 客户等级Id
     *
     * @demo 14
     */
    private Integer levelId;

    /**
     * 渠道来源,见渠道来源表
     *
     * @demo 123
     */
    @NotNull(message = "渠道来源不能为空")
    private Integer channel;

    /**
     * 需求渠道来源
     */
    private Long channelId;

    /**
     * 省份ID，0表示未匹配。手工添加时必填
     *
     * @demo 123
     */
    @NotNull(message = "所在区域不能为空")
    private Integer provinceId;

    /**FR
     * 省份名称
     *
     * @demo 广东省
     */
    @NotBlank(message = "所在区域不能为空")
    private String provinceName;

    /**
     * 城市ID，0表示未匹配。手工添加时必填
     *
     * @demo 4232
     */
    private Integer cityId;

    /**
     * 城市名称
     *
     * @demo 广州市
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配。手工添加时必填
     *
     * @demo 102432
     */
    private Integer areaId;

    /**
     * 区域名称
     *
     * @demo 越秀区
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 线索类型 1:订单类 2:信息类
     *
     * @demo 1
     */
    private Integer type = 2;

    /**
     * 意向产品ids
     *
     * @demo [1, 2, 3, 4]
     */
    private Set<Long> productIds;
    /**
     * 备注
     *
     * @demo remark
     */
    private String remark;

    public Integer getCrateMode() {
        return crateMode;
    }

    public void setCrateMode(Integer crateMode) {
        this.crateMode = crateMode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public Integer getChannel() {
        return channel;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Set<Long> getProductIds() {
        return productIds;
    }

    public void setProductIds(Set<Long> productIds) {
        this.productIds = productIds;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

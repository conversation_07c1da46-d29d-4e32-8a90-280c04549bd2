package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/9/9 6:14 PM
 */
public class LeadsStatisticsExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 经销商ID
     *
     * @demo 12345
     */
    private Long agentId;

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    private Long staffId;

    /**
     * 线索数量
     *
     * @demo 1234
     */
    private Integer leadsCount;

    /**
     * 订单数量
     *
     * @demo 231
     */
    private Integer orderCount;

    /**
     * 订单总金额
     *
     * @demo 2324.00
     */
    private BigDecimal orderTotalAmount;

    /**
     * 有效率，单位：%
     *
     * @demo 95
     */
    private BigDecimal availableLeadsRate;

    /**
     * 24小时联系率，单位：%
     *
     * @demo 97
     */
    private BigDecimal contact24hLeadsRate;

    /**
     * 总联系率，单位：%
     *
     * @demo 99
     */
    private BigDecimal contactTotalLeadsRate;

    /**
     * 到店率，单位：%
     *
     * @demo 9
     */
    private BigDecimal storedLeadsRate;

    /**
     * 转化率，单位：%
     *
     * @demo 9
     */
    private BigDecimal tradedLeadsRate;

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Integer getLeadsCount() {
        return leadsCount;
    }

    public void setLeadsCount(Integer leadsCount) {
        this.leadsCount = leadsCount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }

    public BigDecimal getAvailableLeadsRate() {
        return availableLeadsRate;
    }

    public void setAvailableLeadsRate(BigDecimal availableLeadsRate) {
        this.availableLeadsRate = availableLeadsRate;
    }

    public BigDecimal getContact24hLeadsRate() {
        return contact24hLeadsRate;
    }

    public void setContact24hLeadsRate(BigDecimal contact24hLeadsRate) {
        this.contact24hLeadsRate = contact24hLeadsRate;
    }

    public BigDecimal getContactTotalLeadsRate() {
        return contactTotalLeadsRate;
    }

    public void setContactTotalLeadsRate(BigDecimal contactTotalLeadsRate) {
        this.contactTotalLeadsRate = contactTotalLeadsRate;
    }

    public BigDecimal getStoredLeadsRate() {
        return storedLeadsRate;
    }

    public void setStoredLeadsRate(BigDecimal storedLeadsRate) {
        this.storedLeadsRate = storedLeadsRate;
    }

    public BigDecimal getTradedLeadsRate() {
        return tradedLeadsRate;
    }

    public void setTradedLeadsRate(BigDecimal tradedLeadsRate) {
        this.tradedLeadsRate = tradedLeadsRate;
    }
}

package com.inngke.bp.leads.dto.response;

import com.inngke.common.dto.request.BaseBidOptRequest;

import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
* @date 2022/11/25 12:36
* @version 1.0
*/
public class PreFollowStaffAndSetDto implements Serializable {

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 员工姓名
     */
    private String name;

    private List<FollowStaffRuleDto> ruleDtoList;


    public static PreFollowStaffAndSetDto simplePreFollowStaffDto(SimplePreFollowStaffDto simplePreFollowStaffDto) {
        PreFollowStaffAndSetDto result =new PreFollowStaffAndSetDto();

        result.setName(simplePreFollowStaffDto.getName());
        result.setStaffId(simplePreFollowStaffDto.getStaffId());

        return result;
    }


    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<FollowStaffRuleDto> getRuleDtoList() {
        return ruleDtoList;
    }

    public void setRuleDtoList(List<FollowStaffRuleDto> ruleDtoList) {
        this.ruleDtoList = ruleDtoList;
    }
}

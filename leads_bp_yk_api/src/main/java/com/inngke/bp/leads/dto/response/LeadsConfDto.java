package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8 3:50 PM
 */
public class LeadsConfDto implements Serializable {
    /**
     * 商户ID，即bid
     *
     * @demo 1142
     */
    private Integer id;

    /**
     * 线索模块是否开启：0=未开启 1=开启
     *
     * @demo 1
     */
    private Boolean enable;

    /**
     * 线索下发方式：0=手工下发 1=按规则自动下发
     *
     * @demo 1
     */
    private Integer distributeType;

    /**
     * 线索是否允许转发给其它员工
     *
     * @demo true
     */
    private Boolean forwardEnable;

    /**
     * 线索是否允许退回:0:不允许 1:允许
     *
     * @demo true
     */
    private Boolean pushbackEnable;

    /**
     * 重复线索分配方式：0=分配给正在跟进的导购；1=分配给区域接收人
     */
    private Integer repeatDistributionWay;

    /**
     * 是否开启客服接待流程 false-未开启 true-开启
     */
    private Boolean preFollowEnable;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long createTime;

    /**
     * 更新时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long updateTime;

    /**
     * 线索去重开关（1=打开 0=关闭）
     */
    private Integer openRepeatRemove;

    /**
     * 线索去重天数
     */
    private Integer repeatRemoveDay;

    /**
     * 线索退回原因是否必填1必填,0非必填
     */
    private Boolean pushbackReason;

    /**
     * 线索退回上传凭证必填[false, true]
     *
     * @demo true
     */
    private Boolean pushbackImage;

    /**
     * 线索标记为''无效线索''是否必填原因
     */
    private Boolean leadsInvalidReason;

    /**
     * 重复线索显示
     */
    private Boolean displayRepeat;

    /**
     * 每日短信提醒,true开，false关
     */
    private Boolean textMessageNotify;

    /**
     * 自定义设置线索跟进提醒时间,单位为分钟
     */
    private List<Integer> leadsFollowNotifyList;

    public Boolean getTextMessageNotify() {
        return textMessageNotify;
    }

    public void setTextMessageNotify(Boolean textMessageNotify) {
        this.textMessageNotify = textMessageNotify;
    }

    public List<Integer> getLeadsFollowNotifyList() {
        return leadsFollowNotifyList;
    }

    public void setLeadsFollowNotifyList(List<Integer> leadsFollowNotifyList) {
        this.leadsFollowNotifyList = leadsFollowNotifyList;
    }

    public Boolean getPushbackReason() {
        return pushbackReason;
    }

    public void setPushbackReason(Boolean pushbackReason) {
        this.pushbackReason = pushbackReason;
    }

    public Boolean getLeadsInvalidReason() {
        return leadsInvalidReason;
    }

    public void setLeadsInvalidReason(Boolean leadsInvalidReason) {
        this.leadsInvalidReason = leadsInvalidReason;
    }

    public Boolean getPreFollowEnable() {
        return preFollowEnable;
    }

    public void setPreFollowEnable(Boolean preFollowEnable) {
        this.preFollowEnable = preFollowEnable;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getDistributeType() {
        return distributeType;
    }

    public void setDistributeType(Integer distributeType) {
        this.distributeType = distributeType;
    }

    public Boolean getForwardEnable() {
        return forwardEnable;
    }

    public void setForwardEnable(Boolean forwardEnable) {
        this.forwardEnable = forwardEnable;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getPushbackEnable() {
        return pushbackEnable;
    }

    public void setPushbackEnable(Boolean pushbackEnable) {
        this.pushbackEnable = pushbackEnable;
    }

    public Integer getRepeatDistributionWay() {
        return repeatDistributionWay;
    }

    public void setRepeatDistributionWay(Integer repeatDistributionWay) {
        this.repeatDistributionWay = repeatDistributionWay;
    }

    public Integer getOpenRepeatRemove() {
        return openRepeatRemove;
    }

    public void setOpenRepeatRemove(Integer openRepeatRemove) {
        this.openRepeatRemove = openRepeatRemove;
    }

    public Integer getRepeatRemoveDay() {
        return repeatRemoveDay;
    }

    public void setRepeatRemoveDay(Integer repeatRemoveDay) {
        this.repeatRemoveDay = repeatRemoveDay;
    }

    public Boolean getDisplayRepeat() {
        return displayRepeat;
    }

    public void setDisplayRepeat(Boolean displayRepeat) {
        this.displayRepeat = displayRepeat;
    }

    public Boolean getPushbackImage() {
        return pushbackImage;
    }

    public void setPushbackImage(Boolean pushbackImage) {
        this.pushbackImage = pushbackImage;
    }
}

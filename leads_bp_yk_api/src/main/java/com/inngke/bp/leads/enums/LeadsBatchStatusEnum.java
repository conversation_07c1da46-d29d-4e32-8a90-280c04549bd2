package com.inngke.bp.leads.enums;

/**
 * <AUTHOR>
 * @since 2021/9/10 14:56 PM
 */
public enum LeadsBatchStatusEnum {
    /**
     * 未处理
     */
    UNTREATED(0, "未处理"),

    /**
     * 处理中
     */
    BEING(1, "处理中"),

    /**
     * 已处理
     */
    PROCESSED(2, "已处理"),

    /**
     * 已导入
     */
    IMPORTED(3, "已导入")
    ;


    /**
     * 状态
     */
    private final int status;

    /**
     * 名称
     */
    private final String name;

    LeadsBatchStatusEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }


    public static LeadsBatchStatusEnum parse(Integer status) {
        if (status == null) {
            return null;
        }
        for (LeadsBatchStatusEnum leadsBatchStatusEnum : LeadsBatchStatusEnum.values()) {
            if (leadsBatchStatusEnum.status == status) {
                return leadsBatchStatusEnum;
            }
        }
        return null;
    }
}

package com.inngke.bp.leads.dto.response;

import com.inngke.bp.leads.enums.LeadsDistributeRoleType;
import com.inngke.bp.leads.enums.LeadsStatusEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/10/19 4:13 PM
 */
public class LeadsDistributePreviewDto implements Serializable {
    /**
     * 线索ID
     *
     * @demo 1024
     */
    private Long id;

    /**
     * 商户ID
     *
     * @demo 1142
     */
    private Integer bid;


    /**
     * 姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 手机号码
     *
     * @demo 13800138000
     */
    private String mobile;

    /**
     * 省份名称
     *
     * @demo 广东省
     */
    private String provinceName;

    /**
     * 城市名称
     *
     * @demo 广州市
     */
    private String cityName;

    /**
     * 区域名称
     *
     * @demo 越秀区
     */
    private String areaName;


    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String distributeStaffName;

    /**
     * 经销商姓名，需求调整废弃显示
     *
     * @demo 张三
     */
    @Deprecated
    private String distributeAgentName;

    /**
     * 1此线索预览分配给客服
     * 2此线索预览分配给导购
     *
     * @see LeadsDistributeRoleType
     */
    private Integer roleType;

    /**
     * 此线索分配给的员工
     */
    private Long staffId;

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    /**
     * 部门名称
     * @demo
     */
    private String departmentName;


    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getDistributeStaffName() {
        return distributeStaffName;
    }

    public void setDistributeStaffName(String distributeStaffName) {
        this.distributeStaffName = distributeStaffName;
    }

    public String getDistributeAgentName() {
        return distributeAgentName;
    }

    public void setDistributeAgentName(String distributeAgentName) {
        this.distributeAgentName = distributeAgentName;
    }
}

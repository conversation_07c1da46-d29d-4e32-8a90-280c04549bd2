package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsInvalidReasonMoveSortRequest;
import com.inngke.bp.leads.dto.request.LeadsInvalidReasonSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsInvalidReasonDto;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface LeadsInvalidReasonService {

    /**
     * 线索无效列表
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsInvalidReasonDto>> getList(BaseBidRequest request);

    /**
     * 线索无效增加，修改，删除（ enable = -1）
     * @param request
     * @return
     */
    BaseResponse save(LeadsInvalidReasonSaveRequest request);

    /**
     * 线索无效排序
     * @param request
     * @return
     */
    BaseResponse switchSort(LeadsInvalidReasonMoveSortRequest request);

}

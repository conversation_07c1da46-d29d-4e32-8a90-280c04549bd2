package com.inngke.bp.leads.enums;

public enum LeadsLevelEnum {

    UNKNOWN("未知", "未知"),

    A("有明确需求，马上需要装修", "A"),

    B("有需求，计划一个月内装修", "B"),

    C("有需求，计划三个月内装修", "C"),

    D("有需求，装修时间不确定，先了解", "D");

    private final String name;

    private final String code;

    LeadsLevelEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static LeadsLevelEnum parse(String code) {

        for (LeadsLevelEnum leadsTypeEnum : LeadsLevelEnum.values()) {
            if (leadsTypeEnum.code.equals(code)) {
                return leadsTypeEnum;
            }
        }

        return UNKNOWN;
    }
}

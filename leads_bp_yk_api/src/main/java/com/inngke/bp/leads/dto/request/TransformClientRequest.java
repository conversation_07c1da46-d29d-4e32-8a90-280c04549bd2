package com.inngke.bp.leads.dto.request;

import com.inngke.bp.leads.dto.response.LeadsFollowDto;
import com.inngke.common.dto.request.BaseBidOptRequest;

/**
 * TransformClient
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/13 20:27
 */
public class TransformClientRequest extends BaseBidOptRequest {

    /**
     * 线索id
     *
     * @demo 125123
     */
    private Long leadsId;

    /**
     * 员工id
     *
     * @demo 1314
     */
    private Long staffId;

    /**
     * 需求信息
     *
     * @demo 12313
     */
    private DemandCreateRequest demandInfo;

    /**
     * 客户信息
     *
     * @demo 131321
     */
    private ClientCreateRequest clientInfo;

    /**
     * 跟进数据
     */
    private LeadsFollowCreateRequest leadsFollow;

    public Long getLeadsId() {
        return leadsId;
    }

    public void setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public DemandCreateRequest getDemandInfo() {
        return demandInfo;
    }

    public void setDemandInfo(DemandCreateRequest demandInfo) {
        this.demandInfo = demandInfo;
    }

    public ClientCreateRequest getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(ClientCreateRequest clientInfo) {
        this.clientInfo = clientInfo;
    }

    public LeadsFollowCreateRequest getLeadsFollow() {
        return leadsFollow;
    }

    public TransformClientRequest setLeadsFollow(LeadsFollowCreateRequest leadsFollow) {
        this.leadsFollow = leadsFollow;
        return this;
    }
}

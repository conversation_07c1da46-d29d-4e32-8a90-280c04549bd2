package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/7 3:50 PM
 */
public class RegionConfDto implements Serializable {
    /**
     * 行政区域ID
     *
     * @demo 1024
     */
    private Integer id;

    /**
     * 名称(简称，主要用于展示)
     *
     * @demo 广州
     */
    private String name;

    /**
     * 下级行政区域
     */
    private List<RegionConfDto> children;

    /**
     * 指定接收的员工列表
     */
    private List<StaffSimpleInfoDto> staffList;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<RegionConfDto> getChildren() {
        return children;
    }

    public void setChildren(List<RegionConfDto> children) {
        this.children = children;
    }

    public List<StaffSimpleInfoDto> getStaffList() {
        return staffList;
    }

    public void setStaffList(List<StaffSimpleInfoDto> staffList) {
        this.staffList = staffList;
    }
}

package com.inngke.bp.leads.annotation;

import com.inngke.bp.leads.dto.request.LeadsGetRequest;
import com.inngke.bp.leads.dto.response.LeadsDto;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.bp.leads.service.LeadsService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.BidUtils;
import lombok.SneakyThrows;
import org.apache.dubbo.config.annotation.DubboReference;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/14 13:43
 */
public class LeadsIdValidatorServiceImpl implements ConstraintValidator<LeadsIdValidator, Long> {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.leads_bp_yk:}")
    private LeadsService leadsService;

    @Override
    public void initialize(LeadsIdValidator constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @SneakyThrows
    @Override
    public boolean isValid(Long leadsId, ConstraintValidatorContext constraintValidatorContext) {
        Integer bid = BidUtils.getBid();
        LeadsGetRequest leadsGetRequest = new LeadsGetRequest();
        leadsGetRequest.setBid(bid);
        leadsGetRequest.setId(leadsId);

        BaseResponse<LeadsDto> response = leadsService.getLeads(leadsGetRequest);

        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException(40000,"当前线索不存在");
        }
        LeadsDto leadsDto = response.getData();

        if (LeadsStatusEnum.delStatus().contains(leadsDto.getStatus())) {
            //线索已删除/流失
            throw new InngkeServiceException(40000,"当前线索不存在");
        }

        return true;
    }
}
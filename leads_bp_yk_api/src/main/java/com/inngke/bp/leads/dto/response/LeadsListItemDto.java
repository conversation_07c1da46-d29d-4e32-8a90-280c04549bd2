package com.inngke.bp.leads.dto.response;

import com.inngke.bp.leads.dto.request.LeadsAttachment;
import com.inngke.bp.leads.enums.LeadsStatusEnum;
import com.inngke.common.InngkeApiConst;
import com.inngke.common.annotation.TranslateName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/7 4:13 PM
 */
public class LeadsListItemDto implements Serializable {
    /**
     * 线索ID
     *
     * @demo 1024
     */
    private Long id;

    /**
     * 商户ID
     *
     * @demo 1142
     */
    private Integer bid;

    /**
     * 关联的用户ID，即customer.id
     *
     * @demo 102432
     */
    private Long customerId;

    /**
     * 关联的用户UID，即customer.uid
     *
     * @demo 102432
     */
    private Long customerUid;

    /**
     * 姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 手机号码
     *
     * @demo 13800138000
     */
    private String mobile;

    /**
     * 微信号
     *
     * @demo goodman
     */
    private String weChat;

    /**
     * 线索状态： -4=删除 -3=无效线索 -2=分配失败 -1=员工退回 0=待分配 1=未联系 2=24h内联系3=24h后联系 4=有意向 5=量尺 6=到店 7=报价 8=定金 9=待安装 10=已安装 11=已成交
     *
     * @demo 1
     * @see LeadsStatusEnum
     */
    private Integer status;

    /**
     * 转客户后的状态
     *
     * @demo 1
     * @see LeadsStatusEnum
     */
    private Integer clientStatus;

    /**
     * 省份ID，0表示未匹配
     *
     * @demo 123
     */
    private Integer provinceId;

    /**
     * 省份名称
     *
     * @demo 广东省
     */
    private String provinceName;

    /**
     * 城市ID，0表示未匹配
     *
     * @demo 4232
     */
    private Integer cityId;

    /**
     * 城市名称
     *
     * @demo 广州市
     */
    private String cityName;

    /**
     * 区域ID，0表示未匹配
     *
     * @demo 102432
     */
    private Integer areaId;

    /**
     * 区域名称
     *
     * @demo 越秀区
     */
    private String areaName;

    /**
     * 详细地址
     *
     * @demo 流花展贸中心10号馆
     */
    private String address;

    /**
     * 线索来源：0=手工输入 1=天猫 2=京东 3=抖音 4=今日头条 5=西瓜视频 100=其它
     *
     * @demo 1
     */
    private Integer channel;

    /**
     * 选择子渠道的时候，前端需要父级的也返回
     */
    private List<Integer> channelList;

    /**
     * 线索来源类型：0=未知 1=报备 2=权益
     */
    private Integer channelType;

    /**
     * 线索来源渠道，如果是报备：1=转发推广、2=填写报备
     */
    private Integer channelSource;

    /**
     * 线索来源渠道，如果是报备：1=转发推广、2=填写报备
     */
    private String channelSourceText;

    /**
     * 线索来源
     */
    private String leadsSourceText;

    /**
     * 分配的经销商ID，即shop_agent_id
     *
     * @demo 10086
     */
    @TranslateName(type = InngkeApiConst.TRANSLATE_TYPE_AGENT, nameField = "distributeAgentName")
    private Long distributeAgentId;

    /**
     * 分配给哪个员工，即staffId
     *
     * @demo 1008613
     */
    private Long distributeStaffId;

    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String distributeStaffName;

    /**
     * 员工部门信息(如果是退回状态，显示退回员工的部门)
     * @demo 测试
     */
    private String distributeStaffDepartment;

    /**
     * 经销商姓名
     *
     * @demo 张三
     */
    private String distributeAgentName;

    /**
     * 分配时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long distributeTime;

    /**
     * 退回时间时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long pushBackTime;

    /**
     * 异常原因
     *
     * @demo 未指定线索对应的区域分配员工
     */
    private String errorMsg;

    /**
     * 最新一条待跟进信息
     */
    private LeadsFollowSimpleDto follow;

    /**
     * 最后一个订单时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long lastOrderTime;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long createTime;

    /**
     * 更新时间，时间戳，粒度：毫秒
     *
     * @demo 1625053795000
     */
    private Long updateTime;

    /**
     * 支付时间
     * @demo 15755245585
     */
    private Long payTime;

    /**
     * 跟进数量
     */
    private Integer followCount;

    /**
     * 跟进数量(转客户后 + 线索)
     */
    private Integer clientFollowCount = 0;

    /**
     * 渠道
     * @demo 天猫
     */
    private String channelText;

    /**
     * 状态
     * @demo 待联系
     */
    private String statusText;

    /**
     * 客服id
     */
    private Long preFollowStaffId;

    /**
     * 客服名称
     */
    private String preFollowStaffName;

    /**
     * 客服接待时线索状态
     *
     * @demo 1
     */
    private Integer preFollowStatus;

    /**
     * 客服接待时线索状态文本
     *
     * @demo 已分配
     */
    private String preFollowStatusText;

    private String distributeFollowTime;

    private Integer type;


    /**
     * 退回员工Id
     */
    private Long pushBackStaffId;

    /**
     * 退回员工名称
     */
    private String pushBackStaffName;

    private Long pushBackId;

    private List<String> pushBackImages;

    /**
     * 跟进状态
     */
    private List<LeadsFollowStatusDto> followStatuses;

    /**
     * 客服跟进状态
     */
    private List<LeadsFollowStatusDto> kfFollowStatuses;

    /**
     * 计划名称
     */
    private String campaignName;

    /**
     * 等级 A B C D
     */
    private String level;

    /**
     * 等级 A B C D
     */
    private String levelText;

    /**
     * 详细说明
     */
    private String levelDes;

    private Integer levelId;

    /**
     * 附件列表
     */
    private List<LeadsAttachment> attachmentList;

    /**
     * 留资时间
     */
    private Long submitTime;

    /**
     * 转客户的Id，默认=0
     */
    private Long clientId;
    private List<String> enterpriseTags;

    public List<LeadsAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<LeadsAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public Long getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Long submitTime) {
        this.submitTime = submitTime;
    }

    public List<Integer> getChannelList() {
        return channelList;
    }

    public void setChannelList(List<Integer> channelList) {
        this.channelList = channelList;
    }

    public String getLevelText() {
        return levelText;
    }

    public void setLevelText(String levelText) {
        this.levelText = levelText;
    }

    public String getLevelDes() {
        return levelDes;
    }

    public void setLevelDes(String levelDes) {
        this.levelDes = levelDes;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    /**
     * 最后联系时间
     */
    private Long lastContactTime;

    public Long getLastContactTime() {
        return lastContactTime;
    }

    public void setLastContactTime(Long lastContactTime) {
        this.lastContactTime = lastContactTime;
    }

    public String getLeadsSourceText() {
        return leadsSourceText;
    }

    public void setLeadsSourceText(String leadsSourceText) {
        this.leadsSourceText = leadsSourceText;
    }

    public Long getPushBackStaffId() {
        return pushBackStaffId;
    }

    public void setPushBackStaffId(Long pushBackStaffId) {
        this.pushBackStaffId = pushBackStaffId;
    }

    public String getPushBackStaffName() {
        return pushBackStaffName;
    }

    public void setPushBackStaffName(String pushBackStaffName) {
        this.pushBackStaffName = pushBackStaffName;
    }

    public Long getPushBackId() {
        return pushBackId;
    }

    public void setPushBackId(Long pushBackId) {
        this.pushBackId = pushBackId;
    }

    public List<String> getPushBackImages() {
        return pushBackImages;
    }

    public void setPushBackImages(List<String> pushBackImages) {
        this.pushBackImages = pushBackImages;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getPreFollowStaffId() {
        return preFollowStaffId;
    }

    public void setPreFollowStaffId(Long preFollowStaffId) {
        this.preFollowStaffId = preFollowStaffId;
    }

    public String getPreFollowStaffName() {
        return preFollowStaffName;
    }

    public void setPreFollowStaffName(String preFollowStaffName) {
        this.preFollowStaffName = preFollowStaffName;
    }

    public String getChannelText() {
        return channelText;
    }

    public void setChannelText(String channelText) {
        this.channelText = channelText;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    public String getDistributeStaffDepartment() {
        return distributeStaffDepartment;
    }

    public void setDistributeStaffDepartment(String distributeStaffDepartment) {
        this.distributeStaffDepartment = distributeStaffDepartment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(Integer channelSource) {
        this.channelSource = channelSource;
    }

    public String getChannelSourceText() {
        return channelSourceText;
    }

    public void setChannelSourceText(String channelSourceText) {
        this.channelSourceText = channelSourceText;
    }

    public Long getDistributeAgentId() {
        return distributeAgentId;
    }

    public void setDistributeAgentId(Long distributeAgentId) {
        this.distributeAgentId = distributeAgentId;
    }

    public Long getDistributeStaffId() {
        return distributeStaffId;
    }

    public void setDistributeStaffId(Long distributeStaffId) {
        this.distributeStaffId = distributeStaffId;
    }

    public Long getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Long distributeTime) {
        this.distributeTime = distributeTime;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public LeadsFollowSimpleDto getFollow() {
        return follow;
    }

    public void setFollow(LeadsFollowSimpleDto follow) {
        this.follow = follow;
    }

    public Long getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Long lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCustomerUid() {
        return customerUid;
    }

    public void setCustomerUid(Long customerUid) {
        this.customerUid = customerUid;
    }

    public String getDistributeStaffName() {
        return distributeStaffName;
    }

    public void setDistributeStaffName(String distributeStaffName) {
        this.distributeStaffName = distributeStaffName;
    }

    public String getDistributeAgentName() {
        return distributeAgentName;
    }

    public void setDistributeAgentName(String distributeAgentName) {
        this.distributeAgentName = distributeAgentName;
    }

    public Long getPushBackTime() {
        return pushBackTime;
    }

    public void setPushBackTime(Long pushBackTime) {
        this.pushBackTime = pushBackTime;
    }

    public Long getPayTime() {
        return payTime;
    }

    public void setPayTime(Long payTime) {
        this.payTime = payTime;
    }

    public Integer getFollowCount() {
        return followCount;
    }

    public void setFollowCount(Integer followCount) {
        this.followCount = followCount;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Integer getPreFollowStatus() {
        return preFollowStatus;
    }

    public void setPreFollowStatus(Integer preFollowStatus) {
        this.preFollowStatus = preFollowStatus;
    }

    public String getPreFollowStatusText() {
        return preFollowStatusText;
    }

    public void setPreFollowStatusText(String preFollowStatusText) {
        this.preFollowStatusText = preFollowStatusText;
    }


    public String getDistributeFollowTime() {
        return distributeFollowTime;
    }

    public void setDistributeFollowTime(String distributeFollowTime) {
        this.distributeFollowTime = distributeFollowTime;
    }

    public List<LeadsFollowStatusDto> getFollowStatuses() {
        return followStatuses;
    }

    public void setFollowStatuses(List<LeadsFollowStatusDto> followStatuses) {
        this.followStatuses = followStatuses;
    }

    public List<LeadsFollowStatusDto> getKfFollowStatuses() {
        return kfFollowStatuses;
    }

    public void setKfFollowStatuses(List<LeadsFollowStatusDto> kfFollowStatuses) {
        this.kfFollowStatuses = kfFollowStatuses;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Integer getClientFollowCount() {
        return clientFollowCount;
    }

    public void setClientFollowCount(Integer clientFollowCount) {
        this.clientFollowCount = clientFollowCount;
    }

    public Integer getClientStatus() {
        return clientStatus;
    }

    public LeadsListItemDto setClientStatus(Integer clientStatus) {
        this.clientStatus = clientStatus;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public void setEnterpriseTags(List<String> enterpriseTags) {
        this.enterpriseTags = enterpriseTags;
    }

    public List<String> getEnterpriseTags() {
        return enterpriseTags;
    }
}

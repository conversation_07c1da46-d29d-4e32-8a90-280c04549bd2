package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

import java.util.Set;

/**
 * LeadsBatchRelationClientRequest
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/19 16:24
 */
public class LeadsBatchRelationClientRequest extends BaseBidOptRequest {

    private Set<Long> leadsIds;

    private Long clientId;

    public Set<Long> getLeadsIds() {
        return leadsIds;
    }

    public void setLeadsIds(Set<Long> leadsIds) {
        this.leadsIds = leadsIds;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }
}

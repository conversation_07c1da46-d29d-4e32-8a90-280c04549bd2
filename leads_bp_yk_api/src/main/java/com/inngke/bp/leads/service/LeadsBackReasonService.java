package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsBackReasonMoveSortRequest;
import com.inngke.bp.leads.dto.request.LeadsBackReasonSaveRequest;
import com.inngke.bp.leads.dto.response.LeadsBackReasonDto;
import com.inngke.common.dto.request.BaseBidRequest;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

public interface LeadsBackReasonService {

    /**
     * 线索退回列表(供前端使用)
     *
     * @param request
     * @return
     */
    BaseResponse<List<LeadsBackReasonDto>> getList(BaseBidRequest request);

    /**
     * 线索退回增加，修改，删除（ status = -1）
     * @param request
     * @return
     */
    BaseResponse save(LeadsBackReasonSaveRequest request);

    /**
     * 线索退回排序
     * @param request
     * @return
     */
    BaseResponse switchSort(LeadsBackReasonMoveSortRequest request);

}

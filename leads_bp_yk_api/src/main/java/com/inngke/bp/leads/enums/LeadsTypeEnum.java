package com.inngke.bp.leads.enums;

public enum LeadsTypeEnum {

    NONE("其它", 0),

    ORDER("订单类", 1),

    INFORMATION("信息类", 2);

    private final String name;

    private final Integer code;

    LeadsTypeEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }

    public static LeadsTypeEnum parse(String name) {
        if (name == null){
            return LeadsTypeEnum.NONE;
        }
        for (LeadsTypeEnum leadsTypeEnum : LeadsTypeEnum.values()) {
            if (leadsTypeEnum.name.equals(name)) {
                return leadsTypeEnum;
            }
        }

        return LeadsTypeEnum.NONE;
    }
}

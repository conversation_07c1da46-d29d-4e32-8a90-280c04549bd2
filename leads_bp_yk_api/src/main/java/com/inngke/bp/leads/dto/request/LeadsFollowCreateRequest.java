package com.inngke.bp.leads.dto.request;

import com.inngke.bp.leads.enums.LeadsStatusEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8 5:48 PM
 */
public class LeadsFollowCreateRequest extends LeadsGetRequest {
    /**
     * 线索状态：已流失:-6,已回收:-5,删除:-4,无效线索:-3,分配失败:-2,员工退回:-1,待分配:0,待联系:1,已联系:2,已成功联系:3,有意向:4,已量尺:5,已到店:6,已报价:7,已下定:8,待安装:9,已安装:10,已成交:11,客服接待:21,待确认意向:-10
     *
     * @demo 5
     * @required
     * @see LeadsStatusEnum
     */
    private Integer status;

    /**
     * 跟进记录
     *
     * @demo 已经量尺
     * @required
     */
    private String content;

    /**
     * 上传的图片地址
     *
     * @demo ["https://card-1259510193.image.myqcloud.com/image/20210831/edbf4ec4167fcd47.jpg"]
     */
    private List<String> images;

    /**
     * 员工Id
     * @demo 4455
     */
    private Long staffId;

    /**
     * 检查成交数
     */
    private boolean checkTradedCount=true;

    /**
     * 客户流失原因
     */
    private Integer clientLostReasonType;

    /**
     * 无效线索/线索流失时填写
     *
     * @demo 客户明确表示无需求
     * @required
     */
    private String reason;

    /**
     * 无效线索/线索流失时填写
     *
     * @demo 1
     * @required
     */
    private Long reasonId;

    /**
     * 客户无效原因
     */
    private Integer clientInvalidReasonType;

    /**
     * 提醒时间
     *
     * @demo 2022-03-10
     */
    private String reminderTime;

    /**
     * 计划跟进内容
     *
     * @demo xxxx
     */
    private String planContent;

    /**
     * 客户端: 小程序=mp pc=web
     *
     * @demo mp
     */
    private String client;

    public Integer getClientInvalidReasonType() {
        return clientInvalidReasonType;
    }

    public void setClientInvalidReasonType(Integer clientInvalidReasonType) {
        this.clientInvalidReasonType = clientInvalidReasonType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public boolean isCheckTradedCount() {
        return checkTradedCount;
    }

    public void setCheckTradedCount(boolean checkTradedCount) {
        this.checkTradedCount = checkTradedCount;
    }

    public Integer getClientLostReasonType() {
        return clientLostReasonType;
    }

    public void setClientLostReasonType(Integer clientLostReasonType) {
        this.clientLostReasonType = clientLostReasonType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public String getReminderTime() {
        return reminderTime;
    }

    public void setReminderTime(String reminderTime) {
        this.reminderTime = reminderTime;
    }

    public String getPlanContent() {
        return planContent;
    }

    public void setPlanContent(String planContent) {
        this.planContent = planContent;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }
}
package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/4 10:16
 */
public class MultiSetLeadsTpConfRequest extends BaseBidRequest {

    /**
     * 多个配置项
     *
     */
    @NotEmpty(message = "配置项不能为空")
    private List<SetLeadsTpConfRequest> confList;

    public List<SetLeadsTpConfRequest> getConfList() {
        return confList;
    }

    public void setConfList(List<SetLeadsTpConfRequest> confList) {
        this.confList = confList;
    }
}

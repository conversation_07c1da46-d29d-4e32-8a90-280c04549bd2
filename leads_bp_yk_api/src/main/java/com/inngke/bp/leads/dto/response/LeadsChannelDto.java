package com.inngke.bp.leads.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LeadsChannelDto implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 渠道名
     */
    private String name;

    /**
     * 父类Id
     */
    private Long parentId;

    /**
     * 渠道值
     */
    private Integer value;

    /**
     * 状态（删除=-1 停用=0 启用=1）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 编辑类型（只读=-1 只排序=0 可编辑=1）
     */
    private Integer editType;

    /**
     * 子类
     */
    private List<LeadsChannelDto> children;
}

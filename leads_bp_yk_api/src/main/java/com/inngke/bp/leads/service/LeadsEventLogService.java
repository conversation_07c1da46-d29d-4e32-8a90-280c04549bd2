package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsEventChangeRequest;
import com.inngke.bp.leads.dto.request.LeadsFollowEsBatchRequest;
import com.inngke.bp.leads.dto.request.StoreOrderCreateEventRequest;
import com.inngke.bp.leads.dto.response.LeadsEventEsDto;
import com.inngke.common.dto.response.BaseResponse;

import java.util.List;

/**
 * 跟进记录es管理 新增,更新,删除es
 */
public interface LeadsEventLogService {

    /**
     * 通过线索跟进记录构建线索事件es
     */
    BaseResponse<Boolean> structureLeadsEventLogFromLeadsFollow(LeadsFollowEsBatchRequest request);

    /**
     * 处理状态更改更新线索事件日志
     */
    BaseResponse<Boolean> handleStatusChangeUpdateLeadsEventLog(LeadsEventChangeRequest request);


    BaseResponse<Boolean> handleStoreOrderCreateAddLeadsEventLog(StoreOrderCreateEventRequest request);
}

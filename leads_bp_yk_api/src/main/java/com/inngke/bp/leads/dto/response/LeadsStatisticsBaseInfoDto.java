package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 管理员查看线索统计基础数据
 *
 * <AUTHOR>
 */
public class LeadsStatisticsBaseInfoDto implements Serializable {

    /**
     * 线索总数
     *
     * @demo 100
     */
    private Integer totalCount = 0;

    /**
     * 24小时内联系线索数
     *
     * @demo 20
     */
    private Integer contactTimelyCount = 0;

    /**
     * 待联系线索数
     *
     * @demo 10
     */
    private Integer toBeContactedCount = 0;

    /**
     * 联系中线索数
     *
     * @demo 17
     */
    private Integer InContactCount = 0;

    /**
     * 已联系数量
     */
    private Integer contactedCount = 0;

    /**
     * 已成交线索数
     *
     * @demo 12
     */
    private Integer dealCount = 0;

    /**
     * 转客户
     *
     * @demo 12
     */
    private Integer clientCount = 0;

    /**
     * 量尺数
     *
     * @demo 13
     */
    private Integer measurementCount = 0;

    private Integer depositCount = 0;

    private Integer signBillCount = 0;

    /**
     * 签单金额
     */
    private String signBillAmount = BigDecimal.ZERO.toString();

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getContactTimelyCount() {
        return contactTimelyCount;
    }

    public void setContactTimelyCount(Integer contactTimelyCount) {
        this.contactTimelyCount = contactTimelyCount;
    }

    public Integer getToBeContactedCount() {
        return toBeContactedCount;
    }

    public void setToBeContactedCount(Integer toBeContactedCount) {
        this.toBeContactedCount = toBeContactedCount;
    }

    public Integer getInContactCount() {
        return InContactCount;
    }

    public void setInContactCount(Integer inContactCount) {
        InContactCount = inContactCount;
    }

    public Integer getContactedCount() {
        return contactedCount;
    }

    public void setContactedCount(Integer contactedCount) {
        this.contactedCount = contactedCount;
    }

    public Integer getDealCount() {
        return dealCount;
    }

    public void setDealCount(Integer dealCount) {
        this.dealCount = dealCount;
    }

    public Integer getClientCount() {
        return clientCount;
    }

    public void setClientCount(Integer clientCount) {
        this.clientCount = clientCount;
    }

    public Integer getMeasurementCount() {
        return measurementCount;
    }

    public void setMeasurementCount(Integer measurementCount) {
        this.measurementCount = measurementCount;
    }

    public Integer getDepositCount() {
        return depositCount;
    }

    public void setDepositCount(Integer depositCount) {
        this.depositCount = depositCount;
    }

    public Integer getSignBillCount() {
        return signBillCount;
    }

    public void setSignBillCount(Integer signBillCount) {
        this.signBillCount = signBillCount;
    }

    public String getSignBillAmount() {
        return signBillAmount;
    }

    public void setSignBillAmount(String signBillAmount) {
        this.signBillAmount = signBillAmount;
    }
}

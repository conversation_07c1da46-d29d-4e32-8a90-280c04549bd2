package com.inngke.bp.leads.enums;

/**
 * 线索数据来源 -> channelType
 *
 * <AUTHOR>
 * @date 2022/3/2 16:48
 */
public enum LeadsDataSourceEnum {
    //线索数据来源类型：0=未知 1=报备 2=权益 3=手工导入 4=AI客服 5=飞鱼CRM 6=腾讯广告平台 7=系统对接
    UNKNOWN(0, "未知"),
    REPORTING(1, "报备"),
    RIGHTS_AND_INTERESTS(2, "权益"),
    MANUAL_IMPORT(3, "手工导入"),
    AI_CUSTOMER_SERVICE(4, "AI客服"),
    FLYING_FISH_CRM(5, "飞鱼CRM"),
    TENCENT_AD(6, "腾讯广告平台"),
    SYSTEM_DOCKING(7, "系统对接"),
    GUIDE_CREATE(8, "门店线索"),
    GUIDE_REPORT(9, "上报线索");

    private final Integer code;

    private final String name;

    LeadsDataSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LeadsDataSourceEnum parse(Integer code) {
        if (code == null) {
            return LeadsDataSourceEnum.UNKNOWN;
        }
        for (LeadsDataSourceEnum leadsDataSourceEnum : LeadsDataSourceEnum.values()) {
            if (leadsDataSourceEnum.code.equals(code)) {
                return leadsDataSourceEnum;
            }
        }
        return LeadsDataSourceEnum.UNKNOWN;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}

package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.request.BaseBidRequest;

import java.util.List;

public class LeadsIntentionTransformClient extends BaseBidOptRequest {

    private Long leadsId;

    private String content;

    private Long guideId;

    private String demandName;

    private List<String> images;
    private Long clientId;

    public Long getLeadsId() {
        return leadsId;
    }

    public LeadsIntentionTransformClient setLeadsId(Long leadsId) {
        this.leadsId = leadsId;
        return this;
    }

    public String getContent() {
        return content;
    }

    public LeadsIntentionTransformClient setContent(String content) {
        this.content = content;
        return this;
    }

    public Long getGuideId() {
        return guideId;
    }

    public LeadsIntentionTransformClient setGuideId(Long guideId) {
        this.guideId = guideId;
        return this;
    }

    public String getDemandName() {
        return demandName;
    }

    public LeadsIntentionTransformClient setDemandName(String demandName) {
        this.demandName = demandName;
        return this;
    }

    public List<String> getImages() {
        return images;
    }

    public LeadsIntentionTransformClient setImages(List<String> images) {
        this.images = images;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }
}

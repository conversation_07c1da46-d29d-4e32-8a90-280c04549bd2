package com.inngke.bp.leads.enums;

/**
 * <AUTHOR>
 * @since 2022/10/25
 **/
public enum LeadsDistributeRoleType {

    CUSTOMER_SERVICE(1, "客服"),

    GUIDE(2, "导购");

    private final Integer code;

    private final String msg;

    LeadsDistributeRoleType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

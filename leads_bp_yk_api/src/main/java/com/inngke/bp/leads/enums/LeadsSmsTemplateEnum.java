package com.inngke.bp.leads.enums;

public enum LeadsSmsTemplateEnum {

    /**
     * 量尺短信
     */
    MEASURING_RULER(1, "1352708"),

    /**
     * 到店短信
     */
    TO_STORE(2,"1352709");

    private final Integer code;

    private final String tplId;

    LeadsSmsTemplateEnum(Integer code, String tplId) {
        this.code = code;
        this.tplId = tplId;
    }

    public Integer getCode() {
        return code;
    }

    public String getTplId() {
        return tplId;
    }
}

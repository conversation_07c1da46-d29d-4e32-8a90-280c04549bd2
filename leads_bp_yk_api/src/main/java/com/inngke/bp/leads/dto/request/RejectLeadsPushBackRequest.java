package com.inngke.bp.leads.dto.request;

import com.inngke.common.dto.request.BaseBidOptRequest;

/**
 * RejectLeadsPushBackRequest
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/6 18:01
 */
public class RejectLeadsPushBackRequest extends BaseBidOptRequest {

    /**
     * 退回记录id
     *
     * @demo 123
     */
    private Long pushBackLogId;

    /**
     * 驳回原因
     *
     * @demo 122314
     */
    private String rejectReason;

    public Long getPushBackLogId() {
        return pushBackLogId;
    }

    public void setPushBackLogId(Long pushBackLogId) {
        this.pushBackLogId = pushBackLogId;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

}

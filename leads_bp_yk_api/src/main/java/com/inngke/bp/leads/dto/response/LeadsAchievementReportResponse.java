package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * LeadsAchievementReportResponse
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/11 16:00
 */
public class LeadsAchievementReportResponse implements Serializable {

    /**
     * 员工id
     *
     * @demo 1241243123
     */
    private Long staffId;

    /**
     * 员工名
     *
     * @demo 测试
     */
    private String staffName;

    /**
     * 手机号
     *
     * @demo 12141
     */
    private String mobile;

    /**
     * 部门id
     *
     * @demo 131
     */
    private Long deptId;

    /**
     * 部门名
     *
     * @demo 测试
     */
    private String deptName;

    /**
     * 部门名称链
     *
     * @demo a/b/c
     */
    private String deptChainName;

    /**
     * 经销商id
     *
     * @demo 14124
     */
    private Long agentId;

    /**
     * 经销商名
     *
     * @demo 测试
     */
    private String agentName;

    /**
     * 下发数
     *
     * @demo 12
     */
    private Integer total = 0;

    /**
     * 量尺数
     *
     * @demo 12
     */
    private Integer measureNum = 0;

    /**
     * 到店数
     *
     * @demo 41
     */
    private Integer intoStoreNum = 0;

    /**
     * 定金数
     *
     * @demo 124
     */
    private Integer depositNum = 0;


    /**
     * 成交数
     *
     * @demo 12
     */
    private Integer tradingNum = 0;

    /**
     * 流失数量
     *
     * @demo 12
     */
    private Integer lossNum = 0;

    /**
     * 定金金额
     *
     * @demo 12441
     */
    private BigDecimal depositAmount = BigDecimal.ZERO;

    /**
     * 成交金额
     *
     * @demo 12141
     */
    private BigDecimal tradingAmount = BigDecimal.ZERO;


    private Set<Long> leadsIds;


    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getAgentId() {
        return agentId;
    }

    public String getDeptChainName() {
        return deptChainName;
    }

    public void setDeptChainName(String deptChainName) {
        this.deptChainName = deptChainName;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getMeasureNum() {
        return measureNum;
    }

    public void setMeasureNum(Integer measureNum) {
        this.measureNum = measureNum;
    }

    public Integer getIntoStoreNum() {
        return intoStoreNum;
    }

    public void setIntoStoreNum(Integer intoStoreNum) {
        this.intoStoreNum = intoStoreNum;
    }

    public Integer getDepositNum() {
        return depositNum;
    }

    public void setDepositNum(Integer depositNum) {
        this.depositNum = depositNum;
    }

    public Integer getTradingNum() {
        return tradingNum;
    }

    public void setTradingNum(Integer tradingNum) {
        this.tradingNum = tradingNum;
    }

    public Integer getLossNum() {
        return lossNum;
    }

    public void setLossNum(Integer lossNum) {
        this.lossNum = lossNum;
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    public BigDecimal getTradingAmount() {
        return tradingAmount;
    }

    public void setTradingAmount(BigDecimal tradingAmount) {
        this.tradingAmount = tradingAmount;
    }

    public Set<Long> getLeadsIds() {
        return leadsIds;
    }

    public void setLeadsIds(Set<Long> leadsIds) {
        this.leadsIds = leadsIds;
    }
}

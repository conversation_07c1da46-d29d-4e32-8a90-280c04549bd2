package com.inngke.bp.leads.service;

import com.inngke.bp.leads.dto.request.LeadsEventDialRequest;
import com.inngke.bp.leads.dto.request.LeadsPhoneGetRequest;
import com.inngke.bp.leads.dto.request.LeadsPrivatePhoneCallLogRequest;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;

public interface LeadsEventService {

    /**
     * 拨打线索电话
     * @param request
     * @return
     */
    BaseResponse<Boolean> dial(LeadsEventDialRequest request);

    /**
     * 获取线索完整手机号
     * @param request
     * @return
     */
    BaseResponse<Boolean> getPhone(LeadsPhoneGetRequest request);

    /**
     * 添加线索的隐号拨打记录
     * @param request
     * @return
     */
    BaseResponse<Boolean> createCallLog(LeadsPrivatePhoneCallLogRequest request);

    /**
     * 上报联系失败事件
     * @param request
     * @return
     */
    BaseResponse<Boolean> contactFail(BaseIdRequest request);
}

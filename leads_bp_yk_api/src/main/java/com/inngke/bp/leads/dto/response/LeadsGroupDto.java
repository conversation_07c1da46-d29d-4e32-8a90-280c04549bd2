package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8 5:39 PM
 */
public class LeadsGroupDto implements Serializable {

    private final String ALL = "全部";
    private final String NOT_CONTACT = "待联系";
    private final String IN_CONTACT = "联系中";
    private final String CONTACTED = "已成交";

    public LeadsGroupDto() {
    }

    public LeadsGroupDto(Integer groupId, String groupName, Integer leadsCount) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.leadsCount = leadsCount;
    }

    public LeadsGroupDto(Integer groupId, String groupName, Integer leadsCount, List<LeadsGroupDto> children) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.leadsCount = leadsCount;
        this.children = children;
    }


    /**
     * 分组ID：1=待联系 2=跟进中 3=已成交
     *
     * @demo 1
     */
    private Integer groupId;

    /**
     * 分组名称
     *
     * @demo 待联系
     */
    private String groupName;

    /**
     * 线索数量
     *
     * @demo 12304
     */
    private Integer leadsCount;

    /**
     * 二級分类
     */
    private List<LeadsGroupDto> children = new ArrayList<>();

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
        switch (groupId){
            case 0:
                this.groupName = ALL;
                break;
            case 1:
                this.groupName = NOT_CONTACT;
                break;
            case 2:
                this.groupName = IN_CONTACT;
                break;
            case 3:
                this.groupName = CONTACTED;
                break;
            default: break;
        }
    }

    public Integer getLeadsCount() {
        return leadsCount;
    }

    public void setLeadsCount(Integer leadsCount) {
        this.leadsCount = leadsCount;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<LeadsGroupDto> getChildren() {
        return children;
    }

    public void setChildren(List<LeadsGroupDto> children) {
        this.children = children;
    }
}

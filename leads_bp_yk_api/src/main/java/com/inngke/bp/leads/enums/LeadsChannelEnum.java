package com.inngke.bp.leads.enums;

import com.inngke.bp.leads.dto.response.LeadsLostReasonDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/9/7 4:16 PM
 */
@Deprecated
public enum LeadsChannelEnum {

    //天猫、京东、抖音、巨量线索、公众号、视频号、微信客服、腾讯广告、百度、品牌官网、
    //户外广告、橙子建站、云店、星图、获客宝、住小帮、其他，共17个

    MANUAL(0, "手工输入", 0),
    TMALL(1, "天猫", 10),
    JD(2, "京东", 9),
    DOU_YIN(3, "抖音", 7),
    TOU_TIAO(4, "今日头条", 0),
    I_XI_GUA(5, "西瓜视频", 0),
    REPORT(6, "报备", 0),
    THE_PUBLIC(7, "公众号", 0),
    VIDEO(8, "视频号", 0),
    FOUR_HUNDRED_TELEPHONE(9, "400电话", 0),
    BAIDU(10, "百度", 0),
    BRAND_OFFICIAL_WEBSITE(11, "品牌官网", 0),
    OUTDOOR_ADVERTISING(12, "户外广告", 0),

    //2022-03-15-对接不同数据来源
    JU_LIANG(20, "巨量线索", 0),
    WE_CHAT_CS(21, "微信客服", 0),
    TENCENT_AD(22, "腾讯广告", 0),
    ORANGE_BUILD(23, "橙子建站", 0),
    CLOUD_SHOP(24, "云店", 0),
    START_IMG(25, "星图", 0),
    GET_OFF_TREASURE(26, "获客宝", 0),
    LIVE_IN_A_SMALL_GANG(27, "住小帮", 0),
    TITOK_ORDER(28, "抖音订单", 8),

    ZH(29, "知乎", 0),

    MT(30, "美团", 0),

    BRAND_ACTIVITY(31, "品牌活动", 0),

    MP(32, "小程序", 0),

    XIAO_HONG_SHU(33, "小红书", 0),

    TAO_XI(34, "淘系", 0),

    QI_JIA_WANG(35, "齐家网", 0),

    WEIBO(36, "新浪微博", 0),

    PDD(37, "拼多多", 0),

    OTHER(100, "其它", 0);

    private final int channel;

    private final String name;

    private final int sort;

    public static ArrayList<Integer> ORDER_CLASS_CHANNEL = new ArrayList<Integer>() {{
        add(LeadsChannelEnum.TMALL.getChannel());
        add(LeadsChannelEnum.JD.getChannel());
    }};

    LeadsChannelEnum(Integer channel, String name, Integer sort) {
        this.channel = channel;
        this.name = name;
        this.sort = sort;
    }

    public Integer getChannel() {
        return channel;
    }

    public String getName() {
        return name;
    }

    public int getSort() {
        return sort;
    }

    public static Integer getChannelByName(String name) {
        for (LeadsChannelEnum ele : values()) {
            if (ele.getName().equals(name)) {
                return ele.getChannel();
            }
        }
        return null;
    }

    public static String getNameByChannel(Integer value) {
        for (LeadsChannelEnum ele : values()) {
            if (ele.getChannel().equals(value)) {
                return ele.getName();
            }
        }
        return null;
    }

    public static boolean isOrder(String name) {
        return ORDER_CLASS_CHANNEL.contains(getChannelByName(name));
    }

    public static boolean isOrder(Integer code) {
        return ORDER_CLASS_CHANNEL.contains(code);
    }

    public static LeadsChannelEnum parseByName(String name) {
        if (name == null || name.length() == 0) {
            return LeadsChannelEnum.OTHER;
        }
        for (LeadsChannelEnum leadsChannelEnum : LeadsChannelEnum.values()) {
            if (leadsChannelEnum.getName().equals(name)) {
                return leadsChannelEnum;
            }
        }

        return LeadsChannelEnum.OTHER;
    }

    /**
     * 将enums转换为List
     *
     * @return
     */
    public static List<Integer> getChannelList() {
        ArrayList<Integer> channelList = new ArrayList<>();
        for (LeadsChannelEnum channel : LeadsChannelEnum.values()) {
            if (!Objects.equals(channel.getChannel(), 100)) {
                channelList.add(channel.getChannel());
            }
        }
        return channelList;
    }
}

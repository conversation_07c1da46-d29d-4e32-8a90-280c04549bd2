package com.inngke.bp.leads.dto.response;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/7 17:14
 */
public class FollowStaffRuleDto implements Serializable {

    /**
     * 规则id
     */
    private Long id;

    /**
     * 规则名
     */
    private String name;

    /**
     * 渠道ID列表
     */
    private Set<String> channelIds;

    /**
     * 区域ID列表
     */
    private Set<String> regionIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Set<String> getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(Set<String> channelIds) {
        this.channelIds = channelIds;
    }

    public Set<String> getRegionIds() {
        return regionIds;
    }

    public void setRegionIds(Set<String> regionIds) {
        this.regionIds = regionIds;
    }
}

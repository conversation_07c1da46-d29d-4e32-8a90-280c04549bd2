package com.inngke.bp.leads.dto.response;

public class LeadsFollowTimeDto {

    /**
     * 线索ID，即leads.id
     */
    private Long id;

    /**
     * 商户ID
     */
    private Integer bid;

    /**
     * 经销商ID
     */
    private Long agentId;

    /**
     * 跟进人员工ID，即staffId
     */
    private Long staffId;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 线索分配的时间
     */
    private Long distributeTime;

    /**
     * 被标识为无效的时间
     */
    private Long stateNoAvail;

    /**
     * 首次联系时间
     */
    private Long stateContact;

    /**
     * 成功联系时间
     */
    private Long stateContactSuccess;

    /**
     * 量尺时间
     */
    private Long stateMeasuring;

    /**
     * 到店时间
     */
    private Long stateArrivalStore;

    /**
     * 定金记录创建时间
     */
    private Long stateDeposit;

    /**
     * 成交时间
     */
    private Long stateOrderSuccess;

    /**
     * 报价时间
     */
    private Long stateOfferPrice;

    /**
     * 安装时间
     */
    private Long stateInstall;

    /**
     * 转客户时间
     */
    private Long transferClientTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public Long getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Long distributeTime) {
        this.distributeTime = distributeTime;
    }

    public Long getStateNoAvail() {
        return stateNoAvail;
    }

    public void setStateNoAvail(Long stateNoAvail) {
        this.stateNoAvail = stateNoAvail;
    }

    public Long getStateContact() {
        return stateContact;
    }

    public void setStateContact(Long stateContact) {
        this.stateContact = stateContact;
    }

    public Long getStateContactSuccess() {
        return stateContactSuccess;
    }

    public void setStateContactSuccess(Long stateContactSuccess) {
        this.stateContactSuccess = stateContactSuccess;
    }

    public Long getStateMeasuring() {
        return stateMeasuring;
    }

    public void setStateMeasuring(Long stateMeasuring) {
        this.stateMeasuring = stateMeasuring;
    }

    public Long getStateArrivalStore() {
        return stateArrivalStore;
    }

    public void setStateArrivalStore(Long stateArrivalStore) {
        this.stateArrivalStore = stateArrivalStore;
    }

    public Long getStateDeposit() {
        return stateDeposit;
    }

    public void setStateDeposit(Long stateDeposit) {
        this.stateDeposit = stateDeposit;
    }

    public Long getStateOrderSuccess() {
        return stateOrderSuccess;
    }

    public void setStateOrderSuccess(Long stateOrderSuccess) {
        this.stateOrderSuccess = stateOrderSuccess;
    }

    public Long getStateOfferPrice() {
        return stateOfferPrice;
    }

    public void setStateOfferPrice(Long stateOfferPrice) {
        this.stateOfferPrice = stateOfferPrice;
    }

    public Long getStateInstall() {
        return stateInstall;
    }

    public void setStateInstall(Long stateInstall) {
        this.stateInstall = stateInstall;
    }

    public Long getTransferClientTime() {
        return transferClientTime;
    }

    public void setTransferClientTime(Long transferClientTime) {
        this.transferClientTime = transferClientTime;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.inngke.bp</groupId>
    <artifactId>leads_bp_yk_api</artifactId>
    <version>${leads_bp_yk_api.version}</version>

    <properties>
        <jdk.version>11</jdk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.inngke.ip</groupId>
            <artifactId>common_ip_yk_api</artifactId>
            <version>${common_ip_yk_api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-api</artifactId>
            <version>${yk-common-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.13.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.12</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-app-brand</artifactId>
            <version>${yk-common-app-brand.version}</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <leads_bp_yk_api.version>2.0.0-SNAPSHOT</leads_bp_yk_api.version>

                <yk-common-api.version>2.0.0-SNAPSHOT</yk-common-api.version>
                <common_ip_yk_api.version>2.0.0-SNAPSHOT</common_ip_yk_api.version>
                <yk-common-app-brand.version>2.0.0-SNAPSHOT</yk-common-app-brand.version>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <leads_bp_yk_api.version>3.0.86</leads_bp_yk_api.version>

                <yk-common-api.version>3.0.0</yk-common-api.version>
                <common_ip_yk_api.version>3.0.27</common_ip_yk_api.version>
                <yk-common-app-brand.version>3.0.22</yk-common-app-brand.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.0.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>inngke-java-maven</id>
            <name>maven</name>
            <url>https://inngke-maven.pkg.coding.net/repository/java/maven/</url>
        </repository>
    </distributionManagement>
</project>

package com.inngke.bp.leads;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @since 2021/9/06 22:28
 */
@SpringBootApplication(scanBasePackages = "com.inngke")
@EnableScheduling
@EnableDiscoveryClient
//@EnableLogRecord(tenant = "com.inngke")
public class Application {
    public static void main(String[] args) {
        new SpringApplication(Application.class).run(args);
    }
}
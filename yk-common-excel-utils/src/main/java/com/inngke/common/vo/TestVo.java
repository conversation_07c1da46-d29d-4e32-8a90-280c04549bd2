package com.inngke.common.vo;

import com.inngke.common.utils.DateUtils;
import com.inngke.common.utils.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class TestVo {

    @Excel(name = "姓名")
    String name;

    @Excel(name = "手机号")
    String phone;

    @Excel(name = "省")
    String province;

    @Excel(name = "市")
    String city;

    @Excel(name = "区/县")
    String area;

    @Excel(name = "详细地址")
    String address;

    @Excel(name = "渠道来源")
    String channelSoure;

    @Excel(name = "线索id")
    String channelId;

    @Excel(name = "广告活动名称")
    String activityName;

    @Excel(name = "创建时间")
    Date createdTime;

    @Excel(name = "其他备注")
    String remarks;

    @Override
    public String toString() {
        return "TestVo{" +
                "name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", area='" + area + '\'' +
                ", address='" + address + '\'' +
                ", channelSoure='" + channelSoure + '\'' +
                ", channelId='" + channelId + '\'' +
                ", activityName='" + activityName + '\'' +
                ", createdTime='" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,createdTime) + '\'' +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
